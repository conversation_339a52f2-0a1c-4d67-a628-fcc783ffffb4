"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["node_modules_github_file-attachment-element_dist_index_js","vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_github_remote--3c9c82"],{91707:(t,e,i)=>{i.r(e),i.d(e,{Attachment:()=>Attachment,default:()=>m});let Attachment=class Attachment{constructor(t,e){this.file=t,this.directory=e,this.state="pending",this.id=null,this.href=null,this.name=null,this.percent=0}static traverse(t,e){var i,s;return i=t,e&&(s=i).items&&Array.from(s.items).some(t=>{let e=t.webkitGetAsEntry&&t.webkitGetAsEntry();return e&&e.isDirectory})?r("",Array.from(i.items).map(t=>t.webkitGetAsEntry()).filter(t=>null!=t)):Promise.resolve(n(Array.from(i.files||[])).map(t=>new Attachment(t)))}static from(t){let e=[];for(let i of t)if(i instanceof File)e.push(new Attachment(i));else if(i instanceof Attachment)e.push(i);else throw Error("Unexpected type");return e}get fullPath(){return this.directory?`${this.directory}/${this.file.name}`:this.file.name}isImage(){return["image/gif","image/png","image/jpg","image/jpeg","image/svg+xml"].indexOf(this.file.type)>-1}isVideo(){return["video/mp4","video/quicktime"].indexOf(this.file.type)>-1}saving(t){if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saving`);this.state="saving",this.percent=t}saved(t){var e,i,n;if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saved`);this.state="saved",this.id=null!=(e=null==t?void 0:t.id)?e:null,this.href=null!=(i=null==t?void 0:t.href)?i:null,this.name=null!=(n=null==t?void 0:t.name)?n:null}isPending(){return"pending"===this.state}isSaving(){return"saving"===this.state}isSaved(){return"saved"===this.state}};function n(t){return Array.from(t).filter(t=>!t.name.startsWith("."))}async function r(t,e){let i=[];for(let s of n(e))if(s.isDirectory)i.push(...await r(s.fullPath,await function(t){return new Promise(function(e,i){let n=[],r=t.createReader(),s=()=>{r.readEntries(t=>{t.length>0?(n.push(...t),s()):e(n)},i)};s()})}(s)));else{let e=await function(t){return new Promise(function(e,i){t.file(e,i)})}(s);i.push(new Attachment(e,t))}return i}let FileAttachmentElement=class FileAttachmentElement extends HTMLElement{connectedCallback(){this.addEventListener("dragenter",o),this.addEventListener("dragover",o),this.addEventListener("dragleave",l),this.addEventListener("drop",u),this.addEventListener("paste",d),this.addEventListener("change",h)}disconnectedCallback(){this.removeEventListener("dragenter",o),this.removeEventListener("dragover",o),this.removeEventListener("dragleave",l),this.removeEventListener("drop",u),this.removeEventListener("paste",d),this.removeEventListener("change",h)}get directory(){return this.hasAttribute("directory")}set directory(t){t?this.setAttribute("directory",""):this.removeAttribute("directory")}async attach(t){let e=t instanceof DataTransfer?await Attachment.traverse(t,this.directory):Attachment.from(t);this.dispatchEvent(new CustomEvent("file-attachment-accept",{bubbles:!0,cancelable:!0,detail:{attachments:e}}))&&e.length&&this.dispatchEvent(new CustomEvent("file-attachment-accepted",{bubbles:!0,detail:{attachments:e}}))}};function s(t){return Array.from(t.types).indexOf("Files")>=0}let a=null;function o(t){let e=t.currentTarget;a&&clearTimeout(a),a=window.setTimeout(()=>e.removeAttribute("hover"),200);let i=t.dataTransfer;i&&s(i)&&(i.dropEffect="copy",e.setAttribute("hover",""),t.preventDefault())}function l(t){t.dataTransfer&&(t.dataTransfer.dropEffect="none"),t.currentTarget.removeAttribute("hover"),t.stopPropagation(),t.preventDefault()}function u(t){let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;e.removeAttribute("hover");let i=t.dataTransfer;i&&s(i)&&(e.attach(i),t.stopPropagation(),t.preventDefault())}let c=/^image\/(gif|png|jpeg)$/;function d(t){if(!t.clipboardData||!t.clipboardData.items)return;let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;let i=function(t){for(let e of t)if("file"===e.kind&&c.test(e.type))return e.getAsFile();return null}(t.clipboardData.items);i&&(e.attach([i]),t.preventDefault())}function h(t){let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;let i=t.target;if(!(i instanceof HTMLInputElement))return;let n=e.getAttribute("input");if(n&&i.id!==n)return;let r=i.files;r&&0!==r.length&&(e.attach(r),i.value="")}window.customElements.get("file-attachment")||(window.FileAttachmentElement=FileAttachmentElement,window.customElements.define("file-attachment",FileAttachmentElement));let m=FileAttachmentElement},13937:(t,e,i)=>{let n;i.d(e,{Ax:()=>l,JW:()=>u,ZV:()=>o});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(t,e){super(t),this.response=e}};function r(){let t,e;return[new Promise(function(i,n){t=i,e=n}),t,e]}let s=[],a=[];function o(t){s.push(t)}function l(t){a.push(t)}function u(t,e){n||(n=new Map,"undefined"!=typeof document&&document.addEventListener("submit",c));let i=n.get(t)||[];n.set(t,[...i,e])}function c(t){let e;if(!(t.target instanceof HTMLFormElement)||t.defaultPrevented)return;let i=t.target,o=function(t){let e=[],i=e=>"object"==typeof e?e===t:"string"==typeof e&&t.matches(e);for(let t of n.keys())if(i(t)){let i=n.get(t)||[];e.push(...i)}return e}(i);if(0===o.length)return;let l=function(t,e){let i={method:e?.formMethod||t.method||"GET",url:t.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===i.method.toUpperCase()){let e=function(t){let e=new URLSearchParams;for(let[i,n]of[...new FormData(t).entries()])e.append(i,n.toString());return e.toString()}(t);e&&(i.url+=(~i.url.indexOf("?")?"&":"?")+e)}else i.body=new FormData(t);return i}(i,t instanceof SubmitEvent?t.submitter:null),[u,c,m]=r();t.preventDefault(),d(o,i,l,u).then(async t=>{if(t){for(let t of a)await t(i);h(l).then(c,m).catch(()=>{}).then(()=>{for(let t of s)t(i)})}else i.submit()},t=>{i.submit(),setTimeout(()=>{throw t})})}async function d(t,e,i,n){let s=!1;for(let a of t){let[t,o]=r(),l=()=>(s=!0,o(),n),u={text:l,json:()=>(i.headers.set("Accept","application/json"),l()),html:()=>(i.headers.set("Accept","text/html"),l())};await Promise.race([t,a(e,u,i)])}return s}async function h(t){let e=await window.fetch(t.url,{method:t.method,body:null!==t.body?t.body:void 0,headers:t.headers,credentials:"same-origin"}),i={url:e.url,status:e.status,statusText:e.statusText,headers:e.headers,text:"",get json(){let t=JSON.parse(this.text);return delete this.json,this.json=t,this.json},get html(){return delete this.html,this.html=function(t,e){let i=t.createElement("template");return i.innerHTML=e,t.importNode(i.content,!0)}(document,this.text),this.html}};if(i.text=await e.text(),e.ok)return i;throw new ErrorWithResponse("request failed",i)}},35908:(t,e,i)=>{i.d(e,{A:()=>Combobox});let Combobox=class Combobox{constructor(t,e,{tabInsertsSuggestions:i,firstOptionSelectionMode:n,scrollIntoViewOptions:s}={}){this.input=t,this.list=e,this.tabInsertsSuggestions=null==i||i,this.firstOptionSelectionMode=null!=n?n:"none",this.scrollIntoViewOptions=null!=s?s:{block:"nearest",inline:"nearest"},this.isComposing=!1,e.id||(e.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=t=>(function(t,e){if(!t.shiftKey&&!t.metaKey&&!t.altKey&&(e.ctrlBindings||!t.ctrlKey)&&!e.isComposing)switch(t.key){case"Enter":r(e.input,e.list)&&t.preventDefault();break;case"Tab":e.tabInsertsSuggestions&&r(e.input,e.list)&&t.preventDefault();break;case"Escape":e.clearSelection();break;case"ArrowDown":e.navigate(1),t.preventDefault();break;case"ArrowUp":e.navigate(-1),t.preventDefault();break;case"n":e.ctrlBindings&&t.ctrlKey&&(e.navigate(1),t.preventDefault());break;case"p":e.ctrlBindings&&t.ctrlKey&&(e.navigate(-1),t.preventDefault());break;default:if(t.ctrlKey)break;e.resetSelection()}})(t,this),this.compositionEventHandler=t=>(function(t,e){e.isComposing="compositionstart"===t.type,document.getElementById(e.input.getAttribute("aria-controls")||"")&&e.clearSelection()})(t,this),this.inputHandler=this.clearSelection.bind(this),t.setAttribute("role","combobox"),t.setAttribute("aria-controls",e.id),t.setAttribute("aria-expanded","false"),t.setAttribute("aria-autocomplete","list"),t.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",n),this.resetSelection()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",n)}indicateDefaultOption(){var t;"active"===this.firstOptionSelectionMode?null==(t=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(s)[0])||t.setAttribute("data-combobox-option-default","true"):"selected"===this.firstOptionSelectionMode&&this.navigate(1)}navigate(t=1){let e=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(s)[0],i=Array.from(this.list.querySelectorAll('[role="option"]')).filter(s),n=i.indexOf(e);if(n===i.length-1&&1===t||0===n&&-1===t){this.clearSelection(),this.input.focus();return}let r=1===t?0:i.length-1;if(e&&n>=0){let e=n+t;e>=0&&e<i.length&&(r=e)}let a=i[r];if(a)for(let t of i)t.removeAttribute("data-combobox-option-default"),a===t?(this.input.setAttribute("aria-activedescendant",a.id),a.setAttribute("aria-selected","true"),a.dispatchEvent(new Event("combobox-select",{bubbles:!0})),a.scrollIntoView(this.scrollIntoViewOptions)):t.removeAttribute("aria-selected")}clearSelection(){for(let t of(this.input.removeAttribute("aria-activedescendant"),this.list.querySelectorAll('[aria-selected="true"], [data-combobox-option-default="true"]')))t.removeAttribute("aria-selected"),t.removeAttribute("data-combobox-option-default")}resetSelection(){this.clearSelection(),this.indicateDefaultOption()}};function n(t){if(!(t.target instanceof Element))return;let e=t.target.closest('[role="option"]');if(e){var i,n;"true"!==e.getAttribute("aria-disabled")&&(i=e,n={event:t},i.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:n})))}}function r(t,e){let i=e.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!i&&("true"===i.getAttribute("aria-disabled")||(i.click(),!0))}function s(t){return!t.hidden&&!(t instanceof HTMLInputElement&&"hidden"===t.type)&&(t.offsetWidth>0||t.offsetHeight>0)}}}]);
//# sourceMappingURL=vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_github_remote--3c9c82-d2a44178bf70.js.map