"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["element-registry"],{14487:(e,s,t)=>{(0,t(39595).Se)({"animated-image":()=>Promise.all([t.e("vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_hydro-analytics-c-35f15c"),t.e("app_components_accessibility_animated-image-element_ts")]).then(t.bind(t,47433)),"actions-caches-filter":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_delegated-events_dist_index_js-node_modules_github_hotkey_dist_index_js--870134"),t.e("ui_packages_fetch-headers_fetch-headers_ts-ui_packages_form-utils_form-utils_ts-ui_packages_i-d3f7bc"),t.e("app_assets_modules_github_filter-input_ts"),t.e("node_modules_lit-html_directives_until_js-app_components_actions_actions-caches-filter-elemen-3b13d7")]).then(t.bind(t,75929)),"actions-workflow-filter":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_delegated-events_dist_index_js-node_modules_github_hotkey_dist_index_js--870134"),t.e("ui_packages_fetch-headers_fetch-headers_ts-ui_packages_form-utils_form-utils_ts-ui_packages_i-d3f7bc"),t.e("app_assets_modules_github_filter-input_ts"),t.e("node_modules_lit-html_directives_until_js-app_components_actions_actions-workflow-filter-elem-8a2138")]).then(t.bind(t,29071)),"actions-workflow-list":()=>t.e("app_components_actions_actions-workflow-list-element_ts").then(t.bind(t,13517)),"variable-value":()=>t.e("app_components_actions_variables_variable-value-element_ts").then(t.bind(t,65531)),"variables-input":()=>t.e("app_components_actions_variables_variables-input-element_ts").then(t.bind(t,32257)),"variables-pagination":()=>t.e("app_components_actions_variables_variables-pagination-element_ts").then(t.bind(t,34075)),"cvss-calculator":()=>t.e("app_components_advisories_cvss-calculator-element_ts").then(t.bind(t,92595)),"cvss-calculator-metric":()=>t.e("app_components_advisories_cvss-calculator-metric-element_ts").then(t.bind(t,64250)),"metric-selection":()=>t.e("app_components_advisories_metric-selection-element_ts").then(t.bind(t,67076)),"severity-calculator":()=>t.e("app_components_advisories_severity-calculator-element_ts").then(t.bind(t,67655)),"severity-score":()=>t.e("app_components_advisories_severity-score-element_ts").then(t.bind(t,30091)),"severity-selection":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("app_components_advisories_severity-selection-element_ts")]).then(t.bind(t,83539)),"severity-selection-next":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("app_components_advisories_severity-selection-next-element_ts")]).then(t.bind(t,72919)),"severity-tracking":()=>t.e("app_components_advisories_severity-tracking-element_ts").then(t.bind(t,10406)),"webauthn-status":()=>t.e("app_components_behaviors_webauthn-status-element_ts").then(t.bind(t,35064)),"downgrade-dialog":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("app_components_billing_settings_downgrade-dialog-element_ts")]).then(t.bind(t,58343)),"manage-subscription":()=>t.e("app_components_billing_settings_upgrade_manage-subscription-element_ts").then(t.bind(t,87627)),"pending-cycle-changes-component":()=>t.e("app_components_billing_stafftools_pending-cycle-changes-component-element_ts").then(t.bind(t,64129)),"create-branch":()=>Promise.all([t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_virtualized-list_es_inde-5cfb7e"),t.e("app_assets_modules_github_ref-selector_ts"),t.e("app_components_branch_create-branch-element_ts")]).then(t.bind(t,95125)),"create-repo-from-selector":()=>t.e("app_components_branch_create-repo-from-selector-element_ts").then(t.bind(t,46862)),"select-all":()=>t.e("app_components_businesses_people_select-all-element_ts").then(t.bind(t,15826)),"close-reason-selector":()=>t.e("app_components_closables_buttons_close-reason-selector-element_ts").then(t.bind(t,26399)),"reopen-reason-selector":()=>t.e("app_components_closables_buttons_reopen-reason-selector-element_ts").then(t.bind(t,5794)),"alert-dismissal-details":()=>t.e("app_components_code_scanning_alert-dismissal-details-element_ts").then(t.bind(t,11572)),"code-scanning-alert-filter":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_delegated-events_dist_index_js-node_modules_github_hotkey_dist_index_js--870134"),t.e("ui_packages_fetch-headers_fetch-headers_ts-ui_packages_form-utils_form-utils_ts-ui_packages_i-d3f7bc"),t.e("app_assets_modules_github_filter-input_ts"),t.e("node_modules_lit-html_directives_until_js-app_components_code_scanning_code-scanning-alert-fi-158275")]).then(t.bind(t,11798)),"pretty-cron":()=>Promise.all([t.e("vendors-node_modules_cronstrue_dist_cronstrue_js"),t.e("app_components_code_scanning_pretty-cron-element_ts")]).then(t.bind(t,73934)),"timeout-content":()=>t.e("app_components_code_scanning_timeout-content-element_ts").then(t.bind(t,68722)),"message-list":()=>t.e("app_components_code_scanning_tool_status_message-list-element_ts").then(t.bind(t,77979)),"sku-list":()=>t.e("app_components_codespaces_advanced_options_sku-list-element_ts").then(t.bind(t,94496)),"create-button":()=>t.e("app_components_codespaces_create-button-element_ts").then(t.bind(t,12115)),"editor-forwarder":()=>t.e("app_components_codespaces_editor-forwarder-element_ts").then(t.bind(t,3542)),"command-palette-page":()=>Promise.all([t.e("vendors-node_modules_allex_crc32_lib_crc32_esm_js-node_modules_github_mini-throttle_dist_deco-26fa0f"),t.e("app_assets_modules_github_command-palette_items_help-item_ts-app_assets_modules_github_comman-48ad9d"),t.e("app_components_command_palette_command-palette-page-element_ts")]).then(t.bind(t,40988)),"command-palette-page-stack":()=>Promise.all([t.e("vendors-node_modules_allex_crc32_lib_crc32_esm_js-node_modules_github_mini-throttle_dist_deco-26fa0f"),t.e("app_assets_modules_github_command-palette_items_help-item_ts-app_assets_modules_github_comman-48ad9d"),t.e("app_components_command_palette_command-palette-page-stack-element_ts")]).then(t.bind(t,61859)),"feed-post":()=>t.e("app_components_conduit_feed-post-element_ts").then(t.bind(t,21447)),"copilot-signup-choose-plan-type":()=>t.e("app_components_copilot_copilot-signup-choose-plan-type-element_ts").then(t.bind(t,24928)),"copilot-business-signup-seat-management":()=>t.e("app_components_copilot_seat_management_copilot-business-signup-seat-management-element_ts").then(t.bind(t,78684)),"loading-context":()=>t.e("app_components_dashboard_loading-context-element_ts").then(t.bind(t,14454)),"portal-fragment":()=>t.e("app_components_dashboard_portal-fragment-element_ts").then(t.bind(t,68509)),"query-search":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_delegated-events_dist_index_js-node_modules_github_hotkey_dist_index_js--870134"),t.e("ui_packages_fetch-headers_fetch-headers_ts-ui_packages_form-utils_form-utils_ts-ui_packages_i-d3f7bc"),t.e("app_assets_modules_github_filter-input_ts"),t.e("app_components_dashboard_query-search-element_ts-node_modules_github_memoize_dist_esm_decorator_js")]).then(t.bind(t,83235)),"dependabot-alert-dismissal":()=>t.e("app_components_dependabot_alerts_dependabot-alert-dismissal-element_ts").then(t.bind(t,64537)),"dependabot-alert-load-all":()=>t.e("app_components_dependabot_alerts_dependabot-alert-load-all-element_ts").then(t.bind(t,69950)),"dependabot-alert-row":()=>t.e("app_components_dependabot_alerts_dependabot-alert-row-element_ts").then(t.bind(t,47872)),"dependabot-alert-table-header":()=>t.e("app_components_dependabot_alerts_dependabot-alert-table-header-element_ts").then(t.bind(t,95434)),"dependabot-updates-paused":()=>t.e("app_components_dependabot_dependabot-updates-paused-element_ts").then(t.bind(t,24566)),"deferred-diff-lines":()=>t.e("app_components_diffs_deferred-diff-lines-element_ts").then(t.bind(t,98695)),"edit-history":()=>t.e("app_components_discussions_edit-history-element_ts").then(t.bind(t,52782)),"conduit-profile-feed-visibility":()=>t.e("app_components_feed_conduit-profile-feed-visibility-element_ts").then(t.bind(t,82634)),"readme-toc":()=>t.e("app_components_files_readme-toc-element_ts").then(t.bind(t,67724)),"delayed-loading":()=>t.e("app_components_github_delayed-loading-element_ts").then(t.bind(t,55898)),"remote-pagination":()=>t.e("app_components_github_remote-pagination-element_ts").then(t.bind(t,37524)),"dialog-hydro":()=>t.e("app_components_hydro_dialog-hydro-element_ts").then(t.bind(t,48985)),"track-view":()=>t.e("app_components_hydro_track-view-element_ts").then(t.bind(t,78857)),"development-menu":()=>t.e("app_components_issues_references_development-menu-element_ts").then(t.bind(t,76542)),"load-versions":()=>t.e("app_components_marketplace_load-versions-element_ts").then(t.bind(t,23569)),"math-renderer":()=>Promise.all([t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("app_components_mathjax_math-renderer-element_ts")]).then(t.bind(t,45063)),"memex-project-picker":()=>Promise.all([t.e("app_assets_modules_github_virtual-listbox-focus-state_ts-node_modules_github_template-parts_l-448df2"),t.e("app_components_memex_memex-project-picker-element_ts")]).then(t.bind(t,6841)),"memex-project-picker-panel":()=>t.e("app_components_memex_memex-project-picker-panel-element_ts").then(t.bind(t,61266)),"memex-project-picker-interstitial":()=>t.e("app_components_memex_project_list_memex-project-picker-interstitial-element_ts").then(t.bind(t,31919)),"memex-project-picker-unlink":()=>t.e("app_components_memex_project_list_memex-project-picker-unlink-element_ts").then(t.bind(t,71882)),"project-buttons-list":()=>t.e("app_components_memex_project_list_project-buttons-list-element_ts").then(t.bind(t,96783)),"navigation-list":()=>t.e("app_components_navigation_navigation-list-element_ts").then(t.bind(t,3749)),"notification-shelf-watcher":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("ui_packages_updatable-content_updatable-content_ts"),t.e("node_modules_scroll-anchoring_dist_scroll-anchoring_esm_js-app_components_notifications_notif-ab7b66")]).then(t.bind(t,84695)),"feature-request":()=>t.e("app_components_organizations_member_requests_feature-request-element_ts").then(t.bind(t,86044)),"allowed-values-input":()=>t.e("app_components_organizations_settings_codespaces_policy_form_constraint_row_allowed-values-in-672002").then(t.bind(t,31052)),"host-setup":()=>t.e("app_components_organizations_settings_codespaces_policy_form_constraint_row_host-setup-element_ts").then(t.bind(t,48218)),"max-value":()=>t.e("app_components_organizations_settings_codespaces_policy_form_constraint_row_max-value-element_ts").then(t.bind(t,20450)),"codespaces-policy-form":()=>t.e("app_components_organizations_settings_codespaces-policy-form-element_ts").then(t.bind(t,1586)),"private-registry-form":()=>t.e("app_components_organizations_settings_private-registry-form-element_ts").then(t.bind(t,57168)),"repository-selection-input":()=>t.e("app_components_packages_repository-selection-input-element_ts").then(t.bind(t,79571)),"experimental-action-menu":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("app_components_primer_action_menu_experimental-action-menu-element_ts")]).then(t.bind(t,34511)),"custom-focus-group":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("app_components_primer_experimental_select_menu_custom-focus-group-element_ts")]).then(t.bind(t,13429)),"select-panel":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("app_components_primer_experimental_select-panel-element_ts")]).then(t.bind(t,91953)),"internal-nav-list-group":()=>t.e("app_components_primer_experimental_side_panel_nav_list_internal-nav-list-group-element_ts").then(t.bind(t,44533)),"split-page-layout":()=>t.e("app_components_primer_experimental_split-page-layout-element_ts").then(t.bind(t,80619)),"toggle-switch":()=>t.e("app_components_primer_experimental_toggle-switch-element_ts").then(t.bind(t,47677)),"lazy-load-section":()=>t.e("app_components_primer_navigation_list_lazy-load-section-element_ts").then(t.bind(t,88806)),"profile-timezone":()=>t.e("app_components_profiles_profile-timezone-element_ts").then(t.bind(t,9791)),"comment-actions":()=>t.e("app_components_pull_requests_comment-actions-element_ts").then(t.bind(t,44033)),"copilot-marketing-popover":()=>t.e("app_components_pull_requests_copilot-marketing-popover-element_ts").then(t.bind(t,36791)),"file-filter":()=>t.e("app_components_pull_requests_file_tree_file-filter-element_ts").then(t.bind(t,93337)),"file-tree":()=>t.e("app_components_pull_requests_file_tree_file-tree-element_ts").then(t.bind(t,58903)),"file-tree-toggle":()=>t.e("app_components_pull_requests_file_tree_file-tree-toggle-element_ts").then(t.bind(t,42082)),"reactions-menu":()=>t.e("app_components_reactions_reactions-menu-element_ts").then(t.bind(t,91632)),"pin-organization-repo":()=>t.e("app_components_repositories_pin-organization-repo-element_ts").then(t.bind(t,75022)),"custom-scopes":()=>t.e("app_components_search_custom-scopes-element_ts").then(t.bind(t,88001)),"feature-preview-auto-enroll":()=>t.e("app_components_search_feature-preview-auto-enroll-element_ts").then(t.bind(t,52681)),"qbsearch-input":()=>Promise.all([t.e("vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js"),t.e("vendors-node_modules_github_combobox-nav_dist_index_js-node_modules_github_jtml_lib_index_js"),t.e("ui_packages_paths_index_ts"),t.e("ui_packages_query-builder-element_query-builder-element_ts"),t.e("app_assets_modules_github_blob-anchor_ts-ui_packages_code-nav_code-nav_ts-ui_packages_filter--8253c1"),t.e("ui_packages_hydro-analytics_hydro-analytics_ts-ui_packages_jump-to-element_model_ts"),t.e("app_components_search_qbsearch-input-element_ts")]).then(t.bind(t,28956)),"alert-dismissal":()=>t.e("app_components_secret_scanning_alert_centric_view_alert-dismissal-element_ts").then(t.bind(t,83732)),"preview-announcement-button":()=>t.e("app_components_settings_messages_preview-announcement-button-element_ts").then(t.bind(t,37929)),"recovery-codes":()=>t.e("app_components_settings_recovery-codes-element_ts").then(t.bind(t,76001)),"project-picker":()=>Promise.all([t.e("vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js"),t.e("app_assets_modules_github_virtual-listbox-focus-state_ts-node_modules_github_template-parts_l-448df2"),t.e("app_components_sidebar_project-picker-element_ts")]).then(t.bind(t,9282)),"launch-code":()=>t.e("app_components_signups_launch-code-element_ts").then(t.bind(t,97562)),"deferred-side-panel":()=>t.e("app_components_site_header_deferred-side-panel-element_ts").then(t.bind(t,87015)),"notification-indicator":()=>t.e("app_components_site_header_notification-indicator-element_ts").then(t.bind(t,88128)),"user-drawer-side-panel":()=>t.e("app_components_site_header_user-drawer-side-panel-element_ts").then(t.bind(t,69977)),"slash-command-toolbar-button":()=>t.e("app_components_slash_commands_slash-command-toolbar-button-element_ts").then(t.bind(t,90500)),"featured-work":()=>t.e("app_components_sponsors_dashboard_featured-work-element_ts").then(t.bind(t,28718)),"sponsors-account-switcher":()=>t.e("app_components_sponsors_sponsors-account-switcher-element_ts").then(t.bind(t,5698)),"adjacent-text-nodes-menu":()=>t.e("app_components_stafftools_adjacent_text_nodes_adjacent-text-nodes-menu-element_ts").then(t.bind(t,97980)),"variant-menu-item":()=>t.e("app_components_stafftools_azure_exp_variant-menu-item-element_ts").then(t.bind(t,68946)),"metered-billing-settings-component":()=>t.e("app_components_stafftools_billing_businesses_metered-billing-settings-component-element_ts").then(t.bind(t,46638)),"billing-transaction-component":()=>t.e("app_components_stafftools_billing_history_billing-transaction-component-element_ts").then(t.bind(t,41796)),"invoice-download":()=>t.e("app_components_stafftools_billing_history_invoice-download-element_ts").then(t.bind(t,96438)),"payment-history":()=>t.e("app_components_stafftools_billing_history_payment-history-element_ts").then(t.bind(t,59675)),"sponsorships-tabs":()=>t.e("app_components_stafftools_billing_sponsorships-tabs-element_ts").then(t.bind(t,34727)),"bundle-size-stats":()=>Promise.all([t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("app_components_stafftools_bundle-size-stats_bundle-size-stats-element_ts")]).then(t.bind(t,85526)),"datahpc-staffbar":()=>t.e("app_components_stafftools_data_hpc_datahpc-staffbar-element_ts").then(t.bind(t,22855)),"react-profiling-toggle":()=>t.e("app_components_stafftools_react_react-profiling-toggle-element_ts").then(t.bind(t,63674)),"react-query-devtools":()=>Promise.all([t.e("react-core"),t.e("react-lib"),t.e("app_components_stafftools_react_react-query-devtools-element_ts")]).then(t.bind(t,42747)),"react-staffbar":()=>Promise.all([t.e("react-core"),t.e("app_components_stafftools_react_react-staffbar-element_ts")]).then(t.bind(t,88296)),"soft-nav-staffbar":()=>t.e("app_components_stafftools_soft_nav_soft-nav-staffbar-element_ts").then(t.bind(t,7664)),"soft-nav-staffbar-preview":()=>t.e("app_components_stafftools_soft_nav_soft-nav-staffbar-preview-element_ts").then(t.bind(t,58909)),"stafftools-invoiced-sponsorship-payment-options":()=>t.e("app_components_stafftools_sponsors_invoiced_stafftools-invoiced-sponsorship-payment-options-e-a6e272").then(t.bind(t,4269)),"suggestions-collapsible":()=>t.e("app_components_suggestions_suggestions-collapsible-element_ts").then(t.bind(t,49253)),"themed-picture":()=>t.e("app_components_themed_pictures_themed-picture-element_ts").then(t.bind(t,92421)),"tasklist-block-add-tasklist":()=>Promise.all([t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("vendors-node_modules_github_sortablejs_Sortable_js"),t.e("vendors-node_modules_color-convert_index_js"),t.e("app_components_tracking_blocks_tracking-block-element_ts"),t.e("app_components_tracking_blocks_tasklist-block-add-tasklist-element_ts")]).then(t.bind(t,67907)),"tasklist-block-title":()=>t.e("app_components_tracking_blocks_tasklist-block-title-element_ts").then(t.bind(t,90686)),"tracking-block":()=>Promise.all([t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("vendors-node_modules_github_sortablejs_Sortable_js"),t.e("vendors-node_modules_color-convert_index_js"),t.e("app_components_tracking_blocks_tracking-block-element_ts")]).then(t.bind(t,81749)),"tracking-block-omnibar":()=>t.e("app_components_tracking_blocks_tracking-block-omnibar-element_ts").then(t.bind(t,166)),"two-factor-fallback-sms-config-toggle":()=>t.e("app_components_users_settings_two-factor-fallback-sms-config-toggle-element_ts").then(t.bind(t,76561)),"two-factor-inline-expander":()=>t.e("app_components_users_settings_two-factor-inline-expander-element_ts").then(t.bind(t,1198)),"actions-announceable-search-result-summary":()=>t.e("ui_packages_actions-announceable-search-result-summary-element_element-entry_ts").then(t.bind(t,76340)),"announce-live":()=>t.e("ui_packages_announce-live-element_element-entry_ts").then(t.bind(t,13084)),"avatar-reset":()=>t.e("ui_packages_avatar-reset-element_element-entry_ts").then(t.bind(t,96575)),"billing-checkout":()=>Promise.all([t.e("ui_packages_paths_index_ts"),t.e("ui_packages_copilot-chat_utils_copilot-chat-helpers_ts"),t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_microsoft-analytics_microsoft-analytics_ts"),t.e("ui_packages_billing-checkout-element_element-entry_ts-ui_packages_copilot-chat_utils_copilot--62eb4b")]).then(t.bind(t,67114)),"billing-country-and-region-selection":()=>t.e("ui_packages_billing-country-and-region-selection-element_element-entry_ts").then(t.bind(t,61206)),"business-shipping-information":()=>t.e("ui_packages_business-shipping-information-element_element-entry_ts").then(t.bind(t,98689)),"business-trial-accounts-form":()=>t.e("ui_packages_business-trial-accounts-form-element_element-entry_ts").then(t.bind(t,25315)),"business-use-billing-information-for-shipping":()=>t.e("ui_packages_business-use-billing-information-for-shipping-element_element-entry_ts").then(t.bind(t,62964)),"change-password":()=>t.e("ui_packages_change-password-element_element-entry_ts").then(t.bind(t,64741)),"codespace-share-dialog":()=>t.e("ui_packages_codespace-share-dialog-element_element-entry_ts").then(t.bind(t,49065)),"cohort-widget":()=>t.e("ui_packages_cohort-widget-element_element-entry_ts").then(t.bind(t,9201)),"context-region":()=>Promise.all([t.e("vendors-node_modules_github_mini-throttle_dist_decorators_js-node_modules_github_jtml_lib_ind-5c8d7e"),t.e("ui_packages_context-region-element_element-entry_ts")]).then(t.bind(t,62674)),"cookie-consent-link":()=>Promise.all([t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_cookie-consent-link-element_element-entry_ts-ui_packages_promise-with-resolvers-p-7b7d42")]).then(t.bind(t,4968)),"copilot-business-settings":()=>t.e("ui_packages_copilot-business-settings-element_element-entry_ts").then(t.bind(t,77489)),"copilot-dashboard-entrypoint":()=>Promise.all([t.e("ui_packages_paths_index_ts"),t.e("ui_packages_copilot-chat_utils_copilot-chat-helpers_ts"),t.e("ui_packages_copilot-chat_utils_copilot-chat-types_ts-ui_packages_copilot-dashboard-entrypoint-79f82c")]).then(t.bind(t,70348)),"copilot-dashboard-no-quota":()=>t.e("ui_packages_copilot-dashboard-no-quota-element_element-entry_ts").then(t.bind(t,7871)),"copilot-mixed-license-orgs-list":()=>t.e("ui_packages_copilot-mixed-license-orgs-list-element_element-entry_ts").then(t.bind(t,19498)),"copilot-plan-account-select":()=>t.e("ui_packages_copilot-plan-account-select-element_element-entry_ts").then(t.bind(t,87342)),"copilot-plan-select-dialog":()=>t.e("ui_packages_copilot-plan-select-dialog-element_element-entry_ts").then(t.bind(t,37291)),"copilot-purchase-intent-form":()=>Promise.all([t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_microsoft-analytics_microsoft-analytics_ts"),t.e("ui_packages_copilot-purchase-intent-form-element_element-entry_ts-ui_packages_promise-with-re-906b61")]).then(t.bind(t,59175)),"copilot-text-completion":()=>Promise.all([t.e("vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_hotkey_dist_index-b25f11"),t.e("ui_packages_copilot-text-completion-element_element-entry_ts")]).then(t.bind(t,29931)),"copilot-user-settings":()=>t.e("ui_packages_copilot-user-settings-element_element-entry_ts").then(t.bind(t,51930)),"copy-project":()=>t.e("ui_packages_copy-project-element_element-entry_ts").then(t.bind(t,43498)),"deploy-key-policy-warning":()=>t.e("ui_packages_deploy-key-policy-warning-element_element-entry_ts").then(t.bind(t,81936)),"discussion-spotlight-container":()=>t.e("ui_packages_discussion-spotlight-container-element_element-entry_ts").then(t.bind(t,56848)),"document-dropzone":()=>t.e("ui_packages_document-dropzone-element_element-entry_ts").then(t.bind(t,70693)),"edit-hook-secret":()=>t.e("ui_packages_edit-hook-secret-element_element-entry_ts").then(t.bind(t,30013)),"education-overview-component":()=>t.e("ui_packages_education-overview-component-element_element-entry_ts").then(t.bind(t,9695)),"emu-contribution-blocked-hint":()=>t.e("ui_packages_emu-contribution-blocked-hint-element_element-entry_ts").then(t.bind(t,74088)),"expandable-role-row":()=>t.e("ui_packages_expandable-role-row-element_element-entry_ts").then(t.bind(t,93213)),"experimental-action-list":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("ui_packages_experimental-action-list-element_element-entry_ts")]).then(t.bind(t,65874)),"fgp-search":()=>t.e("ui_packages_fgp-search-element_element-entry_ts").then(t.bind(t,70335)),"flywheel-return-to-tour":()=>t.e("ui_packages_flywheel-return-to-tour-element_element-entry_ts").then(t.bind(t,34940)),"fullstory-capture":()=>Promise.all([t.e("vendors-node_modules_fullstory_browser_dist_index_esm_js"),t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_fullstory-capture-element_element-entry_ts-ui_packages_promise-with-resolvers-pol-0a51eb")]).then(t.bind(t,11803)),"ghcc-consent":()=>Promise.all([t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_ghcc-consent-element_element-entry_ts-ui_packages_promise-with-resolvers-polyfill-e4d890")]).then(t.bind(t,13588)),"inline-security-checkup-notice":()=>t.e("ui_packages_inline-security-checkup-notice-element_element-entry_ts").then(t.bind(t,49313)),"input-page-refresh":()=>t.e("ui_packages_input-page-refresh-element_element-entry_ts").then(t.bind(t,23915)),"integration-agent-form":()=>t.e("ui_packages_integration-agent-form-element_element-entry_ts").then(t.bind(t,44750)),"issue-create":()=>t.e("ui_packages_issue-create-element_element-entry_ts").then(t.bind(t,439)),"jump-to":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_delegated-events_dist_index_js-node_modules_github_hotkey_dist_index_js--870134"),t.e("ui_packages_fetch-headers_fetch-headers_ts-ui_packages_form-utils_form-utils_ts-ui_packages_i-d3f7bc"),t.e("ui_packages_hydro-analytics_hydro-analytics_ts-ui_packages_jump-to-element_model_ts"),t.e("ui_packages_jump-to-element_element-entry_ts")]).then(t.bind(t,3353)),"licensing-apply-coupon-code":()=>t.e("ui_packages_licensing-apply-coupon-code-element_element-entry_ts").then(t.bind(t,97097)),"linked-sku-select":()=>t.e("ui_packages_linked-sku-select-element_element-entry_ts").then(t.bind(t,23889)),"markdown-accessiblity-table":()=>t.e("ui_packages_markdown-accessiblity-table-element_element-entry_ts").then(t.bind(t,61295)),"marketplace-security-compliance-trader-self-certification":()=>t.e("ui_packages_marketplace-security-compliance-trader-self-certification-element_element-entry_ts").then(t.bind(t,42827)),"microsoft-analytics":()=>Promise.all([t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_microsoft-analytics_microsoft-analytics_ts"),t.e("ui_packages_microsoft-analytics-element_element-entry_ts-ui_packages_promise-with-resolvers-p-9fec2a")]).then(t.bind(t,47589)),"microsoft-analytics-event":()=>Promise.all([t.e("ui_packages_cookie-consent_cookie-consent_ts"),t.e("ui_packages_microsoft-analytics_microsoft-analytics_ts"),t.e("ui_packages_microsoft-analytics-event-element_element-entry_ts-ui_packages_promise-with-resol-72e13e")]).then(t.bind(t,40045)),"query-builder":()=>Promise.all([t.e("vendors-node_modules_github_combobox-nav_dist_index_js-node_modules_github_jtml_lib_index_js"),t.e("ui_packages_query-builder-element_query-builder-element_ts"),t.e("ui_packages_hydro-analytics_hydro-analytics_ts-ui_packages_query-builder-element_element-entry_ts")]).then(t.bind(t,16913)),"react-partial-anchor":()=>t.e("ui_packages_react-partial-anchor-element_element-entry_ts").then(t.bind(t,6352)),"security-analysis-dependabot-updates":()=>t.e("ui_packages_security-analysis-dependabot-updates-element_element-entry_ts").then(t.bind(t,43637)),"security-analysis-ghas":()=>t.e("ui_packages_security-analysis-ghas-element_element-entry_ts").then(t.bind(t,6471)),"share-button-form-submit-handler":()=>t.e("ui_packages_share-button-form-submit-handler-element_element-entry_ts").then(t.bind(t,93522)),"show-dialog-on-load":()=>t.e("ui_packages_show-dialog-on-load-element_element-entry_ts").then(t.bind(t,4454)),"sidebar-pinned-topics":()=>t.e("ui_packages_sidebar-pinned-topics-element_element-entry_ts").then(t.bind(t,9504)),"signup-form":()=>t.e("ui_packages_signup-form-element_element-entry_ts").then(t.bind(t,21123)),"signups-marketing-consent-fields":()=>t.e("ui_packages_signups-marketing-consent-fields-element_element-entry_ts").then(t.bind(t,72316)),"site-header-logged-in-user-menu":()=>t.e("ui_packages_site-header-logged-in-user-menu-element_element-entry_ts").then(t.bind(t,26418)),"stafftools-topics-table":()=>t.e("ui_packages_stafftools-topics-table-element_element-entry_ts").then(t.bind(t,89010)),"task-component":()=>t.e("ui_packages_task-component-element_element-entry_ts").then(t.bind(t,14557)),"team-sync-okta-config-form":()=>t.e("ui_packages_team-sync-okta-config-form-element_element-entry_ts").then(t.bind(t,4138)),"unveil-container":()=>t.e("ui_packages_unveil-container-element_element-entry_ts").then(t.bind(t,81009)),"updatable-content":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("ui_packages_updatable-content_updatable-content_ts"),t.e("node_modules_scroll-anchoring_dist_scroll-anchoring_esm_js-ui_packages_stats_stats_ts-ui_pack-128132")]).then(t.bind(t,68091)),"visible-password":()=>t.e("ui_packages_visible-password-element_element-entry_ts").then(t.bind(t,56562)),"webauthn-get":()=>t.e("ui_packages_webauthn-get-element_element-entry_ts").then(t.bind(t,17226)),"webauthn-subtle":()=>t.e("ui_packages_webauthn-subtle-element_element-entry_ts").then(t.bind(t,50222)),"profile-pins":()=>t.e("app_assets_modules_github_profile_profile-pins-element_ts").then(t.bind(t,5778)),"emoji-picker":()=>t.e("app_assets_modules_github_emoji-picker-element_ts").then(t.bind(t,43707)),"insights-query":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_insights-query_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,76583)),"remote-clipboard-copy":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("app_assets_modules_github_behaviors_remote-clipboard-copy_ts")]).then(t.bind(t,44056)),"series-table":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_series-table_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,61987)),"line-chart":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_line-chart_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,80452)),"bar-chart":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_bar-chart_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,163)),"column-chart":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_column-chart_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,54132)),"stacked-area-chart":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_stacked-area-chart_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,28623)),"hero-stat":()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_date-fns_format_mjs"),t.e("vendors-node_modules_date-fns_addWeeks_mjs-node_modules_date-fns_addYears_mjs-node_modules_da-827f4f"),t.e("vendors-node_modules_chart_js_dist_chart_js"),t.e("vendors-node_modules_ml-regression-simple-linear_src_index_js-node_modules_chartjs-adapter-da-5ef63e"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_hero-stat_ts-node_modules_date-fns_parseISO_mjs")]).then(t.bind(t,36714)),"pulse-authors-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("app_assets_modules_github_d3_tip_ts-ui_packages_fetch-utils_fetch-utils_ts-node_modules_d3-sc-1030c7"),t.e("app_assets_modules_github_graphs_pulse-authors-graph-element_ts")]).then(t.bind(t,38419)),"community-contributions-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_graphs_community-contributions_ts")]).then(t.bind(t,78088)),"discussion-page-views-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("app_assets_modules_github_d3_tip_ts-ui_packages_fetch-utils_fetch-utils_ts-node_modules_d3-sc-1030c7"),t.e("app_assets_modules_github_graphs_discussion-page-views_ts")]).then(t.bind(t,51512)),"discussions-daily-contributors":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_graphs_discussions-daily-contributors_ts")]).then(t.bind(t,69569)),"discussions-new-contributors":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_graphs_discussions-new-contributors_ts")]).then(t.bind(t,19844)),"code-frequency-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_graphs_code-frequency-graph-element_ts")]).then(t.bind(t,66389)),"contributors-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("vendors-node_modules_d3-transition_src_index_js"),t.e("vendors-node_modules_d3-collection_src_index_js-node_modules_d3-shape_src_area_js-node_module-46277c"),t.e("app_assets_modules_github_graphs_contributors-graph-element_ts")]).then(t.bind(t,45381)),"traffic-clones-graph":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_d3_tip_ts-app_assets_modules_github_graphs_traffic_ts-ui_packages_f-37800b"),t.e("app_assets_modules_github_graphs_traffic-clones-graph-element_ts")]).then(t.bind(t,15557)),"traffic-visitors-graph":()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_d3_tip_ts-app_assets_modules_github_graphs_traffic_ts-ui_packages_f-37800b"),t.e("app_assets_modules_github_graphs_traffic-visitors-graph-element_ts")]).then(t.bind(t,42984)),"commit-activity-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-transition_src_index_js"),t.e("app_assets_modules_github_d3_tip_ts-ui_packages_fetch-utils_fetch-utils_ts-node_modules_d3-sc-1030c7"),t.e("app_assets_modules_github_graphs_commit-activity-graph-element_ts")]).then(t.bind(t,54136)),"marketplace-insights-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-axis_src_axis_js-node_modules_d3-shape_src_array_js-node_modules_d3-s-d668ee"),t.e("vendors-node_modules_d3-shape_src_line_js-node_modules_d3-scale_src_time_js"),t.e("app_assets_modules_github_d3_tip_ts-app_assets_modules_github_graphs_traffic_ts-ui_packages_f-37800b"),t.e("app_assets_modules_github_graphs_marketplace-insights-graph-element_ts")]).then(t.bind(t,304)),"user-sessions-map":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("vendors-node_modules_d3-transition_src_index_js"),t.e("vendors-node_modules_d3-zoom_src_index_js"),t.e("vendors-node_modules_d3-ease_src_circle_js-node_modules_d3-geo_src_centroid_js-node_modules_d-126cf1"),t.e("app_assets_modules_github_settings_user-sessions-map-element_ts")]).then(t.bind(t,61363)),"reload-after-polling":()=>t.e("app_assets_modules_github_behaviors_reload-after-polling-element_ts").then(t.bind(t,42908)),"package-dependencies-security-graph":()=>Promise.all([t.e("vendors-node_modules_d3-interpolate_src_value_js-node_modules_d3-selection_src_select_js"),t.e("vendors-node_modules_github_memoize_dist_esm_index_js-node_modules_d3-array_src_max_js-node_m-d98b1b"),t.e("app_assets_modules_github_d3_tip_ts-ui_packages_fetch-utils_fetch-utils_ts-node_modules_d3-sc-1030c7"),t.e("app_assets_modules_github_graphs_package-dependencies-security-graph-element_ts")]).then(t.bind(t,7132)),".js-sub-dependencies":()=>t.e("app_assets_modules_github_dependencies_ts").then(t.bind(t,72236)),"network-graph":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("app_assets_modules_github_graphs_network-graph-element_ts")]).then(t.bind(t,80357)),"inline-machine-translation":()=>t.e("app_assets_modules_github_localization_inline-machine-translation-element_ts").then(t.bind(t,11652)),"custom-patterns-filter":()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_index_mjs"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_dompurify_dist_purify_es_mjs"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_delegated-events_dist_index_js-node_modules_github_hotkey_dist_index_js--870134"),t.e("ui_packages_fetch-headers_fetch-headers_ts-ui_packages_form-utils_form-utils_ts-ui_packages_i-d3f7bc"),t.e("app_assets_modules_github_filter-input_ts"),t.e("node_modules_lit-html_directives_until_js-app_assets_modules_github_secret-scanning_custom-pa-892833")]).then(t.bind(t,72938))})},39595:(e,s,t)=>{let _;t.d(s,{CF:()=>h,p_:()=>C,FB:()=>m,Se:()=>w,aC:()=>S,zV:()=>A});let n=new WeakSet,o=new WeakMap;function i(e=document){if(o.has(e))return o.get(e);let s=!1,t=new MutationObserver(e=>{for(let s of e)if("attributes"===s.type&&s.target instanceof Element)r(s.target);else if("childList"===s.type&&s.addedNodes.length)for(let e of s.addedNodes)e instanceof Element&&a(e)});t.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let _={get closed(){return s},unsubscribe(){s=!0,o.delete(e),t.disconnect()}};return o.set(e,_),_}function a(e){for(let s of e.querySelectorAll("[data-action]"))r(s);e instanceof Element&&e.hasAttribute("data-action")&&r(e)}function d(e){let s=e.currentTarget;for(let t of l(s))if(e.type===t.type){let _=s.closest(t.tag);n.has(_)&&"function"==typeof _[t.method]&&_[t.method](e);let o=s.getRootNode();if(o instanceof ShadowRoot&&n.has(o.host)&&o.host.matches(t.tag)){let s=o.host;"function"==typeof s[t.method]&&s[t.method](e)}}}function*l(e){for(let s of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=s.lastIndexOf(":"),t=Math.max(0,s.lastIndexOf("#"))||s.length;yield{type:s.slice(0,e),tag:s.slice(e+1,t),method:s.slice(t+1)||"handleEvent"}}}function r(e){for(let s of l(e))e.addEventListener(s.type,d)}function m(e,s){let t=e.tagName.toLowerCase();if(e.shadowRoot){for(let _ of e.shadowRoot.querySelectorAll(`[data-target~="${t}.${s}"]`))if(!_.closest(t))return _}for(let _ of e.querySelectorAll(`[data-target~="${t}.${s}"]`))if(_.closest(t)===e)return _}let c=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),p=(e,s="property")=>{let t=c(e);if(!t.includes("-"))throw new DOMException(`${s}: ${String(e)} is not a valid ${s} name`,"SyntaxError");return t},u="attr";function h(e,s){q(e,u).add(s)}let b=new WeakSet;function g(e,s){if(b.has(e))return;b.add(e);let t=Object.getPrototypeOf(e),_=t?.constructor?.attrPrefix??"data-";for(let n of(s||(s=q(t,u)),s)){let s=e[n],t=p(`${_}${n}`),o={configurable:!0,get(){return this.getAttribute(t)||""},set(e){this.setAttribute(t,e||"")}};"number"==typeof s?o={configurable:!0,get(){return Number(this.getAttribute(t)||0)},set(e){this.setAttribute(t,e)}}:"boolean"==typeof s&&(o={configurable:!0,get(){return this.hasAttribute(t)},set(e){this.toggleAttribute(t,e)}}),Object.defineProperty(e,n,o),n in e&&!e.hasAttribute(t)&&o.set.call(e,s)}}let f=new Map,v=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),y=new Promise(e=>{let s=new AbortController;s.signal.addEventListener("abort",()=>e());let t={once:!0,passive:!0,signal:s.signal},_=()=>s.abort();document.addEventListener("mousedown",_,t),document.addEventListener("touchstart",_,t),document.addEventListener("keydown",_,t),document.addEventListener("pointerdown",_,t)}),k={ready:()=>v,firstInteraction:()=>y,visible:e=>new Promise(s=>{let t=new IntersectionObserver(e=>{for(let _ of e)if(_.isIntersecting){s(),t.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let s of document.querySelectorAll(e))t.observe(s)})},j=new WeakMap;function x(e){cancelAnimationFrame(j.get(e)||0),j.set(e,requestAnimationFrame(()=>{for(let s of f.keys()){let t=e instanceof Element&&e.matches(s)?e:e.querySelector(s);if(customElements.get(s)||t){let _=t?.getAttribute("data-load-on")||"ready",n=_ in k?k[_]:k.ready;for(let e of f.get(s)||[])n(s).then(e);f.delete(s),j.delete(e)}}}))}function w(e,s){for(let[t,_]of("string"==typeof e&&s&&(e={[e]:s}),Object.entries(e)))f.has(t)||f.set(t,new Set),f.get(t).add(_);P(document)}function P(e){_||(_=new MutationObserver(e=>{if(f.size)for(let s of e)for(let e of s.addedNodes)e instanceof Element&&x(e)})),x(e),_.observe(e,{subtree:!0,childList:!0})}let z=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let s=this,t=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){s.connectedCallback(this,t)};let _=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){s.disconnectedCallback(this,_)};let n=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,t,_){s.attributeChangedCallback(this,e,t,_,n)};let o=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return s.observedAttributes(this,o)},set(e){o=e}}),function(e){let s=e.observedAttributes||[],t=e.attrPrefix??"data-",_=e=>p(`${t}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...q(e.prototype,u)].map(_).concat(s),set(e){s=e}})}(e),function(e){let s=c(e.name).replace(/-element$/,"");try{window.customElements.define(s,e),window[e.name]=customElements.get(s)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,s){return s}connectedCallback(e,s){var t,_;for(let s of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))s.parentElement===e&&e.attachShadow({mode:"closed"===s.getAttribute("data-shadowroot")?"closed":"open"}).append(s.content.cloneNode(!0));(g(e),n.add(e),e.shadowRoot&&(a(_=e.shadowRoot),i(_)),a(e),i(e.ownerDocument),s?.call(e),e.shadowRoot)&&(a(t=e.shadowRoot),i(t),P(e.shadowRoot))}disconnectedCallback(e,s){s?.call(e)}attributeChangedCallback(e,s,t,_,n){g(e),"data-catalyst"!==s&&n&&n.call(e,s,t,_)}};function q(e,s){if(!Object.prototype.hasOwnProperty.call(e,z)){let s=e[z],t=e[z]=new Map;if(s)for(let[e,_]of s)t.set(e,new Set(_))}let t=e[z];return t.has(s)||t.set(s,new Set),t.get(s)}function S(e,s){q(e,"target").add(s),Object.defineProperty(e,s,{configurable:!0,get(){return m(this,s)}})}function A(e,s){q(e,"targets").add(s),Object.defineProperty(e,s,{configurable:!0,get(){let e=this.tagName.toLowerCase(),t=[];if(this.shadowRoot)for(let _ of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${s}"]`))_.closest(e)||t.push(_);for(let _ of this.querySelectorAll(`[data-targets~="${e}.${s}"]`))_.closest(e)===this&&t.push(_);return t}})}function C(e){new CatalystDelegate(e)}}},e=>{e(e.s=14487)}]);
//# sourceMappingURL=element-registry-9de0b75166ab.js.map