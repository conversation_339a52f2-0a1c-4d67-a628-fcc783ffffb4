"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-189aa3","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-4da1df"],{39595:(e,t,n)=>{let o;n.d(t,{CF:()=>p,p_:()=>N,FB:()=>u,Se:()=>k,aC:()=>x,zV:()=>L});let r=new WeakSet,a=new WeakMap;function i(e=document){if(a.has(e))return a.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)d(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&l(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let o={get closed(){return t},unsubscribe(){t=!0,a.delete(e),n.disconnect()}};return a.set(e,o),o}function l(e){for(let t of e.querySelectorAll("[data-action]"))d(t);e instanceof Element&&e.hasAttribute("data-action")&&d(e)}function s(e){let t=e.currentTarget;for(let n of c(t))if(e.type===n.type){let o=t.closest(n.tag);r.has(o)&&"function"==typeof o[n.method]&&o[n.method](e);let a=t.getRootNode();if(a instanceof ShadowRoot&&r.has(a.host)&&a.host.matches(n.tag)){let t=a.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function d(e){for(let t of c(e))e.addEventListener(t.type,s)}function u(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let o of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!o.closest(n))return o}for(let o of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(o.closest(n)===e)return o}let h=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),f=(e,t="property")=>{let n=h(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},m="attr";function p(e,t){O(e,m).add(t)}let g=new WeakSet;function b(e,t){if(g.has(e))return;g.add(e);let n=Object.getPrototypeOf(e),o=n?.constructor?.attrPrefix??"data-";for(let r of(t||(t=O(n,m)),t)){let t=e[r],n=f(`${o}${r}`),a={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?a={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(a={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,r,a),r in e&&!e.hasAttribute(n)&&a.set.call(e,t)}}let y=new Map,w=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),_=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},o=()=>t.abort();document.addEventListener("mousedown",o,n),document.addEventListener("touchstart",o,n),document.addEventListener("keydown",o,n),document.addEventListener("pointerdown",o,n)}),A={ready:()=>w,firstInteraction:()=>_,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},E=new WeakMap;function C(e){cancelAnimationFrame(E.get(e)||0),E.set(e,requestAnimationFrame(()=>{for(let t of y.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let o=n?.getAttribute("data-load-on")||"ready",r=o in A?A[o]:A.ready;for(let e of y.get(t)||[])r(t).then(e);y.delete(t),E.delete(e)}}}))}function k(e,t){for(let[n,o]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))y.has(n)||y.set(n,new Set),y.get(n).add(o);S(document)}function S(e){o||(o=new MutationObserver(e=>{if(y.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&C(e)})),C(e),o.observe(e,{subtree:!0,childList:!0})}let $=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let o=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,o)};let r=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,o){t.attributeChangedCallback(this,e,n,o,r)};let a=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,a)},set(e){a=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",o=e=>f(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...O(e.prototype,m)].map(o).concat(t),set(e){t=e}})}(e),function(e){let t=h(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,o;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(b(e),r.add(e),e.shadowRoot&&(l(o=e.shadowRoot),i(o)),l(e),i(e.ownerDocument),t?.call(e),e.shadowRoot)&&(l(n=e.shadowRoot),i(n),S(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,o,r){b(e),"data-catalyst"!==t&&r&&r.call(e,t,n,o)}};function O(e,t){if(!Object.prototype.hasOwnProperty.call(e,$)){let t=e[$],n=e[$]=new Map;if(t)for(let[e,o]of t)n.set(e,new Set(o))}let n=e[$];return n.has(t)||n.set(t,new Set),n.get(t)}function x(e,t){O(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return u(this,t)}})}function L(e,t){O(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let o of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)||n.push(o);for(let o of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)===this&&n.push(o);return n}})}function N(e){new CatalystDelegate(e)}},18679:(e,t,n)=>{n.d(t,{s:()=>AnalyticsClient});let o=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","scid"];var r=n(36301);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,r.y)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...function(){let e={};try{for(let[t,n]of new URLSearchParams(window.location.search)){let r=t.toLowerCase();o.includes(r)&&(e[r]=n)}return e}catch(e){return{}}}(),...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n=JSON.stringify({client_id:this.clientId,page_views:e,events:t,request_context:{referrer:function(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}(),user_agent:navigator.userAgent,screen_resolution:function(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}(),browser_resolution:function(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}(),browser_languages:navigator.languages?navigator.languages.join(","):navigator.language||"",pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}});try{if(navigator.sendBeacon)return void navigator.sendBeacon(this.collectorUrl,n)}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:n,keepalive:!1})}}},70837:(e,t,n)=>{n.d(t,{O:()=>o});function o(e="ha"){let t,n={};for(let o of Array.from(document.head.querySelectorAll(`meta[name^="${e}-"]`))){let{name:r,content:a}=o,i=r.replace(`${e}-`,"").replace(/-/g,"_");"url"===i?t=a:n[i]=a}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}},37732:(e,t,n)=>{n.d(t,{a:()=>l});let o=new Set(["children","localName","ref","style","className"]),r=new WeakMap,a=(e,t,n,o,a)=>{let i=null==a?void 0:a[t];void 0===i||n===o?null==n&&t in HTMLElement.prototype?e.removeAttribute(t):e[t]=n:((e,t,n)=>{let o=r.get(e);void 0===o&&r.set(e,o=new Map);let a=o.get(t);void 0!==n?void 0===a?(o.set(t,a={handleEvent:n}),e.addEventListener(t,a)):a.handleEvent=n:void 0!==a&&(o.delete(t),e.removeEventListener(t,a))})(e,i,n)},i=(e,t)=>{"function"==typeof e?e(t):e.current=t};function l(e=window.React,t,n,r,s){let c,d,u;void 0===t?({tagName:d,elementClass:u,events:r,displayName:s}=e,c=e.react):(c=e,u=n,d=t);let h=c.Component,f=c.createElement,m=new Set(Object.keys(null!=r?r:{}));let v=class v extends h{constructor(){super(...arguments),this.o=null}t(e){if(null!==this.o)for(let t in this.i)a(this.o,t,this.props[t],e?e[t]:void 0,r)}componentDidMount(){var e;this.t(),null==(e=this.o)||e.removeAttribute("defer-hydration")}componentDidUpdate(e){this.t(e)}render(){let{_$Gl:e,...t}=this.props;this.h!==e&&(this.u=t=>{null!==e&&i(e,t),this.o=t,this.h=e}),this.i={};let n={ref:this.u};for(let[e,r]of Object.entries(t))o.has(e)?n["className"===e?"class":e]=r:m.has(e)||e in u.prototype?this.i[e]=r:n[e]=r;return n.suppressHydrationWarning=!0,f(d,n)}};v.displayName=null!=s?s:u.name;let p=c.forwardRef((e,t)=>f(v,{...e,_$Gl:t},null==e?void 0:e.children));return p.displayName=v.displayName,p}}}]);
//# sourceMappingURL=vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-189aa3-3a22bc08abaf.js.map