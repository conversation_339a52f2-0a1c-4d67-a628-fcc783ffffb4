"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_delegated-events_dist_index_js-node_modules_github_memoize_dist_esm_inde-7ea0ea","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-378c59","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-a58b06"],{13937:(e,t,n)=>{let r;n.d(t,{Ax:()=>l,JW:()=>u,ZV:()=>a});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function i(){let e,t;return[new Promise(function(n,r){e=n,t=r}),e,t]}let s=[],o=[];function a(e){s.push(e)}function l(e){o.push(e)}function u(e,t){r||(r=new Map,"undefined"!=typeof document&&document.addEventListener("submit",c));let n=r.get(e)||[];r.set(e,[...n,t])}function c(e){let t;if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let n=e.target,a=function(e){let t=[],n=t=>"object"==typeof t?t===e:"string"==typeof t&&e.matches(t);for(let e of r.keys())if(n(e)){let n=r.get(e)||[];t.push(...n)}return t}(n);if(0===a.length)return;let l=function(e,t){let n={method:t?.formMethod||e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===n.method.toUpperCase()){let t=function(e){let t=new URLSearchParams;for(let[n,r]of[...new FormData(e).entries()])t.append(n,r.toString());return t.toString()}(e);t&&(n.url+=(~n.url.indexOf("?")?"&":"?")+t)}else n.body=new FormData(e);return n}(n,e instanceof SubmitEvent?e.submitter:null),[u,c,h]=i();e.preventDefault(),d(a,n,l,u).then(async e=>{if(e){for(let e of o)await e(n);p(l).then(c,h).catch(()=>{}).then(()=>{for(let e of s)e(n)})}else n.submit()},e=>{n.submit(),setTimeout(()=>{throw e})})}async function d(e,t,n,r){let s=!1;for(let o of e){let[e,a]=i(),l=()=>(s=!0,a(),r),u={text:l,json:()=>(n.headers.set("Accept","application/json"),l()),html:()=>(n.headers.set("Accept","text/html"),l())};await Promise.race([e,o(t,u,n)])}return s}async function p(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=JSON.parse(this.text);return delete this.json,this.json=e,this.json},get html(){return delete this.html,this.html=function(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}(document,this.text),this.html}};if(n.text=await t.text(),t.ok)return n;throw new ErrorWithResponse("request failed",n)}},97797:(e,t,n)=>{function r(){if(!(this instanceof r))return new r;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{h:()=>A,A:()=>E,on:()=>j});var i,s=window.document.documentElement,o=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector;r.prototype.matchesSelector=function(e,t){return o.call(e,t)},r.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},r.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);else if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var u=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(u))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),r.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},i="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var c=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var n,r,i,s,o,a,l=(e=e.slice(0).concat(e.default)).length,u=t,d=[];do if(c.exec(""),(i=c.exec(u))&&(u=i[3],i[2]||!u)){for(n=0;n<l;n++)if(o=(a=e[n]).selector(i[1])){for(r=d.length,s=!1;r--;)if(d[r].index===a&&d[r].key===o){s=!0;break}s||d.push({index:a,key:o});break}}while(i)return d}function p(e,t){return e.id-t.id}r.prototype.logDefaultIndexUsed=function(){},r.prototype.add=function(e,t){var n,r,s,o,a,l,u,c,p=this.activeIndexes,h=this.selectors,f=this.selectorObjects;if("string"==typeof e){for(r=0,f[(n={id:this.uid++,selector:e,data:t}).id]=n,u=d(this.indexes,e);r<u.length;r++)o=(c=u[r]).key,(a=function(e,t){var n,r,i;for(n=0,r=e.length;n<r;n++)if(i=e[n],t.isPrototypeOf(i))return i}(p,s=c.index))||((a=Object.create(s)).map=new i,p.push(a)),s===this.indexes.default&&this.logDefaultIndexUsed(n),(l=a.map.get(o))||(l=[],a.map.set(o,l)),l.push(n);this.size++,h.push(e)}},r.prototype.remove=function(e,t){if("string"==typeof e){var n,r,i,s,o,a,l,u,c=this.activeIndexes,p=this.selectors=[],h=this.selectorObjects,f={},m=1==arguments.length;for(i=0,n=d(this.indexes,e);i<n.length;i++)for(r=n[i],s=c.length;s--;)if(a=c[s],r.index.isPrototypeOf(a)){if(l=a.map.get(r.key))for(o=l.length;o--;)(u=l[o]).selector===e&&(m||u.data===t)&&(l.splice(o,1),f[u.id]=!0);break}for(i in f)delete h[i],this.size--;for(i in h)p.push(h[i].selector)}},r.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,r,i,s,o,a,l,u={},c=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,r=d.length;t<r;t++)for(n=0,s=d[t],i=(o=this.matches(s)).length;n<i;n++)u[(l=o[n]).id]?a=u[l.id]:(a={id:l.id,selector:l.selector,data:l.data,elements:[]},u[l.id]=a,c.push(a)),a.elements.push(s);return c.sort(p)},r.prototype.matches=function(e){if(!e)return[];var t,n,r,i,s,o,a,l,u,c,d,h=this.activeIndexes,f={},m=[];for(t=0,i=h.length;t<i;t++)if(l=(a=h[t]).element(e)){for(n=0,s=l.length;n<s;n++)if(u=a.map.get(l[n]))for(r=0,o=u.length;r<o;r++)!f[d=(c=u[r]).id]&&this.matchesSelector(e,c.selector)&&(f[d]=!0,m.push(c))}return m.sort(p)};var h={},f={},m=new WeakMap,g=new WeakMap,v=new WeakMap,b=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function y(e,t,n){var r=e[t];return e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)},e}function x(){m.set(this,!0)}function w(){m.set(this,!0),g.set(this,!0)}function _(){return v.get(this)||null}function S(e,t){b&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||b.get})}function T(e){if(function(e){try{return e.eventPhase,!0}catch(e){return!1}}(e)){var t=(1===e.eventPhase?f:h)[e.type];if(t){var n=function(e,t,n){var r=[],i=t;do{if(1!==i.nodeType)break;var s=e.matches(i);if(s.length){var o={node:i,observers:s};n?r.unshift(o):r.push(o)}}while(i=i.parentElement)return r}(t,e.target,1===e.eventPhase);if(n.length){y(e,"stopPropagation",x),y(e,"stopImmediatePropagation",w),S(e,_);for(var r=0,i=n.length;r<i&&!m.get(e);r++){var s=n[r];v.set(e,s.node);for(var o=0,a=s.observers.length;o<a&&!g.get(e);o++)s.observers[o].data.call(s.node,e)}v.delete(e),S(e)}}}}function j(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=!!i.capture,o=s?f:h,a=o[e];a||(a=new r,o[e]=a,document.addEventListener(e,T,s)),a.add(t,n)}function E(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!r.capture,s=i?f:h,o=s[e];o&&(o.remove(t,n),o.size||(delete s[e],document.removeEventListener(e,T,i)))}function A(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},5225:(e,t,n)=>{function r(...e){return JSON.stringify(e,(e,t)=>"object"==typeof t?t:String(t))}function i(e,t={}){let{hash:n=r,cache:s=new Map}=t;return function(...t){let r=n.apply(this,t);if(s.has(r))return s.get(r);let i=e.apply(this,t);return i instanceof Promise&&(i=i.catch(e=>{throw s.delete(r),e})),s.set(r,i),i}}n.d(t,{A:()=>i})},78134:(e,t,n)=>{n.d(t,{i4:()=>TemplateInstance,xr:()=>d});let r=new Map;function i(e){if(r.has(e))return r.get(e);let t=e.length,n=0,i=0,s=0,o=[];for(let r=0;r<t;r+=1){let t=e[r],a=e[r+1],l=e[r-1];"{"===t&&"{"===a&&"\\"!==l?(1===(s+=1)&&(i=r),r+=1):"}"===t&&"}"===a&&"\\"!==l&&s&&0==(s-=1)&&(i>n&&(o.push(Object.freeze({type:"string",start:n,end:i,value:e.slice(n,i)})),n=i),o.push(Object.freeze({type:"part",start:i,end:r+2,value:e.slice(n+2,r).trim()})),r+=1,n=r+1)}return n<t&&o.push(Object.freeze({type:"string",start:n,end:t,value:e.slice(n,t)})),r.set(e,Object.freeze(o)),r.get(e)}let s=new WeakMap,o=new WeakMap;let AttributeTemplatePart=class AttributeTemplatePart{constructor(e,t){this.expression=t,s.set(this,e),e.updateParent("")}get attributeName(){return s.get(this).attr.name}get attributeNamespace(){return s.get(this).attr.namespaceURI}get value(){return o.get(this)}set value(e){o.set(this,e||""),s.get(this).updateParent(e)}get element(){return s.get(this).element}get booleanValue(){return s.get(this).booleanValue}set booleanValue(e){s.get(this).booleanValue=e}};let AttributeValueSetter=class AttributeValueSetter{constructor(e,t){this.element=e,this.attr=t,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(e){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=e?"":null}append(e){this.partList.push(e)}updateParent(e){if(1===this.partList.length&&null===e)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let e=this.partList.map(e=>"string"==typeof e?e:e.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,e)}}};let a=new WeakMap;let NodeTemplatePart=class NodeTemplatePart{constructor(e,t){this.expression=t,a.set(this,[e]),e.textContent=""}get value(){return a.get(this).map(e=>e.textContent).join("")}set value(e){this.replace(e)}get previousSibling(){return a.get(this)[0].previousSibling}get nextSibling(){return a.get(this)[a.get(this).length-1].nextSibling}replace(...e){var t,n;let r=e.map(e=>"string"==typeof e?new Text(e):e);r.length||r.push(new Text(""));let i=a.get(this)[0];for(let e of r)null==(t=i.parentNode)||t.insertBefore(e,i);for(let e of a.get(this))null==(n=e.parentNode)||n.removeChild(e);a.set(this,r)}};let InnerTemplatePart=class InnerTemplatePart extends NodeTemplatePart{constructor(e){var t;super(e,null!=(t=e.getAttribute("expression"))?t:""),this.template=e}get directive(){var e;return null!=(e=this.template.getAttribute("directive"))?e:""}};function l(e){return{processCallback(t,n,r){var i;if("object"==typeof r&&r){for(let t of n)if(t.expression in r){let n=null!=(i=r[t.expression])?i:"";e(t,n,r)}}}}}function u(e,t){e.value=t instanceof Node?t:String(t)}let c=l(u),d=l((e,t)=>{!function(e,t){return"boolean"==typeof t&&e instanceof AttributeTemplatePart&&"boolean"==typeof e.element[e.attributeName]&&(e.booleanValue=t,!0)}(e,t)&&u(e,t)}),p=new WeakMap,h=new WeakMap;let TemplateInstance=class TemplateInstance extends(globalThis.DocumentFragment||EventTarget){constructor(e,t,n=c){var r,s;super(),Object.getPrototypeOf(this)!==TemplateInstance.prototype&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(e.content.cloneNode(!0)),h.set(this,Array.from(function* e(t){let n,r=t.ownerDocument.createTreeWalker(t,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null);for(;n=r.nextNode();)if(n instanceof HTMLTemplateElement)if(n.hasAttribute("directive"))yield new InnerTemplatePart(n);else for(let t of e(n.content))yield t;else if(n instanceof Element&&n.hasAttributes())for(let e=0;e<n.attributes.length;e+=1){let t=n.attributes.item(e);if(t&&t.value.includes("{{")){let e=new AttributeValueSetter(n,t);for(let n of i(t.value))if("string"===n.type)e.append(n.value);else{let t=new AttributeTemplatePart(e,n.value);e.append(t),yield t}}}else if(n instanceof Text&&n.textContent&&n.textContent.includes("{{")){let e=i(n.textContent);for(let t=0;t<e.length;t+=1){let r=e[t];r.end<n.textContent.length&&n.splitText(r.end),"part"===r.type&&(yield new NodeTemplatePart(n,r.value));break}}}(this))),p.set(this,n),null==(s=(r=p.get(this)).createCallback)||s.call(r,this,h.get(this),t),p.get(this).processCallback(this,h.get(this),t)}update(e){p.get(this).processCallback(this,h.get(this),e)}}},74043:(e,t,n)=>{function r(e){let t="==".slice(0,(4-e.length%4)%4),n=atob(e.replace(/-/g,"+").replace(/_/g,"/")+t),r=new ArrayBuffer(n.length),i=new Uint8Array(r);for(let e=0;e<n.length;e++)i[e]=n.charCodeAt(e);return r}function i(e){let t=new Uint8Array(e),n="";for(let e of t)n+=String.fromCharCode(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}n.d(t,{$j:()=>x,Jt:()=>_,PG:()=>b,d5:()=>y,vt:()=>w});var s="copy",o="convert";function a(e,t,n){if(t===s)return n;if(t===o)return e(n);if(t instanceof Array)return n.map(n=>a(e,t[0],n));if(t instanceof Object){let r={};for(let[i,s]of Object.entries(t)){if(s.derive){let e=s.derive(n);void 0!==e&&(n[i]=e)}if(!(i in n)){if(s.required)throw Error(`Missing key: ${i}`);continue}if(null==n[i]){r[i]=null;continue}r[i]=a(e,s.schema,n[i])}return r}}function l(e,t){return{required:!0,schema:e,derive:t}}function u(e){return{required:!0,schema:e}}function c(e){return{required:!1,schema:e}}var d={type:u(s),id:u(o),transports:c(s)},p={appid:c(s),appidExclude:c(s),credProps:c(s)},h={appid:c(s),appidExclude:c(s),credProps:c(s)},f={publicKey:u({rp:u(s),user:u({id:u(o),name:u(s),displayName:u(s)}),challenge:u(o),pubKeyCredParams:u(s),timeout:c(s),excludeCredentials:c([d]),authenticatorSelection:c(s),attestation:c(s),extensions:c(p)}),signal:c(s)},m={type:u(s),id:u(s),rawId:u(o),authenticatorAttachment:c(s),response:u({clientDataJSON:u(o),attestationObject:u(o),transports:l(s,e=>{var t;return(null==(t=e.getTransports)?void 0:t.call(e))||[]})}),clientExtensionResults:l(h,e=>e.getClientExtensionResults())},g={mediation:c(s),publicKey:u({challenge:u(o),timeout:c(s),rpId:c(s),allowCredentials:c([d]),userVerification:c(s),extensions:c(p)}),signal:c(s)},v={type:u(s),id:u(s),rawId:u(o),authenticatorAttachment:c(s),response:u({clientDataJSON:u(o),authenticatorData:u(o),signature:u(o),userHandle:u(o)}),clientExtensionResults:l(h,e=>e.getClientExtensionResults())};function b(e){return a(r,f,e)}function y(e){return a(r,g,e)}function x(){return!!(navigator.credentials&&navigator.credentials.create&&navigator.credentials.get&&window.PublicKeyCredential)}async function w(e){let t=await navigator.credentials.create(e);return t.toJSON=()=>a(i,m,t),t}async function _(e){let t=await navigator.credentials.get(e);return t.toJSON=()=>a(i,v,t),t}}}]);
//# sourceMappingURL=vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67-b46689470d92.js.map