"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_memoize_dist_esm_index_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_catalyst_lib_inde-dbbea9"],{70170:(t,e,o)=>{function n(t,e=0,{start:o=!0,middle:a=!0,once:r=!1}={}){let i,l=o,s=0,c=!1;function d(...n){if(c)return;let u=Date.now()-s;s=Date.now(),o&&a&&u>=e&&(l=!0),l?(l=!1,t.apply(this,n),r&&d.cancel()):(a&&u<e||!a)&&(clearTimeout(i),i=setTimeout(()=>{s=Date.now(),t.apply(this,n),r&&d.cancel()},a?e-u:e))}return d.cancel=()=>{clearTimeout(i),c=!0},d}function a(t,e=0,{start:o=!1,middle:r=!1,once:i=!1}={}){return n(t,e,{start:o,middle:r,once:i})}o.d(e,{n:()=>n,s:()=>a})},39595:(t,e,o)=>{let n;o.d(e,{CF:()=>p,p_:()=>L,FB:()=>u,Se:()=>S,aC:()=>O,zV:()=>x});let a=new WeakSet,r=new WeakMap;function i(t=document){if(r.has(t))return r.get(t);let e=!1,o=new MutationObserver(t=>{for(let e of t)if("attributes"===e.type&&e.target instanceof Element)d(e.target);else if("childList"===e.type&&e.addedNodes.length)for(let t of e.addedNodes)t instanceof Element&&l(t)});o.observe(t,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let n={get closed(){return e},unsubscribe(){e=!0,r.delete(t),o.disconnect()}};return r.set(t,n),n}function l(t){for(let e of t.querySelectorAll("[data-action]"))d(e);t instanceof Element&&t.hasAttribute("data-action")&&d(t)}function s(t){let e=t.currentTarget;for(let o of c(e))if(t.type===o.type){let n=e.closest(o.tag);a.has(n)&&"function"==typeof n[o.method]&&n[o.method](t);let r=e.getRootNode();if(r instanceof ShadowRoot&&a.has(r.host)&&r.host.matches(o.tag)){let e=r.host;"function"==typeof e[o.method]&&e[o.method](t)}}}function*c(t){for(let e of(t.getAttribute("data-action")||"").trim().split(/\s+/)){let t=e.lastIndexOf(":"),o=Math.max(0,e.lastIndexOf("#"))||e.length;yield{type:e.slice(0,t),tag:e.slice(t+1,o),method:e.slice(o+1)||"handleEvent"}}}function d(t){for(let e of c(t))t.addEventListener(e.type,s)}function u(t,e){let o=t.tagName.toLowerCase();if(t.shadowRoot){for(let n of t.shadowRoot.querySelectorAll(`[data-target~="${o}.${e}"]`))if(!n.closest(o))return n}for(let n of t.querySelectorAll(`[data-target~="${o}.${e}"]`))if(n.closest(o)===t)return n}let f=t=>String("symbol"==typeof t?t.description:t).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),b=(t,e="property")=>{let o=f(t);if(!o.includes("-"))throw new DOMException(`${e}: ${String(t)} is not a valid ${e} name`,"SyntaxError");return o},h="attr";function p(t,e){$(t,h).add(e)}let g=new WeakSet;function m(t,e){if(g.has(t))return;g.add(t);let o=Object.getPrototypeOf(t),n=o?.constructor?.attrPrefix??"data-";for(let a of(e||(e=$(o,h)),e)){let e=t[a],o=b(`${n}${a}`),r={configurable:!0,get(){return this.getAttribute(o)||""},set(t){this.setAttribute(o,t||"")}};"number"==typeof e?r={configurable:!0,get(){return Number(this.getAttribute(o)||0)},set(t){this.setAttribute(o,t)}}:"boolean"==typeof e&&(r={configurable:!0,get(){return this.hasAttribute(o)},set(t){this.toggleAttribute(o,t)}}),Object.defineProperty(t,a,r),a in t&&!t.hasAttribute(o)&&r.set.call(t,e)}}let y=new Map,w=new Promise(t=>{"loading"!==document.readyState?t():document.addEventListener("readystatechange",()=>t(),{once:!0})}),A=new Promise(t=>{let e=new AbortController;e.signal.addEventListener("abort",()=>t());let o={once:!0,passive:!0,signal:e.signal},n=()=>e.abort();document.addEventListener("mousedown",n,o),document.addEventListener("touchstart",n,o),document.addEventListener("keydown",n,o),document.addEventListener("pointerdown",n,o)}),v={ready:()=>w,firstInteraction:()=>A,visible:t=>new Promise(e=>{let o=new IntersectionObserver(t=>{for(let n of t)if(n.isIntersecting){e(),o.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let e of document.querySelectorAll(t))o.observe(e)})},C=new WeakMap;function _(t){cancelAnimationFrame(C.get(t)||0),C.set(t,requestAnimationFrame(()=>{for(let e of y.keys()){let o=t instanceof Element&&t.matches(e)?t:t.querySelector(e);if(customElements.get(e)||o){let n=o?.getAttribute("data-load-on")||"ready",a=n in v?v[n]:v.ready;for(let t of y.get(e)||[])a(e).then(t);y.delete(e),C.delete(t)}}}))}function S(t,e){for(let[o,n]of("string"==typeof t&&e&&(t={[t]:e}),Object.entries(t)))y.has(o)||y.set(o,new Set),y.get(o).add(n);k(document)}function k(t){n||(n=new MutationObserver(t=>{if(y.size)for(let e of t)for(let t of e.addedNodes)t instanceof Element&&_(t)})),_(t),n.observe(t,{subtree:!0,childList:!0})}let E=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(t){let e=this,o=t.prototype.connectedCallback;t.prototype.connectedCallback=function(){e.connectedCallback(this,o)};let n=t.prototype.disconnectedCallback;t.prototype.disconnectedCallback=function(){e.disconnectedCallback(this,n)};let a=t.prototype.attributeChangedCallback;t.prototype.attributeChangedCallback=function(t,o,n){e.attributeChangedCallback(this,t,o,n,a)};let r=t.observedAttributes||[];Object.defineProperty(t,"observedAttributes",{configurable:!0,get(){return e.observedAttributes(this,r)},set(t){r=t}}),function(t){let e=t.observedAttributes||[],o=t.attrPrefix??"data-",n=t=>b(`${o}${t}`);Object.defineProperty(t,"observedAttributes",{configurable:!0,get:()=>[...$(t.prototype,h)].map(n).concat(e),set(t){e=t}})}(t),function(t){let e=f(t.name).replace(/-element$/,"");try{window.customElements.define(e,t),window[t.name]=customElements.get(e)}catch(t){if(!(t instanceof DOMException&&"NotSupportedError"===t.name))throw t}}(t)}observedAttributes(t,e){return e}connectedCallback(t,e){var o,n;for(let e of(t.toggleAttribute("data-catalyst",!0),customElements.upgrade(t),t.querySelectorAll("template[data-shadowroot]")))e.parentElement===t&&t.attachShadow({mode:"closed"===e.getAttribute("data-shadowroot")?"closed":"open"}).append(e.content.cloneNode(!0));(m(t),a.add(t),t.shadowRoot&&(l(n=t.shadowRoot),i(n)),l(t),i(t.ownerDocument),e?.call(t),t.shadowRoot)&&(l(o=t.shadowRoot),i(o),k(t.shadowRoot))}disconnectedCallback(t,e){e?.call(t)}attributeChangedCallback(t,e,o,n,a){m(t),"data-catalyst"!==e&&a&&a.call(t,e,o,n)}};function $(t,e){if(!Object.prototype.hasOwnProperty.call(t,E)){let e=t[E],o=t[E]=new Map;if(e)for(let[t,n]of e)o.set(t,new Set(n))}let o=t[E];return o.has(e)||o.set(e,new Set),o.get(e)}function O(t,e){$(t,"target").add(e),Object.defineProperty(t,e,{configurable:!0,get(){return u(this,e)}})}function x(t,e){$(t,"targets").add(e),Object.defineProperty(t,e,{configurable:!0,get(){let t=this.tagName.toLowerCase(),o=[];if(this.shadowRoot)for(let n of this.shadowRoot.querySelectorAll(`[data-targets~="${t}.${e}"]`))n.closest(t)||o.push(n);for(let n of this.querySelectorAll(`[data-targets~="${t}.${e}"]`))n.closest(t)===this&&o.push(n);return o}})}function L(t){new CatalystDelegate(t)}},5225:(t,e,o)=>{function n(...t){return JSON.stringify(t,(t,e)=>"object"==typeof e?e:String(e))}function a(t,e={}){let{hash:o=n,cache:r=new Map}=e;return function(...e){let n=o.apply(this,e);if(r.has(n))return r.get(n);let a=t.apply(this,e);return a instanceof Promise&&(a=a.catch(t=>{throw r.delete(n),t})),r.set(n,a),a}}o.d(e,{A:()=>a})}}]);
//# sourceMappingURL=vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_catalyst_lib_inde-dbbea9-08e586e099b9.js.map