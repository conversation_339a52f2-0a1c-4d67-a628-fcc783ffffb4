"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["node_modules_github_file-attachment-element_dist_index_js","vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-cadbad"],{91707:(t,e,i)=>{i.r(e),i.d(e,{Attachment:()=>Attachment,default:()=>f});let Attachment=class Attachment{constructor(t,e){this.file=t,this.directory=e,this.state="pending",this.id=null,this.href=null,this.name=null,this.percent=0}static traverse(t,e){var i,o;return i=t,e&&(o=i).items&&Array.from(o.items).some(t=>{let e=t.webkitGetAsEntry&&t.webkitGetAsEntry();return e&&e.isDirectory})?r("",Array.from(i.items).map(t=>t.webkitGetAsEntry()).filter(t=>null!=t)):Promise.resolve(n(Array.from(i.files||[])).map(t=>new Attachment(t)))}static from(t){let e=[];for(let i of t)if(i instanceof File)e.push(new Attachment(i));else if(i instanceof Attachment)e.push(i);else throw Error("Unexpected type");return e}get fullPath(){return this.directory?`${this.directory}/${this.file.name}`:this.file.name}isImage(){return["image/gif","image/png","image/jpg","image/jpeg","image/svg+xml"].indexOf(this.file.type)>-1}isVideo(){return["video/mp4","video/quicktime"].indexOf(this.file.type)>-1}saving(t){if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saving`);this.state="saving",this.percent=t}saved(t){var e,i,n;if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saved`);this.state="saved",this.id=null!=(e=null==t?void 0:t.id)?e:null,this.href=null!=(i=null==t?void 0:t.href)?i:null,this.name=null!=(n=null==t?void 0:t.name)?n:null}isPending(){return"pending"===this.state}isSaving(){return"saving"===this.state}isSaved(){return"saved"===this.state}};function n(t){return Array.from(t).filter(t=>!t.name.startsWith("."))}async function r(t,e){let i=[];for(let o of n(e))if(o.isDirectory)i.push(...await r(o.fullPath,await function(t){return new Promise(function(e,i){let n=[],r=t.createReader(),o=()=>{r.readEntries(t=>{t.length>0?(n.push(...t),o()):e(n)},i)};o()})}(o)));else{let e=await function(t){return new Promise(function(e,i){t.file(e,i)})}(o);i.push(new Attachment(e,t))}return i}let FileAttachmentElement=class FileAttachmentElement extends HTMLElement{connectedCallback(){this.addEventListener("dragenter",a),this.addEventListener("dragover",a),this.addEventListener("dragleave",l),this.addEventListener("drop",c),this.addEventListener("paste",u),this.addEventListener("change",d)}disconnectedCallback(){this.removeEventListener("dragenter",a),this.removeEventListener("dragover",a),this.removeEventListener("dragleave",l),this.removeEventListener("drop",c),this.removeEventListener("paste",u),this.removeEventListener("change",d)}get directory(){return this.hasAttribute("directory")}set directory(t){t?this.setAttribute("directory",""):this.removeAttribute("directory")}async attach(t){let e=t instanceof DataTransfer?await Attachment.traverse(t,this.directory):Attachment.from(t);this.dispatchEvent(new CustomEvent("file-attachment-accept",{bubbles:!0,cancelable:!0,detail:{attachments:e}}))&&e.length&&this.dispatchEvent(new CustomEvent("file-attachment-accepted",{bubbles:!0,detail:{attachments:e}}))}};function o(t){return Array.from(t.types).indexOf("Files")>=0}let s=null;function a(t){let e=t.currentTarget;s&&clearTimeout(s),s=window.setTimeout(()=>e.removeAttribute("hover"),200);let i=t.dataTransfer;i&&o(i)&&(i.dropEffect="copy",e.setAttribute("hover",""),t.preventDefault())}function l(t){t.dataTransfer&&(t.dataTransfer.dropEffect="none"),t.currentTarget.removeAttribute("hover"),t.stopPropagation(),t.preventDefault()}function c(t){let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;e.removeAttribute("hover");let i=t.dataTransfer;i&&o(i)&&(e.attach(i),t.stopPropagation(),t.preventDefault())}let h=/^image\/(gif|png|jpeg)$/;function u(t){if(!t.clipboardData||!t.clipboardData.items)return;let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;let i=function(t){for(let e of t)if("file"===e.kind&&h.test(e.type))return e.getAsFile();return null}(t.clipboardData.items);i&&(e.attach([i]),t.preventDefault())}function d(t){let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;let i=t.target;if(!(i instanceof HTMLInputElement))return;let n=e.getAttribute("input");if(n&&i.id!==n)return;let r=i.files;r&&0!==r.length&&(e.attach(r),i.value="")}window.customElements.get("file-attachment")||(window.FileAttachmentElement=FileAttachmentElement,window.customElements.define("file-attachment",FileAttachmentElement));let f=FileAttachmentElement},70170:(t,e,i)=>{function n(t,e=0,{start:i=!0,middle:r=!0,once:o=!1}={}){let s,a=i,l=0,c=!1;function h(...n){if(c)return;let u=Date.now()-l;l=Date.now(),i&&r&&u>=e&&(a=!0),a?(a=!1,t.apply(this,n),o&&h.cancel()):(r&&u<e||!r)&&(clearTimeout(s),s=setTimeout(()=>{l=Date.now(),t.apply(this,n),o&&h.cancel()},r?e-u:e))}return h.cancel=()=>{clearTimeout(s),c=!0},h}function r(t,e=0,{start:i=!1,middle:o=!1,once:s=!1}={}){return n(t,e,{start:i,middle:o,once:s})}i.d(e,{n:()=>n,s:()=>r})},86643:(t,e,i)=>{var n,r,o,s,a,l,c,h,u,d,f,p,m,g,v,b,w,y,E,A,L,k,T,C,S,x,M,I,P,O,D,q,R,H,j,W,F,_,V,$,B,N,z,K,Y,X,G,U,J,Q,Z,tt,te,ti,tn,tr,to,ts,ta,tl,tc,th,tu,td,tf,tp,tm,tg,tv,tb,tw,ty,tE,tA,tL,tk,tT,tC,tS,tx,tM,tI,tP,tO,tD,tq,tR,tH,tj,tW,tF,t_,tV,t$,tB,tN,tz,tK,tY,tX,tG,tU,tJ,tQ,tZ,t0,t1,t3,t2,t7,t9,t4,t5,t6,t8,et,ee,ei,en,er,eo,es,ea,el=i(94147);i(27552);var ec=i(39595),eh=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i},eu=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let ActionListTruncationObserver=class ActionListTruncationObserver{constructor(t){this.resizeObserver=new ResizeObserver(t=>{for(let e of t){let t=e.target;t instanceof HTMLElement&&this.update(t)}}),this.resizeObserver.observe(t)}unobserve(t){this.resizeObserver.unobserve(t)}update(t){for(let e of t.querySelectorAll("li")){let t=e.querySelector(".ActionListItem-label");if(!t)continue;let i=e.querySelector(".ActionListItem-truncationTooltip");i&&(t.scrollWidth>t.clientWidth?i.style.display="":i.style.display="none")}}};let ed=class ActionListElement extends HTMLElement{constructor(){super(...arguments),n.set(this,void 0)}connectedCallback(){eh(this,n,new ActionListTruncationObserver(this),"f")}disconnectedCallback(){eu(this,n,"f").unobserve(this)}};n=new WeakMap,ed=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s}([ec.p_],ed);var ef=i(69676),ep=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},em=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},eg=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};let ev=new IntersectionObserver(t=>{for(let e of t){let t=e.target;e.isIntersecting&&t instanceof ew&&t.update()}}),eb=new ResizeObserver(t=>{for(let e of t){let t=e.target;t instanceof ew&&t.update()}});!function(t){t[t.Item=0]="Item",t[t.Divider=1]="Divider"}(u||(u={}));let ew=class ActionBarElement extends HTMLElement{constructor(){super(...arguments),r.add(this),o.set(this,null)}connectedCallback(){eb.observe(this),ev.observe(this),requestAnimationFrame(()=>{this.style.overflow="visible",this.update()})}disconnectedCallback(){eb.unobserve(this),ev.unobserve(this)}menuItemClick(t){let e=t.currentTarget,i=e?.getAttribute("data-for");i&&document.getElementById(i)?.click()}update(){let t=em(this,r,"a",s);if(!t)return;let e=t.getBoundingClientRect().top,i=null;em(this,r,"m",h).call(this,(t,n,o)=>{let s=t.getBoundingClientRect().top;return o===u.Item&&(s>e?(em(this,r,"m",l).call(this,n),this.moreMenu.hidden&&(this.moreMenu.hidden=!1),i===u.Divider&&em(this,r,"m",l).call(this,n-1)):(em(this,r,"m",a).call(this,n),n===this.items.length-1&&(this.moreMenu.hidden=!0),i===u.Divider&&em(this,r,"m",a).call(this,n-1))),i=o,!0}),em(this,o,"f")&&em(this,o,"f").abort(),eg(this,o,(0,ef.zB)(this,{bindKeys:ef.z0.ArrowHorizontal|ef.z0.HomeAndEnd,focusOutBehavior:"wrap",focusableElementFilter:t=>{let e=this.items.indexOf(t.parentElement),i=e>-1&&"visible"===this.items[e].style.visibility,n=t===this.moreMenu.invokerElement&&!this.moreMenu.hidden;return i||n}}),"f")}};o=new WeakMap,r=new WeakSet,s=function(){let t=null;return em(this,r,"m",h).call(this,(e,i,n)=>n!==u.Item||(t=e,!1)),t},a=function(t){let e=this.items[t],i=em(this,r,"a",c)[t];e&&i&&(e.style.setProperty("visibility","visible"),i.hidden=!0)},l=function(t){let e=this.items[t],i=em(this,r,"a",c)[t];e&&i&&(e.style.setProperty("visibility","hidden"),i.hidden=!1)},c=function(){return this.moreMenu.querySelectorAll('[role="menu"] > li')},h=function(t){for(let e=0;e<this.items.length;e++){let i=this.items[e],n=i.classList.contains("ActionBar-divider")?u.Divider:u.Item;if(!t(i,e,n))break}},ep([ec.zV],ew.prototype,"items",void 0),ep([ec.aC],ew.prototype,"itemContainer",void 0),ep([ec.aC],ew.prototype,"moreMenu",void 0),ew=ep([ec.p_],ew),window.ActionBarElement=ew,i(60612);var ey=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},eE=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};let eA=(()=>{let t=new Set,e=null,i=null;function n(){for(let e of t)e.update()}return r=>{window.addEventListener("resize",n),window.addEventListener("scroll",n),e||(e=new IntersectionObserver(e=>{for(let i of e){let e=i.target;i.isIntersecting?(e.update(),t.add(e)):t.delete(e)}})),i||(i=new ResizeObserver(()=>{for(let e of t)e.update()})),i.observe(r.ownerDocument.documentElement),e.observe(r)}})();let AnchoredPositionElement=class AnchoredPositionElement extends HTMLElement{constructor(){super(...arguments),d.set(this,null),f.set(this,void 0)}get align(){let t=this.getAttribute("align");return"center"===t||"end"===t?t:"start"}set align(t){this.setAttribute("align",`${t}`)}get side(){let t=this.getAttribute("side");return"inside-top"===t||"inside-bottom"===t||"inside-left"===t||"inside-right"===t||"inside-center"===t||"outside-top"===t||"outside-left"===t||"outside-right"===t?t:"outside-bottom"}set side(t){this.setAttribute("side",`${t}`)}get anchorOffset(){let t=this.getAttribute("anchor-offset");return"spacious"===t||"8"===t?8:4}set anchorOffset(t){this.setAttribute("anchor-offset",`${t}`)}get anchor(){return this.getAttribute("anchor")||""}set anchor(t){this.setAttribute("anchor",`${t}`)}get anchorElement(){if(ey(this,d,"f"))return ey(this,d,"f");let t=this.anchor;return t?this.ownerDocument.getElementById(t):null}set anchorElement(t){eE(this,d,t,"f"),ey(this,d,"f")||this.removeAttribute("anchor")}get alignmentOffset(){return Number(this.getAttribute("alignment-offset"))}set alignmentOffset(t){this.setAttribute("alignment-offset",`${t}`)}get allowOutOfBounds(){return this.hasAttribute("allow-out-of-bounds")}set allowOutOfBounds(t){this.toggleAttribute("allow-out-of-bounds",t)}connectedCallback(){this.update(),this.addEventListener("beforetoggle",()=>this.update()),eA(this)}attributeChangedCallback(){this.update()}update(){this.isConnected&&(cancelAnimationFrame(ey(this,f,"f")),eE(this,f,requestAnimationFrame(()=>{let t=this.anchorElement;if(this.classList.toggle("not-anchored",!t),t){let{left:e,top:i}=(0,ef.uG)(this,t,this);this.style.top=`${i}px`,this.style.left=`${e}px`,this.style.bottom="auto",this.style.right="auto"}else this.style.top="0",this.style.left="0",this.style.bottom="0",this.style.right="0"}),"f"))}};d=new WeakMap,f=new WeakMap,AnchoredPositionElement.observedAttributes=["align","side","anchor","alignment-offset","allow-out-of-bounds"],customElements.get("anchored-position")||(window.AnchoredPositionElement=AnchoredPositionElement,customElements.define("anchored-position",AnchoredPositionElement));var eL=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i},ek=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};function eT(t){let e=t.target,i=e?.closest("button");if(!i||i.hasAttribute("disabled")||"true"===i.getAttribute("aria-disabled"))return;let n=i?.getAttribute("data-show-dialog-id");if(n){let e=document.getElementById(n);if(e instanceof HTMLDialogElement){e.showModal(),t.preventDefault();let n=i,r=!1;for(;n;)(n=n.parentElement?.closest("[popover]:not(:popover-open)"))&&"auto"===n.popover&&(n.classList.add("dialog-inside-popover-fix"),n.popover="manual",n.showPopover(),r=!0);r&&(e.addEventListener("close",t=>t.stopImmediatePropagation(),{once:!0}),e.close(),e.showModal(),e.addEventListener("close",()=>{for(let t of e.ownerDocument.querySelectorAll(".dialog-inside-popover-fix"))t.contains(e)&&(t.classList.remove("dialog-inside-popover-fix"),t.popover="auto",t.showPopover())},{once:!0}))}}if(n=i.getAttribute("data-close-dialog-id")||i.getAttribute("data-submit-dialog-id")){let t=document.getElementById(n);t instanceof HTMLDialogElement&&t.open&&t.close()}}let DialogHelperElement=class DialogHelperElement extends HTMLElement{constructor(){super(...arguments),p.add(this),m.set(this,null)}get dialog(){return this.querySelector("dialog")}connectedCallback(){let{signal:t}=eL(this,m,new AbortController,"f");document.addEventListener("click",eT,!0),document.addEventListener("click",this,{signal:t}),this.ownerDocument.body.style.setProperty("--dialog-scrollgutter",`${window.innerWidth-this.ownerDocument.body.clientWidth}px`),new MutationObserver(t=>{for(let e of t)e.target===this.dialog&&ek(this,p,"m",g).call(this)}).observe(this,{subtree:!0,attributeFilter:["open"]}),ek(this,p,"m",g).call(this)}disconnectedCallback(){ek(this,m,"f")?.abort()}handleEvent(t){let e=t.target,i=this.dialog;if(e!==i||!i?.open||i.querySelector("form"))return;let n=i.getBoundingClientRect();n.top<=t.clientY&&t.clientY<=n.top+n.height&&n.left<=t.clientX&&t.clientX<=n.left+n.width||i.close()}};m=new WeakMap,p=new WeakSet,g=function(){this.dialog&&this.dialog.matches("[open]:not(:modal)")&&(this.dialog.addEventListener("close",t=>t.stopImmediatePropagation(),{once:!0}),this.dialog.close(),this.dialog.showModal())},window.customElements.get("dialog-helper")||(window.DialogHelperElement=DialogHelperElement,window.customElements.define("dialog-helper",DialogHelperElement));var eC=class extends Event{oldState;newState;constructor(t,{oldState:e="",newState:i="",...n}={}){super(t,n),this.oldState=String(e||""),this.newState=String(i||"")}},eS=new WeakMap;function ex(t,e,i){eS.set(t,setTimeout(()=>{eS.has(t)&&t.dispatchEvent(new eC("toggle",{cancelable:!1,oldState:e,newState:i}))},0))}var eM=globalThis.ShadowRoot||function(){},eI=globalThis.HTMLDialogElement||function(){},eP=new WeakMap,eO=new WeakMap,eD=new WeakMap;function eq(t){return eD.get(t)||"hidden"}var eR=new WeakMap;function eH(t,e){return!("auto"!==t.popover&&"manual"!==t.popover||!t.isConnected||e&&"showing"!==eq(t)||!e&&"hidden"!==eq(t)||t instanceof eI&&t.hasAttribute("open"))&&document.fullscreenElement!==t}function ej(t){return t?Array.from(eO.get(t.ownerDocument)||[]).indexOf(t)+1:0}function eW(t){let e=eO.get(t);for(let t of e||[])if(t.isConnected)return t;else e.delete(t);return null}function eF(t){return"function"==typeof t.getRootNode?t.getRootNode():t.parentNode?eF(t.parentNode):t}function e_(t){for(;t;){if(t instanceof HTMLElement&&"auto"===t.popover&&"showing"===eD.get(t))return t;if((t=t instanceof Element&&t.assignedSlot||t.parentElement||eF(t))instanceof eM&&(t=t.host),t instanceof Document)return}}var eV=new WeakMap;function e$(t){if(!eH(t,!1))return;let e=t.ownerDocument;if(!t.dispatchEvent(new eC("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!eH(t,!1))return;let i=!1;if("auto"===t.popover){let i=t.getAttribute("popover");if(ez(function(t){let e=new Map,i=0;for(let n of eO.get(t.ownerDocument)||[])e.set(n,i),i+=1;e.set(t,i),i+=1;let n=null;return!function(t){let i=e_(t);if(null===i)return;let r=e.get(i);(null===n||e.get(n)<r)&&(n=i)}(t.parentElement||eF(t)),n}(t)||e,!1,!0),i!==t.getAttribute("popover")||!eH(t,!1))return}eW(e)||(i=!0),eV.delete(t);let n=e.activeElement;t.classList.add(":popover-open"),eD.set(t,"showing"),eP.has(e)||eP.set(e,new Set),eP.get(e).add(t),(function(t){if(t.shadowRoot&&!0!==t.shadowRoot.delegatesFocus)return null;let e=t;e.shadowRoot&&(e=e.shadowRoot);let i=e.querySelector("[autofocus]");if(i)return i;for(let t of e.querySelectorAll("slot"))for(let e of t.assignedElements({flatten:!0}))if(e.hasAttribute("autofocus"))return e;else if(i=e.querySelector("[autofocus]"))return i;let n=t.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_ELEMENT),r=n.currentNode;for(;r;){var o;if(!((o=r).hidden||o instanceof eM||(o instanceof HTMLButtonElement||o instanceof HTMLInputElement||o instanceof HTMLSelectElement||o instanceof HTMLTextAreaElement||o instanceof HTMLOptGroupElement||o instanceof HTMLOptionElement||o instanceof HTMLFieldSetElement)&&o.disabled||o instanceof HTMLInputElement&&"hidden"===o.type||o instanceof HTMLAnchorElement&&""===o.href)&&"number"==typeof o.tabIndex&&-1!==o.tabIndex)return r;r=n.nextNode()}})(t)?.focus(),"auto"===t.popover&&(eO.has(e)||eO.set(e,new Set),eO.get(e).add(t),eG(eR.get(t),!0)),i&&n&&"auto"===t.popover&&eV.set(t,n),ex(t,"closed","open")}function eB(t,e=!1,i=!1){if(!eH(t,!0))return;let n=t.ownerDocument;if("auto"===t.popover&&(ez(t,e,i),!eH(t,!0))||(eG(eR.get(t),!1),eR.delete(t),i&&(t.dispatchEvent(new eC("beforetoggle",{oldState:"open",newState:"closed"})),!eH(t,!0))))return;eP.get(n)?.delete(t),eO.get(n)?.delete(t),t.classList.remove(":popover-open"),eD.set(t,"hidden"),i&&ex(t,"open","closed");let r=eV.get(t);r&&(eV.delete(t),e&&r.focus())}function eN(t,e=!1,i=!1){let n=eW(t);for(;n;)eB(n,e,i),n=eW(t)}function ez(t,e,i){let n=t.ownerDocument||t;if(t instanceof Document)return eN(n,e,i);let r=null,o=!1;for(let e of eO.get(n)||[])if(e===t)o=!0;else if(o){r=e;break}if(!o)return eN(n,e,i);for(;r&&"showing"===eq(r)&&eO.get(n)?.size;)eB(r,e,i)}var eK=new WeakMap;function eY(t){if(!t.isTrusted)return;let e=t.composedPath()[0];if(!e)return;let i=e.ownerDocument;if(!eW(i))return;let n=function(t){let e=e_(t),i=function(t){for(;t;){let e=t.popoverTargetElement;if(e instanceof HTMLElement)return e;if((t=t.parentElement||eF(t))instanceof eM&&(t=t.host),t instanceof Document)return}}(t);return ej(e)>ej(i)?e:i}(e);if(n&&"pointerdown"===t.type)eK.set(i,n);else if("pointerup"===t.type){let t=eK.get(i)===n;eK.delete(i),t&&ez(n||i,!1,!0)}}var eX=new WeakMap;function eG(t,e=!1){if(!t)return;eX.has(t)||eX.set(t,t.getAttribute("aria-expanded"));let i=t.popoverTargetElement;if(i instanceof HTMLElement&&"auto"===i.popover)t.setAttribute("aria-expanded",String(e));else{let e=eX.get(t);e?t.setAttribute("aria-expanded",e):t.removeAttribute("aria-expanded")}}var eU=globalThis.ShadowRoot||function(){};function eJ(t,e,i){let n=t[e];Object.defineProperty(t,e,{value(t){return n.call(this,i(t))}})}var eQ=/(^|[^\\]):popover-open\b/g,eZ=null;function e0(t){let e=function(){let t="function"==typeof globalThis.CSSLayerBlockRule;return`
${t?"@layer popover-polyfill {":""}
  :where([popover]) {
    position: fixed;
    z-index: 2147483647;
    inset: 0;
    padding: 0.25em;
    width: fit-content;
    height: fit-content;
    border-width: initial;
    border-color: initial;
    border-image: initial;
    border-style: solid;
    background-color: canvas;
    color: canvastext;
    overflow: auto;
    margin: auto;
  }

  :where([popover]:not(.\\:popover-open)) {
    display: none;
  }

  :where(dialog[popover].\\:popover-open) {
    display: block;
  }

  :where(dialog[popover][open]) {
    display: revert;
  }

  :where([anchor].\\:popover-open) {
    inset: auto;
  }

  :where([anchor]:popover-open) {
    inset: auto;
  }

  @supports not (background-color: canvas) {
    :where([popover]) {
      background-color: white;
      color: black;
    }
  }

  @supports (width: -moz-fit-content) {
    :where([popover]) {
      width: -moz-fit-content;
      height: -moz-fit-content;
    }
  }

  @supports not (inset: 0) {
    :where([popover]) {
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
    }
  }
${t?"}":""}
`}();if(null===eZ)try{(eZ=new CSSStyleSheet).replaceSync(e)}catch{eZ=!1}if(!1===eZ){let i=document.createElement("style");i.textContent=e,t instanceof Document?t.head.prepend(i):t.prepend(i)}else t.adoptedStyleSheets=[eZ,...t.adoptedStyleSheets]}"undefined"!=typeof HTMLElement&&"object"==typeof HTMLElement.prototype&&"popover"in HTMLElement.prototype||function(){var t;if("undefined"==typeof window)return;function e(t){return t?.includes(":popover-open")&&(t=t.replace(eQ,"$1.\\:popover-open")),t}window.ToggleEvent=window.ToggleEvent||eC,eJ(Document.prototype,"querySelector",e),eJ(Document.prototype,"querySelectorAll",e),eJ(Element.prototype,"querySelector",e),eJ(Element.prototype,"querySelectorAll",e),eJ(Element.prototype,"matches",e),eJ(Element.prototype,"closest",e),eJ(DocumentFragment.prototype,"querySelectorAll",e),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let t=(this.getAttribute("popover")||"").toLowerCase();return""===t||"auto"==t?"auto":"manual"},set(t){null===t?this.removeAttribute("popover"):this.setAttribute("popover",t)}},showPopover:{enumerable:!0,configurable:!0,value(){e$(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){eB(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(t){"showing"===eD.get(this)&&void 0===t||!1===t?eB(this,!0,!0):(void 0===t||!0===t)&&e$(this)}}});let i=Element.prototype.attachShadow;i&&Object.defineProperties(Element.prototype,{attachShadow:{enumerable:!0,configurable:!0,writable:!0,value(t){let e=i.call(this,t);return e0(e),e}}});let n=HTMLElement.prototype.attachInternals;n&&Object.defineProperties(HTMLElement.prototype,{attachInternals:{enumerable:!0,configurable:!0,writable:!0,value(){let t=n.call(this);return t.shadowRoot&&e0(t.shadowRoot),t}}});let r=new WeakMap;function o(t){Object.defineProperties(t.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(t){if(null===t)this.removeAttribute("popovertarget"),r.delete(this);else if(t instanceof Element)this.setAttribute("popovertarget",""),r.set(this,t);else throw TypeError("popoverTargetElement must be an element or null")},get(){if("button"!==this.localName&&"input"!==this.localName||"input"===this.localName&&"reset"!==this.type&&"image"!==this.type&&"button"!==this.type||this.disabled||this.form&&"submit"===this.type)return null;let t=r.get(this);if(t&&t.isConnected)return t;if(t&&!t.isConnected)return r.delete(this),null;let e=eF(this),i=this.getAttribute("popovertarget");return(e instanceof Document||e instanceof eU)&&i&&e.getElementById(i)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let t=(this.getAttribute("popovertargetaction")||"").toLowerCase();return"show"===t||"hide"===t?t:"toggle"},set(t){this.setAttribute("popovertargetaction",t)}}})}o(HTMLButtonElement),o(HTMLInputElement);(t=document).addEventListener("click",t=>{let e=t.composedPath(),i=e[0];if(!(i instanceof Element)||i?.shadowRoot)return;let n=eF(i);if(!(n instanceof eU||n instanceof Document))return;let r=e.find(t=>t.matches?.("[popovertargetaction],[popovertarget]"));if(r){!function(t){let e=t.popoverTargetElement;if(!(e instanceof HTMLElement))return;let i=eq(e);("show"!==t.popoverTargetAction||"showing"!==i)&&("hide"!==t.popoverTargetAction||"hidden"!==i)&&("showing"===i?eB(e,!0,!0):eH(e,!1)&&(eR.set(e,t),e$(e)))}(r),t.preventDefault();return}}),t.addEventListener("keydown",t=>{let e=t.key,i=t.target;!t.defaultPrevented&&i&&("Escape"===e||"Esc"===e)&&ez(i.ownerDocument,!0,!0)}),t.addEventListener("pointerdown",eY),t.addEventListener("pointerup",eY),e0(document)}();var e1=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i},e3=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let e2=['[role="menuitem"]','[role="menuitemcheckbox"]','[role="menuitemradio"]','[role="option"]'].map(t=>`:not([hidden]) > ${t}`).join(", "),e7=t=>t.textContent?.trim()[0].toLowerCase(),e9=/^\S$/;let FocusGroupElement=class FocusGroupElement extends HTMLElement{constructor(){super(...arguments),v.add(this),b.set(this,null),w.set(this,null)}get nowrap(){return this.hasAttribute("nowrap")}set nowrap(t){this.toggleAttribute("nowrap",t)}get direction(){return"horizontal"===this.getAttribute("direction")?"horizontal":"vertical"}set direction(t){this.setAttribute("direction",`${t}`)}get retain(){return this.hasAttribute("retain")}set retain(t){this.toggleAttribute("retain",t)}get mnemonics(){return this.hasAttribute("mnemonics")}connectedCallback(){e1(this,w,new AbortController,"f");let{signal:t}=e3(this,w,"f");this.addEventListener("keydown",this,{signal:t}),this.addEventListener("click",this,{signal:t}),this.addEventListener("mouseover",this,{signal:t}),this.addEventListener("focusin",this,{signal:t})}disconnectedCallback(){e3(this,w,"f")?.abort()}handleEvent(t){let{direction:e,nowrap:i}=this;if("focusin"===t.type){if(this.retain&&t.target instanceof Element&&t.target.matches(e2)){e3(this,b,"f")?.abort();let{signal:e}=e1(this,b,new AbortController,"f");for(let i of e3(this,v,"a",y)){i.setAttribute("tabindex",i===t.target?"0":"-1");let n=t.target.closest("[popover]");i===t.target&&n?.popover==="auto"&&n.closest("focus-group")===this&&n.addEventListener("toggle",t=>{if(t.target instanceof Element&&"closed"===t.newState&&(e3(this,b,"f")?.abort(),i.setAttribute("tabindex","-1"),n.id)){let t=this.querySelector(`[popovertarget="${n.id}"]`);t?t.setAttribute("tabindex","0"):e3(this,v,"a",y)[0]?.setAttribute("tabindex","0")}},{signal:e})}}}else if(t instanceof KeyboardEvent){let n=Array.from(e3(this,v,"a",y)),r=n.indexOf(t.target),o=t.key;if("Up"===o||"ArrowUp"===o)("vertical"===e||"both"===e)&&(r-=r<0?0:1,t.preventDefault());else if("Down"===o||"ArrowDown"===o)("vertical"===e||"both"===e)&&(r+=1,t.preventDefault());else if("Left"===t.key||"ArrowLeft"===t.key)("horizontal"===e||"both"===e)&&(r-=1,t.preventDefault());else if("Right"===t.key||"ArrowRight"===t.key)("horizontal"===e||"both"===e)&&(r+=1,t.preventDefault());else if("Home"===t.key||"PageUp"===t.key)r=0,t.preventDefault();else if("End"===t.key||"PageDown"===t.key)r=n.length-1,t.preventDefault();else{if(!(this.mnemonics&&e9.test(o)))return;let e=o.toLowerCase(),s=r>0&&e7(t.target)===e?r:0;(r=n.findIndex((t,i)=>i>s&&e7(t)===e))<0&&!i&&(r=n.findIndex(t=>e7(t)===e))}i&&r<0&&(r=0),!i&&r>=n.length&&(r=0);let s=n.at(Math.min(r,n.length-1));{let e=s;do e=e.closest("[popover]:not(:popover-open)"),e?.popover!=="auto"||["ArrowRight","ArrowLeft"].includes(t.key)||e.showPopover(),e=e?.parentElement||null;while(e)}s?.focus()}}};b=new WeakMap,w=new WeakMap,v=new WeakSet,y=function(){return this.querySelectorAll(e2)},customElements.get("focus-group")||(window.FocusGroupElement=FocusGroupElement,customElements.define("focus-group",FocusGroupElement));var e4=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s};let e5=class ScrollableRegionElement extends HTMLElement{constructor(){super(...arguments),this.hasOverflow=!1,this.labelledBy=""}connectedCallback(){this.style.overflow="auto",this.observer=new ResizeObserver(t=>{for(let e of t)this.hasOverflow=e.target.scrollHeight>e.target.clientHeight||e.target.scrollWidth>e.target.clientWidth}),this.observer.observe(this)}disconnectedCallback(){this.observer.disconnect()}attributeChangedCallback(t){"data-has-overflow"===t&&(this.hasOverflow?(this.setAttribute("aria-labelledby",this.labelledBy),this.setAttribute("role","region"),this.setAttribute("tabindex","0")):(this.removeAttribute("aria-labelledby"),this.removeAttribute("role"),this.removeAttribute("tabindex")))}};e4([ec.CF],e5.prototype,"hasOverflow",void 0),e4([ec.CF],e5.prototype,"labelledBy",void 0),e5=e4([ec.p_],e5),window.ScrollableRegionElement=e5,i(80147);var e6=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},e8=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i},it=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let ie=class NavListElement extends HTMLElement{constructor(){super(...arguments),E.add(this),A.set(this,void 0)}connectedCallback(){this.topLevelList&&e8(this,A,new ActionListTruncationObserver(this.topLevelList),"f")}disconnectedCallback(){this.topLevelList&&it(this,A,"f").unobserve(this.topLevelList)}selectItemById(t){if(!t)return!1;let e=it(this,E,"m",L).call(this,t);return!!e&&(it(this,E,"m",C).call(this,e),!0)}selectItemByHref(t){if(!t)return!1;let e=it(this,E,"m",k).call(this,t);return!!e&&(it(this,E,"m",C).call(this,e),!0)}selectItemByCurrentLocation(){let t=it(this,E,"m",T).call(this);return!!t&&(it(this,E,"m",C).call(this,t),!0)}expandItem(t){t.nextElementSibling?.removeAttribute("data-hidden"),t.setAttribute("aria-expanded","true")}collapseItem(t){t.nextElementSibling?.setAttribute("data-hidden",""),t.setAttribute("aria-expanded","false"),t.focus()}itemIsExpanded(t){return t?.tagName==="A"||t?.getAttribute("aria-expanded")==="true"}handleItemWithSubItemClick(t){let e=t.target;if(!(e instanceof HTMLElement))return;let i=e.closest("button");i&&(this.itemIsExpanded(i)?this.collapseItem(i):this.expandItem(i),t.stopPropagation())}handleItemWithSubItemKeydown(t){let e=t.currentTarget;if(!(e instanceof HTMLElement))return;let i=e.closest("button");if(!i){let t=e.getAttribute("aria-labelledby");if(!t)return;i=document.getElementById(t)}this.itemIsExpanded(i)&&"Escape"===t.key&&this.collapseItem(i),t.stopPropagation()}};A=new WeakMap,E=new WeakSet,L=function(t){for(let e of this.items)if(!e.classList.contains("ActionListItem--hasSubItem")&&(e.getAttribute("data-item-id")?.split(" ")||[]).includes(t))return e;return null},k=function(t){let e=this.querySelector(`.ActionListContent[href="${t}"]`);return e?e.closest(".ActionListItem"):null},T=function(){return it(this,E,"m",k).call(this,window.location.pathname)},C=function(t){let e=this.querySelector(".ActionListItem--navActive");e&&it(this,E,"m",S).call(this,e),t.classList.add("ActionListItem--navActive"),t.children.length>0&&t.children[0].setAttribute("aria-current","page");let i=it(this,E,"m",x).call(this,t);i&&(this.expandItem(i),i.classList.add("ActionListContent--hasActiveSubItem"))},S=function(t){t.classList.remove("ActionListItem--navActive"),t.children.length>0&&t.children[0].removeAttribute("aria-current");let e=it(this,E,"m",x).call(this,t);e&&(this.collapseItem(e),e.classList.remove("ActionListContent--hasActiveSubItem"))},x=function(t){if(!t.classList.contains("ActionListItem--subItem"))return null;let e=t.closest("li.ActionListItem--hasSubItem")?.querySelector("button.ActionListContent");return e||null},e6([ec.zV],ie.prototype,"items",void 0),e6([ec.aC],ie.prototype,"topLevelList",void 0),ie=e6([ec.p_],ie);var ii=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},ir=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let io=class NavListGroupElement extends HTMLElement{constructor(){super(...arguments),M.add(this),P.set(this,new ActionListTruncationObserver(this))}connectedCallback(){this.setShowMoreItemState()}get showMoreDisabled(){return this.showMoreItem.hasAttribute("aria-disabled")}set showMoreDisabled(t){t?this.showMoreItem.setAttribute("aria-disabled","true"):this.showMoreItem.removeAttribute("aria-disabled"),this.showMoreItem.classList.toggle("disabled",t)}set currentPage(t){this.showMoreItem.setAttribute("data-current-page",t.toString())}get currentPage(){return parseInt(this.showMoreItem.getAttribute("data-current-page"))||1}get totalPages(){return parseInt(this.showMoreItem.getAttribute("data-total-pages"))||1}get paginationSrc(){return this.showMoreItem.getAttribute("src")||""}async showMore(t){let e;if(t.preventDefault(),this.showMoreDisabled)return;this.showMoreDisabled=!0;try{let t=new URL(this.paginationSrc,window.location.origin);this.currentPage++,t.searchParams.append("page",this.currentPage.toString());let i=await fetch(t);if(!i.ok)return;e=await i.text(),this.currentPage===this.totalPages&&(this.showMoreItem.hidden=!0)}catch(t){this.showMoreDisabled=!1,this.currentPage--;return}let i=ir(this,M,"m",I).call(this,document,e);i?.querySelector("li > a")?.setAttribute("data-targets","nav-list-group.focusMarkers");let n=t.target.closest("button").getAttribute("data-list-id");document.getElementById(n).append(i),this.focusMarkers.pop()?.focus(),this.showMoreDisabled=!1}setShowMoreItemState(){this.showMoreItem&&(this.currentPage<this.totalPages?this.showMoreItem.hidden=!1:this.showMoreItem.hidden=!0)}};P=new WeakMap,M=new WeakSet,I=function(t,e){let i=t.createElement("template");return i.innerHTML=e,t.importNode(i.content,!0)},ii([ec.aC],io.prototype,"showMoreItem",void 0),ii([ec.zV],io.prototype,"focusMarkers",void 0),io=ii([ec.p_],io),window.NavListGroupElement=io;var is=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},ia=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let il=class SegmentedControlElement extends HTMLElement{constructor(){super(...arguments),O.add(this)}connectedCallback(){ia(this,O,"m",D).call(this)}select(t){let e=t.currentTarget;for(let t of this.items)t.classList.remove("SegmentedControl-item--selected"),t.querySelector("[aria-current]")?.setAttribute("aria-current","false");e.closest("li.SegmentedControl-item")?.classList.add("SegmentedControl-item--selected"),e.setAttribute("aria-current","true")}};O=new WeakSet,D=function(){for(let t of this.querySelectorAll(".Button-label"))t.setAttribute("data-content",t.textContent||"")},is([ec.zV],il.prototype,"items",void 0),il=is([ec.p_],il),window.customElements.get("segmented-control")||(window.SegmentedControlElement=il,window.customElements.define("segmented-control",il));var ic=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s};let ih=class ToggleSwitchElement extends HTMLElement{constructor(){super(...arguments),this.turbo=!1,this.toggling=!1}get src(){let t=this.getAttribute("src");if(!t)return null;let e=this.ownerDocument.createElement("a");return e.href=t,e.href}get csrf(){let t=this.querySelector("[data-csrf]");return this.getAttribute("data-csrf")||t instanceof HTMLInputElement&&t.value||null}get csrfField(){return this.getAttribute("csrf-field")||"authenticity_token"}isRemote(){return null!=this.src}async toggle(){if(!this.toggling&&(this.toggling=!0,!this.isDisabled())){if(!this.isRemote()){this.performToggle(),this.toggling=!1;return}this.performToggle(),this.setLoadingState();try{await this.submitForm()}catch(t){t instanceof Error&&(this.setErrorState(t.message||"An error occurred, please try again."),this.performToggle());return}finally{this.toggling=!1}this.setSuccessState()}}turnOn(){this.isDisabled()||(this.switch.setAttribute("aria-pressed","true"),this.classList.add("ToggleSwitch--checked"))}turnOff(){this.isDisabled()||(this.switch.setAttribute("aria-pressed","false"),this.classList.remove("ToggleSwitch--checked"))}isOn(){return"true"===this.switch.getAttribute("aria-pressed")}isOff(){return!this.isOn()}isDisabled(){return null!=this.switch.getAttribute("disabled")}disable(){this.switch.setAttribute("disabled","disabled")}enable(){this.switch.removeAttribute("disabled")}performToggle(){this.isOn()?this.turnOff():this.turnOn()}setLoadingState(){this.errorIcon.setAttribute("hidden","hidden"),this.loadingSpinner.removeAttribute("hidden");let t=new CustomEvent("toggleSwitchLoading",{bubbles:!0});this.dispatchEvent(t)}setSuccessState(){let t=new CustomEvent("toggleSwitchSuccess",{bubbles:!0});this.dispatchEvent(t),this.setFinishedState(!1)}setErrorState(t){let e=new CustomEvent("toggleSwitchError",{bubbles:!0,detail:t});this.dispatchEvent(e),this.setFinishedState(!0)}setFinishedState(t){t&&this.errorIcon.removeAttribute("hidden"),this.loadingSpinner.setAttribute("hidden","hidden")}async submitForm(){let t,e=new FormData;if(this.csrf&&e.append(this.csrfField,this.csrf),e.append("value",this.isOn()?"1":"0"),!this.src)throw Error("invalid src");let i={"Requested-With":"XMLHttpRequest","X-Requested-With":"XMLHttpRequest"};this.turbo&&(i.Accept="text/vnd.turbo-stream.html");try{t=await fetch(this.src,{credentials:"same-origin",method:"POST",headers:i,body:e})}catch(t){throw Error("A network error occurred, please try again.")}if(!t.ok)throw Error(await t.text());let n=t.headers.get("Content-Type");window.Turbo&&this.turbo&&n?.startsWith("text/vnd.turbo-stream.html")&&window.Turbo.renderStreamMessage(await t.text())}};ic([ec.aC],ih.prototype,"switch",void 0),ic([ec.aC],ih.prototype,"loadingSpinner",void 0),ic([ec.aC],ih.prototype,"errorIcon",void 0),ic([ec.CF],ih.prototype,"turbo",void 0),ih=ic([ec.p_],ih),window.customElements.get("toggle-switch")||(window.ToggleSwitchElement=ih,window.customElements.define("toggle-switch",ih));var iu=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},id=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};let ip=(()=>{let t;return e=>t?e.matches(t):function(e){try{return t=":popover-open",e.matches(t)}catch{try{return t=":open",e.matches(":open")}catch{return t=".\\:popover-open",e.matches(".\\:popover-open")}}}(e)})(),im="sr-only",ig=["tooltip-n","tooltip-s","tooltip-e","tooltip-w","tooltip-ne","tooltip-se","tooltip-nw","tooltip-sw"];function iv(t){for(let e of iE)e!==t&&(ip(e)?e.hidePopover():iE.delete(e))}function ib(){iv()}function iw(t){setTimeout(()=>{for(let e of iE)ip(e)&&"focus"===e.showReason&&e.control!==t.target&&e.hidePopover()},0)}let iy=new Set,iE=new Set;let ToolTipElement=class ToolTipElement extends HTMLElement{constructor(){super(...arguments),q.add(this),R.set(this,void 0),H.set(this,"center"),j.set(this,"outside-bottom"),W.set(this,!1),F.set(this,"mouse")}styles(){return`
      :host {
        --tooltip-top: var(--tool-tip-position-top, 0);
        --tooltip-left: var(--tool-tip-position-left, 0);
        padding: var(--overlay-paddingBlock-condensed) var(--overlay-padding-condensed) !important;
        font: var(--text-body-shorthand-small);
        color: var(--tooltip-fgColor, var(--fgColor-onEmphasis)) !important;
        text-align: center;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        letter-spacing: normal;
        word-wrap: break-word;
        white-space: pre;
        background: var(--tooltip-bgColor, var(--bgColor-emphasis)) !important;
        border-radius: var(--borderRadius-medium);
        border: 0 !important;
        opacity: 0;
        max-width: var(--overlay-width-small);
        word-wrap: break-word;
        white-space: normal;
        width: max-content !important;
        inset: var(--tooltip-top) auto auto var(--tooltip-left) !important;
        overflow: visible !important;
        text-wrap: balance;
      }

      :host(:is(.tooltip-n, .tooltip-nw, .tooltip-ne)) {
        --tooltip-top: calc(var(--tool-tip-position-top, 0) - var(--overlay-offset, 0.25rem));
        --tooltip-left: var(--tool-tip-position-left);
      }

      :host(:is(.tooltip-s, .tooltip-sw, .tooltip-se)) {
        --tooltip-top: calc(var(--tool-tip-position-top, 0) + var(--overlay-offset, 0.25rem));
        --tooltip-left: var(--tool-tip-position-left);
      }

      :host(.tooltip-w) {
        --tooltip-top: var(--tool-tip-position-top);
        --tooltip-left: calc(var(--tool-tip-position-left, 0) - var(--overlay-offset, 0.25rem));
      }

      :host(.tooltip-e) {
        --tooltip-top: var(--tool-tip-position-top);
        --tooltip-left: calc(var(--tool-tip-position-left, 0) + var(--overlay-offset, 0.25rem));
      }

      :host:after{
        position: absolute;
        display: block;
        right: 0;
        left: 0;
        height: var(--overlay-offset, 0.25rem);
        content: "";
      }

      :host(.tooltip-s):after,
      :host(.tooltip-se):after,
      :host(.tooltip-sw):after {
        bottom: 100%
      }

      :host(.tooltip-n):after,
      :host(.tooltip-ne):after,
      :host(.tooltip-nw):after {
        top: 100%;
      }

      @keyframes tooltip-appear {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }

      :host(:popover-open),
      :host(:popover-open):before {
        animation-name: tooltip-appear;
        animation-duration: .1s;
        animation-fill-mode: forwards;
        animation-timing-function: ease-in;
      }

      :host(.\\:popover-open) {
        animation-name: tooltip-appear;
        animation-duration: .1s;
        animation-fill-mode: forwards;
        animation-timing-function: ease-in;
      }

      @media (forced-colors: active) {
        :host {
          outline: solid 1px transparent;
        }

        :host:before {
          display: none;
        }
      }
    `}get showReason(){return iu(this,F,"f")}get htmlFor(){return this.getAttribute("for")||""}set htmlFor(t){this.setAttribute("for",t)}get type(){return"label"===this.getAttribute("data-type")?"label":"description"}set type(t){this.setAttribute("data-type",t)}get direction(){return this.getAttribute("data-direction")||"s"}set direction(t){this.setAttribute("data-direction",t)}get control(){return this.ownerDocument.getElementById(this.htmlFor)}set hiddenFromView(t){t&&ip(this)?this.hidePopover():t||ip(this)||this.showPopover()}get hiddenFromView(){return!ip(this)}connectedCallback(){if(iy.add(this),iu(this,q,"m",$).call(this),iu(this,q,"m",B).call(this),!this.shadowRoot){let t=this.attachShadow({mode:"open"});t.appendChild(document.createElement("style")).textContent=this.styles(),t.appendChild(document.createElement("slot"))}iu(this,q,"m",_).call(this,!1),id(this,W,!0,"f"),iu(this,q,"m",V).call(this)}disconnectedCallback(){iy.delete(this),iE.delete(this),iu(this,R,"f")?.abort()}async handleEvent(t){if(!this.control)return;let e=ip(this),i="mouseenter"===t.type||"focus"===t.type&&(navigator.webdriver||this.control.matches(":focus-visible")),n="mouseleave"===t.type&&t.relatedTarget!==this.control&&t.relatedTarget!==this,r="keydown"===t.type&&"Escape"===t.key,o="mousedown"===t.type&&t.currentTarget===this.control,s="beforetoggle"===t.type&&t.currentTarget!==this;e&&r&&(t.stopImmediatePropagation(),t.preventDefault()),await Promise.resolve(),e||!i||ip(this)?e&&(n||r||o||s)&&ip(this)&&this.hidePopover():(id(this,F,"mouseenter"===t.type?"mouse":"focus","f"),this.showPopover()),"toggle"===t.type&&iu(this,q,"m",_).call(this,"open"===t.newState)}attributeChangedCallback(t){this.isConnected&&("for"===t?iu(this,q,"m",V).call(this):"id"===t||"data-type"===t?iu(this,q,"m",$).call(this):"data-direction"===t&&iu(this,q,"m",B).call(this))}};R=new WeakMap,H=new WeakMap,j=new WeakMap,W=new WeakMap,F=new WeakMap,q=new WeakSet,_=function(t){t?(iE.add(this),this.classList.remove(im),iv(this),iu(this,q,"m",N).call(this)):(iE.delete(this),this.classList.remove(...ig),this.classList.add(im))},V=function(){if(!this.control)return;this.setAttribute("role","tooltip"),iu(this,R,"f")?.abort(),id(this,R,new AbortController,"f");let{signal:t}=iu(this,R,"f");this.addEventListener("mouseleave",this,{signal:t}),this.addEventListener("toggle",this,{signal:t}),this.control.addEventListener("mouseenter",this,{signal:t}),this.control.addEventListener("mouseleave",this,{signal:t}),this.control.addEventListener("focus",this,{signal:t}),this.control.addEventListener("mousedown",this,{signal:t}),this.control.popoverTargetElement?.addEventListener("beforetoggle",this,{signal:t}),this.ownerDocument.addEventListener("focusout",ib),this.ownerDocument.addEventListener("focusin",iw),this.ownerDocument.addEventListener("keydown",this,{signal:t,capture:!0})},$=function(){if(this.id&&this.control)if("label"===this.type){let t=this.control.getAttribute("aria-labelledby");t=t?t.split(" ").includes(this.id)?`${t}`:`${t} ${this.id}`:this.id,this.control.setAttribute("aria-labelledby",t),this.setAttribute("aria-hidden","true")}else{let t=this.control.getAttribute("aria-describedby");t=t?t.split(" ").includes(this.id)?`${t}`:`${t} ${this.id}`:this.id,this.control.setAttribute("aria-describedby",t)}},B=function(){this.classList.remove(...ig);let t=this.direction;"n"===t?(id(this,H,"center","f"),id(this,j,"outside-top","f")):"ne"===t?(id(this,H,"end","f"),id(this,j,"outside-top","f")):"e"===t?(id(this,H,"center","f"),id(this,j,"outside-right","f")):"se"===t?(id(this,H,"end","f"),id(this,j,"outside-bottom","f")):"s"===t?(id(this,H,"center","f"),id(this,j,"outside-bottom","f")):"sw"===t?(id(this,H,"start","f"),id(this,j,"outside-bottom","f")):"w"===t?(id(this,H,"center","f"),id(this,j,"outside-left","f")):"nw"===t&&(id(this,H,"start","f"),id(this,j,"outside-top","f"))},N=function(){if(!this.control||!iu(this,W,"f")||!ip(this))return;let t=(0,ef.uG)(this,this.control,{side:iu(this,j,"f"),align:iu(this,H,"f"),anchorOffset:0}),e=t.anchorSide,i=t.anchorAlign;this.style.setProperty("--tool-tip-position-top",`${t.top}px`),this.style.setProperty("--tool-tip-position-left",`${t.left}px`);let n="s";n="outside-left"===e?"w":"outside-right"===e?"e":"outside-top"===e?"center"===i?"n":"start"===i?"ne":"nw":"center"===i?"s":"start"===i?"se":"sw",this.classList.add(`tooltip-${n}`)},ToolTipElement.observedAttributes=["data-type","data-direction","id","for"],window.customElements.get("tool-tip")||(window.ToolTipElement=ToolTipElement,window.customElements.define("tool-tip",ToolTipElement));var iA=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},iL=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let ik=class XBannerElement extends HTMLElement{constructor(){super(...arguments),z.add(this)}dismiss(){if("remove"===iL(this,z,"a",K)){let t=this.parentElement;if(!t)return;t.removeChild(this)}else this.hide();this.dispatchEvent(new CustomEvent("banner:dismiss"))}show(){this.style.setProperty("display","initial")}hide(){this.style.setProperty("display","none")}};function iT(t){t.style.display="inline-block"}function iC(t){t.style.display="none"}z=new WeakSet,K=function(){return this.getAttribute("data-dismiss-scheme")},iA([ec.aC],ik.prototype,"titleText",void 0),ik=iA([ec.p_],ik),window.customElements.get("x-banner")||(window.XBannerElement=ik,window.customElements.define("x-banner",ik)),i(96907),i(14940);let iS=new WeakMap;document.addEventListener("clipboard-copy",({target:t})=>{if(!(t instanceof HTMLElement)||!t.hasAttribute("data-view-component"))return;let e=iS.get(t),i=t.parentNode?.querySelector("[data-clipboard-copy-feedback]"),n="Copied!";e?(clearTimeout(e),iS.delete(t)):(!function(t){let[e,i]=t.querySelectorAll(".octicon");e&&i&&(iC(e),iT(i))}(t),i&&(i.textContent===n?i.textContent=`${n}\u00A0`:i.textContent=n)),iS.set(t,setTimeout(()=>{!function(t){let[e,i]=t.querySelectorAll(".octicon");e&&i&&(iT(e),iC(i))}(t),iS.delete(t)},2e3))}),i(4712),i(49728);var ix=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s};let iM=class PrimerMultiInputElement extends HTMLElement{activateField(t){let e=this.findField(t);if(e){for(let t of this.fields)t!==e&&(t.setAttribute("disabled","disabled"),t.setAttribute("hidden","hidden"),t.parentElement?.setAttribute("hidden","hidden"));e.removeAttribute("disabled"),e.removeAttribute("hidden"),e.parentElement?.removeAttribute("hidden")}}findField(t){for(let e of this.fields)if(e.getAttribute("data-name")===t)return e;return null}};ix([ec.zV],iM.prototype,"fields",void 0),iM=ix([ec.p_],iM),window.customElements.get("primer-multi-input")||(Object.assign(window,{PrimerMultiInputElement:iM}),window.customElements.define("primer-multi-input",iM)),i(20761);var iI=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},iP=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},iO=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};let iD=class PrimerTextFieldElement extends HTMLElement{constructor(){super(...arguments),Y.set(this,void 0)}connectedCallback(){iP(this,Y,"f")?.abort();let{signal:t}=iO(this,Y,new AbortController,"f");this.addEventListener("auto-check-success",async t=>{let e=await t.detail.response.text();e&&e.length>0?this.setSuccess(e):this.clearError()},{signal:t}),this.addEventListener("auto-check-error",async t=>{let e=await t.detail.response.text();this.setError(e)},{signal:t})}disconnectedCallback(){iP(this,Y,"f")?.abort()}clearContents(){this.inputElement.value="",this.inputElement.focus(),this.inputElement.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))}clearError(){this.inputElement.removeAttribute("invalid"),this.validationElement.hidden=!0,this.validationMessageElement.replaceChildren()}setValidationMessage(t){let e=document.createElement("template");e.innerHTML=t;let i=document.importNode(e.content,!0);this.validationMessageElement.replaceChildren(i)}toggleValidationStyling(t){t?this.validationElement.classList.remove("FormControl-inlineValidation--success"):this.validationElement.classList.add("FormControl-inlineValidation--success"),this.validationSuccessIcon.hidden=t,this.validationErrorIcon.hidden=!t,this.inputElement.setAttribute("invalid",t?"true":"false")}setSuccess(t){this.toggleValidationStyling(!1),this.setValidationMessage(t),this.validationElement.hidden=!1}setError(t){this.toggleValidationStyling(!0),this.setValidationMessage(t),this.validationElement.hidden=!1}showLeadingSpinner(){this.leadingSpinner?.removeAttribute("hidden"),this.leadingVisual?.setAttribute("hidden","")}hideLeadingSpinner(){this.leadingSpinner?.setAttribute("hidden",""),this.leadingVisual?.removeAttribute("hidden")}};Y=new WeakMap,iI([ec.aC],iD.prototype,"inputElement",void 0),iI([ec.aC],iD.prototype,"validationElement",void 0),iI([ec.aC],iD.prototype,"validationMessageElement",void 0),iI([ec.aC],iD.prototype,"validationSuccessIcon",void 0),iI([ec.aC],iD.prototype,"validationErrorIcon",void 0),iI([ec.aC],iD.prototype,"leadingVisual",void 0),iI([ec.aC],iD.prototype,"leadingSpinner",void 0),iD=iI([ec.p_],iD);var iq=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s};let iR=class ToggleSwitchInputElement extends HTMLElement{connectedCallback(){this.addEventListener("toggleSwitchError",t=>{this.validationMessageElement.textContent=t.detail,this.validationElement.removeAttribute("hidden")}),this.addEventListener("toggleSwitchSuccess",()=>{this.validationMessageElement.textContent="",this.validationElement.setAttribute("hidden","hidden")}),this.addEventListener("toggleSwitchLoading",()=>{this.validationMessageElement.textContent="",this.validationElement.setAttribute("hidden","hidden")})}};iq([ec.aC],iR.prototype,"validationElement",void 0),iq([ec.aC],iR.prototype,"validationMessageElement",void 0),iR=iq([ec.p_],iR);let iH=(t,e,i)=>{if(e())i();else{let n=new MutationObserver(()=>{e()&&(i(),n.disconnect())});n.observe(t,{childList:!0,subtree:!0})}};var ij=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},iW=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i},iF=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)};let i_=['[role="menuitem"]','[role="menuitemcheckbox"]','[role="menuitemradio"]'],iV=i_.map(t=>`:not([hidden]) > ${t}`),i$=class ActionMenuElement extends HTMLElement{constructor(){super(...arguments),X.add(this),G.set(this,void 0),U.set(this,""),J.set(this,""),Q.set(this,!1),Z.set(this,void 0)}get selectVariant(){return this.getAttribute("data-select-variant")}set selectVariant(t){t?this.setAttribute("data-select-variant",t):this.removeAttribute("variant")}get dynamicLabelPrefix(){let t=this.getAttribute("data-dynamic-label-prefix");return t?`${t}:`:""}set dynamicLabelPrefix(t){this.setAttribute("data-dynamic-label",t)}get dynamicLabel(){return this.hasAttribute("data-dynamic-label")}set dynamicLabel(t){this.toggleAttribute("data-dynamic-label",t)}get popoverElement(){return this.invokerElement?.popoverTargetElement||null}get invokerElement(){let t=this.querySelector("[role=menu]")?.id;if(!t)return null;for(let e of this.querySelectorAll("[aria-controls]"))if(e.getAttribute("aria-controls")===t)return e;return null}get invokerLabel(){return this.invokerElement?this.invokerElement.querySelector(".Button-label"):null}get selectedItems(){let t=this.querySelectorAll("[aria-checked=true]"),e=[];for(let i of t){let t=i.querySelector(".ActionListItem-label");e.push({label:t?.textContent,value:i?.getAttribute("data-value"),element:i})}return e}connectedCallback(){let{signal:t}=iW(this,G,new AbortController,"f");this.addEventListener("keydown",this,{signal:t}),this.addEventListener("click",this,{signal:t}),this.addEventListener("mouseover",this,{signal:t}),this.addEventListener("focusout",this,{signal:t}),this.addEventListener("mousedown",this,{signal:t}),this.popoverElement?.addEventListener("toggle",this,{signal:t}),iF(this,X,"m",td).call(this),iF(this,X,"m",tf).call(this),iF(this,X,"m",tt).call(this),this.includeFragment&&this.includeFragment.addEventListener("include-fragment-replaced",this,{signal:t});let e=()=>{iF(this,X,"m",tu).call(this)&&this.overlay?.update()};iW(this,Z,new IntersectionObserver(t=>{for(let i of t)i.target===this.invokerElement&&(i.isIntersecting?window.addEventListener("scroll",e,{capture:!0}):window.removeEventListener("scroll",e,{capture:!0}))}),"f"),iH(this,()=>!!this.invokerElement,()=>iF(this,Z,"f").observe(this.invokerElement)),this.includeFragment||this.setAttribute("data-ready","true")}disconnectedCallback(){iF(this,G,"f").abort()}handleEvent(t){let e=this.invokerElement?.contains(t.target),i=iF(this,X,"m",tn).call(this,t);if("toggle"===t.type&&"open"===t.newState&&window.requestAnimationFrame(()=>{iF(this,X,"a",tp)?.focus()}),e&&"mousedown"===t.type)return void iW(this,Q,!0,"f");if("mousedown"===t.type)return void t.preventDefault();if(e&&i){iF(this,X,"m",tr).call(this,t),iW(this,Q,!1,"f");return}if("focusout"===t.type){if(iF(this,Q,"f"))return;requestAnimationFrame(()=>{this.contains(document.activeElement)&&document.activeElement!==this.invokerElement||iF(this,X,"m",tl).call(this)});return}let n=t.target.closest(iV.join(","));if(null!==n&&i){if(iF(this,X,"m",te).call(this,t))return;let e=n.closest("[data-show-dialog-id]");if(e){let i=this.ownerDocument.getElementById(e.getAttribute("data-show-dialog-id")||"");if(i&&this.contains(e))return void iF(this,X,"m",to).call(this,t,i)}iF(this,X,"m",ti).call(this,t)&&(t.preventDefault(),n.click()),iF(this,X,"m",ts).call(this,n);return}"include-fragment-replaced"===t.type&&iF(this,X,"m",ta).call(this)}get items(){return Array.from(this.querySelectorAll(iV.join(",")))}getItemById(t){return this.querySelector(`li[data-item-id="${t}"`)}isItemDisabled(t){return!!t&&t.classList.contains("ActionListItem--disabled")}disableItem(t){t&&(t.classList.add("ActionListItem--disabled"),t.querySelector(".ActionListContent").setAttribute("aria-disabled","true"))}enableItem(t){t&&(t.classList.remove("ActionListItem--disabled"),t.querySelector(".ActionListContent").removeAttribute("aria-disabled"))}isItemHidden(t){return!!t&&t.hasAttribute("hidden")}hideItem(t){t&&t.setAttribute("hidden","hidden")}showItem(t){t&&t.removeAttribute("hidden")}isItemChecked(t){return!!t&&"true"===t.querySelector(".ActionListContent").getAttribute("aria-checked")}checkItem(t){if(t&&("single"===this.selectVariant||"multiple"===this.selectVariant)){let e=t.querySelector(".ActionListContent");"true"!==e.getAttribute("aria-checked")&&iF(this,X,"m",ts).call(this,e)}}uncheckItem(t){if(t&&("single"===this.selectVariant||"multiple"===this.selectVariant)){let e=t.querySelector(".ActionListContent");"true"===e.getAttribute("aria-checked")&&iF(this,X,"m",ts).call(this,e)}}};G=new WeakMap,U=new WeakMap,J=new WeakMap,Q=new WeakMap,Z=new WeakMap,X=new WeakSet,tt=function(){let{signal:t}=iF(this,G,"f");for(let e of this.querySelectorAll(i_.join(",")))e.addEventListener("click",iF(this,X,"m",te).bind(this),{signal:t}),e.addEventListener("keydown",iF(this,X,"m",te).bind(this),{signal:t})},te=function(t){if(!iF(this,X,"m",tn).call(this,t))return!1;let e=t.target.closest(iV.join(","));return!!e&&!!e.getAttribute("aria-disabled")&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation(),!0)},ti=function(t){return t.target instanceof HTMLAnchorElement&&t instanceof KeyboardEvent&&"keydown"===t.type&&!(t.ctrlKey||t.altKey||t.metaKey||t.shiftKey)&&" "===t.key},tn=function(t){return t instanceof MouseEvent&&"click"===t.type||iF(this,X,"m",ti).call(this,t)},tr=function(t){t.preventDefault(),t.stopPropagation(),iF(this,X,"m",tu).call(this)?iF(this,X,"m",th).call(this):iF(this,X,"m",tc).call(this)},to=function(t,e){this.contains(e)&&(this.querySelector(".ActionListWrap").style.display="none");let i=new AbortController,{signal:n}=i,r=()=>{i.abort(),this.contains(e)&&(this.querySelector(".ActionListWrap").style.display="",iF(this,X,"m",tu).call(this)&&iF(this,X,"m",th).call(this));let t=this.ownerDocument.activeElement,n=this.ownerDocument.activeElement===this.ownerDocument.body,r=this.contains(t),o=e.contains(t);(n||r||o)&&setTimeout(()=>{let e=this.ownerDocument.activeElement;(e===t||e===this.ownerDocument.body)&&this.invokerElement?.focus()},0)};e.addEventListener("close",r,{signal:n}),e.addEventListener("cancel",r,{signal:n})},ts=function(t){if("multiple"!==this.selectVariant&&setTimeout(()=>{iF(this,X,"m",tu).call(this)&&iF(this,X,"m",th).call(this)}),"multiple"!==this.selectVariant&&"single"!==this.selectVariant)return;let e="true"!==t.getAttribute("aria-checked");if("single"===this.selectVariant){for(let i of(e&&t.setAttribute("aria-checked","true"),this.querySelectorAll("[aria-checked]")))i!==t&&i.setAttribute("aria-checked","false");iF(this,X,"m",td).call(this)}else t.setAttribute("aria-checked",`${e}`);iF(this,X,"m",tf).call(this),this.dispatchEvent(new CustomEvent("itemActivated",{bubbles:!0,detail:{item:t.parentElement,checked:this.isItemChecked(t.parentElement)}}))},ta=function(){iF(this,X,"a",tp)?.focus(),iF(this,X,"m",tt).call(this),this.setAttribute("data-ready","true")},tl=function(){iF(this,X,"m",th).call(this)},tc=function(){this.popoverElement?.showPopover()},th=function(){this.popoverElement?.hidePopover()},tu=function(){return this.popoverElement?.matches(":popover-open")},td=function(){if("single"!==this.selectVariant||!this.dynamicLabel)return;let t=this.invokerLabel;if(!t)return;iW(this,U,iF(this,U,"f")||t.textContent||"","f");let e=this.querySelector("[aria-checked=true] .ActionListItem-label");if(e&&this.dynamicLabel){let i=document.createElement("span");i.classList.add("color-fg-muted");let n=document.createElement("span");i.textContent=this.dynamicLabelPrefix,n.textContent=e.textContent||"",t.replaceChildren(i,n)}else t.textContent=iF(this,U,"f")},tf=function(){if("single"===this.selectVariant){let t=this.querySelector("[data-list-inputs=true] input");if(!t)return;let e=this.selectedItems[0];e?(t.value=(e.value||e.label||"").trim(),t.removeAttribute("disabled")):t.setAttribute("disabled","disabled")}else if("none"!==this.selectVariant){let t=this.querySelector("[data-list-inputs=true]");if(!t)return;let e=t.querySelectorAll("input");for(let i of(e.length>0&&iW(this,J,iF(this,J,"f")||e[0].name,"f"),this.selectedItems)){let e=document.createElement("input");e.setAttribute("data-list-input","true"),e.type="hidden",e.autocomplete="off",e.name=iF(this,J,"f"),e.value=(i.value||i.label||"").trim(),t.append(e)}for(let t of e)t.remove()}},tp=function(){return this.querySelector(iV.join(","))},ij([ec.aC],i$.prototype,"includeFragment",void 0),ij([ec.aC],i$.prototype,"overlay",void 0),i$=ij([ec.p_],i$),window.customElements.get("action-menu")||(window.ActionMenuElement=i$,window.customElements.define("action-menu",i$));let iB={Less:"less",Equal:"equal",Greater:"greater"};var iN=t=>{throw TypeError(t)},iz=(t,e,i)=>e.has(t)||iN("Cannot "+i),iK=(t,e,i)=>(iz(t,e,"read from private field"),i?i.call(t):e.get(t)),iY=(t,e,i)=>e.has(t)?iN("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),iX=(t,e,i,n)=>(iz(t,e,"write to private field"),e.set(t,i),i),iG=(t,e,i)=>(iz(t,e,"access private method"),i);let MinHeap=class MinHeap{constructor({compareFn:t}){iY(this,tv),iY(this,tm),iY(this,tg),iX(this,tm,t),iX(this,tg,[])}insert(t){iK(this,tg).push(t),iG(this,tv,tw).call(this)}pop(){let t=iK(this,tg)[0];return iK(this,tg)[iK(this,tg).length-1]&&(iK(this,tg)[0]=iK(this,tg)[iK(this,tg).length-1],iK(this,tg).pop()),iG(this,tv,tb).call(this),t}peek(){return iK(this,tg)[0]}delete(t){let e=iK(this,tg).indexOf(t);-1!==e&&(iJ(iK(this,tg),e,iK(this,tg).length-1),iK(this,tg).pop(),iG(this,tv,tb).call(this))}clear(){iX(this,tg,[])}get size(){return iK(this,tg).length}};function iU(t){return Math.floor((t-1)/2)}function iJ(t,e,i){let n=t[e];t[e]=t[i],t[i]=n}tm=new WeakMap,tg=new WeakMap,tv=new WeakSet,tb=function(){let t=0;for(;2*t+1<iK(this,tg).length;){var e,i;let n=2*t+1;if(2*t+2<iK(this,tg).length&&iK(this,tm).call(this,(e=iK(this,tg),e[2*t+2]),(i=iK(this,tg),i[2*t+1]))===iB.Less&&(n=2*t+2),iK(this,tm).call(this,iK(this,tg)[t],iK(this,tg)[n])===iB.Less)break;iJ(iK(this,tg),t,n),t=n}},tw=function(){var t;let e=iK(this,tg).length-1;for(;e>0&&iK(this,tm).call(this,iK(this,tg)[e],(t=iK(this,tg),t[iU(e)]))===iB.Less;)iJ(iK(this,tg),e,iU(e)),e=iU(e)};var iQ=Object.defineProperty,iZ=t=>{throw TypeError(t)},i0=(t,e,i)=>e in t?iQ(t,e,{enumerable:!0,configurable:!0,writable:!0,value:i}):t[e]=i,i1=(t,e,i)=>i0(t,"symbol"!=typeof e?e+"":e,i),i3=(t,e,i)=>e.has(t)||iZ("Cannot "+i),i2=(t,e,i)=>(i3(t,e,"read from private field"),i?i.call(t):e.get(t)),i7=(t,e,i)=>e.has(t)?iZ("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),i9=(t,e,i,n)=>(i3(t,e,"write to private field"),e.set(t,i),i);ty=Symbol.toStringTag;let Deferred=class Deferred{constructor(){i1(this,ty,"Deferred"),i7(this,tE),i7(this,tA),i7(this,tL),i9(this,tE,new Promise((t,e)=>{i9(this,tA,t),i9(this,tL,e)}))}then(t,e){return Promise.prototype.then.apply(i2(this,tE),[t,e])}catch(t){return Promise.prototype.catch.apply(i2(this,tE),[t])}finally(t){return Promise.prototype.finally.apply(i2(this,tE),[t])}resolve(t){i2(this,tA).call(this,t)}reject(t){i2(this,tL).call(this,t)}getPromise(){return i2(this,tE)}};tE=new WeakMap,tA=new WeakMap,tL=new WeakMap;var i4=t=>{throw TypeError(t)},i5=(t,e,i)=>e.has(t)||i4("Cannot "+i),i6=(t,e,i)=>(i5(t,e,"read from private field"),i?i.call(t):e.get(t)),i8=(t,e,i)=>e.has(t)?i4("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,i),nt=(t,e,i,n)=>(i5(t,e,"write to private field"),e.set(t,i),i),ne=(t,e,i)=>(i5(t,e,"access private method"),i);let LiveRegionElement=class LiveRegionElement extends HTMLElement{constructor(){if(super(),i8(this,tS),i8(this,tk),i8(this,tT),i8(this,tC),!this.shadowRoot){let t=(ni||((ni=document.createElement("template")).innerHTML=nn),ni);this.attachShadow({mode:"open"}).appendChild(t.content.cloneNode(!0))}nt(this,tk,!1),nt(this,tC,null),nt(this,tT,new MinHeap({compareFn:nr}))}get delay(){let t=this.getAttribute("delay");return t?parseInt(t,10):150}set delay(t){this.setAttribute("delay",`${t}`)}announce(t,e={}){let{delayMs:i,politeness:n="polite"}=e,r=Date.now(),o=new Deferred,s={deferred:o,politeness:n,contents:t,scheduled:void 0!==i?r+i:r};return i6(this,tT).insert(s),ne(this,tS,tx).call(this),{...o.getPromise(),cancel:()=>{i6(this,tT).delete(s),o.resolve()}}}announceFromElement(t,e){var i;let n,r=(n="",(i=t).hasAttribute("aria-label")?n=i.getAttribute("aria-label"):i.innerText?n=i.innerText:i.textContent&&(n=i.textContent),n?n.trim():"");return""!==r?this.announce(r,e):{...Promise.resolve(),cancel:no}}getMessage(t="polite"){let e=this.shadowRoot?.getElementById(t);if(!e)throw Error("Unable to find container for message");return e.textContent}clear(){null!==i6(this,tC)&&(clearTimeout(i6(this,tC)),nt(this,tC,null)),nt(this,tk,!1),i6(this,tT).clear()}};tk=new WeakMap,tT=new WeakMap,tC=new WeakMap,tS=new WeakSet,tx=function(){if(i6(this,tk))return;let t=i6(this,tT).peek();if(!t)return;null!==i6(this,tC)&&(clearTimeout(i6(this,tC)),nt(this,tC,null));let e=Date.now();if(t.scheduled<=e){(t=i6(this,tT).pop())&&ne(this,tS,tM).call(this,t),ne(this,tS,tx).call(this);return}let i=t.scheduled-e;nt(this,tC,window.setTimeout(()=>{nt(this,tC,null),ne(this,tS,tx).call(this)},i))},tM=function(t){nt(this,tk,!0);let{contents:e,deferred:i,politeness:n}=t,r=this.shadowRoot?.getElementById(n);if(!r)throw nt(this,tk,!1),Error(`Unable to find container for message. Expected a container with id="${n}"`);r.textContent===e?r.textContent=`${e}\xa0`:r.textContent=e,null!==i6(this,tC)&&clearTimeout(i6(this,tC)),i.resolve(),this.delay>0?nt(this,tC,window.setTimeout(()=>{nt(this,tC,null),nt(this,tk,!1),ne(this,tS,tx).call(this)},this.delay)):(nt(this,tC,null),nt(this,tk,!1),ne(this,tS,tx).call(this))};let ni=null,nn=`
<style>
:host {
  border: 0;
  clip: rect(0 0 0 0);
  clip-path: inset(50%);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
</style>
<div id="polite" aria-live="polite" aria-atomic="true"></div>
<div id="assertive" aria-live="assertive" aria-atomic="true"></div>
`;function nr(t,e){return t.politeness===e.politeness?t.scheduled===e.scheduled?iB.Equal:t.scheduled<e.scheduled?iB.Less:iB.Greater:"assertive"===t.politeness&&"assertive"!==e.politeness?iB.Less:"assertive"!==t.politeness&&"assertive"===e.politeness?iB.Greater:iB.Equal}function no(){}customElements.get("live-region")||customElements.define("live-region",LiveRegionElement);var ns=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s},na=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},nl=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};let nc=['[role="option"]'],nh=nc.join(","),nu=nc.map(t=>`:not([hidden]) > ${t}`).join(",");!function(t){t[t.REMOTE=0]="REMOTE",t[t.EVENTUALLY_LOCAL=1]="EVENTUALLY_LOCAL",t[t.LOCAL=2]="LOCAL"}(es||(es={})),function(t){t[t.BODY=0]="BODY",t[t.BANNER=1]="BANNER"}(ea||(ea={}));let nd=(()=>{let t=new Set,e=null;function i(){for(let e of t)e.updateAnchorPosition()}return n=>{window.addEventListener("resize",i),window.addEventListener("scroll",i),e||(e=new ResizeObserver(()=>{for(let e of t)e.updateAnchorPosition()})),e.observe(n.ownerDocument.documentElement),n.addEventListener("dialog:close",()=>{n.invokerElement?.setAttribute("aria-expanded","false"),t.delete(n)}),n.addEventListener("dialog:open",()=>{t.add(n)})}})(),nf=class SelectPanelElement extends HTMLElement{constructor(){super(...arguments),tI.add(this),tP.set(this,void 0),tO.set(this,void 0),tD.set(this,""),tq.set(this,""),tR.set(this,new Map),tH.set(this,null),tj.set(this,null),tW.set(this,!1)}get open(){return this.dialog.open}get selectVariant(){return this.getAttribute("data-select-variant")}get ariaSelectionType(){return"multiple"===this.selectVariant?"aria-checked":"aria-selected"}set selectVariant(t){t?this.setAttribute("data-select-variant",t):this.removeAttribute("variant")}get dynamicLabelPrefix(){let t=this.getAttribute("data-dynamic-label-prefix");return t?`${t}:`:""}get dynamicAriaLabelPrefix(){let t=this.getAttribute("data-dynamic-aria-label-prefix");return t?`${t}:`:""}set dynamicLabelPrefix(t){this.setAttribute("data-dynamic-label",t)}get dynamicLabel(){return this.hasAttribute("data-dynamic-label")}set dynamicLabel(t){this.toggleAttribute("data-dynamic-label",t)}get invokerElement(){let t=this.querySelector("dialog")?.id;if(!t)return null;for(let e of this.querySelectorAll("[aria-controls]"))if(e.getAttribute("aria-controls")===t)return e;return null}get closeButton(){return this.querySelector("button[data-close-dialog-id]")}get invokerLabel(){return this.invokerElement?this.invokerElement.querySelector(".Button-label"):null}get selectedItems(){return Array.from(na(this,tR,"f").values())}get align(){return this.getAttribute("anchor-align")||"start"}get side(){return this.getAttribute("anchor-side")||"outside-bottom"}updateAnchorPosition(){if(this&&null===this.offsetParent&&this.hide(),this.invokerElement){let{top:t,left:e}=(0,ef.uG)(this.dialog,this.invokerElement,{align:this.align,side:this.side,anchorOffset:4});this.dialog.style.top=`${t}px`,this.dialog.style.left=`${e}px`,this.dialog.style.bottom="auto",this.dialog.style.right="auto"}}connectedCallback(){let{signal:t}=nl(this,tO,new AbortController,"f");this.addEventListener("keydown",this,{signal:t}),this.addEventListener("click",this,{signal:t}),this.addEventListener("mousedown",this,{signal:t}),this.addEventListener("input",this,{signal:t}),this.addEventListener("remote-input-success",this,{signal:t}),this.addEventListener("remote-input-error",this,{signal:t}),this.addEventListener("loadstart",this,{signal:t}),na(this,tI,"m",et).call(this),na(this,tI,"m",ee).call(this),na(this,tI,"m",tF).call(this),nd(this),iH(this,()=>!!this.remoteInput,()=>{this.remoteInput.addEventListener("loadstart",this,{signal:t}),this.remoteInput.addEventListener("loadend",this,{signal:t})}),iH(this,()=>!!this.includeFragment,()=>{this.includeFragment.addEventListener("include-fragment-replaced",this,{signal:t}),this.includeFragment.addEventListener("error",this,{signal:t}),this.includeFragment.addEventListener("loadend",this,{signal:t})}),nl(this,tP,new IntersectionObserver(t=>{for(let e of t){let t=e.target;e.isIntersecting&&t===this.dialog&&(this.filterInputTextField&&document.activeElement!==this.filterInputTextField&&this.filterInputTextField.focus(),this.dialog.setAttribute("data-ready","true"),this.updateAnchorPosition(),na(this,tI,"a",t7)===es.LOCAL&&na(this,tI,"m",tZ).call(this))}}),"f"),iH(this,()=>!!this.dialog,()=>{na(this,tP,"f").observe(this.dialog),this.dialog.addEventListener("close",this,{signal:t}),"true"===this.getAttribute("data-open-on-load")&&this.show()}),na(this,tI,"a",t7)===es.LOCAL&&iH(this,()=>this.items.length>0,()=>{na(this,tI,"m",tZ).call(this),na(this,tI,"m",ee).call(this)})}disconnectedCallback(){na(this,tO,"f").abort()}handleEvent(t){if(t.target===this.filterInputTextField)return void na(this,tI,"m",tQ).call(this,t);if(t.target===this.remoteInput)return void na(this,tI,"m",tU).call(this,t);let e=this.invokerElement?.contains(t.target),i=this.closeButton?.contains(t.target),n=na(this,tI,"m",tB).call(this,t);if(e&&"mousedown"===t.type||"mousedown"===t.type&&t.target instanceof HTMLInputElement)return;if("mousedown"===t.type)return void t.preventDefault();if(e&&n)return void na(this,tI,"m",t5).call(this,t);if(i&&n)return;if(t.target===this.dialog&&"close"===t.type){if(this.dialog.removeAttribute("data-ready"),this.invokerElement?.setAttribute("aria-expanded","false"),this.filterInputTextField){let t=this.filterInputTextField.value.length>0;this.filterInputTextField.value="",t&&this.filterInputTextField.dispatchEvent(new Event("input"))}this.dispatchEvent(new CustomEvent("panelClosed",{detail:{panel:this},bubbles:!0}));return}let r=t.target.closest(nu)?.parentElement;if(null!=r&&n){if(na(this,tI,"m",tV).call(this,t))return;let e=r.closest("[data-show-dialog-id]");if(e){let i=this.ownerDocument.getElementById(e.getAttribute("data-show-dialog-id")||"");if(i&&this.contains(e)&&this.contains(i))return void na(this,tI,"m",t6).call(this,t,i)}na(this,tI,"m",t$).call(this,t)&&(t.preventDefault(),na(this,tI,"m",eo).call(this,r)?.click()),na(this,tI,"m",t8).call(this,r);return}if("click"===t.type){let e=this.dialog.getBoundingClientRect();e.top<=t.clientY&&t.clientY<=e.top+e.height&&e.left<=t.clientX&&t.clientX<=e.left+e.width||this.hide()}t.target instanceof el.T&&na(this,tI,"m",tX).call(this,t)}show(){this.updateAnchorPosition(),this.dialog.showModal(),this.invokerElement?.setAttribute("aria-expanded","true");let t=new CustomEvent("dialog:open",{detail:{dialog:this.dialog}});this.dispatchEvent(t)}hide(){this.dialog.close()}get visibleItems(){return Array.from(this.querySelectorAll(nu)).map(t=>t.parentElement)}get items(){return Array.from(this.querySelectorAll(nh)).map(t=>t.parentElement)}get focusableItem(){for(let t of this.items){let e=na(this,tI,"m",eo).call(this,t);if(e&&"0"===e.getAttribute("tabindex"))return e}}getItemById(t){return this.querySelector(`li[data-item-id="${t}"`)}isItemDisabled(t){return!!t&&t.classList.contains("ActionListItem--disabled")}disableItem(t){t&&(t.classList.add("ActionListItem--disabled"),na(this,tI,"m",eo).call(this,t).setAttribute("aria-disabled","true"))}enableItem(t){t&&(t.classList.remove("ActionListItem--disabled"),na(this,tI,"m",eo).call(this,t).removeAttribute("aria-disabled"))}isItemHidden(t){return!!t&&t.hasAttribute("hidden")}isItemChecked(t){return!!t&&"true"===na(this,tI,"m",eo).call(this,t).getAttribute(this.ariaSelectionType)}checkItem(t){t&&("single"===this.selectVariant||"multiple"===this.selectVariant)&&!this.isItemChecked(t)&&na(this,tI,"m",t8).call(this,t)}uncheckItem(t){t&&("single"===this.selectVariant||"multiple"===this.selectVariant)&&this.isItemChecked(t)&&na(this,tI,"m",t8).call(this,t)}};tP=new WeakMap,tO=new WeakMap,tD=new WeakMap,tq=new WeakMap,tR=new WeakMap,tH=new WeakMap,tj=new WeakMap,tW=new WeakMap,tI=new WeakSet,tF=function(){let{signal:t}=na(this,tO,"f");for(let e of this.querySelectorAll(nc.join(",")))e.addEventListener("click",na(this,tI,"m",tV).bind(this),{signal:t}),e.addEventListener("keydown",na(this,tI,"m",tV).bind(this),{signal:t})},t_=function(){let t=!1;if("single"===this.selectVariant)for(let e of this.items){let i=na(this,tI,"m",eo).call(this,e);i&&(!this.isItemHidden(e)&&this.isItemChecked(e)&&!t?(i.setAttribute("tabindex","0"),t=!0):i.setAttribute("tabindex","-1"),e.removeAttribute("tabindex"))}else for(let t of this.items){let e=na(this,tI,"m",eo).call(this,t);e&&(e.setAttribute("tabindex","-1"),t.removeAttribute("tabindex"))}!t&&na(this,tI,"a",ei)&&na(this,tI,"m",eo).call(this,na(this,tI,"a",ei))?.setAttribute("tabindex","0")},tV=function(t){if(!na(this,tI,"m",tB).call(this,t))return!1;let e=t.target.closest(nu);return!!e&&!!e.getAttribute("aria-disabled")&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation(),!0)},t$=function(t){return t.target instanceof HTMLAnchorElement&&t instanceof KeyboardEvent&&"keydown"===t.type&&!(t.ctrlKey||t.altKey||t.metaKey||t.shiftKey)&&" "===t.key},tB=function(t){return t instanceof MouseEvent&&"click"===t.type||na(this,tI,"m",t$).call(this,t)},tN=function(){for(let t of this.items){let e=na(this,tI,"m",eo).call(this,t);if(!e)continue;let i=e.getAttribute("data-value");i&&na(this,tR,"f").has(i)&&e.setAttribute(this.ariaSelectionType,"true")}na(this,tI,"m",ee).call(this)},tz=function(t){let e=na(this,tI,"m",eo).call(this,t);if(!e)return;let i=e.getAttribute("data-value");i&&na(this,tR,"f").set(i,{value:i,label:e.querySelector(".ActionListItem-label")?.textContent?.trim(),inputName:e.getAttribute("data-input-name")})},tK=function(t){let e=na(this,tI,"m",eo).call(this,t);if(!e)return;let i=e.getAttribute("data-value");i&&na(this,tR,"f").delete(i)},tY=function(){na(this,tI,"a",t9)&&(na(this,tH,"f")&&clearTimeout(na(this,tH,"f")),na(this,tj,"f")&&clearTimeout(na(this,tj,"f")),nl(this,tj,setTimeout(()=>{this.liveRegion.announce("Loading")},2e3),"f"),nl(this,tH,setTimeout(()=>{na(this,tI,"a",t9)?.showLeadingSpinner()},1e3),"f"))},tX=function(t){switch(t.type){case"include-fragment-replaced":na(this,tI,"m",tZ).call(this);break;case"loadstart":na(this,tI,"m",tG).call(this,!1);break;case"loadend":na(this,tI,"a",t9)?.hideLeadingSpinner(),this.dispatchEvent(new CustomEvent("loadend"));break;case"error":{na(this,tI,"m",tG).call(this,!0);let t=this.fragmentErrorElement;if(t&&!t.hasAttribute("hidden"))return void this.liveRegion.announceFromElement(t,{politeness:"assertive"})}}},tG=function(t){for(let e of this.includeFragment.querySelectorAll("[data-show-on-error]"))e instanceof HTMLElement&&(e.hidden=!t);for(let e of this.includeFragment.querySelectorAll("[data-hide-on-error]"))e instanceof HTMLElement&&(e.hidden=t)},tU=function(t){switch(t.type){case"remote-input-success":na(this,tI,"m",t3).call(this),na(this,tI,"m",tZ).call(this),na(this,tI,"m",tN).call(this);break;case"remote-input-error":this.bodySpinner?.setAttribute("hidden",""),this.includeFragment||0===this.visibleItems.length?na(this,tI,"m",t1).call(this,ea.BODY):na(this,tI,"m",t1).call(this,ea.BANNER);break;case"loadstart":if(!na(this,tI,"m",t4).call(this)){if(na(this,tI,"m",t3).call(this),this.bodySpinner?.removeAttribute("hidden"),this.bodySpinner)break;na(this,tI,"m",tY).call(this)}break;case"loadend":na(this,tI,"a",t9)?.hideLeadingSpinner(),na(this,tj,"f")&&clearTimeout(na(this,tj,"f")),na(this,tH,"f")&&clearTimeout(na(this,tH,"f")),this.dispatchEvent(new CustomEvent("loadend"))}},tJ=function(t,e){return(t.getAttribute("data-filter-string")||t.textContent||"").toLowerCase().indexOf(e.toLowerCase())>-1},tQ=function(t){if("keydown"===t.type){let e=t.key;if("Enter"===e){let t=this.visibleItems[0];if(t){let e=na(this,tI,"m",eo).call(this,t);e&&e.click()}}else if("ArrowDown"===e){let e=this.focusableItem||na(this,tI,"m",eo).call(this,this.visibleItems[0]);e&&(e.focus(),t.preventDefault())}else if("Home"===e){let e=this.visibleItems[0];if(e){let i=na(this,tI,"m",eo).call(this,e);i&&i.focus(),t.preventDefault()}}else if("End"===e&&this.visibleItems.length>0){let e=this.visibleItems[this.visibleItems.length-1],i=na(this,tI,"m",eo).call(this,e);i&&i.focus(),t.preventDefault()}}if("input"===t.type&&(this.bodySpinner||na(this,tI,"m",t4).call(this)||na(this,tI,"m",tY).call(this),na(this,tI,"a",t7)===es.LOCAL||na(this,tI,"a",t7)===es.EVENTUALLY_LOCAL)){if(this.includeFragment)return void this.includeFragment.refetch();na(this,tI,"m",tZ).call(this)}},tZ=function(){if(!this.list)return;let t=!1;if(na(this,tI,"m",t4).call(this)){let e=this.filterInputTextField?.value??"",i=this.filterFn||na(this,tI,"m",tJ);for(let n of this.items)i(n,e)?(na(this,tI,"m",er).call(this,n),t=!0):na(this,tI,"m",en).call(this,n)}else t=this.items.length>0;for(let t of(na(this,tI,"m",t_).call(this),na(this,tI,"m",t2).call(this),this.items)){let e=na(this,tI,"m",eo).call(this,t);if(!e)continue;let i=e.getAttribute("data-value");na(this,tW,"f")?i&&!na(this,tR,"f").has(i)&&e.setAttribute(this.ariaSelectionType,"false"):i&&!na(this,tR,"f").has(i)&&this.isItemChecked(t)&&na(this,tI,"m",tz).call(this,t)}if(nl(this,tW,!0,"f"),this.noResults){if(na(this,tI,"m",t0).call(this))return void this.noResults.setAttribute("hidden","");t?(this.noResults.setAttribute("hidden",""),this.list?.querySelector(".ActionListWrap")?.removeAttribute("hidden")):(this.list?.querySelector(".ActionListWrap")?.setAttribute("hidden",""),this.noResults.removeAttribute("hidden"))}},t0=function(){return!(!this.fragmentErrorElement||this.fragmentErrorElement.hasAttribute("hidden"))||!!this.bannerErrorElement&&!this.bannerErrorElement.hasAttribute("hidden")},t1=function(t){let e=this.fragmentErrorElement;if(t===ea.BODY&&this.fragmentErrorElement?(this.fragmentErrorElement.removeAttribute("hidden"),this.bannerErrorElement.setAttribute("hidden","")):(e=this.bannerErrorElement,this.bannerErrorElement?.removeAttribute("hidden"),this.fragmentErrorElement?.setAttribute("hidden","")),e&&!e.hasAttribute("hidden"))return void this.liveRegion.announceFromElement(e,{politeness:"assertive"})},t3=function(){this.fragmentErrorElement?.setAttribute("hidden",""),this.bannerErrorElement.setAttribute("hidden","")},t2=function(){if(this.open&&this.list){let t=this.visibleItems;if(t.length>0)this.liveRegion.announce(`${t.length} result${1===t.length?"":"s"} tab for results`);else{let t=this.noResults;t&&this.liveRegion.announceFromElement(t)}}},t7=function(){if(!this.list)return es.REMOTE;switch(this.list.getAttribute("data-fetch-strategy")){case"local":return es.LOCAL;case"eventually_local":return es.EVENTUALLY_LOCAL;default:return es.REMOTE}},t9=function(){return this.filterInputTextField?.closest("primer-text-field")},t4=function(){return na(this,tI,"a",t7)===es.LOCAL||na(this,tI,"a",t7)===es.EVENTUALLY_LOCAL},t5=function(t){t.preventDefault(),t.stopPropagation(),this.open?this.hide():this.show()},t6=function(t,e){this.querySelector(".ActionListWrap").style.display="none";let i=new AbortController,{signal:n}=i,r=()=>{i.abort(),this.querySelector(".ActionListWrap").style.display="",this.open&&this.hide();let t=this.ownerDocument.activeElement,e=this.ownerDocument.activeElement===this.ownerDocument.body,n=this.contains(t);(e||n)&&setTimeout(()=>this.invokerElement?.focus(),0)};e.addEventListener("close",r,{signal:n}),e.addEventListener("cancel",r,{signal:n})},t8=function(t){if("multiple"!==this.selectVariant&&setTimeout(()=>{this.open&&this.hide()}),"multiple"!==this.selectVariant&&"single"!==this.selectVariant)return;let e=this.isItemChecked(t),i=!e;if(!this.dispatchEvent(new CustomEvent("beforeItemActivated",{bubbles:!0,cancelable:!0,detail:{item:t,checked:i,value:na(this,tI,"m",eo).call(this,t)?.getAttribute("data-value")}})))return;let n=na(this,tI,"m",eo).call(this,t);if("single"===this.selectVariant){if(n?.getAttribute("href"))return;if(!e){for(let t of this.items)na(this,tI,"m",eo).call(this,t)?.setAttribute(this.ariaSelectionType,"false");na(this,tR,"f").clear(),i&&(na(this,tI,"m",tz).call(this,t),n?.setAttribute(this.ariaSelectionType,"true")),na(this,tI,"m",et).call(this)}}else n?.setAttribute(this.ariaSelectionType,`${i}`),i?na(this,tI,"m",tz).call(this,t):na(this,tI,"m",tK).call(this,t);na(this,tI,"m",ee).call(this),na(this,tI,"m",t_).call(this),this.dispatchEvent(new CustomEvent("itemActivated",{bubbles:!0,detail:{item:t,checked:i,value:na(this,tI,"m",eo).call(this,t)?.getAttribute("data-value")}}))},et=function(){if(!this.dynamicLabel)return;let t=this.invokerLabel;if(!t)return;nl(this,tD,na(this,tD,"f")||t.textContent||"","f");let e=this.querySelector(`[${this.ariaSelectionType}=true] .ActionListItem-label`)?.textContent||na(this,tD,"f");if(e){let i=document.createElement("span");i.classList.add("color-fg-muted");let n=document.createElement("span");i.textContent=`${this.dynamicLabelPrefix} `,n.textContent=e,t.replaceChildren(i,n),this.dynamicAriaLabelPrefix&&this.invokerElement?.setAttribute("aria-label",`${this.dynamicAriaLabelPrefix} ${e.trim()}`)}else t.textContent=na(this,tD,"f")},ee=function(){if("single"===this.selectVariant){let t=this.querySelector("[data-select-panel-inputs=true] input")??this.querySelector("[data-list-inputs=true] input");if(!t)return;let e=this.selectedItems[0];e?(t.value=(e.value||e.label||"").trim(),e.inputName&&(t.name=e.inputName),t.removeAttribute("disabled")):na(this,tW,"f")&&t.setAttribute("disabled","disabled")}else if("none"!==this.selectVariant){let t=!!this.querySelector("[data-select-panel-inputs=true]"),e=this.querySelector("[data-select-panel-inputs=true]")??this.querySelector("[data-list-inputs=true]");if(!e)return;let i=e.querySelectorAll("input");for(let n of(i.length>0&&nl(this,tq,na(this,tq,"f")||i[0].name,"f"),this.selectedItems)){let i=document.createElement("input");i.setAttribute(`${t?"data-select-panel-input":"data-list-input"}`,"true"),i.type="hidden",i.autocomplete="off",i.name=n.inputName||na(this,tq,"f"),i.value=(n.value||n.label||"").trim(),e.append(i)}for(let t of i)t.remove()}},ei=function(){return this.querySelector(nu)?.parentElement||null},en=function(t){t&&t.setAttribute("hidden","hidden")},er=function(t){t&&t.removeAttribute("hidden")},eo=function(t){return t.querySelector(".ActionListContent")},ns([ec.aC],nf.prototype,"includeFragment",void 0),ns([ec.aC],nf.prototype,"dialog",void 0),ns([ec.aC],nf.prototype,"filterInputTextField",void 0),ns([ec.aC],nf.prototype,"remoteInput",void 0),ns([ec.aC],nf.prototype,"list",void 0),ns([ec.aC],nf.prototype,"noResults",void 0),ns([ec.aC],nf.prototype,"fragmentErrorElement",void 0),ns([ec.aC],nf.prototype,"bannerErrorElement",void 0),ns([ec.aC],nf.prototype,"bodySpinner",void 0),ns([ec.aC],nf.prototype,"liveRegion",void 0),nf=ns([ec.p_],nf),window.customElements.get("select-panel")||(window.SelectPanelElement=nf,window.customElements.define("select-panel",nf));var np=function(t,e,i,n){var r,o=arguments.length,s=o<3?e:null===n?n=Object.getOwnPropertyDescriptor(e,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,i,n);else for(var a=t.length-1;a>=0;a--)(r=t[a])&&(s=(o<3?r(s):o>3?r(e,i,s):r(e,i))||s);return o>3&&s&&Object.defineProperty(e,i,s),s};let nm=class DetailsToggleElement extends HTMLElement{toggle(){if(this.detailsTarget.hasAttribute("open")){let t=this.summaryTarget.getAttribute("data-aria-label-closed");t&&this.summaryTarget.setAttribute("aria-label",t),this.summaryTarget.setAttribute("aria-expanded","false")}else{let t=this.summaryTarget.getAttribute("data-aria-label-open");t&&this.summaryTarget.setAttribute("aria-label",t),this.summaryTarget.setAttribute("aria-expanded","true")}}};np([ec.aC],nm.prototype,"detailsTarget",void 0),np([ec.aC],nm.prototype,"summaryTarget",void 0),nm=np([ec.p_],nm),window.DetailsToggleElement=nm},97797:(t,e,i)=>{function n(){if(!(this instanceof n))return new n;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}i.d(e,{h:()=>S,A:()=>C,on:()=>T});var r,o=window.document.documentElement,s=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.oMatchesSelector||o.msMatchesSelector;n.prototype.matchesSelector=function(t,e){return s.call(t,e)},n.prototype.querySelectorAll=function(t,e){return e.querySelectorAll(t)},n.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"ID",selector:function(t){var e;if(e=t.match(a))return e[0].slice(1)},element:function(t){if(t.id)return[t.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"CLASS",selector:function(t){var e;if(e=t.match(l))return e[0].slice(1)},element:function(t){var e=t.className;if(e){if("string"==typeof e)return e.split(/\s/);else if("object"==typeof e&&"baseVal"in e)return e.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"TAG",selector:function(t){var e;if(e=t.match(c))return e[0].toUpperCase()},element:function(t){return[t.nodeName.toUpperCase()]}}),n.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},r="function"==typeof window.Map?window.Map:function(){function t(){this.map={}}return t.prototype.get=function(t){return this.map[t+" "]},t.prototype.set=function(t,e){this.map[t+" "]=e},t}();var h=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function u(t,e){var i,n,r,o,s,a,l=(t=t.slice(0).concat(t.default)).length,c=e,u=[];do if(h.exec(""),(r=h.exec(c))&&(c=r[3],r[2]||!c)){for(i=0;i<l;i++)if(s=(a=t[i]).selector(r[1])){for(n=u.length,o=!1;n--;)if(u[n].index===a&&u[n].key===s){o=!0;break}o||u.push({index:a,key:s});break}}while(r)return u}function d(t,e){return t.id-e.id}n.prototype.logDefaultIndexUsed=function(){},n.prototype.add=function(t,e){var i,n,o,s,a,l,c,h,d=this.activeIndexes,f=this.selectors,p=this.selectorObjects;if("string"==typeof t){for(n=0,p[(i={id:this.uid++,selector:t,data:e}).id]=i,c=u(this.indexes,t);n<c.length;n++)s=(h=c[n]).key,(a=function(t,e){var i,n,r;for(i=0,n=t.length;i<n;i++)if(r=t[i],e.isPrototypeOf(r))return r}(d,o=h.index))||((a=Object.create(o)).map=new r,d.push(a)),o===this.indexes.default&&this.logDefaultIndexUsed(i),(l=a.map.get(s))||(l=[],a.map.set(s,l)),l.push(i);this.size++,f.push(t)}},n.prototype.remove=function(t,e){if("string"==typeof t){var i,n,r,o,s,a,l,c,h=this.activeIndexes,d=this.selectors=[],f=this.selectorObjects,p={},m=1==arguments.length;for(r=0,i=u(this.indexes,t);r<i.length;r++)for(n=i[r],o=h.length;o--;)if(a=h[o],n.index.isPrototypeOf(a)){if(l=a.map.get(n.key))for(s=l.length;s--;)(c=l[s]).selector===t&&(m||c.data===e)&&(l.splice(s,1),p[c.id]=!0);break}for(r in p)delete f[r],this.size--;for(r in f)d.push(f[r].selector)}},n.prototype.queryAll=function(t){if(!this.selectors.length)return[];var e,i,n,r,o,s,a,l,c={},h=[],u=this.querySelectorAll(this.selectors.join(", "),t);for(e=0,n=u.length;e<n;e++)for(i=0,o=u[e],r=(s=this.matches(o)).length;i<r;i++)c[(l=s[i]).id]?a=c[l.id]:(a={id:l.id,selector:l.selector,data:l.data,elements:[]},c[l.id]=a,h.push(a)),a.elements.push(o);return h.sort(d)},n.prototype.matches=function(t){if(!t)return[];var e,i,n,r,o,s,a,l,c,h,u,f=this.activeIndexes,p={},m=[];for(e=0,r=f.length;e<r;e++)if(l=(a=f[e]).element(t)){for(i=0,o=l.length;i<o;i++)if(c=a.map.get(l[i]))for(n=0,s=c.length;n<s;n++)!p[u=(h=c[n]).id]&&this.matchesSelector(t,h.selector)&&(p[u]=!0,m.push(h))}return m.sort(d)};var f={},p={},m=new WeakMap,g=new WeakMap,v=new WeakMap,b=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function w(t,e,i){var n=t[e];return t[e]=function(){return i.apply(t,arguments),n.apply(t,arguments)},t}function y(){m.set(this,!0)}function E(){m.set(this,!0),g.set(this,!0)}function A(){return v.get(this)||null}function L(t,e){b&&Object.defineProperty(t,"currentTarget",{configurable:!0,enumerable:!0,get:e||b.get})}function k(t){if(function(t){try{return t.eventPhase,!0}catch(t){return!1}}(t)){var e=(1===t.eventPhase?p:f)[t.type];if(e){var i=function(t,e,i){var n=[],r=e;do{if(1!==r.nodeType)break;var o=t.matches(r);if(o.length){var s={node:r,observers:o};i?n.unshift(s):n.push(s)}}while(r=r.parentElement)return n}(e,t.target,1===t.eventPhase);if(i.length){w(t,"stopPropagation",y),w(t,"stopImmediatePropagation",E),L(t,A);for(var n=0,r=i.length;n<r&&!m.get(t);n++){var o=i[n];v.set(t,o.node);for(var s=0,a=o.observers.length;s<a&&!g.get(t);s++)o.observers[s].data.call(o.node,t)}v.delete(t),L(t)}}}}function T(t,e,i){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!!r.capture,s=o?p:f,a=s[t];a||(a=new n,s[t]=a,document.addEventListener(t,k,o)),a.add(e,i)}function C(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=!!n.capture,o=r?p:f,s=o[t];s&&(s.remove(e,i),s.size||(delete o[t],document.removeEventListener(t,k,r)))}function S(t,e,i){return t.dispatchEvent(new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:i}))}},91385:(t,e,i)=>{i.d(e,{Xq:()=>a,ai:()=>r,fN:()=>s,qA:()=>l});var n=-1/0,r=1/0;function o(t,e,i,r){for(var o=t.length,s=e.length,a=t.toLowerCase(),l=e.toLowerCase(),c=function(t){for(var e=t.length,i=Array(e),n="/",r=0;r<e;r++){var o,s=t[r];"/"===n?i[r]=.9:"-"===n||"_"===n||" "===n?i[r]=.8:"."===n?i[r]=.6:(o=n).toLowerCase()===o&&s.toUpperCase()===s?i[r]=.7:i[r]=0,n=s}return i}(e,c),h=0;h<o;h++){i[h]=Array(s),r[h]=Array(s);for(var u=n,d=h===o-1?-.005:-.01,f=0;f<s;f++)if(a[h]===l[f]){var p=n;h?f&&(p=Math.max(r[h-1][f-1]+c[f],i[h-1][f-1]+1)):p=-.005*f+c[f],i[h][f]=p,r[h][f]=u=Math.max(p,u+d)}else i[h][f]=n,r[h][f]=u+=d}}function s(t,e){var i=t.length,s=e.length;if(!i||!s)return n;if(i===s)return r;if(s>1024)return n;var a=Array(i),l=Array(i);return o(t,e,a,l),l[i-1][s-1]}function a(t,e){var i=t.length,r=e.length,s=Array(i);if(!i||!r)return s;if(i===r){for(var a=0;a<i;a++)s[a]=a;return s}if(r>1024)return s;var l=Array(i),c=Array(i);o(t,e,l,c);for(var h=!1,a=i-1,u=r-1;a>=0;a--)for(;u>=0;u--)if(l[a][u]!==n&&(h||l[a][u]===c[a][u])){h=a&&u&&c[a][u]===l[a-1][u-1]+1,s[a]=u--;break}return s}function l(t,e){t=t.toLowerCase(),e=e.toLowerCase();for(var i=t.length,n=0,r=0;n<i;n+=1)if(0===(r=e.indexOf(t[n],r)+1))return!1;return!0}},20761:(t,e,i)=>{var n,r,o=i(70170),s=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},a=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};!function(t){t.GET="GET",t.POST="POST"}(r||(r={}));let l=new WeakMap;let AutoCheckEvent=class AutoCheckEvent extends Event{constructor(t){super(`auto-check-${t}`,{bubbles:!0}),this.phase=t}get detail(){return this}};let AutoCheckValidationEvent=class AutoCheckValidationEvent extends AutoCheckEvent{constructor(t,e=""){super(t),this.phase=t,this.message=e,this.setValidity=t=>{this.message=t}}};let AutoCheckCompleteEvent=class AutoCheckCompleteEvent extends AutoCheckEvent{constructor(){super("complete")}};let AutoCheckSuccessEvent=class AutoCheckSuccessEvent extends AutoCheckEvent{constructor(t){super("success"),this.response=t}};let AutoCheckStartEvent=class AutoCheckStartEvent extends AutoCheckValidationEvent{constructor(){super("start","Verifying\u2026")}};let AutoCheckErrorEvent=class AutoCheckErrorEvent extends AutoCheckValidationEvent{constructor(t){super("error","Validation failed"),this.response=t}};let AutoCheckSendEvent=class AutoCheckSendEvent extends AutoCheckEvent{constructor(t){super("send"),this.body=t}};let auto_check_element_AutoCheckElement=class auto_check_element_AutoCheckElement extends HTMLElement{constructor(){super(...arguments),n.set(this,null)}static define(t="auto-check",e=customElements){return e.define(t,this),this}get onloadend(){return s(this,n,"f")}set onloadend(t){s(this,n,"f")&&this.removeEventListener("loadend",s(this,n,"f")),a(this,n,"object"==typeof t||"function"==typeof t?t:null,"f"),"function"==typeof t&&this.addEventListener("loadend",t)}connectedCallback(){let t=this.input;if(!t)return;let e=(0,o.s)(d.bind(null,this),300);l.set(this,{check:e,controller:null});let i=c.bind(null,e);t.addEventListener("blur",i),t.addEventListener("input",i),t.addEventListener("triggervalidation",i),t.autocomplete="off",t.spellcheck=!1}disconnectedCallback(){let t=this.input;if(!t)return;let e=l.get(this);e&&(l.delete(this),t.removeEventListener("input",h),t.removeEventListener("input",e.check),t.setCustomValidity(""))}attributeChangedCallback(t){if("required"===t){let t=this.input;t&&(t.required=this.required)}}triggerValidation(){let t=this.input;t&&t.dispatchEvent(new CustomEvent("triggervalidation"))}static get observedAttributes(){return["required"]}get input(){return this.querySelector("input")}get src(){let t=this.getAttribute("src");if(!t)return"";let e=this.ownerDocument.createElement("a");return e.href=t,e.href}set src(t){this.setAttribute("src",t)}get csrf(){let t=this.querySelector("[data-csrf]");return this.getAttribute("csrf")||t instanceof HTMLInputElement&&t.value||""}set csrf(t){this.setAttribute("csrf",t)}get required(){return this.hasAttribute("required")}set required(t){t?this.setAttribute("required",""):this.removeAttribute("required")}get csrfField(){return this.getAttribute("csrf-field")||"authenticity_token"}set csrfField(t){this.setAttribute("csrf-field",t)}get httpMethod(){return r[this.getAttribute("http-method")]||"POST"}set validateOnKeystroke(t){t?this.setAttribute("validate-on-keystroke",""):this.removeAttribute("validate-on-keystroke")}get validateOnKeystroke(){let t=this.getAttribute("validate-on-keystroke");return"true"===t||""===t}};function c(t,e){let i=e.currentTarget;if(!(i instanceof HTMLInputElement))return;let n=i.closest("auto-check");n instanceof auto_check_element_AutoCheckElement&&("input"===e.type&&n.setAttribute("dirty",""),0!==i.value.length&&("blur"===e.type&&!n.validateOnKeystroke&&n.hasAttribute("dirty")||"input"===e.type&&n.validateOnKeystroke||"triggervalidation"===e.type)&&(h(e),t()))}function h(t){let e=t.currentTarget;if(!(e instanceof HTMLInputElement))return;let i=e.closest("auto-check");if(!(i instanceof auto_check_element_AutoCheckElement))return;let n=i.src,r=i.csrf,o=i.httpMethod,s=l.get(i);if(!n||"POST"===o&&!r||!s)return;let a=new AutoCheckStartEvent;e.dispatchEvent(a),i.required&&e.setCustomValidity(a.message)}async function u(t,e,i){"GET"===i.method&&delete i.body;try{let n=await fetch(e,i);return t.dispatchEvent(new Event("load")),t.dispatchEvent(new Event("loadend")),n}catch(e){throw"AbortError"!==e.name&&(t.dispatchEvent(new Event("error")),t.dispatchEvent(new Event("loadend"))),e}}async function d(t){let e=t.input;if(!e)return;let i=t.csrfField,n=t.src,r=t.csrf,o=l.get(t),s=t.httpMethod;if(!n||"POST"===s&&!r||!o||!e.value.trim()){t.required&&e.setCustomValidity("");return}let a=new FormData,c=new URL(n,window.location.origin);"POST"===s?(a.append(i,r),a.append("value",e.value)):c.search=new URLSearchParams({value:e.value}).toString(),e.dispatchEvent(new AutoCheckSendEvent(a)),o.controller?o.controller.abort():t.dispatchEvent(new Event("loadstart")),o.controller="AbortController"in window?new AbortController:{signal:null,abort(){}},t.removeAttribute("dirty");try{let i=await u(t,c.toString(),{credentials:"same-origin",signal:o.controller.signal,method:s,body:a});if(i.ok)t.required&&e.setCustomValidity(""),t.validateOnKeystroke=!1,e.dispatchEvent(new AutoCheckSuccessEvent(i.clone()));else{t.validateOnKeystroke=!0;let n=new AutoCheckErrorEvent(i.clone());e.dispatchEvent(n),t.required&&e.setCustomValidity(n.message)}o.controller=null,e.dispatchEvent(new AutoCheckCompleteEvent)}catch(t){"AbortError"!==t.name&&(o.controller=null,e.dispatchEvent(new AutoCheckCompleteEvent))}}n=new WeakMap;let f="undefined"!=typeof globalThis?globalThis:window;try{f.AutoCheckElement=auto_check_element_AutoCheckElement.define()}catch(t){if(!(f.DOMException&&t instanceof DOMException&&"NotSupportedError"===t.name)&&!(t instanceof ReferenceError))throw t}},14940:(t,e,i)=>{function n(t){if("clipboard"in navigator)return navigator.clipboard.writeText(t.textContent||"");let e=getSelection();if(null==e)return Promise.reject(Error());e.removeAllRanges();let i=document.createRange();return i.selectNodeContents(t),e.addRange(i),document.execCommand("copy"),e.removeAllRanges(),Promise.resolve()}function r(t){if("clipboard"in navigator)return navigator.clipboard.writeText(t);let e=document.body;if(!e)return Promise.reject(Error());let i=function(t){let e=document.createElement("pre");return e.style.width="1px",e.style.height="1px",e.style.position="fixed",e.style.top="5px",e.textContent=t,e}(t);return e.appendChild(i),n(i),e.removeChild(i),Promise.resolve()}async function o(t){let e=t.getAttribute("for"),i=t.getAttribute("value");function o(){t.dispatchEvent(new CustomEvent("clipboard-copy",{bubbles:!0}))}if("true"!==t.getAttribute("aria-disabled")){if(i)await r(i),o();else if(e){var s;let i="getRootNode"in Element.prototype?t.getRootNode():t.ownerDocument;if(!(i instanceof Document||"ShadowRoot"in window&&i instanceof ShadowRoot))return;let a=i.getElementById(e);a&&(await ((s=a)instanceof HTMLInputElement||s instanceof HTMLTextAreaElement?r(s.value):s instanceof HTMLAnchorElement&&s.hasAttribute("href")?r(s.href):n(s)),o())}}}function s(t){let e=t.currentTarget;e instanceof HTMLElement&&o(e)}function a(t){if(" "===t.key||"Enter"===t.key){let e=t.currentTarget;e instanceof HTMLElement&&(t.preventDefault(),o(e))}}function l(t){t.currentTarget.addEventListener("keydown",a)}function c(t){t.currentTarget.removeEventListener("keydown",a)}i.d(e,{S:()=>clipboard_copy_element_ClipboardCopyElement});let clipboard_copy_element_ClipboardCopyElement=class clipboard_copy_element_ClipboardCopyElement extends HTMLElement{static define(t="clipboard-copy",e=customElements){return e.define(t,this),this}constructor(){super(),this.addEventListener("click",s),this.addEventListener("focus",l),this.addEventListener("blur",c)}connectedCallback(){this.hasAttribute("tabindex")||this.setAttribute("tabindex","0"),this.hasAttribute("role")||this.setAttribute("role","button")}get value(){return this.getAttribute("value")||""}set value(t){this.setAttribute("value",t)}};let h="undefined"!=typeof globalThis?globalThis:window;try{h.ClipboardCopyElement=clipboard_copy_element_ClipboardCopyElement.define()}catch(t){if(!(h.DOMException&&t instanceof DOMException&&"NotSupportedError"===t.name)&&!(t instanceof ReferenceError))throw t}},35908:(t,e,i)=>{i.d(e,{A:()=>Combobox});let Combobox=class Combobox{constructor(t,e,{tabInsertsSuggestions:i,firstOptionSelectionMode:n,scrollIntoViewOptions:o}={}){this.input=t,this.list=e,this.tabInsertsSuggestions=null==i||i,this.firstOptionSelectionMode=null!=n?n:"none",this.scrollIntoViewOptions=null!=o?o:{block:"nearest",inline:"nearest"},this.isComposing=!1,e.id||(e.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=t=>(function(t,e){if(!t.shiftKey&&!t.metaKey&&!t.altKey&&(e.ctrlBindings||!t.ctrlKey)&&!e.isComposing)switch(t.key){case"Enter":r(e.input,e.list)&&t.preventDefault();break;case"Tab":e.tabInsertsSuggestions&&r(e.input,e.list)&&t.preventDefault();break;case"Escape":e.clearSelection();break;case"ArrowDown":e.navigate(1),t.preventDefault();break;case"ArrowUp":e.navigate(-1),t.preventDefault();break;case"n":e.ctrlBindings&&t.ctrlKey&&(e.navigate(1),t.preventDefault());break;case"p":e.ctrlBindings&&t.ctrlKey&&(e.navigate(-1),t.preventDefault());break;default:if(t.ctrlKey)break;e.resetSelection()}})(t,this),this.compositionEventHandler=t=>(function(t,e){e.isComposing="compositionstart"===t.type,document.getElementById(e.input.getAttribute("aria-controls")||"")&&e.clearSelection()})(t,this),this.inputHandler=this.clearSelection.bind(this),t.setAttribute("role","combobox"),t.setAttribute("aria-controls",e.id),t.setAttribute("aria-expanded","false"),t.setAttribute("aria-autocomplete","list"),t.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",n),this.resetSelection()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",n)}indicateDefaultOption(){var t;"active"===this.firstOptionSelectionMode?null==(t=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(o)[0])||t.setAttribute("data-combobox-option-default","true"):"selected"===this.firstOptionSelectionMode&&this.navigate(1)}navigate(t=1){let e=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(o)[0],i=Array.from(this.list.querySelectorAll('[role="option"]')).filter(o),n=i.indexOf(e);if(n===i.length-1&&1===t||0===n&&-1===t){this.clearSelection(),this.input.focus();return}let r=1===t?0:i.length-1;if(e&&n>=0){let e=n+t;e>=0&&e<i.length&&(r=e)}let s=i[r];if(s)for(let t of i)t.removeAttribute("data-combobox-option-default"),s===t?(this.input.setAttribute("aria-activedescendant",s.id),s.setAttribute("aria-selected","true"),s.dispatchEvent(new Event("combobox-select",{bubbles:!0})),s.scrollIntoView(this.scrollIntoViewOptions)):t.removeAttribute("aria-selected")}clearSelection(){for(let t of(this.input.removeAttribute("aria-activedescendant"),this.list.querySelectorAll('[aria-selected="true"], [data-combobox-option-default="true"]')))t.removeAttribute("aria-selected"),t.removeAttribute("data-combobox-option-default")}resetSelection(){this.clearSelection(),this.indicateDefaultOption()}};function n(t){if(!(t.target instanceof Element))return;let e=t.target.closest('[role="option"]');if(e){var i,n;"true"!==e.getAttribute("aria-disabled")&&(i=e,n={event:t},i.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:n})))}}function r(t,e){let i=e.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!i&&("true"===i.getAttribute("aria-disabled")||(i.click(),!0))}function o(t){return!t.hidden&&!(t instanceof HTMLInputElement&&"hidden"===t.type)&&(t.offsetWidth>0||t.offsetHeight>0)}},60612:()=>{let DetailsMenuElement=class DetailsMenuElement extends HTMLElement{get preload(){return this.hasAttribute("preload")}set preload(t){t?this.setAttribute("preload",""):this.removeAttribute("preload")}get src(){return this.getAttribute("src")||""}set src(t){this.setAttribute("src",t)}connectedCallback(){var l;let d;this.hasAttribute("role")||this.setAttribute("role","menu");let f=this.parentElement;if(!f)return;let p=f.querySelector("summary");p&&(p.setAttribute("aria-haspopup","menu"),p.hasAttribute("role")||p.setAttribute("role","button"));let m=[i(f,"compositionstart",t=>u(this,t)),i(f,"compositionend",t=>u(this,t)),i(f,"click",t=>a(f,t)),i(f,"change",t=>a(f,t)),i(f,"keydown",e=>(function(e,i,n){if(!(n instanceof KeyboardEvent)||e.querySelector("details[open]"))return;let r=t.get(i);if(!r||r.isComposing)return;let a=n.target instanceof Element&&"SUMMARY"===n.target.tagName;switch(n.key){case"Escape":e.hasAttribute("open")&&(h(e),n.preventDefault(),n.stopPropagation());break;case"ArrowDown":{a&&!e.hasAttribute("open")&&e.setAttribute("open","");let t=o(e,!0);t&&t.focus(),n.preventDefault()}break;case"ArrowUp":{a&&!e.hasAttribute("open")&&e.setAttribute("open","");let t=o(e,!1);t&&t.focus(),n.preventDefault()}break;case"n":if(s&&n.ctrlKey){let t=o(e,!0);t&&t.focus(),n.preventDefault()}break;case"p":if(s&&n.ctrlKey){let t=o(e,!1);t&&t.focus(),n.preventDefault()}break;case" ":case"Enter":{let t=document.activeElement;t instanceof HTMLElement&&c(t)&&t.closest("details")===e&&(n.preventDefault(),n.stopPropagation(),t.click())}}})(f,this,e)),i(f,"toggle",()=>n(f,this),{once:!0}),i(f,"toggle",()=>(function(t){if(t.hasAttribute("open"))for(let e of document.querySelectorAll("details[open] > details-menu")){let i=e.closest("details");i&&i!==t&&!i.contains(t)&&i.removeAttribute("open")}})(f)),this.preload?i(f,"mouseover",()=>n(f,this),{once:!0}):e,...(d=!1,[i(l=f,"mousedown",()=>d=!0),i(l,"keydown",()=>d=!1),i(l,"toggle",()=>{l.hasAttribute("open")&&!r(l)&&(d||function(t){let e=document.activeElement;if(e&&c(e)&&t.contains(e))return;let i=o(t,!0);i&&i.focus()}(l))})])];t.set(this,{subscriptions:m,loaded:!1,isComposing:!1})}disconnectedCallback(){let e=t.get(this);if(e)for(let i of(t.delete(this),e.subscriptions))i.unsubscribe()}};let t=new WeakMap,e={unsubscribe(){}};function i(t,e,n,r=!1){return t.addEventListener(e,n,r),{unsubscribe:()=>{t.removeEventListener(e,n,r)}}}function n(e,i){let n=i.getAttribute("src");if(!n)return;let o=t.get(i);if(!o||o.loaded)return;o.loaded=!0;let s=i.querySelector("include-fragment");s&&!s.hasAttribute("src")&&(s.addEventListener("loadend",()=>r(e)),s.setAttribute("src",n))}function r(t){if(!t.hasAttribute("open"))return!1;let e=t.querySelector("details-menu [autofocus]");return!!e&&(e.focus(),!0)}function o(t,e){let i=Array.from(t.querySelectorAll('[role^="menuitem"]:not([hidden]):not([disabled])')),n=document.activeElement,r=n instanceof HTMLElement?i.indexOf(n):-1,o=e?i[r+1]:i[r-1],s=e?i[0]:i[i.length-1];return o||s}let s=navigator.userAgent.match(/Macintosh/);function a(t,e){let i=e.target;if(i instanceof Element&&i.closest("details")===t){if("click"===e.type){let e=i.closest('[role="menuitem"], [role="menuitemradio"]');if(!e)return;let n=e.querySelector("input");if("LABEL"===e.tagName&&i===n)return;"LABEL"===e.tagName&&n&&!n.checked||l(e,t)}else if("change"===e.type){let e=i.closest('[role="menuitemradio"], [role="menuitemcheckbox"]');e&&l(e,t)}}}function l(t,e){if(t.hasAttribute("disabled")||"true"===t.getAttribute("aria-disabled"))return;let i=t.closest("details-menu");if(i&&i.dispatchEvent(new CustomEvent("details-menu-select",{cancelable:!0,detail:{relatedTarget:t}}))){!function(t,e){let i=e.querySelector("[data-menu-button]");if(!i)return;let n=function(t){if(!t)return null;let e=t.hasAttribute("data-menu-button-text")?t:t.querySelector("[data-menu-button-text]");return e?e.getAttribute("data-menu-button-text")||e.textContent:null}(t);if(n)i.textContent=n;else{let e=function(t){if(!t)return null;let e=t.hasAttribute("data-menu-button-contents")?t:t.querySelector("[data-menu-button-contents]");return e?e.innerHTML:null}(t);e&&(i.innerHTML=e)}}(t,e);for(let i of e.querySelectorAll('[role="menuitemradio"], [role="menuitemcheckbox"]')){let e=i.querySelector('input[type="radio"], input[type="checkbox"]'),n=(i===t).toString();e instanceof HTMLInputElement&&(n=e.indeterminate?"mixed":e.checked.toString()),i.setAttribute("aria-checked",n)}"menuitemcheckbox"!==t.getAttribute("role")&&h(e),i.dispatchEvent(new CustomEvent("details-menu-selected",{detail:{relatedTarget:t}}))}}function c(t){let e=t.getAttribute("role");return"menuitem"===e||"menuitemcheckbox"===e||"menuitemradio"===e}function h(t){if(!t.hasAttribute("open"))return;t.removeAttribute("open");let e=t.querySelector("summary");e&&e.focus()}function u(e,i){let n=t.get(e);n&&(n.isComposing="compositionstart"===i.type)}window.customElements.get("details-menu")||(window.DetailsMenuElement=DetailsMenuElement,window.customElements.define("details-menu",DetailsMenuElement))},90204:(t,e,i)=>{i.d(e,{R3:()=>o});let n=new Set(["\u{1F44B}","\u{1F91A}","\u{1F590}\uFE0F","\u270B","\u{1F596}","\u{1F44C}","\u{1F90F}","\u270C\uFE0F","\u{1F91E}","\u{1F91F}","\u{1F918}","\u{1F919}","\u{1F448}","\u{1F449}","\u{1F446}","\u{1F595}","\u{1F447}","\u261D\uFE0F","\u{1F44D}","\u{1F44E}","\u270A","\u{1F44A}","\u{1F91B}","\u{1F91C}","\u{1F44F}","\u{1F64C}","\u{1F450}","\u{1F932}","\u{1F64F}","\u270D\uFE0F","\u{1F485}","\u{1F933}","\u{1F4AA}","\u{1F9B5}","\u{1F9B6}","\u{1F442}","\u{1F9BB}","\u{1F443}","\u{1F476}","\u{1F9D2}","\u{1F466}","\u{1F467}","\u{1F9D1}","\u{1F471}","\u{1F468}","\u{1F9D4}","\u{1F471}\u200D\u2642\uFE0F","\u{1F468}\u200D\u{1F9B0}","\u{1F468}\u200D\u{1F9B1}","\u{1F468}\u200D\u{1F9B3}","\u{1F468}\u200D\u{1F9B2}","\u{1F469}","\u{1F471}\u200D\u2640\uFE0F","\u{1F469}\u200D\u{1F9B0}","\u{1F469}\u200D\u{1F9B1}","\u{1F469}\u200D\u{1F9B3}","\u{1F469}\u200D\u{1F9B2}","\u{1F9D3}","\u{1F474}","\u{1F475}","\u{1F64D}","\u{1F64D}\u200D\u2642\uFE0F","\u{1F64D}\u200D\u2640\uFE0F","\u{1F64E}","\u{1F64E}\u200D\u2642\uFE0F","\u{1F64E}\u200D\u2640\uFE0F","\u{1F645}","\u{1F645}\u200D\u2642\uFE0F","\u{1F645}\u200D\u2640\uFE0F","\u{1F646}","\u{1F646}\u200D\u2642\uFE0F","\u{1F646}\u200D\u2640\uFE0F","\u{1F481}","\u{1F481}\u200D\u2642\uFE0F","\u{1F481}\u200D\u2640\uFE0F","\u{1F64B}","\u{1F64B}\u200D\u2642\uFE0F","\u{1F64B}\u200D\u2640\uFE0F","\u{1F9CF}","\u{1F9CF}\u200D\u2642\uFE0F","\u{1F9CF}\u200D\u2640\uFE0F","\u{1F647}","\u{1F647}\u200D\u2642\uFE0F","\u{1F647}\u200D\u2640\uFE0F","\u{1F926}","\u{1F926}\u200D\u2642\uFE0F","\u{1F926}\u200D\u2640\uFE0F","\u{1F937}","\u{1F937}\u200D\u2642\uFE0F","\u{1F937}\u200D\u2640\uFE0F","\u{1F468}\u200D\u2695\uFE0F","\u{1F469}\u200D\u2695\uFE0F","\u{1F468}\u200D\u{1F393}","\u{1F469}\u200D\u{1F393}","\u{1F468}\u200D\u{1F3EB}","\u{1F469}\u200D\u{1F3EB}","\u{1F468}\u200D\u2696\uFE0F","\u{1F469}\u200D\u2696\uFE0F","\u{1F468}\u200D\u{1F33E}","\u{1F469}\u200D\u{1F33E}","\u{1F468}\u200D\u{1F373}","\u{1F469}\u200D\u{1F373}","\u{1F468}\u200D\u{1F527}","\u{1F469}\u200D\u{1F527}","\u{1F468}\u200D\u{1F3ED}","\u{1F469}\u200D\u{1F3ED}","\u{1F468}\u200D\u{1F4BC}","\u{1F469}\u200D\u{1F4BC}","\u{1F468}\u200D\u{1F52C}","\u{1F469}\u200D\u{1F52C}","\u{1F468}\u200D\u{1F4BB}","\u{1F469}\u200D\u{1F4BB}","\u{1F468}\u200D\u{1F3A4}","\u{1F469}\u200D\u{1F3A4}","\u{1F468}\u200D\u{1F3A8}","\u{1F469}\u200D\u{1F3A8}","\u{1F468}\u200D\u2708\uFE0F","\u{1F469}\u200D\u2708\uFE0F","\u{1F468}\u200D\u{1F680}","\u{1F469}\u200D\u{1F680}","\u{1F468}\u200D\u{1F692}","\u{1F469}\u200D\u{1F692}","\u{1F46E}","\u{1F46E}\u200D\u2642\uFE0F","\u{1F46E}\u200D\u2640\uFE0F","\u{1F575}\uFE0F","\u{1F575}\uFE0F\u200D\u2642\uFE0F","\u{1F575}\uFE0F\u200D\u2640\uFE0F","\u{1F482}","\u{1F482}\u200D\u2642\uFE0F","\u{1F482}\u200D\u2640\uFE0F","\u{1F477}","\u{1F477}\u200D\u2642\uFE0F","\u{1F477}\u200D\u2640\uFE0F","\u{1F934}","\u{1F478}","\u{1F473}","\u{1F473}\u200D\u2642\uFE0F","\u{1F473}\u200D\u2640\uFE0F","\u{1F472}","\u{1F9D5}","\u{1F935}","\u{1F470}","\u{1F930}","\u{1F931}","\u{1F47C}","\u{1F385}","\u{1F936}","\u{1F9B8}","\u{1F9B8}\u200D\u2642\uFE0F","\u{1F9B8}\u200D\u2640\uFE0F","\u{1F9B9}","\u{1F9B9}\u200D\u2642\uFE0F","\u{1F9B9}\u200D\u2640\uFE0F","\u{1F9D9}","\u{1F9D9}\u200D\u2642\uFE0F","\u{1F9D9}\u200D\u2640\uFE0F","\u{1F9DA}","\u{1F9DA}\u200D\u2642\uFE0F","\u{1F9DA}\u200D\u2640\uFE0F","\u{1F9DB}","\u{1F9DB}\u200D\u2642\uFE0F","\u{1F9DB}\u200D\u2640\uFE0F","\u{1F9DC}","\u{1F9DC}\u200D\u2642\uFE0F","\u{1F9DC}\u200D\u2640\uFE0F","\u{1F9DD}","\u{1F9DD}\u200D\u2642\uFE0F","\u{1F9DD}\u200D\u2640\uFE0F","\u{1F486}","\u{1F486}\u200D\u2642\uFE0F","\u{1F486}\u200D\u2640\uFE0F","\u{1F487}","\u{1F487}\u200D\u2642\uFE0F","\u{1F487}\u200D\u2640\uFE0F","\u{1F6B6}","\u{1F6B6}\u200D\u2642\uFE0F","\u{1F6B6}\u200D\u2640\uFE0F","\u{1F9CD}","\u{1F9CD}\u200D\u2642\uFE0F","\u{1F9CD}\u200D\u2640\uFE0F","\u{1F9CE}","\u{1F9CE}\u200D\u2642\uFE0F","\u{1F9CE}\u200D\u2640\uFE0F","\u{1F468}\u200D\u{1F9AF}","\u{1F469}\u200D\u{1F9AF}","\u{1F468}\u200D\u{1F9BC}","\u{1F469}\u200D\u{1F9BC}","\u{1F468}\u200D\u{1F9BD}","\u{1F469}\u200D\u{1F9BD}","\u{1F3C3}","\u{1F3C3}\u200D\u2642\uFE0F","\u{1F3C3}\u200D\u2640\uFE0F","\u{1F483}","\u{1F57A}","\u{1F574}\uFE0F","\u{1F9D6}","\u{1F9D6}\u200D\u2642\uFE0F","\u{1F9D6}\u200D\u2640\uFE0F","\u{1F9D7}","\u{1F9D7}\u200D\u2642\uFE0F","\u{1F9D7}\u200D\u2640\uFE0F","\u{1F3C7}","\u{1F3C2}","\u{1F3CC}\uFE0F","\u{1F3CC}\uFE0F\u200D\u2642\uFE0F","\u{1F3CC}\uFE0F\u200D\u2640\uFE0F","\u{1F3C4}","\u{1F3C4}\u200D\u2642\uFE0F","\u{1F3C4}\u200D\u2640\uFE0F","\u{1F6A3}","\u{1F6A3}\u200D\u2642\uFE0F","\u{1F6A3}\u200D\u2640\uFE0F","\u{1F3CA}","\u{1F3CA}\u200D\u2642\uFE0F","\u{1F3CA}\u200D\u2640\uFE0F","\u26F9\uFE0F","\u26F9\uFE0F\u200D\u2642\uFE0F","\u26F9\uFE0F\u200D\u2640\uFE0F","\u{1F3CB}\uFE0F","\u{1F3CB}\uFE0F\u200D\u2642\uFE0F","\u{1F3CB}\uFE0F\u200D\u2640\uFE0F","\u{1F6B4}","\u{1F6B4}\u200D\u2642\uFE0F","\u{1F6B4}\u200D\u2640\uFE0F","\u{1F6B5}","\u{1F6B5}\u200D\u2642\uFE0F","\u{1F6B5}\u200D\u2640\uFE0F","\u{1F938}","\u{1F938}\u200D\u2642\uFE0F","\u{1F938}\u200D\u2640\uFE0F","\u{1F93D}","\u{1F93D}\u200D\u2642\uFE0F","\u{1F93D}\u200D\u2640\uFE0F","\u{1F93E}","\u{1F93E}\u200D\u2642\uFE0F","\u{1F93E}\u200D\u2640\uFE0F","\u{1F939}","\u{1F939}\u200D\u2642\uFE0F","\u{1F939}\u200D\u2640\uFE0F","\u{1F9D8}","\u{1F9D8}\u200D\u2642\uFE0F","\u{1F9D8}\u200D\u2640\uFE0F","\u{1F6C0}","\u{1F6CC}","\u{1F9D1}\u200D\u{1F91D}\u200D\u{1F9D1}","\u{1F46D}","\u{1F46B}","\u{1F46C}"]);function r(t){return n.has(t)}function o(t,e){let i=s(t);if(!r(i))return t;let n=c(e);return n?i.split("\u200D").map(t=>r(t)?a(t,n):t).join("\u200D"):t}function s(t){return[...t].filter(t=>!l(t.codePointAt(0))).join("")}function a(t,e){let i=[...t].map(t=>t.codePointAt(0));return i[1]&&(l(i[1])||65039===i[1])?i[1]=e:i.splice(1,0,e),String.fromCodePoint(...i)}function l(t){return t>=127995&&t<=127999}function c(t){switch(t){case 1:return 127995;case 2:return 127996;case 3:return 127997;case 4:return 127998;case 5:return 127999;default:return null}}let GEmojiElement=class GEmojiElement extends HTMLElement{get image(){return this.firstElementChild instanceof HTMLImageElement?this.firstElementChild:null}get tone(){return(this.getAttribute("tone")||"").split(" ").map(t=>{let e=parseInt(t,10);return e>=0&&e<=5?e:0}).join(" ")}set tone(t){this.setAttribute("tone",t)}connectedCallback(){if(null===this.image&&!GEmojiElement.emojiSupportFunction()){let t=this.getAttribute("fallback-src");if(t){this.textContent="";let e=function(t){let e=document.createElement("img");return e.className="emoji",e.alt=t.getAttribute("alias")||"",e.height=20,e.width=20,e}(this);e.src=t,this.appendChild(e)}}this.hasAttribute("tone")&&h(this)}static get observedAttributes(){return["tone"]}attributeChangedCallback(t){"tone"===t&&h(this)}};function h(t){if(t.image)return;let e=t.tone.split(" ").map(t=>parseInt(t,10));if(0===e.length)t.textContent=s(t.textContent||"");else if(1===e.length){let i=e[0];t.textContent=0===i?s(t.textContent||""):o(t.textContent||"",i)}else t.textContent=function(t,e){let i=s(t);if(!r(i))return t;let n=e.map(t=>c(t));return i.split("\u200D").map(t=>{if(!r(t))return t;let e=n.shift();return e?a(t,e):t}).join("\u200D")}(t.textContent||"",e)}GEmojiElement.emojiSupportFunction=function(){let t=/\bWindows NT 6.1\b/.test(navigator.userAgent),e=/\bWindows NT 6.2\b/.test(navigator.userAgent),i=/\bWindows NT 6.3\b/.test(navigator.userAgent),n=/\bFreeBSD\b/.test(navigator.userAgent),r=/\bLinux\b/.test(navigator.userAgent)&&!/\bAndroid\b/.test(navigator.userAgent);return!(t||e||i||r||n)},window.customElements.get("g-emoji")||(window.GEmojiElement=GEmojiElement,window.customElements.define("g-emoji",GEmojiElement))},92284:()=>{let t=new WeakMap,e=new WeakMap,i=new WeakMap;function n(t){let n=t.currentTarget;if(!(n instanceof ImageCropElement))return;let{box:r,image:o}=i.get(n)||{};if(!r||!o)return;let s=0,a=0;if(t instanceof KeyboardEvent)"ArrowUp"===t.key?a=-1:"ArrowDown"===t.key?a=1:"ArrowLeft"===t.key?s=-1:"ArrowRight"===t.key&&(s=1);else if(e.has(n)&&t instanceof MouseEvent){let i=e.get(n);s=t.pageX-i.dragStartX,a=t.pageY-i.dragStartY}else if(e.has(n)&&t instanceof TouchEvent){let{pageX:i,pageY:r}=t.changedTouches[0],{dragStartX:o,dragStartY:l}=e.get(n);s=i-o,a=r-l}if(0!==s||0!==a){let t=Math.min(Math.max(0,r.offsetLeft+s),o.width-r.offsetWidth),e=Math.min(Math.max(0,r.offsetTop+a),o.height-r.offsetHeight);r.style.left=`${t}px`,r.style.top=`${e}px`,h(n,{x:t,y:e,width:r.offsetWidth,height:r.offsetHeight})}if(t instanceof MouseEvent)e.set(n,{dragStartX:t.pageX,dragStartY:t.pageY});else if(t instanceof TouchEvent){let{pageX:i,pageY:r}=t.changedTouches[0];e.set(n,{dragStartX:i,dragStartY:r})}}function r(e){let n,r,s,c=e.target;if(!(c instanceof HTMLElement))return;let h=o(c);if(!(h instanceof ImageCropElement))return;let{box:u}=i.get(h)||{};if(!u)return;let d=h.getBoundingClientRect();if(e instanceof KeyboardEvent){if("Escape"===e.key)return l(h);if("-"===e.key&&(s=-10),"="===e.key&&(s=10),!s)return;n=u.offsetWidth+s,r=u.offsetHeight+s,t.set(h,{startX:u.offsetLeft,startY:u.offsetTop})}else if(e instanceof MouseEvent){let i=t.get(h);if(!i)return;n=e.pageX-i.startX-d.left-window.pageXOffset,r=e.pageY-i.startY-d.top-window.pageYOffset}else if(e instanceof TouchEvent){let i=t.get(h);if(!i)return;n=e.changedTouches[0].pageX-i.startX-d.left-window.pageXOffset,r=e.changedTouches[0].pageY-i.startY-d.top-window.pageYOffset}n&&r&&a(h,n,r,!(e instanceof KeyboardEvent))}function o(t){let e=t.getRootNode();return e instanceof ShadowRoot?e.host:t}function s(e){let s=e.currentTarget;if(!(s instanceof HTMLElement))return;let a=o(s);if(!(a instanceof ImageCropElement))return;let{box:l}=i.get(a)||{};if(!l)return;let c=e.target;if(c instanceof HTMLElement)if(c.hasAttribute("data-direction")){let i=c.getAttribute("data-direction")||"";a.addEventListener("mousemove",r),a.addEventListener("touchmove",r,{passive:!0}),["nw","se"].indexOf(i)>=0&&a.classList.add("nwse"),["ne","sw"].indexOf(i)>=0&&a.classList.add("nesw"),t.set(a,{startX:l.offsetLeft+(["se","ne"].indexOf(i)>=0?0:l.offsetWidth),startY:l.offsetTop+(["se","sw"].indexOf(i)>=0?0:l.offsetHeight)}),r(e)}else a.addEventListener("mousemove",n),a.addEventListener("touchmove",n,{passive:!0})}function a(e,n,r,o=!0){let s=Math.max(Math.abs(n),Math.abs(r),10),l=t.get(e);if(!l)return;let{box:c,image:u}=i.get(e)||{};if(!c||!u)return;s=Math.min(s,r>0?u.height-l.startY:l.startY,n>0?u.width-l.startX:l.startX);let d=o?Math.round(Math.max(0,n>0?l.startX:l.startX-s)):c.offsetLeft,f=o?Math.round(Math.max(0,r>0?l.startY:l.startY-s)):c.offsetTop;c.style.left=`${d}px`,c.style.top=`${f}px`,c.style.width=`${s}px`,c.style.height=`${s}px`,h(e,{x:d,y:f,width:s,height:s})}function l(e){let{image:n}=i.get(e)||{};if(!n)return;let r=Math.round(n.clientWidth>n.clientHeight?n.clientHeight:n.clientWidth);t.set(e,{startX:(n.clientWidth-r)/2,startY:(n.clientHeight-r)/2}),a(e,r,r)}function c(t){let i=t.currentTarget;i instanceof ImageCropElement&&(e.delete(i),i.classList.remove("nwse","nesw"),i.removeEventListener("mousemove",r),i.removeEventListener("mousemove",n),i.removeEventListener("touchmove",r),i.removeEventListener("touchmove",n))}function h(t,e){let{image:n}=i.get(t)||{};if(!n)return;let r=n.naturalWidth/n.width;for(let i in e){let n=Math.round(e[i]*r);e[i]=n;let o=t.querySelector(`[data-image-crop-input='${i}']`);o instanceof HTMLInputElement&&(o.value=n.toString())}t.dispatchEvent(new CustomEvent("image-crop-change",{bubbles:!0,detail:e}))}let ImageCropElement=class ImageCropElement extends HTMLElement{connectedCallback(){if(i.has(this))return;let t=this.attachShadow({mode:"open"});t.innerHTML=`
<style>
  :host { touch-action: none; display: block; }
  :host(.nesw) { cursor: nesw-resize; }
  :host(.nwse) { cursor: nwse-resize; }
  :host(.nesw) .crop-box, :host(.nwse) .crop-box { cursor: inherit; }
  :host([loaded]) .crop-image { display: block; }
  :host([loaded]) ::slotted([data-loading-slot]), .crop-image { display: none; }

  .crop-wrapper {
    position: relative;
    font-size: 0;
  }
  .crop-container {
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    position: absolute;
    overflow: hidden;
    z-index: 1;
    top: 0;
    width: 100%;
    height: 100%;
  }

  :host([rounded]) .crop-box {
    border-radius: 50%;
    box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.3);
  }
  .crop-box {
    position: absolute;
    border: 1px dashed #fff;
    box-sizing: border-box;
    cursor: move;
  }

  :host([rounded]) .crop-outline {
    outline: none;
  }
  .crop-outline {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    outline: 4000px solid rgba(0, 0, 0, .3);
  }

  .handle { position: absolute; }
  :host([rounded]) .handle::before { border-radius: 50%; }
  .handle:before {
    position: absolute;
    display: block;
    padding: 4px;
    transform: translate(-50%, -50%);
    content: ' ';
    background: #fff;
    border: 1px solid #767676;
  }
  .ne { top: 0; right: 0; cursor: nesw-resize; }
  .nw { top: 0; left: 0; cursor: nwse-resize; }
  .se { bottom: 0; right: 0; cursor: nwse-resize; }
  .sw { bottom: 0; left: 0; cursor: nesw-resize; }
</style>
<slot></slot>
<div class="crop-wrapper">
  <img width="100%" class="crop-image" alt="">
  <div class="crop-container">
    <div data-crop-box class="crop-box">
      <div class="crop-outline"></div>
      <div data-direction="nw" class="handle nw"></div>
      <div data-direction="ne" class="handle ne"></div>
      <div data-direction="sw" class="handle sw"></div>
      <div data-direction="se" class="handle se"></div>
    </div>
  </div>
</div>
`;let e=t.querySelector("[data-crop-box]");if(!(e instanceof HTMLElement))return;let o=t.querySelector("img");o instanceof HTMLImageElement&&(i.set(this,{box:e,image:o}),o.addEventListener("load",()=>{this.loaded=!0,l(this)}),this.addEventListener("mouseleave",c),this.addEventListener("touchend",c),this.addEventListener("mouseup",c),e.addEventListener("mousedown",s),e.addEventListener("touchstart",s,{passive:!0}),this.addEventListener("keydown",n),this.addEventListener("keydown",r),this.src&&(o.src=this.src))}static get observedAttributes(){return["src"]}get src(){return this.getAttribute("src")}set src(t){t?this.setAttribute("src",t):this.removeAttribute("src")}get loaded(){return this.hasAttribute("loaded")}set loaded(t){t?this.setAttribute("loaded",""):this.removeAttribute("loaded")}attributeChangedCallback(t,e,n){let{image:r}=i.get(this)||{};"src"===t&&(this.loaded=!1,r&&(r.src=n))}};window.customElements.get("image-crop")||(window.ImageCropElement=ImageCropElement,window.customElements.define("image-crop",ImageCropElement))},94147:(t,e,i)=>{i.d(e,{T:()=>include_fragment_element_IncludeFragmentElement});var n,r,o,s,a,l,c,h,u=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},d=function(t,e,i,n,r){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?r.call(t,i):r?r.value=i:e.set(t,i),i};let f=new WeakMap,p=null;let include_fragment_element_IncludeFragmentElement=class include_fragment_element_IncludeFragmentElement extends HTMLElement{constructor(){super(...arguments),n.add(this),r.set(this,!1),o.set(this,new IntersectionObserver(t=>{for(let e of t)if(e.isIntersecting){let{target:t}=e;if(u(this,o,"f").unobserve(t),!(t instanceof include_fragment_element_IncludeFragmentElement))return;"lazy"===t.loading&&u(this,n,"m",s).call(this)}},{rootMargin:"0px 0px 256px 0px",threshold:.01}))}static define(t="include-fragment",e=customElements){return e.define(t,this),this}static setCSPTrustedTypesPolicy(t){p=null===t?t:Promise.resolve(t)}static get observedAttributes(){return["src","loading"]}get src(){let t=this.getAttribute("src");if(!t)return"";{let e=this.ownerDocument.createElement("a");return e.href=t,e.href}}set src(t){this.setAttribute("src",t)}get loading(){return"lazy"===this.getAttribute("loading")?"lazy":"eager"}set loading(t){this.setAttribute("loading",t)}get accept(){return this.getAttribute("accept")||""}set accept(t){this.setAttribute("accept",t)}get data(){return u(this,n,"m",l).call(this)}attributeChangedCallback(t,e){"src"===t?this.isConnected&&"eager"===this.loading&&u(this,n,"m",s).call(this):"loading"===t&&this.isConnected&&"eager"!==e&&"eager"===this.loading&&u(this,n,"m",s).call(this)}connectedCallback(){if(!this.shadowRoot){this.attachShadow({mode:"open"});let t=document.createElement("style");t.textContent=":host {display: block;}",this.shadowRoot.append(t,document.createElement("slot"))}this.src&&"eager"===this.loading&&u(this,n,"m",s).call(this),"lazy"===this.loading&&u(this,o,"f").observe(this)}request(){let t=this.src;if(!t)throw Error("missing src");return new Request(t,{method:"GET",credentials:"same-origin",headers:{Accept:this.accept||"text/html"}})}load(){return u(this,n,"m",l).call(this)}fetch(t){return fetch(t)}refetch(){f.delete(this),u(this,n,"m",s).call(this)}};r=new WeakMap,o=new WeakMap,n=new WeakSet,s=async function(){if(!u(this,r,"f")){d(this,r,!0,"f"),u(this,o,"f").unobserve(this);try{let t=await u(this,n,"m",a).call(this);if(t instanceof Error)throw t;let e=document.createElement("template");e.innerHTML=t;let i=document.importNode(e.content,!0);if(!this.dispatchEvent(new CustomEvent("include-fragment-replace",{cancelable:!0,detail:{fragment:i}})))return void d(this,r,!1,"f");this.replaceWith(i),this.dispatchEvent(new CustomEvent("include-fragment-replaced"))}catch(t){this.classList.add("is-error")}finally{d(this,r,!1,"f")}}},a=async function(){let t=this.src,e=f.get(this);if(e&&e.src===t)return e.data;{let e;return e=t?u(this,n,"m",h).call(this):Promise.reject(Error("missing src")),f.set(this,{src:t,data:e}),e}},l=async function(){let t=await u(this,n,"m",a).call(this);if(t instanceof Error)throw t;return t.toString()},c=async function(t,e){for(let i of(await new Promise(t=>setTimeout(t,0)),t))this.dispatchEvent(e?new CustomEvent(i,{detail:{error:e}}):new Event(i))},h=async function(){try{var t;await u(this,n,"m",c).call(this,["loadstart"]);let e=await this.fetch(this.request());if(200!==e.status)throw Error(`Failed to load resource: the server responded with a status of ${e.status}`);let i=e.headers.get("Content-Type");if(!((t=this.accept)&&t.split(",").find(t=>t.match(/^\s*\*\/\*/)))&&(!i||!i.includes(this.accept?this.accept:"text/html")))throw Error(`Failed to load resource: expected ${this.accept||"text/html"} but was ${i}`);let r=await e.text(),o=r;return p&&(o=(await p).createHTML(r,e)),u(this,n,"m",c).call(this,["load","loadend"]),o}catch(t){throw u(this,n,"m",c).call(this,["error","loadend"],t),t}};let m="undefined"!=typeof globalThis?globalThis:window;try{m.IncludeFragmentElement=include_fragment_element_IncludeFragmentElement.define()}catch(t){if(!(m.DOMException&&t instanceof DOMException&&"NotSupportedError"===t.name)&&!(t instanceof ReferenceError))throw t}},5225:(t,e,i)=>{function n(...t){return JSON.stringify(t,(t,e)=>"object"==typeof e?e:String(e))}function r(t,e={}){let{hash:i=n,cache:o=new Map}=e;return function(...e){let n=i.apply(this,e);if(o.has(n))return o.get(n);let r=t.apply(this,e);return r instanceof Promise&&(r=r.catch(t=>{throw o.delete(n),t})),o.set(n,r),r}}i.d(e,{A:()=>r})},44911:()=>{let t="complete"===document.readyState?Promise.resolve():new Promise(t=>{window.addEventListener("load",t)});let TypingEffectElement=class TypingEffectElement extends HTMLElement{async connectedCallback(){await t,this.content&&await e(this.lines,this.content,this.characterDelay,this.lineDelay),this.cursor&&(this.cursor.hidden=!0),this.dispatchEvent(new CustomEvent("typing:complete",{bubbles:!0,cancelable:!0}))}get content(){return this.querySelector('[data-target="typing-effect.content"]')}get cursor(){return this.querySelector('[data-target="typing-effect.cursor"]')}get lines(){let t=this.getAttribute("data-lines");try{return t?JSON.parse(t):[]}catch(t){return[]}}get prefersReducedMotion(){return window.matchMedia("(prefers-reduced-motion)").matches}get characterDelay(){return this.prefersReducedMotion?0:Math.max(0,Math.min(Math.floor(Number(this.getAttribute("data-character-delay"))),0x7fffffff))||40}set characterDelay(t){if(t>0x7fffffff||t<0)throw new DOMException("Value is negative or greater than the allowed amount");this.setAttribute("data-character-delay",String(t))}get lineDelay(){return this.prefersReducedMotion?0:Math.max(0,Math.min(Math.floor(Number(this.getAttribute("data-line-delay"))),0x7fffffff))||40}set lineDelay(t){if(t>0x7fffffff||t<0)throw new DOMException("Value is negative or greater than the allowed amount");this.setAttribute("data-line-delay",String(t))}};async function e(t,e,n,r){for(let o=0;o<t.length;o++){if(0===n)e.append(t[o]);else for(let r of t[o].split(""))await i(n),e.innerHTML+=r;0!==r&&await i(r),o<t.length-1&&e.append(document.createElement("br"))}}async function i(t){return new Promise(e=>{setTimeout(e,t)})}window.customElements.get("typing-effect")||(window.TypingEffectElement=TypingEffectElement,window.customElements.define("typing-effect",TypingEffectElement))}}]);
//# sourceMappingURL=vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-cadbad-fd36e8eee236.js.map