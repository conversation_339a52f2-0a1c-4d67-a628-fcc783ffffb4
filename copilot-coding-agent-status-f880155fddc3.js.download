"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["copilot-coding-agent-status"],{78924:(e,t,o)=>{o.d(t,{I:()=>a});let a=(0,o(96540).createContext)(null)},52811:(e,t,o)=>{o.d(t,{C:()=>s,i:()=>l});var a=o(96679),n=o(27851),r=o(46493);function s(e,t){(0,n.G7)("arianotify_comprehensive_migration")?l(i(e),{...t,element:t?.element??e}):(0,n.G7)("primer_live_region_element")&&t?.element===void 0?(0,r.Cj)(e,{politeness:t?.assertive?"assertive":"polite"}):l(i(e),t)}function l(e,t){let{assertive:o,element:s}=t??{};(0,n.G7)("arianotify_comprehensive_migration")&&"ariaNotify"in Element.prototype?(s||document.body).ariaNotify(e):(0,n.G7)("primer_live_region_element")&&void 0===s?(0,r.iP)(e,{politeness:o?"assertive":"polite"}):function(e,t,o){let n=o??a.XC?.querySelector(t?"#js-global-screen-reader-notice-assertive":"#js-global-screen-reader-notice");n&&(n.textContent===e?n.textContent=`${e}\u00A0`:n.textContent=e)}(e,o,s)}function i(e){return(e.getAttribute("aria-label")||e.innerText||"").trim()}},94118:(e,t,o)=>{var a=o(52497),n=o(74848),r=o(21728),s=o(34164),l=o(96316),i=o(1574),c=o(88795),d=o(47360),u=o(1948),f=o(34614),m=o(38621);let p={copilotCard:"CopilotCodingAgentStatus-module__copilotCard--Kceg7",arrowBoth:"CopilotCodingAgentStatus-module__arrowBoth--ojdJ2"};var h=o(99723);function g(e){switch(e){case i.Y.InProgress:case i.Y.WaitingForUser:case i.Y.Idle:case i.Y.Completed:return(0,n.jsx)(m.CopilotIcon,{size:"small"});case i.Y.Failed:case i.Y.TimedOut:return(0,n.jsx)(m.CopilotWarningIcon,{size:"small",className:"fgColor-danger"});case i.Y.Cancelled:return(0,n.jsx)(m.SquareFillIcon,{size:"small",className:"fgColor-muted"});default:return(0,n.jsx)(m.CopilotIcon,{size:"small"})}}function b(e){let t,o,a,u,b,x,y,_,C,v,j,w,N,S,k=(0,r.c)(41),{useMockData:A,sessionsPollingInterval:E}=e;k[0]!==E||k[1]!==A?(t={initialData:void 0,useMockData:A,sessionsPollingInterval:E},k[0]=E,k[1]=A,k[2]=t):t=k[2];let{data:$,isLoading:I,isError:P}=(0,l.t)(t),{ownerLogin:R,name:O}=(0,c.t)(),{pull:T}=(0,d.mV)(),{number:Y}=T;if(k[3]!==R||k[4]!==Y||k[5]!==O||k[6]!==$||k[7]!==P||k[8]!==I){a=Symbol.for("react.early_return_sentinel");e:{let e=$?.at(-1),t=function(e,t,o,a){if(null!=e&&null!=t&&null!=o)return`/${e}/${t}/pull/${o}/agent-sessions/${a||""}`}(R,O,Y,e?.id??null);if(I){let e,t,o,r;k[18]===Symbol.for("react.memo_cache_sentinel")?(e=(0,s.$)(p.copilotCard,"fgColor-default rounded-2 no-underline mb-3 border d-flex flex-items-stretch"),k[18]=e):e=k[18],k[19]===Symbol.for("react.memo_cache_sentinel")?(t=(0,n.jsx)("div",{className:"mr-2 d-flex flex-column flex-content-start flex-items-start circle Skeleton",style:{height:"16px",width:"16px"},children:"\xa0"}),k[19]=t):t=k[19],k[20]===Symbol.for("react.memo_cache_sentinel")?(o=(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"mb-0 Skeleton Skeleton--text",children:"\xa0"}),(0,n.jsx)("p",{className:"mb-0 Skeleton Skeleton--text",children:"\xa0"})]}),k[20]=o):o=k[20],k[21]===Symbol.for("react.memo_cache_sentinel")?(r=(0,n.jsxs)("div",{className:e,"data-testid":"agent-status-loading-skeleton",children:[t,o,(0,n.jsx)("div",{className:"ml-3 d-flex flex-column flex-justify-start",children:(0,n.jsx)("div",{className:"flex-self-start",style:{height:"16px",width:"16px"}})})]}),k[21]=r):r=k[21],a=r;break e}if(P){let e,t,o,r;k[22]===Symbol.for("react.memo_cache_sentinel")?(e=(0,s.$)(p.copilotCard,"fgColor-default rounded-2 no-underline mb-3 border d-flex flex-items-stretch"),k[22]=e):e=k[22],k[23]===Symbol.for("react.memo_cache_sentinel")?(t=(0,n.jsx)("div",{className:"mr-2 fgColor-muted d-flex flex-column flex-content-start flex-items-start",children:g(i.Y.Failed)}),k[23]=t):t=k[23],k[24]===Symbol.for("react.memo_cache_sentinel")?(o=(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("p",{className:"mb-0 fgColor-default text-semibold",children:"Error loading sessions"}),(0,n.jsx)("p",{className:"mb-0 fgColor-muted text-small",children:"Retrying..."})]}),k[24]=o):o=k[24],k[25]===Symbol.for("react.memo_cache_sentinel")?(r=(0,n.jsxs)("div",{className:e,children:[t,o,(0,n.jsx)("div",{className:"ml-3 d-flex flex-column flex-justify-start",children:(0,n.jsx)("div",{className:"flex-self-start",style:{height:"16px",width:"16px"}})})]}),k[25]=r):r=k[25],a=r;break e}o=f.A,_=t,k[26]===Symbol.for("react.memo_cache_sentinel")?(C=(0,s.$)(p.copilotCard,"fgColor-default rounded-2 no-underline mb-3 border d-flex flex-items-stretch"),k[26]=C):C=k[26],v=(0,n.jsx)("div",{className:"mr-2 fgColor-muted d-flex flex-column flex-content-start flex-items-start",children:g(e?.state??i.Y.Idle)}),x="flex-1",y=(0,n.jsx)("p",{className:"mb-0 fgColor-default text-semibold",children:function(e){switch(e){case i.Y.InProgress:return"In progress";case i.Y.WaitingForUser:return"Copilot is waiting for your input";case i.Y.Idle:return"Copilot is blocked";case i.Y.Completed:return"Copilot is done";case i.Y.Failed:return"Copilot has failed";case i.Y.TimedOut:return"Copilot has timed out";case i.Y.Cancelled:return"Copilot was manually stopped";default:return"In progress"}}(e?.state??i.Y.Idle)}),u="mb-0 fgColor-muted text-small",b=e&&function(e,t,o){let a=new Date(t).toISOString(),r=(0,h.Y)(t,o);switch(e){case i.Y.Completed:return(0,n.jsxs)("span",{role:"timer",children:["completed after ",(0,n.jsx)("time",{"aria-live":"off",children:r})]});case i.Y.Failed:return(0,n.jsxs)("span",{role:"timer",children:["failed after ",(0,n.jsx)("time",{"aria-live":"off",children:r})]});case i.Y.TimedOut:return(0,n.jsxs)("span",{role:"timer",children:["timed out after ",(0,n.jsx)("time",{"aria-live":"off",children:r})]});case i.Y.Cancelled:return(0,n.jsxs)("span",{role:"timer",children:["session stopped after ",(0,n.jsx)("time",{"aria-live":"off",children:r})]});default:return(0,n.jsxs)("span",{role:"timer",children:["started ",(0,n.jsx)("relative-time",{datetime:a,tense:"past"})]})}}(e?.state,e?.created_at,e?.completed_at??"")}k[3]=R,k[4]=Y,k[5]=O,k[6]=$,k[7]=P,k[8]=I,k[9]=o,k[10]=a,k[11]=u,k[12]=b,k[13]=x,k[14]=y,k[15]=_,k[16]=C,k[17]=v}else o=k[9],a=k[10],u=k[11],b=k[12],x=k[13],y=k[14],_=k[15],C=k[16],v=k[17];return a!==Symbol.for("react.early_return_sentinel")?a:(k[27]!==u||k[28]!==b?(j=(0,n.jsx)("p",{className:u,children:b}),k[27]=u,k[28]=b,k[29]=j):j=k[29],k[30]!==j||k[31]!==x||k[32]!==y?(w=(0,n.jsxs)("div",{className:x,children:[y,j]}),k[30]=j,k[31]=x,k[32]=y,k[33]=w):w=k[33],k[34]===Symbol.for("react.memo_cache_sentinel")?(N=(0,n.jsx)("div",{className:"ml-3 d-flex flex-column flex-justify-start",children:(0,n.jsx)("div",{className:"flex-self-start",children:(0,n.jsx)(m.ArrowBothIcon,{className:`${p.arrowBoth} fgColor-muted`})})}),k[34]=N):N=k[34],k[35]!==o||k[36]!==w||k[37]!==_||k[38]!==C||k[39]!==v?(S=(0,n.jsxs)(o,{href:_,className:C,children:[v,w,N]}),k[35]=o,k[36]=w,k[37]=_,k[38]=C,k[39]=v,k[40]=S):S=k[40],S)}function x(e){let t,o,a,s=(0,r.c)(9),{repository:l,pull:i,useMockData:f,sessionsPollingInterval:m}=e;return s[0]!==m||s[1]!==f?(t=(0,n.jsx)(b,{useMockData:f,sessionsPollingInterval:m}),s[0]=m,s[1]=f,s[2]=t):t=s[2],s[3]!==i||s[4]!==t?(o=(0,n.jsx)(u.Fn,{children:(0,n.jsx)(d.mE,{pull:i,children:t})}),s[3]=i,s[4]=t,s[5]=o):o=s[5],s[6]!==l||s[7]!==o?(a=(0,n.jsx)(c.d,{repository:l,children:o}),s[6]=l,s[7]=o,s[8]=a):a=s[8],a}try{b.displayName||(b.displayName="CopilotCodingAgentStatusWidget")}catch{}try{x.displayName||(x.displayName="CopilotCodingAgentStatus")}catch{}(0,a.k)("copilot-coding-agent-status",{Component:x})},39627:(e,t,o)=>{o.d(t,{D:()=>r,Y:()=>s});var a=o(52811),n=o(96679);function r(e){if(!n.XC)return;let t=n.XC.querySelector("title"),o=n.XC.createElement("title");o.textContent=e,t?t.textContent!==e&&(t.replaceWith(o),(0,a.i)(e)):(n.XC.head.appendChild(o),(0,a.i)(e))}function s(e){return document.body.classList.contains("logged-out")?`${e} \xb7 GitHub`:e}},66871:(e,t,o)=>{o.d(t,{C3:()=>l,JV:()=>n,K3:()=>u,MM:()=>i,OE:()=>f,Zu:()=>d,bj:()=>r,jc:()=>c,kd:()=>s});var a=o(96679);function n(){return a.Kn?.state||{}}function r(e){m(n(),"",e)}function s(e){a.Kn?.pushState({appId:n().appId},"",e),p()}function l(e){m({...n(),...e},"",location.href)}function i(e){r(`?${e.toString()}${a.fV.hash}`)}function c(){r(a.fV.pathname+a.fV.hash)}function d(e){r(e.startsWith("#")?e:`#${e}`)}function u(){r(a.fV.pathname+a.fV.search)}function f(){a.Kn?.back()}function m(e,t,o){a.Kn?.replaceState(e,t,o),p()}function p(){a.cg?.dispatchEvent(new CustomEvent("statechange",{bubbles:!1,cancelable:!1}))}},13233:(e,t,o)=>{o.d(t,{l:()=>a});let a=()=>void 0},7531:(e,t,o)=>{o.d(t,{Y:()=>a});function a(){let e={};return e.promise=new Promise((t,o)=>{e.resolve=t,e.reject=o}),e}},41764:(e,t,o)=>{o.d(t,{A:()=>l});let{getItem:a,setItem:n,removeItem:r}=(0,o(85351).A)("localStorage"),s="REACT_PROFILING_ENABLED",l={enable:()=>n(s,"true"),disable:()=>r(s),isEnabled:()=>!!a(s)}},64899:(e,t,o)=>{o.d(t,{A:()=>r});var a=o(17515),n=o(96540);function r(){let e=(0,n.useRef)(!1),t=(0,n.useCallback)(()=>e.current,[]);return(0,a.N)(()=>(e.current=!0,()=>{e.current=!1}),[]),t}},17515:(e,t,o)=>{o.d(t,{N:()=>r});var a=o(96679),n=o(96540);let r=void 0!==a.cg?.document?.createElement?n.useLayoutEffect:n.useEffect},47019:(e,t,o)=>{o.d(t,{A:()=>r});var a=o(64899),n=o(96540);let r=function(e){let t=(0,a.A)(),[o,r]=(0,n.useState)(e);return[o,(0,n.useCallback)(e=>{t()&&r(e)},[t])]}},60039:(e,t,o)=>{o.d(t,{DI:()=>n,QJ:()=>s,Sr:()=>l,lS:()=>r});var a=o(26559);function n(e,t={}){var o=e;if(new URL(o,window.location.origin).origin!==window.location.origin)throw Error("Can not make cross-origin requests from verifiedFetch");let r=function(e){let t=new URL(e,window.location.href),o=new URL(window.location.href,window.location.origin),a=o.searchParams.get("_features");a&&!t.searchParams.has("_features")&&t.searchParams.set("_features",a);let n=o.searchParams.get("_tracing");return n&&!t.searchParams.has("_tracing")&&t.searchParams.set("_tracing",n),e.startsWith(window.location.origin)?t.href:`${t.pathname}${t.search}`}(e),s={...t.headers,"GitHub-Verified-Fetch":"true",...(0,a.kt)()};return fetch(r,{...t,headers:s})}function r(e,t){let o={...t?.headers??{},Accept:"application/json","Content-Type":"application/json"},a=t?.body?JSON.stringify(t.body):void 0;return n(e,{...t,body:a,headers:o})}function s(e,t={}){let o={...t.headers,"GitHub-Is-React":"true"};return n(e,{...t,headers:o})}function l(e,t){let o={...t?.headers??{},"GitHub-Is-React":"true"};return r(e,{...t,headers:o})}},26033:(e,t,o)=>{o.d(t,{y:()=>s});var a=o(74848),n=o(21728),r=o(78924);function s(e){let t,o,s,l=(0,n.c)(7),{children:i,appName:c,category:d,metadata:u}=e;return l[0]!==c||l[1]!==d||l[2]!==u?(o={appName:c,category:d,metadata:u},l[0]=c,l[1]=d,l[2]=u,l[3]=o):o=l[3],t=o,l[4]!==i||l[5]!==t?(s=(0,a.jsx)(r.I.Provider,{value:t,children:i}),l[4]=i,l[5]=t,l[6]=s):s=l[6],s}try{s.displayName||(s.displayName="AnalyticsProvider")}catch{}},60674:(e,t,o)=>{o.d(t,{BP:()=>u,D3:()=>d,O8:()=>i});var a=o(74848),n=o(21728),r=o(96540),s=o(96679),l=o(17515);let i={ServerRender:"ServerRender",ClientHydrate:"ClientHydrate",ClientRender:"ClientRender"},c=(0,r.createContext)(i.ClientRender);function d(e){let t,o,d,u,f=(0,n.c)(8),{wasServerRendered:m,children:p}=e;f[0]!==m?(t=()=>s.X3?i.ServerRender:m?i.ClientHydrate:i.ClientRender,f[0]=m,f[1]=t):t=f[1];let[h,g]=(0,r.useState)(t);return f[2]!==h?(o=()=>{h!==i.ClientRender&&g(i.ClientRender)},d=[h],f[2]=h,f[3]=o,f[4]=d):(o=f[3],d=f[4]),(0,l.N)(o,d),f[5]!==p||f[6]!==h?(u=(0,a.jsx)(c.Provider,{value:h,children:p}),f[5]=p,f[6]=h,f[7]=u):u=f[7],u}function u(){return(0,r.useContext)(c)}try{c.displayName||(c.displayName="RenderPhaseContext")}catch{}try{d.displayName||(d.displayName="RenderPhaseProvider")}catch{}},99543:(e,t,o)=>{o.d(t,{Qn:()=>i,T8:()=>d,Y6:()=>f,k6:()=>u});var a=o(74848),n=o(65556),r=o(96540),s=o(13233),l=o(47019);let i=5e3,c=(0,r.createContext)({addToast:s.l,addPersistedToast:s.l,clearPersistedToast:s.l}),d=(0,r.createContext)({toasts:[],persistedToast:null});function u({children:e}){let[t,o]=(0,l.A)([]),[s,u]=(0,r.useState)(null),{safeSetTimeout:f}=(0,n.A)(),m=(0,r.useCallback)(function(e){o([...t,e]),f(()=>o(t.slice(1)),i)},[t,f,o]),p=(0,r.useCallback)(function(e){u(e)},[u]),h=(0,r.useCallback)(function(){u(null)},[u]),g=(0,r.useMemo)(()=>({addToast:m,addPersistedToast:p,clearPersistedToast:h}),[p,m,h]),b=(0,r.useMemo)(()=>({toasts:t,persistedToast:s}),[t,s]);return(0,a.jsx)(c.Provider,{value:g,children:(0,a.jsx)(d.Provider,{value:b,children:e})})}function f(){return(0,r.useContext)(c)}try{c.displayName||(c.displayName="ToastContext")}catch{}try{d.displayName||(d.displayName="InternalToastsContext")}catch{}try{u.displayName||(u.displayName="ToastContextProvider")}catch{}},42218:(e,t,o)=>{o.d(t,{V:()=>f});var a=o(74848),n=o(96540),r=o(99543),s=o(38621),l=o(65556),i=o(16255);let c={info:"",success:"Toast--success",error:"Toast--error"},d={info:(0,a.jsx)(s.InfoIcon,{}),success:(0,a.jsx)(s.CheckIcon,{}),error:(0,a.jsx)(s.StopIcon,{})},u=({message:e,timeToLive:t,icon:o,type:r="info",role:s="log"})=>{let[u,f]=n.useState(!0),{safeSetTimeout:m}=(0,l.A)();return(0,n.useEffect)(()=>{t&&m(()=>f(!1),t-300)},[m,t]),(0,a.jsx)(i.Z,{children:(0,a.jsx)("div",{className:"p-1 position-fixed bottom-0 left-0 mb-3 ml-3",children:(0,a.jsxs)("div",{className:`Toast ${c[r]} ${u?"Toast--animateIn":"Toast--animateOut"}`,id:"ui-app-toast","data-testid":`ui-app-toast-${r}`,role:s,children:[(0,a.jsx)("span",{className:"Toast-icon",children:o||d[r]}),(0,a.jsx)("span",{className:"Toast-content",children:e})]})})})};try{u.displayName||(u.displayName="Toast")}catch{}function f(){let{toasts:e,persistedToast:t}=(0,n.useContext)(r.T8);return(0,a.jsxs)(a.Fragment,{children:[e.map((e,t)=>(0,a.jsx)(u,{message:e.message,icon:e.icon,timeToLive:r.Qn,type:e.type,role:e.role},t)),t&&(0,a.jsx)(u,{message:t.message,icon:t.icon,type:t.type,role:t.role})]})}try{f.displayName||(f.displayName="Toasts")}catch{}},39595:(e,t,o)=>{let a;o.d(t,{CF:()=>h,p_:()=>$,FB:()=>u,Se:()=>w,aC:()=>A,zV:()=>E});let n=new WeakSet,r=new WeakMap;function s(e=document){if(r.has(e))return r.get(e);let t=!1,o=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)d(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&l(e)});o.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let a={get closed(){return t},unsubscribe(){t=!0,r.delete(e),o.disconnect()}};return r.set(e,a),a}function l(e){for(let t of e.querySelectorAll("[data-action]"))d(t);e instanceof Element&&e.hasAttribute("data-action")&&d(e)}function i(e){let t=e.currentTarget;for(let o of c(t))if(e.type===o.type){let a=t.closest(o.tag);n.has(a)&&"function"==typeof a[o.method]&&a[o.method](e);let r=t.getRootNode();if(r instanceof ShadowRoot&&n.has(r.host)&&r.host.matches(o.tag)){let t=r.host;"function"==typeof t[o.method]&&t[o.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),o=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,o),method:t.slice(o+1)||"handleEvent"}}}function d(e){for(let t of c(e))e.addEventListener(t.type,i)}function u(e,t){let o=e.tagName.toLowerCase();if(e.shadowRoot){for(let a of e.shadowRoot.querySelectorAll(`[data-target~="${o}.${t}"]`))if(!a.closest(o))return a}for(let a of e.querySelectorAll(`[data-target~="${o}.${t}"]`))if(a.closest(o)===e)return a}let f=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),m=(e,t="property")=>{let o=f(e);if(!o.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return o},p="attr";function h(e,t){k(e,p).add(t)}let g=new WeakSet;function b(e,t){if(g.has(e))return;g.add(e);let o=Object.getPrototypeOf(e),a=o?.constructor?.attrPrefix??"data-";for(let n of(t||(t=k(o,p)),t)){let t=e[n],o=m(`${a}${n}`),r={configurable:!0,get(){return this.getAttribute(o)||""},set(e){this.setAttribute(o,e||"")}};"number"==typeof t?r={configurable:!0,get(){return Number(this.getAttribute(o)||0)},set(e){this.setAttribute(o,e)}}:"boolean"==typeof t&&(r={configurable:!0,get(){return this.hasAttribute(o)},set(e){this.toggleAttribute(o,e)}}),Object.defineProperty(e,n,r),n in e&&!e.hasAttribute(o)&&r.set.call(e,t)}}let x=new Map,y=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),_=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let o={once:!0,passive:!0,signal:t.signal},a=()=>t.abort();document.addEventListener("mousedown",a,o),document.addEventListener("touchstart",a,o),document.addEventListener("keydown",a,o),document.addEventListener("pointerdown",a,o)}),C={ready:()=>y,firstInteraction:()=>_,visible:e=>new Promise(t=>{let o=new IntersectionObserver(e=>{for(let a of e)if(a.isIntersecting){t(),o.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))o.observe(t)})},v=new WeakMap;function j(e){cancelAnimationFrame(v.get(e)||0),v.set(e,requestAnimationFrame(()=>{for(let t of x.keys()){let o=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||o){let a=o?.getAttribute("data-load-on")||"ready",n=a in C?C[a]:C.ready;for(let e of x.get(t)||[])n(t).then(e);x.delete(t),v.delete(e)}}}))}function w(e,t){for(let[o,a]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))x.has(o)||x.set(o,new Set),x.get(o).add(a);N(document)}function N(e){a||(a=new MutationObserver(e=>{if(x.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&j(e)})),j(e),a.observe(e,{subtree:!0,childList:!0})}let S=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,o=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,o)};let a=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,a)};let n=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,o,a){t.attributeChangedCallback(this,e,o,a,n)};let r=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,r)},set(e){r=e}}),function(e){let t=e.observedAttributes||[],o=e.attrPrefix??"data-",a=e=>m(`${o}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...k(e.prototype,p)].map(a).concat(t),set(e){t=e}})}(e),function(e){let t=f(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var o,a;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(b(e),n.add(e),e.shadowRoot&&(l(a=e.shadowRoot),s(a)),l(e),s(e.ownerDocument),t?.call(e),e.shadowRoot)&&(l(o=e.shadowRoot),s(o),N(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,o,a,n){b(e),"data-catalyst"!==t&&n&&n.call(e,t,o,a)}};function k(e,t){if(!Object.prototype.hasOwnProperty.call(e,S)){let t=e[S],o=e[S]=new Map;if(t)for(let[e,a]of t)o.set(e,new Set(a))}let o=e[S];return o.has(t)||o.set(t,new Set),o.get(t)}function A(e,t){k(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return u(this,t)}})}function E(e,t){k(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),o=[];if(this.shadowRoot)for(let a of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))a.closest(e)||o.push(a);for(let a of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))a.closest(e)===this&&o.push(a);return o}})}function $(e){new CatalystDelegate(e)}},97286:(e,t,o)=>{o.d(t,{I:()=>r});var a=o(1651),n=o(15985);function r(e,t){return(0,n.t)(e,a.$,t)}}},e=>{var t=t=>e(e.s=t);e.O(0,["primer-react","react-core","react-lib","octicons-react","vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52","vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483","vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6","vendors-node_modules_tanstack_query-core_build_modern_queryObserver_js-node_modules_tanstack_-defd52","ui_packages_failbot_failbot_ts","ui_packages_paths_index_ts","ui_packages_copilot-chat_utils_copilot-chat-helpers_ts","ui_packages_agent-sessions_utils_elapsed-time-util_ts-ui_packages_copilot-chat_utils_copilot--0b99c0"],()=>t(94118)),e.O()}]);
//# sourceMappingURL=copilot-coding-agent-status-86773ccbc811.js.map