"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_ref-selector_RefSelector_tsx"],{99707:(e,t,a)=>{a.d(t,{_:()=>o,d:()=>SearchIndex});var r=a(85351),n=a(7479);function i(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}let{getItem:s,setItem:l,removeItem:c}=(0,r.A)("localStorage",{throwQuotaErrorsOnSet:!0}),o={Branch:"branch",Tag:"tag"};let SearchIndex=class SearchIndex{render(){this.selector.render()}async fetchData(){try{if(!this.isLoading||this.fetchInProgress)return;if(!this.bootstrapFromLocalStorage()){this.fetchInProgress=!0,this.fetchFailed=!1;let e=await fetch(`${this.refEndpoint}?type=${this.refType}`,{headers:{Accept:"application/json"}});await this.processResponse(e)}this.isLoading=!1,this.fetchInProgress=!1,this.render()}catch{this.fetchInProgress=!1,this.fetchFailed=!0}}async processResponse(e){if(this.emitStats(e),!e.ok){this.fetchFailed=!0;return}let t=e.clone(),a=await e.json();this.knownItems=a.refs,this.cacheKey=a.cacheKey,this.flushToLocalStorage(await t.text())}emitStats(e){if(!e.ok)return void(0,n.i)({incrementKey:"REF_SELECTOR_BOOT_FAILED"},!0);switch(e.status){case 200:(0,n.i)({incrementKey:"REF_SELECTOR_BOOTED_FROM_UNCACHED_HTTP"});break;case 304:(0,n.i)({incrementKey:"REF_SELECTOR_BOOTED_FROM_HTTP_CACHE"});break;default:(0,n.i)({incrementKey:"REF_SELECTOR_UNEXPECTED_RESPONSE"})}}search(e){let t;if(this.searchTerm=e,""===e){this.currentSearchResult=this.knownItems;return}let a=[],r=[];for(let n of(this.exactMatchFound=!1,this.knownItems))if(!((t=n.indexOf(e))<0)){if(0===t){e===n?(r.unshift(n),this.exactMatchFound=!0):r.push(n);continue}a.push(n)}this.currentSearchResult=[...r,...a]}bootstrapFromLocalStorage(){let e=s(this.localStorageKey);if(!e)return!1;let t=JSON.parse(e);return t.cacheKey===this.cacheKey&&"refs"in t?(this.knownItems=t.refs,this.isLoading=!1,(0,n.i)({incrementKey:"REF_SELECTOR_BOOTED_FROM_LOCALSTORAGE"}),!0):(c(this.localStorageKey),!1)}async flushToLocalStorage(e){try{l(this.localStorageKey,e)}catch(t){if(t.message.toLowerCase().includes("quota")){this.clearSiblingLocalStorage(),(0,n.i)({incrementKey:"REF_SELECTOR_LOCALSTORAGE_OVERFLOWED"});try{l(this.localStorageKey,e)}catch(e){e.message.toLowerCase().includes("quota")&&(0,n.i)({incrementKey:"REF_SELECTOR_LOCALSTORAGE_GAVE_UP"})}}else throw t}}clearSiblingLocalStorage(){for(let e of Object.keys(localStorage))e.startsWith(SearchIndex.LocalStoragePrefix)&&c(e)}clearLocalStorage(){c(this.localStorageKey)}get localStorageKey(){return`${SearchIndex.LocalStoragePrefix}:${this.nameWithOwner}:${this.refType}`}constructor(e,t,a,r,n){i(this,"refType",void 0),i(this,"selector",void 0),i(this,"knownItems",[]),i(this,"currentSearchResult",[]),i(this,"exactMatchFound",!1),i(this,"searchTerm",""),i(this,"refEndpoint",void 0),i(this,"cacheKey",void 0),i(this,"nameWithOwner",void 0),i(this,"isLoading",!0),i(this,"fetchInProgress",!1),i(this,"fetchFailed",!1),this.refType=e,this.selector=t,this.refEndpoint=a,this.cacheKey=r,this.nameWithOwner=n}};i(SearchIndex,"LocalStoragePrefix","ref-selector")},79637:(e,t,a)=>{a.d(t,{F:()=>o});var r,n=a(74848),i=a(75177),s=a(15385),l=a(96540),c=a(20627);function o({items:e,itemHeight:t,sx:a,renderItem:r,makeKey:i,ariaControls:s,shouldUseActionList:o=!1,className:u}){let f=(0,l.useRef)(null),m=(0,c.Te)({count:e.length,getScrollElement:(0,l.useCallback)(()=>f.current,[]),estimateSize:(0,l.useCallback)(()=>t,[t])});return(0,n.jsx)(h,{ref:f,sx:a,virtualizer:m,id:s,shouldUseActionList:o,className:u,children:m.getVirtualItems().map((t,a)=>(0,n.jsx)(d,{virtualRow:t,children:r(e[t.index],a)},i(e[t.index])))})}let h=l.forwardRef(function({children:e,sx:t,virtualizer:a,id:r,shouldUseActionList:l,className:c},o){return(0,n.jsx)(i.A,{ref:o,sx:t,id:r,className:c,children:l?(0,n.jsx)(s.l,{id:r,role:"menu",style:{height:a.getTotalSize(),width:"100%",position:"relative"},selectionVariant:"single",children:e}):(0,n.jsx)("ul",{role:"menu",style:{height:a.getTotalSize(),width:"100%",position:"relative"},id:r,children:e})})});function d({children:e,virtualRow:t}){return(0,n.jsx)("li",{role:"none",style:{position:"absolute",top:0,left:0,width:"100%",height:`${t.size}px`,transform:`translateY(${t.start}px)`},children:e})}try{o.displayName||(o.displayName="FixedSizeVirtualList")}catch{}try{(r=VirtualListContainerInner).displayName||(r.displayName="VirtualListContainerInner")}catch{}try{d.displayName||(d.displayName="ItemContainer")}catch{}},39461:(e,t,a)=>{a.d(t,{z:()=>i});var r=a(74848),n=a(75177);function i({text:e,search:t,hideOverflow:a=!1,overflowWidth:i=0}){let s=(function(e,t){if(!t)return[e];let a=e.toLowerCase().split(t.toLowerCase());if(a.length<2)return[e];let r=0,n=[];for(let i of a)n.push(e.substring(r,r+i.length)),r+=i.length,n.push(e.substring(r,r+t.length)),r+=t.length;return n})(e,t).map((e,t)=>t%2==1?(0,r.jsx)("strong",{className:"color-fg-default",children:e},t):e),l=i?`${i}px`:void 0;return(0,r.jsx)(n.A,{sx:{maxWidth:l,overflow:a?"hidden":"visible",textOverflow:"ellipsis",whiteSpace:"nowrap",color:t.length?"fg.muted":"fg.default"},children:s})}try{i.displayName||(i.displayName="HighlightedText")}catch{}},33613:(e,t,a)=>{a.d(t,{aH:()=>ee,PI:()=>Z,Qe:()=>q,JJ:()=>Y});var r=a(74848),n=a(55847),i=a(6869),s=a(60499),l=a(96235),c=a(38621),o=a(75177),h=a(84217),d=a(87330),u=a(15385),f=a(9591),m=a(63867),x=a(52464),y=a(53110),p=a(96540),g=a(96679),b=a(80663),j=a(95776),S=a(40961);function C({isOpen:e,onDismiss:t,onConfirm:a}){let[i]=(0,b.I)(()=>document.body,null,[g.XC?.body]);return i?(0,S.createPortal)((0,r.jsxs)(j.A,{isOpen:e,onDismiss:t,children:[(0,r.jsx)(j.A.Header,{children:"Create branch"}),(0,r.jsxs)(o.A,{sx:{p:3},children:[(0,r.jsx)("span",{children:"A tag already exists with the provided branch name. Many Git commands accept both tag and branch names, so creating this branch may cause unexpected behavior. Are you sure you want to create this branch?"}),(0,r.jsxs)(o.A,{sx:{display:"flex",justifyContent:"flex-end",mt:3},children:[(0,r.jsx)(n.Q,{onClick:t,children:"Cancel"}),(0,r.jsx)(n.Q,{variant:"danger",onClick:a,sx:{ml:2},children:"Create"})]})]})]}),document.body):null}try{C.displayName||(C.displayName="CheckTagNameDialog")}catch{}var w=a(60039);async function R(e,t){let a=new FormData;a.set("value",t);let r=await (0,w.DI)(e,{method:"POST",body:a,headers:{Accept:"application/json"}});return!!r.ok&&!!await r.text()}async function T(e,t,a){let r=new FormData;r.set("name",t),r.set("branch",a);let n=await (0,w.DI)(e,{method:"POST",body:r,headers:{Accept:"application/json"}});if(n.ok)return{success:!0,name:(await n.json()).name};try{let{error:e}=await n.json();if(e)return{success:!1,error:e};throw Error("Unknown response from create branch API")}catch{return{success:!1,error:"Something went wrong."}}}var v=a(3971),N=a(69676);function A(e){let{ariaDescribedBy:t,ariaLabelledBy:a,ariaLabel:i,hotKey:s,onOpenChange:l,size:h,displayCommitish:d,refType:u,children:f,preventClosing:m,inputRef:y,overlayOpen:g,onOverlayChange:b,focusTrapEnabled:j=!0,buttonClassName:S,buttonText:C,allowResizing:w,useFocusZone:R}=e,T=e.idEnding?`-${e.idEnding}`:`-${Date.now()}`,A=(0,p.useRef)(`branch-picker${T}`),k=(0,p.useCallback)(e=>{b(e),l?.(e)},[l,b]),L=(0,p.useMemo)(()=>j?{initialFocusRef:y}:{initialFocusRef:y,disabled:!0},[j,y]);return(0,r.jsx)(v.T,{open:g,overlayProps:{role:"dialog",width:"medium","aria-label":"Select a branch"},onOpen:()=>k(!0),onClose:()=>!m&&k(!1),renderAnchor:e=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.Q,{...e,"data-hotkey":s,size:h,sx:{svg:{color:"fg.muted"},display:"flex",minWidth:w?0:void 0,"> span":{width:"inherit"}},trailingVisual:c.TriangleDownIcon,"aria-describedby":t,"aria-labelledby":a,"aria-label":a?void 0:i??`${d} ${u}`,"data-testid":"anchor-button",id:A.current,className:S,children:(0,r.jsxs)(o.A,{sx:{display:"flex",width:"100%"},children:[(0,r.jsx)(o.A,{sx:{mr:1,color:"fg.muted"},children:"tag"===u?(0,r.jsx)(c.TagIcon,{size:"small"}):(0,r.jsx)(c.GitBranchIcon,{size:"small"})}),(0,r.jsx)(o.A,{sx:{fontSize:1,minWidth:0,maxWidth:w?void 0:125,overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},className:"ref-selector-button-text-container",children:(0,r.jsxs)(x.A,{sx:{minWidth:0},children:["\xa0",C??d]})})]})}),(0,r.jsx)("button",{hidden:!0,"data-hotkey":s,onClick:()=>k(!0),"data-hotkey-scope":"read-only-cursor-text-area"})]}),focusTrapSettings:L,focusZoneSettings:R?{bindKeys:N.z0.ArrowAll|N.z0.Tab}:{disabled:!0},children:(0,r.jsx)("div",{"data-testid":"overlay-content","aria-labelledby":A.current,id:"selectPanel",children:f})})}try{A.displayName||(A.displayName="RefSelectorAnchoredOverlay")}catch{}function k({text:e,onClick:t,href:a,sx:n}){let i=!!a,s=(0,r.jsx)(o.A,{sx:{...n},children:e}),l={sx:{minWidth:0}};return i?(0,r.jsx)(u.l.LinkItem,{role:"link",href:a,onClick:()=>t?.(),...l,children:s}):(0,r.jsx)(u.l.Item,{role:"button",onSelect:()=>t?.(),...l,children:s})}try{k.displayName||(k.displayName="RefSelectorFooter")}catch{}var L=a(79637),I=a(47139),E=a(39461);let O=p.memo(function({isCurrent:e,isDefault:t,href:a,gitRef:n,filterText:i,ariaPosInSet:s,ariaSetSize:l,onSelect:c,onClick:o,shouldSetAsDiv:h=!1}){let d=!!a,f=(0,r.jsx)(F,{gitRef:n,isDefault:t,isCurrent:e,filterText:i}),m={"aria-posinset":h?void 0:s,"aria-setsize":h?void 0:l,sx:{minWidth:0},onSelect:()=>c?.(n),onClick:()=>o?.(n)};return h?d?(0,r.jsx)(u.l.LinkItem,{href:a,...m,children:f}):(0,r.jsx)(u.l.Item,{as:"div",...m,children:f}):d?(0,r.jsx)(u.l.LinkItem,{href:a,...m,children:f}):(0,r.jsx)(u.l.Item,{...m,children:f})}),F=p.memo(function({isCurrent:e,isDefault:t,gitRef:a,filterText:n,showLeadingVisual:i=!0}){return(0,r.jsxs)("div",{style:{display:"flex",justifyContent:"space-between"},children:[(0,r.jsxs)("div",{style:{display:"flex",minWidth:0,alignItems:"flex-end"},children:[i&&(0,r.jsx)(y.A,{icon:c.CheckIcon,"aria-hidden":!0,sx:{pr:1,visibility:e?void 0:"hidden"}}),(0,r.jsx)(E.z,{hideOverflow:!0,search:n,text:a},a)]}),t&&(0,r.jsx)(I.A,{children:"default"})]})});try{O.displayName||(O.displayName="RefItem")}catch{}try{F.displayName||(F.displayName="RefItemContent")}catch{}function _(e){return e.refs.length>20?(0,r.jsx)(B,{...e}):(0,r.jsx)(D,{...e})}function D({refs:e,defaultBranch:t,currentCommitish:a,getHref:n,filterText:i,onSelectItem:s,ariaControls:l}){return(0,r.jsx)("ul",{style:{maxHeight:330,overflowY:"auto"},id:l,children:e.map(l=>(0,r.jsx)(O,{href:n?.(l),isCurrent:a===l,isDefault:t===l,filterText:i,gitRef:l,onSelect:s,onClick:s,ariaPosInSet:e.indexOf(l)+1,ariaSetSize:e.length},l))})}function B({refs:e,defaultBranch:t,currentCommitish:a,getHref:n,filterText:i,onSelectItem:s,ariaControls:l}){return(0,r.jsx)(L.F,{ariaControls:l,items:e,itemHeight:32,sx:{maxHeight:330,overflowY:"auto"},makeKey:e=>e,renderItem:l=>(0,r.jsx)(O,{shouldSetAsDiv:!0,href:n?.(l),isCurrent:a===l,isDefault:t===l,filterText:i,gitRef:l,onSelect:s,onClick:s,ariaPosInSet:e.indexOf(l)+1,ariaSetSize:e.length},l)})}try{_.displayName||(_.displayName="RefsList")}catch{}try{D.displayName||(D.displayName="FullRefsList")}catch{}try{B.displayName||(B.displayName="VirtualRefsList")}catch{}var P=a(99707);function z(e,t,a,r,n){return new P.d("branch"===r?P._.Branch:P._.Tag,n,(0,l.SHX)({owner:t,repo:a,action:"refs"}),e,`${t}/${a}`)}function K(e,t){let a=e.fetchFailed?"failed":e.isLoading?"loading":"loaded";return{status:a,refs:e.currentSearchResult,showCreateAction:e.refType===P._.Branch&&t&&e.searchTerm.length>0&&!e.exactMatchFound,searchIndex:e}}function W(e){let t=(0,p.useRef)(void 0);return t.current||(t.current=e()),t}var $=a(52811);function H(e){let{ariaDescribedBy:t,ariaLabelledBy:a,ariaLabel:n,cacheKey:i,owner:s,repo:c,canCreate:o,types:h,hotKey:d,onOpenChange:u,size:f,getHref:m,onBeforeCreate:x,onRefTypeChanged:y,currentCommitish:g,onCreateError:b,onSelectItem:j,closeOnSelect:S,selectedRefType:w,customFooterItemProps:v,buttonClassName:N,buttonText:k,allowResizing:L,idEnding:I,useFocusZone:E}=e,[O,F]=(0,p.useState)(""),_=(0,p.useRef)(null),D=(0,p.useRef)(null),B="tree"===w?g.slice(0,7):g,[P,$]=(0,p.useState)(!1),[H,M]=(0,p.useState)(!0),[G,Q]=(0,p.useState)(!1),[X,U]=(0,p.useState)(("tree"===w?"branch":w)??(h??Y)[0]),J=function(e,t,a,r,n,i){let[s,l]=(0,p.useState)({status:"uninitialized",refs:[],showCreateAction:!1,searchIndex:null}),c=(0,p.useRef)({render:()=>{l(K(h.current,i))}}),o=(0,p.useRef)({render:()=>{l(K(d.current,i))}}),h=W(()=>z(e,t,a,"branch",c.current)),d=W(()=>z(e,t,a,"tag",o.current));return(0,p.useEffect)(()=>{let n=`${t}/${a}`;h.current.nameWithOwner!==n&&(h.current=z(e,t,a,"branch",c.current)),d.current.nameWithOwner!==n&&(d.current=z(e,t,a,"tag",o.current)),async function(){let e="branch"===r?h.current:d.current;e.render(),await e.fetchData(),e.search(""),e.render()}()},[e,t,a,r,h,d]),(0,p.useEffect)(()=>{let e="branch"===r?h.current:d.current;e.search(n),e.render()},[n,r,h,d]),s}(i,s,c,X,O,o),Z=(0,l.SHX)({owner:s,repo:c,action:"branches"}),q=(0,l.FeW)({owner:s,repo:c}),ee=(0,p.useCallback)(async()=>{x?.(O);let e=await T(Z,O,g);e.success?m&&(J.searchIndex?.clearLocalStorage(),window.location.href=m(e.name)):b?.(e.error)},[x,O,Z,g,m,b,J.searchIndex]),et=(0,p.useCallback)(async()=>{if(await R(q,O)){Q(!0),M(!1);return}Q(!1),M(!1),ee()},[q,O,ee,Q]),ea=(0,p.useCallback)(e=>{U(e),y?.(e)},[U,y]);function er(){$(!1)}let en=(0,p.useCallback)((e,t)=>{j?.(e,t),er()},[j]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(A,{ariaDescribedBy:t,ariaLabelledBy:a,ariaLabel:n,refType:X,displayCommitish:B,focusTrapEnabled:H,preventClosing:G,size:f,onOpenChange:u,hotKey:d,inputRef:_,overlayOpen:P,onOverlayChange:$,buttonClassName:N,buttonText:k,allowResizing:L,idEnding:I,useFocusZone:E,children:(0,r.jsx)(V,{filterText:O,displayCommitish:B,onFilterChange:F,refType:X,onRefTypeChange:ea,refsState:J,onCreateError:e.onCreateError,showTagWarningDialog:G,setShowTagWarningDialog:Q,onCreateBranch:et,inputRef:_,createButtonRef:D,onSelectItem:en,closeOnSelect:S,closeRefSelector:er,customFooterItemProps:v,...e,selectedRefType:X})}),G&&(0,r.jsx)(C,{isOpen:G,onDismiss:()=>{Q(!1),D.current?.focus()},onConfirm:ee})]})}function V({canCreate:e,currentCommitish:t,displayCommitish:a,defaultBranch:n,filterText:i,getHref:s,hideShowAll:l,onSelectItem:f,closeOnSelect:m,closeRefSelector:x,onFilterChange:y,onRefTypeChange:g,owner:b,selectedRefType:j,refsState:S,refType:C,repo:w,types:R,onCreateBranch:T,inputRef:v,createButtonRef:N,customFooterItemProps:A,viewAllJustify:L,actionListHeadingText:I}){var E;let{refs:O,showCreateAction:F,status:D}=S;return(0,p.useEffect)(()=>{let e=setTimeout(()=>{(0,$.i)(O.length>0?`${O.length} branches found`:"No branches found")},1e3);return()=>clearTimeout(e)},[O]),(0,r.jsxs)("div",{className:"py-2",children:[(0,r.jsxs)(o.A,{sx:{borderBottom:"1px solid",borderColor:"border.subtle",pb:2},children:[(0,r.jsxs)(o.A,{sx:{display:"flex",pb:2,px:2,justifyContent:"space-between",alignItems:"center"},children:[(0,r.jsx)(h.A,{as:"h2",sx:{pl:2,fontSize:"inherit"},children:I??((E=R??Y).includes("branch")&&E.includes("tag")?"Switch branches/tags":E.includes("branch")?"Switch branches":E.includes("tag")?"Switch tags":void 0)}),(0,r.jsx)(d.K,{tooltipDirection:"w","aria-label":"Cancel",variant:"invisible",icon:c.XIcon,sx:{color:"fg.muted"},onClick:x})]}),(0,r.jsx)(M,{defaultText:i,refType:C,canCreate:e,onFilterChange:y,ref:v})]}),(0,r.jsxs)(o.A,{sx:{pt:2,pb:F&&0===O.length?0:2},children:[(R??Y).length>1&&(0,r.jsx)(o.A,{sx:{px:2,pb:2},children:(0,r.jsx)(q,{refType:C,onRefTypeChanged:g,sx:{mx:-2,borderBottom:"1px solid",borderColor:"border.muted","> nav":{borderBottom:"none"}}})}),"loading"===D||"uninitialized"===D?(0,r.jsx)(Q,{refType:C}):"failed"===D?(0,r.jsx)(ee,{refType:C}):0!==O.length||F?(0,r.jsx)(_,{ariaControls:"branch"===C?"branches":"tags",filterText:i,refs:O,defaultBranch:"branch"===C?n:"",currentCommitish:C===j?t:"",getHref:s,onSelectItem:e=>{f?.(e,C),m&&x()}}):(0,r.jsx)(X,{})]}),(0,r.jsxs)(u.l,{className:"p-0",children:[F&&(0,r.jsxs)(r.Fragment,{children:[O.length>0&&(0,r.jsx)(u.l.Divider,{className:"d-block mt-0",sx:{backgroundColor:"border.subtle"}}),(0,r.jsx)(J,{displayCommitish:a,newRefName:i,onCreateBranch:T,createButtonRef:N})]}),(!l||A)&&(0,r.jsx)(u.l.Divider,{className:"d-block",sx:{marginTop:2*!!F,backgroundColor:"border.subtle"}}),!l&&(0,r.jsx)(U,{justify:L,refType:C,owner:b,repo:w,onClick:x}),A&&(0,r.jsx)(k,{...A,onClick:function(){A?.onClick?.(),x()}})]})]})}let M=(0,p.forwardRef)(G);function G({refType:e,canCreate:t,onFilterChange:a,defaultText:n},i){return(0,r.jsx)(o.A,{sx:{px:2},children:(0,r.jsx)(f.A,{"aria-label":"tag"===e?"Filter tags":"Filter branches",leadingVisual:c.SearchIcon,value:n,sx:{width:"100%"},placeholder:"tag"===e?"Find a tag...":t?"Find or create a branch...":"Find a branch...",ref:i,onInput:e=>{let t=e.target;t instanceof HTMLInputElement&&a(t.value)}})})}function Q({refType:e}){return(0,r.jsx)(o.A,{sx:{display:"flex",justifyContent:"center",p:2},children:(0,r.jsx)(m.A,{size:"medium","aria-label":`Loading ${"branch"===e?"branches":"tags"}...`})})}function X(){return(0,r.jsx)(o.A,{sx:{p:3,display:"flex",justifyContent:"center"},children:"Nothing to show"})}function U({refType:e,owner:t,repo:a,onClick:n,justify:i="start"}){let s="branch"===e?"branches":"tags";return(0,r.jsx)(u.l.LinkItem,{role:"link",href:(0,l.SHX)({owner:t,repo:a,action:s}),onClick:n,sx:{display:"flex",justifyContent:"center"},children:(0,r.jsxs)(o.A,{sx:{display:"flex",justifyContent:i},children:["View all ",s]})})}function J({displayCommitish:e,newRefName:t,onCreateBranch:a,createButtonRef:n}){return(0,r.jsxs)(u.l.Item,{role:"button",onSelect:a,ref:n,children:[(0,r.jsx)(y.A,{icon:c.GitBranchIcon,sx:{mr:2,color:"fg.muted"}}),(0,r.jsx)("span",{children:"Create branch\xa0"}),(0,r.jsx)(x.A,{sx:{fontWeight:600,fontFamily:"monospace"},children:t}),(0,r.jsx)("span",{children:"\xa0from\xa0"}),(0,r.jsx)(x.A,{sx:{fontWeight:600,fontFamily:"monospace"},children:e})]})}try{H.displayName||(H.displayName="RefSelectorV1")}catch{}try{V.displayName||(V.displayName="RefSelectorActionList")}catch{}try{M.displayName||(M.displayName="RefTextFilter")}catch{}try{G.displayName||(G.displayName="RefTextFilterWithRef")}catch{}try{Q.displayName||(Q.displayName="Loading")}catch{}try{X.displayName||(X.displayName="RefsZeroState")}catch{}try{U.displayName||(U.displayName="ViewAllRefsAction")}catch{}try{J.displayName||(J.displayName="CreateRefAction")}catch{}let Y=["branch","tag"];function Z(e){return(0,r.jsx)(H,{...e})}function q({refType:e,onRefTypeChanged:t,sx:a}){return(0,r.jsxs)(s.A,{sx:{pl:2,...a},"aria-label":"Ref type",children:[(0,r.jsx)(s.A.Link,{as:n.Q,id:"branch-button","aria-controls":"branches",selected:"branch"===e,onClick:()=>t("branch"),sx:{borderBottomRightRadius:0,borderBottomLeftRadius:0},children:"Branches"}),(0,r.jsx)(s.A.Link,{as:n.Q,id:"tag-button","aria-controls":"tags",selected:"tag"===e,onClick:()=>t("tag"),sx:{borderBottomRightRadius:0,borderBottomLeftRadius:0},children:"Tags"})]})}function ee({refType:e}){return(0,r.jsxs)(i.A,{variant:"danger",children:["Could not load ","branch"===e?"branches":"tags"]})}try{Z.displayName||(Z.displayName="RefSelector")}catch{}try{q.displayName||(q.displayName="RefTypeTabs")}catch{}try{ee.displayName||(ee.displayName="LoadingFailed")}catch{}}}]);
//# sourceMappingURL=ui_packages_ref-selector_RefSelector_tsx-d308e32470e0.js.map