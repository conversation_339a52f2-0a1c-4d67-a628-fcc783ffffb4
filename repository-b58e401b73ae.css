.MatrixComponent-pending{padding:var(--base-size-12);transition:opacity ease-out .12s}.MatrixComponent-collapse--title{line-height:20px}.WorkflowJob-deployment-progress .Progress{background:none}.WorkflowJob-deployment-progress .WorkflowJob-deployment-progress-complete{background-color:var(--bgColor-accent-emphasis, var(--color-scale-blue-4)) !important}.WorkflowJob-deployment-progress .WorkflowJob-deployment-progress-incomplete{background-color:var(--bgColor-neutral-muted, var(--color-scale-gray-2)) !important}.WorkflowJob{padding:var(--base-size-12);transition:opacity ease-out .12s}.WorkflowJob-title{height:20px;line-height:20px}.WorkflowJob-title::after{position:absolute;top:0;right:0;bottom:0;left:0;content:""}table.capped-list{width:100%;line-height:100%}table.capped-list th{padding:var(--base-size-8);text-align:left;background:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}table.capped-list td{padding:var(--base-size-8);font-size:12px;vertical-align:middle;border-bottom:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted))}table.capped-list th.middle,table.capped-list td.middle{text-align:center}table.capped-list .favicon{width:16px;height:16px;margin:0 var(--base-size-4);vertical-align:middle}table.capped-list .octicon{margin-right:var(--base-size-8);color:var(--fgColor-muted, var(--color-fg-muted));vertical-align:-3px}table.capped-list tr:nth-child(even){background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.capped-list-label{max-width:200px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.top-domains .dots{display:block;margin:167px auto 0}.actions-full-screen .pagehead,.actions-full-screen .hide-full-screen,.actions-full-screen .HeaderMktg,.actions-full-screen .Header{display:none}.branch-action-btn{margin-left:var(--base-size-16)}.check-range-menu-error{display:none}.check-range-menu-loading{display:block}.is-error .check-range-menu-loading{display:none}.is-error .check-range-menu-error{display:block}.checks-list-item.selected .checks-list-item-name{background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis)) !important}.checks-list-item.selected .selected-color-white{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis)) !important}.checks-list-item.selected .selected-color-white:focus-visible{outline:none !important}.checks-list-item.selected:focus-within{outline:2px solid var(--fgColor-onEmphasis, var(--color-fg-on-emphasis)) !important;outline-offset:-4px}.authorized-pushers{width:440px}.authorized-pushers .add-protected-branch-actor{display:block}.authorized-pushers .actor-limit-reached{display:none;padding:var(--base-size-8);font-size:12px}.authorized-pushers.at-limit .add-protected-branch-actor{display:none}.authorized-pushers.at-limit .actor-limit-reached{display:block;width:440px}.autocomplete-suggestion{display:inline-block}.autocomplete-suggestion-issue-number{margin-left:3px;color:var(--fgColor-muted, var(--color-fg-muted))}.automated-check-options{margin-top:var(--base-size-8)}.automated-check-options .listgroup-item label{font-size:inherit}.automated-check-options .listgroup-item input[type=checkbox]{float:none;margin-top:-2px;margin-right:var(--base-size-4);margin-left:0}.automated-check-options .label{margin-top:var(--base-size-4)}.code-scanning-timeline .TimelineItem:last-of-type.code-scanning-timeline-alert-comment::before{width:0}.collaborators{left:0;width:300px}@media(min-width: 768px){.collaborators{right:0;left:unset;width:352px}}.collaborators::before,.collaborators::after{display:none}.collaborators .collab-info{position:relative;top:25%;display:block}.TimelineItem:last-of-type.dependabot-alert-dismissal-comment::before{width:0}.hx_SelectMenu-modal-no-animation{animation:none}.hx_flex-avatar-stack{display:flex;align-items:center}.hx_flex-avatar-stack-item{min-width:0;max-width:var(--base-size-24)}.hx_flex-avatar-stack-item .avatar{display:block;background-color:var(--bgColor-default, var(--color-canvas-default));border:var(--borderWidth-thick) solid var(--bgColor-default, var(--color-canvas-default));box-shadow:none}.hx_flex-avatar-stack-item:last-of-type{flex-shrink:0;max-width:none}.hx_merge_queue_entry_status_icon{fill:none;background-color:rgba(0,0,0,0);border:none}.pull-discussion-timeline.is-pull-restorable .pull-request-ref-restore.last{display:block}.news-full{float:none;width:auto}.protected-branch-admin-permission{padding:var(--base-size-4);margin:-2px 0 -2px calc(var(--base-size-4)*-1);line-height:normal;border:var(--borderWidth-thin) solid rgba(0,0,0,0);border-radius:var(--borderRadius-medium)}.protected-branch-admin-permission.active{animation:toggle-color 1s ease-in-out 0s}@keyframes toggle-color{0%{background-color:rgba(0,0,0,0)}50%{color:#4c4a42;background-color:#fff9ea;border-color:#dfd8c2}100%{background-color:rgba(0,0,0,0)}}.repository-settings-actions [role=tab][aria-selected=true]{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default));border-color:var(--borderColor-severe-emphasis, var(--color-severe-emphasis))}.repository-settings-actions [role=tab][aria-selected=true] .UnderlineNav-octicon{color:var(--fgColor-muted, var(--color-fg-muted))}.branch-action-state-clean .branch-action-icon{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis));border:var(--borderWidth-thin) solid rgba(0,0,0,0)}.branch-action-state-clean .branch-action-body{border-color:var(--borderColor-success-emphasis, var(--color-success-emphasis))}.branch-action-state-clean .branch-action-body::after,.branch-action-state-clean .branch-action-body::before{position:absolute;top:11px;right:100%;left:calc(var(--base-size-8)*-1);display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.branch-action-state-clean .branch-action-body::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--bgColor-default, var(--color-canvas-default)), var(--bgColor-default, var(--color-canvas-default)))}.branch-action-state-clean .branch-action-body::before{background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis))}.branch-action-state-error .branch-action-icon,.is-merging .branch-action-state-error-if-merging .branch-action-icon{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-danger-emphasis, var(--color-danger-emphasis));border:var(--borderWidth-thin) solid rgba(0,0,0,0)}.branch-action-state-error .branch-action-body,.is-merging .branch-action-state-error-if-merging .branch-action-body{border-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.branch-action-state-error .branch-action-body::after,.branch-action-state-error .branch-action-body::before,.is-merging .branch-action-state-error-if-merging .branch-action-body::after,.is-merging .branch-action-state-error-if-merging .branch-action-body::before{position:absolute;top:11px;right:100%;left:calc(var(--base-size-8)*-1);display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.branch-action-state-error .branch-action-body::after,.is-merging .branch-action-state-error-if-merging .branch-action-body::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--bgColor-default, var(--color-canvas-default)), var(--bgColor-default, var(--color-canvas-default)))}.branch-action-state-error .branch-action-body::before,.is-merging .branch-action-state-error-if-merging .branch-action-body::before{background-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.branch-action-state-merged .branch-action-icon{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-done-emphasis, var(--color-done-emphasis));border:var(--borderWidth-thin) solid rgba(0,0,0,0)}.branch-action-state-merged .branch-action-body{border-color:var(--borderColor-done-emphasis, var(--color-done-emphasis))}.branch-action-state-merged .branch-action-body::after,.branch-action-state-merged .branch-action-body::before{position:absolute;top:11px;right:100%;left:calc(var(--base-size-8)*-1);display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.branch-action-state-merged .branch-action-body::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--bgColor-default, var(--color-canvas-default)), var(--bgColor-default, var(--color-canvas-default)))}.branch-action-state-merged .branch-action-body::before{background-color:var(--borderColor-done-emphasis, var(--color-done-emphasis))}.enqueued-pull-request .branch-action-body::after,.enqueued-pull-request .branch-action-body::before{position:absolute;top:11px;right:100%;left:calc(var(--base-size-8)*-1);display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.enqueued-pull-request .branch-action-body::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--bgColor-default, var(--color-canvas-default)), var(--bgColor-default, var(--color-canvas-default)))}.enqueued-pull-request .branch-action-body::before{background-color:var(--borderColor-attention-emphasis, var(--color-attention-emphasis))}.protected-branches{margin-top:var(--base-size-16);margin-bottom:var(--base-size-16)}.protected-branch-options{margin-left:var(--base-size-16);opacity:.5}.protected-branch-options.active{opacity:1}.protected-branch-reviews.on .require-code-owner-review,.protected-branch-reviews.on .reviews-dismiss-on-push,.protected-branch-reviews.on .reviews-include-dismiss,.protected-branch-reviews.on .ignore-approvals-from-contributors,.protected-branch-reviews.on .require-last-push-approval,.protected-branch-reviews.on .allow-force-pushes,.protected-branch-reviews.on .require-approving-reviews{display:block}.protected-branch-reviews .require-code-owner-review,.protected-branch-reviews .reviews-dismiss-on-push,.protected-branch-reviews .reviews-include-dismiss,.protected-branch-reviews .ignore-approvals-from-contributors,.protected-branch-reviews .require-last-push-approval,.protected-branch-reviews .allow-force-pushes,.protected-branch-reviews .require-approving-reviews{display:none}.protected-branch-authorized-pushers-table,.protected-branch-pushers-table{margin-top:var(--base-size-8)}.protected-branch-authorized-pushers-table .boxed-group-inner,.protected-branch-pushers-table .boxed-group-inner{max-height:350px;overflow-y:auto}.protected-branch-authorized-pushers-table .table-list,.protected-branch-pushers-table .table-list{border-bottom:0}.protected-branch-authorized-pushers-table .table-list-cell,.protected-branch-pushers-table .table-list-cell{vertical-align:middle}.protected-branch-authorized-pushers-table .table-list-cell:first-child,.protected-branch-pushers-table .table-list-cell:first-child{width:100%}.protected-branch-authorized-pushers-table .avatar,.protected-branch-authorized-pushers-table .octicon-jersey,.protected-branch-authorized-pushers-table .octicon-organization,.protected-branch-pushers-table .avatar,.protected-branch-pushers-table .octicon-jersey,.protected-branch-pushers-table .octicon-organization{width:36px;margin-right:var(--base-size-8);text-align:center}.pull-merging .pull-merging-error{display:none}.pull-merging.is-error .pull-merging-error{display:block}.pull-merging.is-error .merge-pr{display:none}.merge-pr{padding-top:var(--base-size-8);margin:var(--base-size-16) 0 0;border-top:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.merge-pr.is-squashing .commit-author-fields{display:none}.merge-pr.is-squashing .commit-author-fields.current-user-is-author{display:block}.merge-pr.is-rebasing .commit-form-fields{display:none;transition:opacity .15s linear 0s,margin-top .25s ease .1s}.merge-pr .btn-group-merge,.merge-pr .btn-group-merge-group,.merge-pr .merge-queue-info,.merge-pr .merge-queue-time-to-merge,.merge-pr.is-squashing .btn-group-squash,.merge-pr.is-rebasing .btn-group-rebase,.merge-pr.is-updating-via-merge .btn-group-update-merge,.merge-pr.is-updating-via-rebase .btn-group-update-rebase,.merge-pr.is-merging-solo .btn-group-merge-solo,.merge-pr.is-merging-jump .btn-group-merge-jump,.merge-pr.is-merging-group .btn-group-merge-group,.merge-pr.is-merging .btn-group-merge-directly,.merge-pr.is-merging .merging-directly-warning{display:inline-block}.merge-pr .merging-body,.merge-pr .rebasing-body,.merge-pr .squashing-body,.merge-pr .merging-body-merge-warning,.merge-pr .merging-body-squash-warning,.merge-pr .merging-body-rebase-warning,.merge-pr .merging-directly-warning,.merge-pr .squash-commit-author-email-info,.merge-pr .merge-commit-author-email-info,.merge-pr.is-merging .merge-queue-info,.merge-pr.is-merging .branch-action-state-error-if-merging .merging-body,.merge-pr.is-squashing .branch-action-state-error-if-squashing .squashing-body,.merge-pr.is-rebasing .branch-action-state-error-if-rebasing .rebasing-body{display:none}.merge-pr.is-merging .merging-body,.merge-pr.is-merging .merge-commit-author-email-info,.merge-pr.is-merging-solo .merging-body,.merge-pr.is-merging-jump .merging-body,.merge-pr.is-merging-group .merging-body,.merge-pr.is-rebasing .rebasing-body,.merge-pr.is-squashing .squashing-body,.merge-pr.is-squashing .squash-commit-author-email-info,.merge-pr.is-merging .branch-action-state-error-if-merging .merging-body-merge-warning,.merge-pr.is-squashing .branch-action-state-error-if-squashing .merging-body-squash-warning,.merge-pr.is-rebasing .branch-action-state-error-if-rebasing .merging-body-rebase-warning{display:block}.merge-pr .btn-group-squash,.merge-pr .btn-group-merge-solo,.merge-pr .btn-group-merge-jump,.merge-pr .btn-group-merge-directly,.merge-pr .btn-group-rebase,.merge-pr .btn-group-update-merge,.merge-pr .btn-group-update-rebase,.merge-pr.is-squashing .btn-group-merge,.merge-pr.is-rebasing .btn-group-merge,.merge-pr.is-merging-solo .btn-group-merge-group,.merge-pr.is-merging-jump .btn-group-merge-group,.merge-pr.is-merging .btn-group-merge-group{display:none;margin-left:0}.merge-pr.open .merge-branch-form{display:block}.merge-pr.open .branch-action{display:none}.merge-pr.is-merging-jump.open .queue-branch-form,.merge-pr.is-merging-group.open .queue-branch-form,.merge-pr.is-merging-solo.open .queue-branch-form{display:block}.discussion-timeline-actions .merge-pr{padding-top:0;border-top:0}.merge-branch-heading{margin:0;line-height:1;color:var(--fgColor-default, var(--color-fg-default))}.merge-branch-prh-output{margin-top:var(--base-size-8)}.merge-branch-manually{display:none;padding-top:var(--base-size-16);margin-top:var(--base-size-16);background-color:rgba(0,0,0,0);border-top:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.merge-branch-manually p{margin-bottom:0}.merge-branch-manually h3{margin-bottom:var(--base-size-8)}.merge-branch-manually .intro{padding-bottom:var(--base-size-8);margin-top:0}.merge-branch-manually .step{margin:var(--base-size-16) 0 var(--base-size-4)}.open .merge-branch-manually{display:block}.post-merge-message{padding:var(--base-size-16)}.status-meta-file-name{padding:.2em .4em;margin:0;font-size:85%;background-color:rgba(27,31,35,.05);border-radius:var(--borderRadius-medium)}.commit-form{position:relative;padding:var(--base-size-16);border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-medium)}.commit-form::after,.commit-form::before{position:absolute;top:11px;right:100%;left:calc(var(--base-size-8)*-1);display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.commit-form::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--bgColor-default, var(--color-canvas-default)), var(--bgColor-default, var(--color-canvas-default)))}.commit-form::before{background-color:var(--borderColor-default, var(--color-border-default))}.commit-form .input-block{margin-top:var(--base-size-8);margin-bottom:var(--base-size-8)}.commit-form-avatar{float:left;margin-left:calc(var(--base-size-64)*-1);border-radius:var(--borderRadius-medium)}.commit-form-actions::before{display:table;content:""}.commit-form-actions::after{display:table;clear:both;content:""}.commit-form-actions .BtnGroup{margin-right:var(--base-size-4)}.merge-commit-message{resize:vertical}@media(max-width: 768px){.commit-form::after,.commit-form::before{display:none !important}}.commit-toolbar{top:var(--base-sticky-header-height, 0)}.full-commit{padding:var(--base-size-8) var(--base-size-8) 0;margin:var(--base-size-8) 0;font-size:14px;background:var(--bgColor-muted, var(--color-neutral-subtle));border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-medium)}.full-commit:first-child{margin-top:0}.full-commit .commit-build-statuses .status-checks-dropdown{width:500px}.full-commit div.commit-title{font-size:16px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.full-commit .branches-list{display:inline;margin-right:var(--base-size-8);margin-left:2px;vertical-align:middle;list-style:none}.full-commit .branches-list li{display:inline-block;padding-left:var(--base-size-4);font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.full-commit .branches-list li::before{padding-right:var(--base-size-4);font-weight:var(--base-text-weight-normal, 400);content:"+"}.full-commit .branches-list li:first-child{padding-left:0}.full-commit .branches-list li:first-child::before{padding-right:0;content:""}.full-commit .branches-list li.loading{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.full-commit .branches-list li.pull-request{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.full-commit .branches-list li.pull-request::before{margin-left:calc(var(--base-size-8)*-1);content:""}.full-commit .branches-list li.pull-request-error{margin-bottom:-1px}.full-commit .branches-list li a{color:inherit}.full-commit .commit-meta{padding:var(--base-size-8);margin-right:calc(var(--base-size-8)*-1);margin-left:calc(var(--base-size-8)*-1);background:var(--bgColor-default, var(--color-canvas-default));border-top:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:var(--borderRadius-medium);border-bottom-left-radius:var(--borderRadius-medium)}.full-commit .sha-block{margin-left:var(--base-size-16);font-size:12px;line-height:24px;color:var(--fgColor-muted, var(--color-fg-muted))}.full-commit .sha-block>.sha{color:var(--fgColor-default, var(--color-fg-default))}.full-commit .sha-block>a{color:var(--fgColor-default, var(--color-fg-default));text-decoration:none;border-bottom:var(--borderWidth-thin) dotted var(--borderColor-muted, var(--color-border-muted))}.full-commit .sha-block>a:hover{border-bottom:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.full-commit .commit-desc{display:block;margin:calc(var(--base-size-4)*-1) 0 var(--base-size-8)}.full-commit .commit-desc pre{max-width:100%;overflow:visible;font-size:13px;word-wrap:break-word}.merge-branch-form,.queue-branch-form{display:none;padding-left:60px;margin:var(--base-size-16) 0}.merge-branch-form .commit-form,.queue-branch-form .commit-form{border-color:var(--borderColor-success-emphasis, var(--color-success-emphasis))}.merge-branch-form .commit-form::before,.queue-branch-form .commit-form::before{display:none}@media(min-width: 768px){.merge-branch-form .commit-form::before,.queue-branch-form .commit-form::before{display:block;border-right-color:var(--borderColor-default, var(--color-border-default))}}.merge-branch-form .commit-form::after,.queue-branch-form .commit-form::after{display:none}@media(min-width: 768px){.merge-branch-form .commit-form::after,.queue-branch-form .commit-form::after{display:block}}.merge-branch-form.error .commit-form,.merge-branch-form.danger .commit-form,.queue-branch-form.error .commit-form,.queue-branch-form.danger .commit-form{border-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.merge-branch-form.error .commit-form::before,.merge-branch-form.danger .commit-form::before,.queue-branch-form.error .commit-form::before,.queue-branch-form.danger .commit-form::before{border-right-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.commit-form-fields{transition:opacity .15s linear .1s,margin-top .25s ease 0s}.compare-pr-header{display:none}.is-pr-composer-expanded .compare-show-header{display:none}.is-pr-composer-expanded .compare-pr-header{display:block}.range-editor{position:relative;padding:var(--base-size-4) var(--base-size-16) var(--base-size-4) var(--base-size-40);margin-top:var(--base-size-16);margin-bottom:var(--base-size-16);background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-medium)}.range-editor .dots{font-size:16px}.range-editor .select-menu{position:relative;display:inline-block}.range-editor .select-menu.fork-suggester{display:none}.range-editor .branch-name{line-height:22px}.range-editor .branch .css-truncate-target,.range-editor .fork-suggester .css-truncate-target{max-width:180px}.range-editor .pre-mergability{display:inline-block;padding:var(--base-size-4);line-height:26px;vertical-align:middle}.range-editor .pre-mergability .octicon{vertical-align:text-bottom}.range-editor.is-cross-repo .select-menu.fork-suggester{display:inline-block}.range-editor-icon{float:left;margin-top:var(--base-size-8);margin-left:calc(var(--base-size-24)*-1);color:var(--fgColor-muted, var(--color-fg-muted))}.range-cross-repo-pair{display:inline-block;padding:var(--base-size-4);white-space:nowrap}.details-collapse .collapse{position:relative;display:none;height:0;overflow:hidden;transition:height .35s ease-in-out}.details-collapse.open .collapse{display:block;height:auto;overflow:visible}:root{--pr-toolbar-sticky-header-height: max(var(--sticky-is-stuck-calculated-height, 60px), 60px)}.diffbar{background-color:var(--bgColor-default, var(--color-canvas-default))}.diffbar .show-if-stuck{display:none}.diffbar .container{width:auto}.diffbar .table-of-contents{margin-bottom:0}.diffbar .table-of-contents ol{margin-bottom:calc(var(--base-size-16)*-1)}.diffbar .table-of-contents li{border-top:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted))}.diffbar .table-of-contents li:first-child{border-top:0}.diffbar [role^=menuitem]:focus:not(.is-range-selected) .text-emphasized,.diffbar [role^=menuitem]:hover:not(.is-range-selected) .text-emphasized{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.is-stuck .diffbar .show-if-stuck,.position-stuck .diffbar .show-if-stuck{display:block}.is-stuck .diffbar .diffstat,.position-stuck .diffbar .diffstat{display:none}.is-stuck .diffbar .stale-files-tab,.position-stuck .diffbar .stale-files-tab{margin-top:calc(var(--base-size-8)*-1)}.diffbar-item{float:left;font-size:12px;vertical-align:middle}.full-width .diffbar .container{padding-right:0;padding-left:0}.pr-toolbar{position:sticky;top:0;z-index:29;min-height:var(--pr-toolbar-sticky-header-height);padding:0 var(--base-size-16);margin:calc(var(--base-size-16)*-1) calc(var(--base-size-16)*-1) 0}.pr-toolbar.js-sticky-is-stuck{max-width:100vw;padding-block:var(--base-size-8);padding-inline:var(--base-size-16);margin:calc(var(--base-size-16)*-1) calc(var(--base-size-32)*-1) 0;inset:-1px 0 auto}@media(min-width: 768px){.pr-toolbar.js-sticky-is-stuck{padding-inline:var(--base-size-24)}}@media(min-width: 1012px){.pr-toolbar.js-sticky-is-stuck{padding-inline:var(--base-size-32)}}.pr-toolbar.js-sticky-is-stuck.position-stuck{background-color:var(--bgColor-default, var(--color-canvas-default));border-bottom:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted));box-shadow:var(--shadow-resting-medium)}.pr-toolbar .float-right .diffbar-item{margin-right:0}.pr-toolbar .float-right .diffbar-item+.diffbar-item{margin-left:var(--base-size-16)}.pr-toolbar.is-stuck{height:var(--pr-toolbar-sticky-header-height);background-color:var(--bgColor-default, var(--color-canvas-default))}.files-next-bucket .file,.files-next-bucket .full-commit{margin-top:0;margin-bottom:var(--base-size-16)}.files-bucket{margin-bottom:var(--base-size-16)}.ds-action-prompt-container{height:auto;background-image:url("/assets/ds-actions-prompt-banner-mobile-e5e057312e9f.png");background-repeat:no-repeat;background-position:top right}.ds-action-prompt-container .content{width:95%}@media(min-width: 768px){.ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-708e9c012f66.png")}.ds-action-prompt-container .content{width:80%}}[data-color-mode=light][data-light-theme*=dark] .ds-action-prompt-container,[data-color-mode=dark][data-dark-theme*=dark] .ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-mobile-dark-3f109897c743.png")}@media(min-width: 768px){[data-color-mode=light][data-light-theme*=dark] .ds-action-prompt-container,[data-color-mode=dark][data-dark-theme*=dark] .ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-dark-7b6a0129760b.png")}}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark] .ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-mobile-dark-3f109897c743.png")}}@media(prefers-color-scheme: light)and (min-width: 768px){[data-color-mode=auto][data-light-theme*=dark] .ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-dark-7b6a0129760b.png")}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark] .ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-mobile-dark-3f109897c743.png")}}@media(prefers-color-scheme: dark)and (min-width: 768px){[data-color-mode=auto][data-dark-theme*=dark] .ds-action-prompt-container{background-image:url("/assets/ds-actions-prompt-banner-dark-7b6a0129760b.png")}}.form-group .edit-action{opacity:.6}.form-group .form-field-hover{background-color:none;border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.form-group:hover .edit-action{cursor:pointer;opacity:.7}.form-group:hover .form-field-hover{cursor:pointer;border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.edit-labels{display:none}.preview-section{display:block}.edit-section{display:none}.Box .section-focus .preview-section{display:none}.Box .section-focus .edit-section{display:block}.template-previews{max-width:768px}.template-previews .Box .expand-group{display:none;height:0}.template-previews .Box .dismiss-preview-button{display:none}.template-previews .Box.expand-preview .expand-group{display:block;height:100%;transition:height 3s}.template-previews .Box.expand-preview .preview-button{display:none}.template-previews .Box.expand-preview .dismiss-preview-button{display:inline}.template-previews .discussion-sidebar-heading{font-size:14px;color:var(--fgColor-neutral, var(--color-neutral-emphasis))}.template-previews .discussion-sidebar-heading:hover{color:var(--fgColor-accent, var(--color-accent-emphasis))}.placeholder-box{border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.quick-pull-new-branch-icon{top:9px;left:10px}.milestone-description-html{display:none}.milestone-description{margin-top:var(--base-size-4)}.milestone-description .expand-more{color:var(--fgColor-accent, var(--color-accent-fg));cursor:pointer}.milestone-description .expand-more:hover{text-decoration:underline}.milestone-description.open .milestone-description-plaintext{display:none}.milestone-description.open .milestone-description-html{display:block}.milestone-progress{width:auto;max-width:420px}.milestone-progress .progress-bar{margin-top:var(--base-size-8);margin-bottom:var(--base-size-12)}.milestone-meta{font-size:14px}.milestone-meta-item{display:inline-block;margin-right:var(--base-size-8)}.milestone-meta-item .octicon{width:16px;text-align:center}.milestone-title{width:500px}.milestone-title-link{margin-top:0;margin-bottom:var(--base-size-4);font-size:24px;font-weight:var(--base-text-weight-normal, 400);line-height:1.2}.milestone-title-link a{color:var(--fgColor-default, var(--color-fg-default), #333)}.milestone-title-link a:hover{color:var(--fgColor-accent, var(--color-accent-fg))}.milestones-flexbox-gap{gap:10px}.table-list-milestones .stats{gap:0 15px}.table-list-milestones .table-list-cell{padding:var(--base-size-16) var(--base-size-16)}.table-list-milestones .stat{display:inline-block;font-size:14px;font-weight:var(--base-text-weight-semibold, 600);line-height:1.2;color:var(--fgColor-muted, var(--color-fg-muted));white-space:nowrap}.table-list-milestones .stat a{color:inherit}.table-list-milestones .stat-label{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.hx_Layout.hx_Layout--sidebar-hidden{grid-auto-flow:row;grid-gap:0;grid-template-columns:1fr}.hx_Layout.hx_Layout--sidebar-hidden .Layout-sidebar{display:none}.hx_Layout.hx_Layout--sidebar-hidden .Layout-main{grid-column:auto}.hx_Layout--sidebar{top:60px;box-sizing:border-box;overscroll-behavior:contain}.issue-reorder-warning{z-index:110}.new-pr-form{margin-top:var(--base-size-16);margin-bottom:var(--base-size-16)}.new-pr-form::before{display:table;content:""}.new-pr-form::after{display:table;clear:both;content:""}.markdown-body .highlight pre:has(+.zeroclipboard-container){min-height:52px}.table-of-contents{margin:var(--base-size-16) 0}.table-of-contents li{padding:var(--base-size-8) 0;list-style-type:none}.table-of-contents li+li{border-top:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted))}.table-of-contents li>.octicon{margin-right:var(--base-size-4)}.table-of-contents .toc-diff-stats{padding-left:var(--base-size-16);line-height:26px}.table-of-contents .toc-diff-stats .octicon{margin-top:var(--base-size-4);margin-left:calc(var(--base-size-16)*-1);color:hsl(213.3333333333,10.843373494%,79.7254901961%)}.table-of-contents .toc-diff-stats .btn-link{font-weight:var(--base-text-weight-semibold, 600)}.table-of-contents .toc-diff-stats+.content{padding-top:var(--base-size-4)}.table-of-contents .octicon-diff-removed{color:var(--fgColor-danger, var(--color-danger-fg))}.table-of-contents .octicon-diff-renamed{color:var(--fgColor-muted, var(--color-fg-muted))}.table-of-contents .octicon-diff-modified{color:var(--fgColor-attention, var(--color-attention-fg))}.table-of-contents .octicon-diff-added{color:var(--fgColor-success, var(--color-success-fg))}
/*# sourceMappingURL=index.scss.map */

/*# sourceMappingURL=repository-d870412a478c.css.map*/