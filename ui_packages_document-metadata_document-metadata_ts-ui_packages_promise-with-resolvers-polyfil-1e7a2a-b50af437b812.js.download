"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_document-metadata_document-metadata_ts-ui_packages_promise-with-resolvers-polyfil-1e7a2a"],{78924:(e,t,n)=>{n.d(t,{I:()=>o});let o=(0,n(96540).createContext)(null)},52811:(e,t,n)=>{n.d(t,{C:()=>r,i:()=>i});var o=n(96679),a=n(27851),l=n(46493);function r(e,t){(0,a.G7)("arianotify_comprehensive_migration")?i(s(e),{...t,element:t?.element??e}):(0,a.G7)("primer_live_region_element")&&t?.element===void 0?(0,l.Cj)(e,{politeness:t?.assertive?"assertive":"polite"}):i(s(e),t)}function i(e,t){let{assertive:n,element:r}=t??{};(0,a.G7)("arianotify_comprehensive_migration")&&"ariaNotify"in Element.prototype?(r||document.body).ariaNotify(e):(0,a.G7)("primer_live_region_element")&&void 0===r?(0,l.iP)(e,{politeness:n?"assertive":"polite"}):function(e,t,n){let a=n??o.XC?.querySelector(t?"#js-global-screen-reader-notice-assertive":"#js-global-screen-reader-notice");a&&(a.textContent===e?a.textContent=`${e}\u00A0`:a.textContent=e)}(e,n,r)}function s(e){return(e.getAttribute("aria-label")||e.innerText||"").trim()}},39627:(e,t,n)=>{n.d(t,{D:()=>l,Y:()=>r});var o=n(52811),a=n(96679);function l(e){if(!a.XC)return;let t=a.XC.querySelector("title"),n=a.XC.createElement("title");n.textContent=e,t?t.textContent!==e&&(t.replaceWith(n),(0,o.i)(e)):(a.XC.head.appendChild(n),(0,o.i)(e))}function r(e){return document.body.classList.contains("logged-out")?`${e} \xb7 GitHub`:e}},13233:(e,t,n)=>{n.d(t,{l:()=>o});let o=()=>void 0},7531:(e,t,n)=>{n.d(t,{Y:()=>o});function o(){let e={};return e.promise=new Promise((t,n)=>{e.resolve=t,e.reject=n}),e}},41764:(e,t,n)=>{n.d(t,{A:()=>i});let{getItem:o,setItem:a,removeItem:l}=(0,n(85351).A)("localStorage"),r="REACT_PROFILING_ENABLED",i={enable:()=>a(r,"true"),disable:()=>l(r),isEnabled:()=>!!o(r)}},64899:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(17515),a=n(96540);function l(){let e=(0,a.useRef)(!1),t=(0,a.useCallback)(()=>e.current,[]);return(0,o.N)(()=>(e.current=!0,()=>{e.current=!1}),[]),t}},17515:(e,t,n)=>{n.d(t,{N:()=>l});var o=n(96679),a=n(96540);let l=void 0!==o.cg?.document?.createElement?a.useLayoutEffect:a.useEffect},47019:(e,t,n)=>{n.d(t,{A:()=>l});var o=n(64899),a=n(96540);let l=function(e){let t=(0,o.A)(),[n,l]=(0,a.useState)(e);return[n,(0,a.useCallback)(e=>{t()&&l(e)},[t])]}},26033:(e,t,n)=>{n.d(t,{y:()=>r});var o=n(74848),a=n(21728),l=n(78924);function r(e){let t,n,r,i=(0,a.c)(7),{children:s,appName:d,category:c,metadata:u}=e;return i[0]!==d||i[1]!==c||i[2]!==u?(n={appName:d,category:c,metadata:u},i[0]=d,i[1]=c,i[2]=u,i[3]=n):n=i[3],t=n,i[4]!==s||i[5]!==t?(r=(0,o.jsx)(l.I.Provider,{value:t,children:s}),i[4]=s,i[5]=t,i[6]=r):r=i[6],r}try{r.displayName||(r.displayName="AnalyticsProvider")}catch{}},59760:(e,t,n)=>{n.d(t,{P:()=>i});var o=n(74848),a=n(21728),l=n(23815);let r={ControlGroupFix:"ContrastSetting-module__ControlGroupFix--ujUVH"};function i(e){let t,n,i,s,d,c,u,m,h,p,f,b,y=(0,a.c)(30),{lightModeValue:g,darkModeValue:x,onChange:C,border:v}=e,j=void 0===v||v;y[0]!==x||y[1]!==g?(t=()=>"enabled"===g||"enabled"===x?"enabled":"disabled",y[0]=x,y[1]=g,y[2]=t):t=y[2];let N=t;y[3]!==g||y[4]!==C?(n=()=>{C({light:"enabled"===g?"disabled":"enabled"})},y[3]=g,y[4]=C,y[5]=n):n=y[5];let _=n;y[6]!==x||y[7]!==C?(i=()=>{C({dark:"enabled"===x?"disabled":"enabled"})},y[6]=x,y[7]=C,y[8]=i):i=y[8];let I=i;y[9]!==N||y[10]!==C?(s=()=>{let e="enabled"===N()?"disabled":"enabled";C({light:e,dark:e})},y[9]=N,y[10]=C,y[11]=s):s=y[11];let k=s;y[12]===Symbol.for("react.memo_cache_sentinel")?(d=(0,o.jsx)(l.t.Title,{id:"increase-contrast-label",as:"h2",children:"Increase contrast"}),y[12]=d):d=y[12],y[13]===Symbol.for("react.memo_cache_sentinel")?(c=(0,o.jsx)(l.t.Description,{children:(0,o.jsx)("span",{id:"increase-contrast-description",children:"Enable high contrast for light or dark mode (or both) based on your system settings"})}),y[13]=c):c=y[13];let w="enabled"===N();y[14]!==w||y[15]!==k?(u=(0,o.jsxs)(l.t.Item,{contentsClassname:r.ControlGroupFix,children:[d,c,(0,o.jsx)(l.t.ToggleSwitch,{"aria-labelledby":"increase-contrast-label","aria-describedby":"increase-contrast-description",checked:w,onClick:k})]}),y[14]=w,y[15]=k,y[16]=u):u=y[16],y[17]===Symbol.for("react.memo_cache_sentinel")?(m=(0,o.jsx)(l.t.Title,{id:"light-mode-label",children:"Light mode"}),y[17]=m):m=y[17];let S="enabled"===g;y[18]!==S||y[19]!==_?(h=(0,o.jsxs)(l.t.Item,{nestedLevel:1,contentsClassname:r.ControlGroupFix,children:[m,(0,o.jsx)(l.t.ToggleSwitch,{"aria-labelledby":"light-mode-label",checked:S,onClick:_})]}),y[18]=S,y[19]=_,y[20]=h):h=y[20],y[21]===Symbol.for("react.memo_cache_sentinel")?(p=(0,o.jsx)(l.t.Title,{id:"dark-mode-label",children:"Dark mode"}),y[21]=p):p=y[21];let A="enabled"===x;return y[22]!==A||y[23]!==I?(f=(0,o.jsxs)(l.t.Item,{nestedLevel:1,contentsClassname:r.ControlGroupFix,children:[p,(0,o.jsx)(l.t.ToggleSwitch,{"aria-labelledby":"dark-mode-label",checked:A,onClick:I})]}),y[22]=A,y[23]=I,y[24]=f):f=y[24],y[25]!==j||y[26]!==h||y[27]!==f||y[28]!==u?(b=(0,o.jsxs)(l.t,{border:j,children:[u,h,f]}),y[25]=j,y[26]=h,y[27]=f,y[28]=u,y[29]=b):b=y[29],b}try{i.displayName||(i.displayName="ContrastSetting")}catch{}},23815:(e,t,n)=>{n.d(t,{t:()=>D});var o=n(74848);let a={ControlGroupContainer:"ControlGroupContainer-module__ControlGroupContainer--W4M1K",fullWidth:"ControlGroupContainer-module__fullWidth--DTxQK",border:"ControlGroupContainer-module__border--rm_Uv"};var l=n(34164);let r=({children:e,fullWidth:t=!1,border:n=!0,"data-testid":r,className:i})=>(0,o.jsx)("div",{"data-testid":r,className:(0,l.$)(a.ControlGroupContainer,t&&a.fullWidth,n&&a.border,i),children:e});try{r.displayName||(r.displayName="ControlGroupContainer")}catch{}var i=n(84217);let s={Heading:"Title-module__Heading--saopa"},d=({children:e,as:t="h3",sx:n,className:a,...r})=>(0,o.jsx)(i.A,{...r,as:t,sx:{...n},className:(0,l.$)(s.Heading,a),children:e});d.displayName="ControlGroup.Title";var c=n(52464),u=n(75177);let m={Text:"Description-module__Text--aisR2",Box:"Description-module__Box--OHUTv"},h=({children:e,...t})=>"string"==typeof e?(0,o.jsx)(c.A,{as:"p",className:m.Text,...t,children:e}):(0,o.jsx)(u.A,{as:"span",className:m.Box,...t,children:e});h.displayName="ControlGroup.Description";var p=n(24021),f=n(38621),b=n(81679),y=n(55847),g=n(87330);let x={Box:"Controls-module__Box--VPTk1",IconButton:"Controls-module__IconButton--_wVfj"},C=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});C.displayName="ControlGroup.Custom";let v=e=>(0,o.jsx)(b.A,{size:"small",...e});v.displayName="ControlGroup.ToggleSwitch";let j=e=>(0,o.jsx)(y.Q,{...e,children:e.children});j.displayName="ControlGroup.Button";let N=({value:e=null,...t})=>(0,o.jsxs)("div",{className:(0,l.$)("inlineEdit",x.Box),children:[(0,o.jsx)("span",{children:e}),(0,o.jsx)(g.K,{unsafeDisableTooltip:!0,icon:f.PencilIcon,"aria-label":"Edit",className:x.IconButton,...t})]});N.displayName="ControlGroup.InlineEdit";let _={container:"Item-module__container--l4xIR",disabled:"Item-module__disabled--urojv",contents:"Item-module__contents--gSkzV",leadingVisual:"Item-module__leadingVisual--Q6N6U",title:"Item-module__title--vfUpQ",description:"Item-module__description--ArTPU",blockControl:"Item-module__blockControl--EHxT9",trailingVisual:"Item-module__trailingVisual--idzpZ"},I={title:d,description:h,custom:C,toggle:v,button:j,inlineEdit:N},k=({children:e,nestedLevel:t=0,disabled:n=!1,contentsClassname:a})=>{let[{title:r,description:i,custom:s,toggle:d,button:c,inlineEdit:u}]=(0,p.H)(e,I),m=c||u||s||d;return(0,o.jsx)("div",{className:(0,l.$)("controlBoxContainer",_.container,n&&_.disabled),style:{"--nested-level":t},children:(0,o.jsxs)("div",{className:(0,l.$)(_.contents,a),children:[r&&(0,o.jsx)("div",{className:(0,l.$)(_.title,"titleBox"),children:r}),i&&(0,o.jsx)("div",{className:(0,l.$)(_.description,"descriptionBox"),children:i}),m&&(0,o.jsx)("div",{className:(0,l.$)(_.blockControl,"blockControl"),children:m})]})})};try{k.displayName||(k.displayName="Item")}catch{}var w=n(34614),S=n(38267);let A={container:"LinkItem-module__container--uZtuw",leadingIcon:"LinkItem-module__leadingIcon--_rwSU",linkIndicator:"LinkItem-module__linkIndicator--5_w0W"},T={title:d,description:h},E=(0,S.Ay)(w.A).withConfig({displayName:"LinkItem__StyledLink",componentId:"sc-715cadb5-0"})(["text-decoration:none;color:inherit;:hover{text-decoration:none;}::after{position:absolute;left:0;right:0;bottom:0;top:0;content:'';}:focus{text-decoration:underline;outline:none;}"]),$=({children:e,leadingIcon:t=null,href:n,value:a,nestedLevel:r=0})=>{let[i]=(0,p.H)(e,T),{title:s,description:d}=i;return(0,o.jsx)("div",{className:(0,l.$)(_.container,A.container),style:{"--nested-level":r},children:(0,o.jsxs)("div",{className:_.contents,children:[t?(0,o.jsx)("div",{className:_.leadingVisual,children:(0,o.jsx)("div",{className:A.leadingIcon,children:t})}):null,(0,o.jsx)(E,{href:n,className:_.title,children:s}),(0,o.jsx)("div",{className:(0,l.$)("descriptionBox",_.description),children:d}),(0,o.jsx)("div",{className:_.trailingVisual,children:(0,o.jsxs)("div",{className:A.linkIndicator,children:[a&&(0,o.jsx)("span",{children:a}),(0,o.jsx)(f.ChevronRightIcon,{})]})})]})})};try{E.displayName||(E.displayName="StyledLink")}catch{}try{$.displayName||($.displayName="LinkItem")}catch{}var P=n(10569),L=n(15385),R=n(96540);let G=({title:e,description:t,modes:n,...a})=>{let l=(0,R.useRef)(!1),r=n.find(e=>e.name===a.selectedMode),i=(0,R.useId)(),s=(0,R.useId)(),c=(0,R.useId)(),u=(0,R.useId)();(0,R.useEffect)(()=>{l.current=!1},[a.selectedMode]);let m=n[0];return 1===n.length&&B(m)?(0,o.jsxs)(k,{children:[(0,o.jsx)(d,{id:i,children:e}),(0,o.jsx)(h,{id:s,children:t}),(0,o.jsx)(C,{children:m.renderEditor({labelId:i,descriptionId:s})})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(k,{children:[(0,o.jsx)(d,{id:i,children:e}),(0,o.jsx)(h,{id:s,children:t}),(0,o.jsx)(C,{children:(0,o.jsx)(O,{modes:n,...a,labelId:i,descriptionId:s,onModeChange:e=>{l.current=!0,a.onModeChange?.(e)}})})]}),r&&B(r)&&(0,o.jsxs)(k,{children:[(0,o.jsx)(d,{id:c,children:r.editorLabel??r.label}),(0,o.jsx)(h,{id:u,children:r.editorDescription??r.description}),(0,o.jsx)(C,{children:r.renderEditor({shouldOpen:l.current,labelId:c,descriptionId:u})})]})]})},O=({selectedMode:e,onModeChange:t,modes:n,selectorIcon:a,labelId:l,descriptionId:r})=>{let i=n.find(t=>t.name===e),s=(0,R.useId)();return(0,o.jsxs)(P.W,{children:[(0,o.jsx)(P.W.Button,{leadingVisual:a,"aria-labelledby":`${l} ${s}`,"aria-describedby":r,children:(0,o.jsx)("span",{id:s,children:i?i.label:e})}),(0,o.jsx)(P.W.Overlay,{style:{width:"max-content",maxWidth:"500px"},children:(0,o.jsx)(L.l,{selectionVariant:"single",children:n.map(n=>(0,o.jsxs)(L.l.Item,{selected:n.name===e,onSelect:()=>t?.(n.name),children:[n.label,n.description&&(0,o.jsx)(L.l.Description,{variant:"block",children:n.description})]},n.name))})})]})},B=e=>e?.renderEditor!==void 0;try{G.displayName||(G.displayName="Selector")}catch{}try{O.displayName||(O.displayName="ModeSelector")}catch{}let D=Object.assign(r,{Item:k,LinkItem:$,Title:d,Description:h,Custom:C,ToggleSwitch:v,Button:j,InlineEdit:N,Selector:G});try{D.displayName||(D.displayName="ControlGroup")}catch{}},60674:(e,t,n)=>{n.d(t,{BP:()=>u,D3:()=>c,O8:()=>s});var o=n(74848),a=n(21728),l=n(96540),r=n(96679),i=n(17515);let s={ServerRender:"ServerRender",ClientHydrate:"ClientHydrate",ClientRender:"ClientRender"},d=(0,l.createContext)(s.ClientRender);function c(e){let t,n,c,u,m=(0,a.c)(8),{wasServerRendered:h,children:p}=e;m[0]!==h?(t=()=>r.X3?s.ServerRender:h?s.ClientHydrate:s.ClientRender,m[0]=h,m[1]=t):t=m[1];let[f,b]=(0,l.useState)(t);return m[2]!==f?(n=()=>{f!==s.ClientRender&&b(s.ClientRender)},c=[f],m[2]=f,m[3]=n,m[4]=c):(n=m[3],c=m[4]),(0,i.N)(n,c),m[5]!==p||m[6]!==f?(u=(0,o.jsx)(d.Provider,{value:f,children:p}),m[5]=p,m[6]=f,m[7]=u):u=m[7],u}function u(){return(0,l.useContext)(d)}try{d.displayName||(d.displayName="RenderPhaseContext")}catch{}try{c.displayName||(c.displayName="RenderPhaseProvider")}catch{}},99543:(e,t,n)=>{n.d(t,{Qn:()=>s,T8:()=>c,Y6:()=>m,k6:()=>u});var o=n(74848),a=n(65556),l=n(96540),r=n(13233),i=n(47019);let s=5e3,d=(0,l.createContext)({addToast:r.l,addPersistedToast:r.l,clearPersistedToast:r.l}),c=(0,l.createContext)({toasts:[],persistedToast:null});function u({children:e}){let[t,n]=(0,i.A)([]),[r,u]=(0,l.useState)(null),{safeSetTimeout:m}=(0,a.A)(),h=(0,l.useCallback)(function(e){n([...t,e]),m(()=>n(t.slice(1)),s)},[t,m,n]),p=(0,l.useCallback)(function(e){u(e)},[u]),f=(0,l.useCallback)(function(){u(null)},[u]),b=(0,l.useMemo)(()=>({addToast:h,addPersistedToast:p,clearPersistedToast:f}),[p,h,f]),y=(0,l.useMemo)(()=>({toasts:t,persistedToast:r}),[t,r]);return(0,o.jsx)(d.Provider,{value:b,children:(0,o.jsx)(c.Provider,{value:y,children:e})})}function m(){return(0,l.useContext)(d)}try{d.displayName||(d.displayName="ToastContext")}catch{}try{c.displayName||(c.displayName="InternalToastsContext")}catch{}try{u.displayName||(u.displayName="ToastContextProvider")}catch{}},42218:(e,t,n)=>{n.d(t,{V:()=>m});var o=n(74848),a=n(96540),l=n(99543),r=n(38621),i=n(65556),s=n(16255);let d={info:"",success:"Toast--success",error:"Toast--error"},c={info:(0,o.jsx)(r.InfoIcon,{}),success:(0,o.jsx)(r.CheckIcon,{}),error:(0,o.jsx)(r.StopIcon,{})},u=({message:e,timeToLive:t,icon:n,type:l="info",role:r="log"})=>{let[u,m]=a.useState(!0),{safeSetTimeout:h}=(0,i.A)();return(0,a.useEffect)(()=>{t&&h(()=>m(!1),t-300)},[h,t]),(0,o.jsx)(s.Z,{children:(0,o.jsx)("div",{className:"p-1 position-fixed bottom-0 left-0 mb-3 ml-3",children:(0,o.jsxs)("div",{className:`Toast ${d[l]} ${u?"Toast--animateIn":"Toast--animateOut"}`,id:"ui-app-toast","data-testid":`ui-app-toast-${l}`,role:r,children:[(0,o.jsx)("span",{className:"Toast-icon",children:n||c[l]}),(0,o.jsx)("span",{className:"Toast-content",children:e})]})})})};try{u.displayName||(u.displayName="Toast")}catch{}function m(){let{toasts:e,persistedToast:t}=(0,a.useContext)(l.T8);return(0,o.jsxs)(o.Fragment,{children:[e.map((e,t)=>(0,o.jsx)(u,{message:e.message,icon:e.icon,timeToLive:l.Qn,type:e.type,role:e.role},t)),t&&(0,o.jsx)(u,{message:t.message,icon:t.icon,type:t.type,role:t.role})]})}try{m.displayName||(m.displayName="Toasts")}catch{}},39595:(e,t,n)=>{let o;n.d(t,{CF:()=>f,p_:()=>T,FB:()=>u,Se:()=>_,aC:()=>S,zV:()=>A});let a=new WeakSet,l=new WeakMap;function r(e=document){if(l.has(e))return l.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)c(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&i(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let o={get closed(){return t},unsubscribe(){t=!0,l.delete(e),n.disconnect()}};return l.set(e,o),o}function i(e){for(let t of e.querySelectorAll("[data-action]"))c(t);e instanceof Element&&e.hasAttribute("data-action")&&c(e)}function s(e){let t=e.currentTarget;for(let n of d(t))if(e.type===n.type){let o=t.closest(n.tag);a.has(o)&&"function"==typeof o[n.method]&&o[n.method](e);let l=t.getRootNode();if(l instanceof ShadowRoot&&a.has(l.host)&&l.host.matches(n.tag)){let t=l.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*d(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function c(e){for(let t of d(e))e.addEventListener(t.type,s)}function u(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let o of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!o.closest(n))return o}for(let o of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(o.closest(n)===e)return o}let m=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),h=(e,t="property")=>{let n=m(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},p="attr";function f(e,t){w(e,p).add(t)}let b=new WeakSet;function y(e,t){if(b.has(e))return;b.add(e);let n=Object.getPrototypeOf(e),o=n?.constructor?.attrPrefix??"data-";for(let a of(t||(t=w(n,p)),t)){let t=e[a],n=h(`${o}${a}`),l={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?l={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(l={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,a,l),a in e&&!e.hasAttribute(n)&&l.set.call(e,t)}}let g=new Map,x=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),C=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},o=()=>t.abort();document.addEventListener("mousedown",o,n),document.addEventListener("touchstart",o,n),document.addEventListener("keydown",o,n),document.addEventListener("pointerdown",o,n)}),v={ready:()=>x,firstInteraction:()=>C,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},j=new WeakMap;function N(e){cancelAnimationFrame(j.get(e)||0),j.set(e,requestAnimationFrame(()=>{for(let t of g.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let o=n?.getAttribute("data-load-on")||"ready",a=o in v?v[o]:v.ready;for(let e of g.get(t)||[])a(t).then(e);g.delete(t),j.delete(e)}}}))}function _(e,t){for(let[n,o]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))g.has(n)||g.set(n,new Set),g.get(n).add(o);I(document)}function I(e){o||(o=new MutationObserver(e=>{if(g.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&N(e)})),N(e),o.observe(e,{subtree:!0,childList:!0})}let k=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let o=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,o)};let a=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,o){t.attributeChangedCallback(this,e,n,o,a)};let l=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,l)},set(e){l=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",o=e=>h(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...w(e.prototype,p)].map(o).concat(t),set(e){t=e}})}(e),function(e){let t=m(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,o;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(y(e),a.add(e),e.shadowRoot&&(i(o=e.shadowRoot),r(o)),i(e),r(e.ownerDocument),t?.call(e),e.shadowRoot)&&(i(n=e.shadowRoot),r(n),I(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,o,a){y(e),"data-catalyst"!==t&&a&&a.call(e,t,n,o)}};function w(e,t){if(!Object.prototype.hasOwnProperty.call(e,k)){let t=e[k],n=e[k]=new Map;if(t)for(let[e,o]of t)n.set(e,new Set(o))}let n=e[k];return n.has(t)||n.set(t,new Set),n.get(t)}function S(e,t){w(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return u(this,t)}})}function A(e,t){w(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let o of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)||n.push(o);for(let o of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)===this&&n.push(o);return n}})}function T(e){new CatalystDelegate(e)}}}]);
//# sourceMappingURL=ui_packages_document-metadata_document-metadata_ts-ui_packages_promise-with-resolvers-polyfil-1e7a2a-a30fe5e5675d.js.map