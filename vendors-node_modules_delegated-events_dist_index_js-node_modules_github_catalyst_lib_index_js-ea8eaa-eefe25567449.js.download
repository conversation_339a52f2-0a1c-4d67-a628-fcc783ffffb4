"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js","vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-ea8eaa","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_memoize_dist_esm_index_js"],{97797:(e,t,n)=>{function o(){if(!(this instanceof o))return new o;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{h:()=>E,A:()=>C,on:()=>k});var r,a=window.document.documentElement,i=a.matches||a.webkitMatchesSelector||a.mozMatchesSelector||a.oMatchesSelector||a.msMatchesSelector;o.prototype.matchesSelector=function(e,t){return i.call(e,t)},o.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},o.prototype.indexes=[];var s=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(s))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);else if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),o.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},r="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var d=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function u(e,t){var n,o,r,a,i,s,l=(e=e.slice(0).concat(e.default)).length,c=t,u=[];do if(d.exec(""),(r=d.exec(c))&&(c=r[3],r[2]||!c)){for(n=0;n<l;n++)if(i=(s=e[n]).selector(r[1])){for(o=u.length,a=!1;o--;)if(u[o].index===s&&u[o].key===i){a=!0;break}a||u.push({index:s,key:i});break}}while(r)return u}function f(e,t){return e.id-t.id}o.prototype.logDefaultIndexUsed=function(){},o.prototype.add=function(e,t){var n,o,a,i,s,l,c,d,f=this.activeIndexes,h=this.selectors,p=this.selectorObjects;if("string"==typeof e){for(o=0,p[(n={id:this.uid++,selector:e,data:t}).id]=n,c=u(this.indexes,e);o<c.length;o++)i=(d=c[o]).key,(s=function(e,t){var n,o,r;for(n=0,o=e.length;n<o;n++)if(r=e[n],t.isPrototypeOf(r))return r}(f,a=d.index))||((s=Object.create(a)).map=new r,f.push(s)),a===this.indexes.default&&this.logDefaultIndexUsed(n),(l=s.map.get(i))||(l=[],s.map.set(i,l)),l.push(n);this.size++,h.push(e)}},o.prototype.remove=function(e,t){if("string"==typeof e){var n,o,r,a,i,s,l,c,d=this.activeIndexes,f=this.selectors=[],h=this.selectorObjects,p={},g=1==arguments.length;for(r=0,n=u(this.indexes,e);r<n.length;r++)for(o=n[r],a=d.length;a--;)if(s=d[a],o.index.isPrototypeOf(s)){if(l=s.map.get(o.key))for(i=l.length;i--;)(c=l[i]).selector===e&&(g||c.data===t)&&(l.splice(i,1),p[c.id]=!0);break}for(r in p)delete h[r],this.size--;for(r in h)f.push(h[r].selector)}},o.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,o,r,a,i,s,l,c={},d=[],u=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,o=u.length;t<o;t++)for(n=0,a=u[t],r=(i=this.matches(a)).length;n<r;n++)c[(l=i[n]).id]?s=c[l.id]:(s={id:l.id,selector:l.selector,data:l.data,elements:[]},c[l.id]=s,d.push(s)),s.elements.push(a);return d.sort(f)},o.prototype.matches=function(e){if(!e)return[];var t,n,o,r,a,i,s,l,c,d,u,h=this.activeIndexes,p={},g=[];for(t=0,r=h.length;t<r;t++)if(l=(s=h[t]).element(e)){for(n=0,a=l.length;n<a;n++)if(c=s.map.get(l[n]))for(o=0,i=c.length;o<i;o++)!p[u=(d=c[o]).id]&&this.matchesSelector(e,d.selector)&&(p[u]=!0,g.push(d))}return g.sort(f)};var h={},p={},g=new WeakMap,b=new WeakMap,m=new WeakMap,y=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function v(e,t,n){var o=e[t];return e[t]=function(){return n.apply(e,arguments),o.apply(e,arguments)},e}function w(){g.set(this,!0)}function x(){g.set(this,!0),b.set(this,!0)}function A(){return m.get(this)||null}function S(e,t){y&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||y.get})}function _(e){if(function(e){try{return e.eventPhase,!0}catch(e){return!1}}(e)){var t=(1===e.eventPhase?p:h)[e.type];if(t){var n=function(e,t,n){var o=[],r=t;do{if(1!==r.nodeType)break;var a=e.matches(r);if(a.length){var i={node:r,observers:a};n?o.unshift(i):o.push(i)}}while(r=r.parentElement)return o}(t,e.target,1===e.eventPhase);if(n.length){v(e,"stopPropagation",w),v(e,"stopImmediatePropagation",x),S(e,A);for(var o=0,r=n.length;o<r&&!g.get(e);o++){var a=n[o];m.set(e,a.node);for(var i=0,s=a.observers.length;i<s&&!b.get(e);i++)a.observers[i].data.call(a.node,e)}m.delete(e),S(e)}}}}function k(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=!!r.capture,i=a?p:h,s=i[e];s||(s=new o,i[e]=s,document.addEventListener(e,_,a)),s.add(t,n)}function C(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=!!o.capture,a=r?p:h,i=a[e];i&&(i.remove(t,n),i.size||(delete a[e],document.removeEventListener(e,_,r)))}function E(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},39595:(e,t,n)=>{let o;n.d(t,{CF:()=>g,p_:()=>P,FB:()=>u,Se:()=>_,aC:()=>O,zV:()=>j});let r=new WeakSet,a=new WeakMap;function i(e=document){if(a.has(e))return a.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)d(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&s(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let o={get closed(){return t},unsubscribe(){t=!0,a.delete(e),n.disconnect()}};return a.set(e,o),o}function s(e){for(let t of e.querySelectorAll("[data-action]"))d(t);e instanceof Element&&e.hasAttribute("data-action")&&d(e)}function l(e){let t=e.currentTarget;for(let n of c(t))if(e.type===n.type){let o=t.closest(n.tag);r.has(o)&&"function"==typeof o[n.method]&&o[n.method](e);let a=t.getRootNode();if(a instanceof ShadowRoot&&r.has(a.host)&&a.host.matches(n.tag)){let t=a.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function d(e){for(let t of c(e))e.addEventListener(t.type,l)}function u(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let o of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!o.closest(n))return o}for(let o of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(o.closest(n)===e)return o}let f=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),h=(e,t="property")=>{let n=f(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},p="attr";function g(e,t){E(e,p).add(t)}let b=new WeakSet;function m(e,t){if(b.has(e))return;b.add(e);let n=Object.getPrototypeOf(e),o=n?.constructor?.attrPrefix??"data-";for(let r of(t||(t=E(n,p)),t)){let t=e[r],n=h(`${o}${r}`),a={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?a={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(a={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,r,a),r in e&&!e.hasAttribute(n)&&a.set.call(e,t)}}let y=new Map,v=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),w=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},o=()=>t.abort();document.addEventListener("mousedown",o,n),document.addEventListener("touchstart",o,n),document.addEventListener("keydown",o,n),document.addEventListener("pointerdown",o,n)}),x={ready:()=>v,firstInteraction:()=>w,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},A=new WeakMap;function S(e){cancelAnimationFrame(A.get(e)||0),A.set(e,requestAnimationFrame(()=>{for(let t of y.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let o=n?.getAttribute("data-load-on")||"ready",r=o in x?x[o]:x.ready;for(let e of y.get(t)||[])r(t).then(e);y.delete(t),A.delete(e)}}}))}function _(e,t){for(let[n,o]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))y.has(n)||y.set(n,new Set),y.get(n).add(o);k(document)}function k(e){o||(o=new MutationObserver(e=>{if(y.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&S(e)})),S(e),o.observe(e,{subtree:!0,childList:!0})}let C=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let o=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,o)};let r=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,o){t.attributeChangedCallback(this,e,n,o,r)};let a=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,a)},set(e){a=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",o=e=>h(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...E(e.prototype,p)].map(o).concat(t),set(e){t=e}})}(e),function(e){let t=f(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,o;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(m(e),r.add(e),e.shadowRoot&&(s(o=e.shadowRoot),i(o)),s(e),i(e.ownerDocument),t?.call(e),e.shadowRoot)&&(s(n=e.shadowRoot),i(n),k(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,o,r){m(e),"data-catalyst"!==t&&r&&r.call(e,t,n,o)}};function E(e,t){if(!Object.prototype.hasOwnProperty.call(e,C)){let t=e[C],n=e[C]=new Map;if(t)for(let[e,o]of t)n.set(e,new Set(o))}let n=e[C];return n.has(t)||n.set(t,new Set),n.get(t)}function O(e,t){E(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return u(this,t)}})}function j(e,t){E(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let o of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)||n.push(o);for(let o of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)===this&&n.push(o);return n}})}function P(e){new CatalystDelegate(e)}},5225:(e,t,n)=>{function o(...e){return JSON.stringify(e,(e,t)=>"object"==typeof t?t:String(t))}function r(e,t={}){let{hash:n=o,cache:a=new Map}=t;return function(...t){let o=n.apply(this,t);if(a.has(o))return a.get(o);let r=e.apply(this,t);return r instanceof Promise&&(r=r.catch(e=>{throw a.delete(o),e})),a.set(o,r),r}}n.d(t,{A:()=>r})}}]);
//# sourceMappingURL=vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-ea8eaa-22b803d679a3.js.map