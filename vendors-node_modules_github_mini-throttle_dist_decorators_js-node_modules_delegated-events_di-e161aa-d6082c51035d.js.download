"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_mini-throttle_dist_decorators_js-node_modules_delegated-events_di-e161aa"],{45062:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(70170);function i(e=0,t={}){return(n,i,s)=>{if(!s||"function"!=typeof s.value)throw Error("debounce can only decorate functions");let o=s.value;s.value=(0,r.s)(o,e,t),Object.defineProperty(n,i,s)}}},97797:(e,t,n)=>{function r(){if(!(this instanceof r))return new r;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{h:()=>M,A:()=>j,on:()=>O});var i,s=window.document.documentElement,o=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector;r.prototype.matchesSelector=function(e,t){return o.call(e,t)},r.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},r.prototype.indexes=[];var l=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var a=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);else if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),r.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},i="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var u=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function h(e,t){var n,r,i,s,o,l,a=(e=e.slice(0).concat(e.default)).length,c=t,h=[];do if(u.exec(""),(i=u.exec(c))&&(c=i[3],i[2]||!c)){for(n=0;n<a;n++)if(o=(l=e[n]).selector(i[1])){for(r=h.length,s=!1;r--;)if(h[r].index===l&&h[r].key===o){s=!0;break}s||h.push({index:l,key:o});break}}while(i)return h}function d(e,t){return e.id-t.id}r.prototype.logDefaultIndexUsed=function(){},r.prototype.add=function(e,t){var n,r,s,o,l,a,c,u,d=this.activeIndexes,f=this.selectors,p=this.selectorObjects;if("string"==typeof e){for(r=0,p[(n={id:this.uid++,selector:e,data:t}).id]=n,c=h(this.indexes,e);r<c.length;r++)o=(u=c[r]).key,(l=function(e,t){var n,r,i;for(n=0,r=e.length;n<r;n++)if(i=e[n],t.isPrototypeOf(i))return i}(d,s=u.index))||((l=Object.create(s)).map=new i,d.push(l)),s===this.indexes.default&&this.logDefaultIndexUsed(n),(a=l.map.get(o))||(a=[],l.map.set(o,a)),a.push(n);this.size++,f.push(e)}},r.prototype.remove=function(e,t){if("string"==typeof e){var n,r,i,s,o,l,a,c,u=this.activeIndexes,d=this.selectors=[],f=this.selectorObjects,p={},g=1==arguments.length;for(i=0,n=h(this.indexes,e);i<n.length;i++)for(r=n[i],s=u.length;s--;)if(l=u[s],r.index.isPrototypeOf(l)){if(a=l.map.get(r.key))for(o=a.length;o--;)(c=a[o]).selector===e&&(g||c.data===t)&&(a.splice(o,1),p[c.id]=!0);break}for(i in p)delete f[i],this.size--;for(i in f)d.push(f[i].selector)}},r.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,r,i,s,o,l,a,c={},u=[],h=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,r=h.length;t<r;t++)for(n=0,s=h[t],i=(o=this.matches(s)).length;n<i;n++)c[(a=o[n]).id]?l=c[a.id]:(l={id:a.id,selector:a.selector,data:a.data,elements:[]},c[a.id]=l,u.push(l)),l.elements.push(s);return u.sort(d)},r.prototype.matches=function(e){if(!e)return[];var t,n,r,i,s,o,l,a,c,u,h,f=this.activeIndexes,p={},g=[];for(t=0,i=f.length;t<i;t++)if(a=(l=f[t]).element(e)){for(n=0,s=a.length;n<s;n++)if(c=l.map.get(a[n]))for(r=0,o=c.length;r<o;r++)!p[h=(u=c[r]).id]&&this.matchesSelector(e,u.selector)&&(p[h]=!0,g.push(u))}return g.sort(d)};var f={},p={},g=new WeakMap,m=new WeakMap,v=new WeakMap,y=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function b(e,t,n){var r=e[t];return e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)},e}function w(){g.set(this,!0)}function k(){g.set(this,!0),m.set(this,!0)}function x(){return v.get(this)||null}function T(e,t){y&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||y.get})}function S(e){if(function(e){try{return e.eventPhase,!0}catch(e){return!1}}(e)){var t=(1===e.eventPhase?p:f)[e.type];if(t){var n=function(e,t,n){var r=[],i=t;do{if(1!==i.nodeType)break;var s=e.matches(i);if(s.length){var o={node:i,observers:s};n?r.unshift(o):r.push(o)}}while(i=i.parentElement)return r}(t,e.target,1===e.eventPhase);if(n.length){b(e,"stopPropagation",w),b(e,"stopImmediatePropagation",k),T(e,x);for(var r=0,i=n.length;r<i&&!g.get(e);r++){var s=n[r];v.set(e,s.node);for(var o=0,l=s.observers.length;o<l&&!m.get(e);o++)s.observers[o].data.call(s.node,e)}v.delete(e),T(e)}}}}function O(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=!!i.capture,o=s?p:f,l=o[e];l||(l=new r,o[e]=l,document.addEventListener(e,S,s)),l.add(t,n)}function j(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!r.capture,s=i?p:f,o=s[e];o&&(o.remove(t,n),o.size||(delete s[e],document.removeEventListener(e,S,i)))}function M(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},50515:(e,t,n)=>{n.d(t,{JC:()=>y,KK:()=>SequenceTracker,Vy:()=>o,ai:()=>v,oc:()=>a,rd:()=>u});let Leaf=class Leaf{constructor(e){this.children=[],this.parent=e}delete(e){let t=this.children.indexOf(e);return -1!==t&&(this.children=this.children.slice(0,t).concat(this.children.slice(t+1)),0===this.children.length&&this.parent.delete(this),!0)}add(e){return this.children.push(e),this}};let RadixTrie=class RadixTrie{constructor(e){this.parent=null,this.children={},this.parent=e||null}get(e){return this.children[e]}insert(e){let t=this;for(let n=0;n<e.length;n+=1){let r=e[n],i=t.get(r);if(n===e.length-1)return i instanceof RadixTrie&&(t.delete(i),i=null),i||(i=new Leaf(t),t.children[r]=i),i;i instanceof Leaf&&(i=null),i||(i=new RadixTrie(t),t.children[r]=i),t=i}return t}delete(e){for(let t in this.children)if(this.children[t]===e){let e=delete this.children[t];return 0===Object.keys(this.children).length&&this.parent&&this.parent.delete(this),e}return!1}};let r={"\xa1":"1","\u2122":"2","\xa3":"3","\xa2":"4","\u221E":"5","\xa7":"6","\xb6":"7","\u2022":"8","\xaa":"9","\xba":"0","\u2013":"-","\u2260":"=","\u2044":"!","\u20AC":"@","\u2039":"#","\u203A":"$",\uFB01:"%",\uFB02:"^","\u2021":"&","\xb0":"*","\xb7":"(","\u201A":")","\u2014":"_","\xb1":"+",\u0153:"q","\u2211":"w","\xae":"r","\u2020":"t","\xa5":"y","\xf8":"o",\u03C0:"p","\u201C":"[","\u2018":"]","\xab":"\\",\u0152:"Q","\u201E":"W","\xb4":"E","\u2030":"R",\u02C7:"T","\xc1":"Y","\xa8":"U",\u02C6:"I","\xd8":"O","\u220F":"P","\u201D":"{","\u2019":"}","\xbb":"|","\xe5":"a","\xdf":"s","\u2202":"d",\u0192:"f","\xa9":"g","\u02D9":"h","\u2206":"j","\u02DA":"k","\xac":"l","\u2026":";","\xe6":"'","\xc5":"A","\xcd":"S","\xce":"D","\xcf":"F","\u02DD":"G","\xd3":"H","\xd4":"J","\uF8FF":"K","\xd2":"L","\xda":":","\xc6":'"',\u03A9:"z","\u2248":"x","\xe7":"c","\u221A":"v","\u222B":"b","\xb5":"m","\u2264":",","\u2265":".","\xf7":"/","\xb8":"Z","\u02DB":"X","\xc7":"C","\u25CA":"V",\u0131:"B","\u02DC":"N","\xc2":"M","\xaf":"<","\u02D8":">","\xbf":"?"},i={"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+","[":"{","]":"}","\\":"|",";":":","'":'"',",":"<",".":">","/":"?",q:"Q",w:"W",e:"E",r:"R",t:"T",y:"Y",u:"U",i:"I",o:"O",p:"P",a:"A",s:"S",d:"D",f:"F",g:"G",h:"H",j:"J",k:"K",l:"L",z:"Z",x:"X",c:"C",v:"V",b:"B",n:"N",m:"M"},s={" ":"Space","+":"Plus"};function o(e,t=navigator.platform){var n,a,u;let{ctrlKey:h,altKey:d,metaKey:f,shiftKey:p,key:g}=e,m=[];for(let[e,t]of[h,d,f,p].entries())t&&m.push(l[e]);if(!l.includes(g)){let e=m.includes("Alt")&&c.test(t)&&null!=(n=r[g])?n:g,o=m.includes("Shift")&&c.test(t)&&null!=(a=i[e])?a:e,l=null!=(u=s[o])?u:o;m.push(l)}return m.join("+")}let l=["Control","Alt","Meta","Shift"];function a(e,t){let n;var r=function(e,t){var n;let r="undefined"==typeof window?void 0:window,i=c.test(null!=(n=null!=t?t:null==r?void 0:r.navigator.platform)?n:"")?"Meta":"Control";return e.replace("Mod",i)}(e,t);let i=r.split("+").pop(),s=[];for(let e of["Control","Alt","Meta","Shift"])r.includes(e)&&s.push(e);return i&&s.push(i),s.join("+")}let c=/Mac|iPod|iPhone|iPad/i;let SequenceTracker=class SequenceTracker{constructor({onReset:e}={}){this._path=[],this.timer=null,this.onReset=e}get path(){return this._path}get sequence(){return this._path.join(" ")}registerKeypress(e){this._path=[...this._path,o(e)],this.startTimer()}reset(){var e;this.killTimer(),this._path=[],null==(e=this.onReset)||e.call(this)}killTimer(){null!=this.timer&&window.clearTimeout(this.timer),this.timer=null}startTimer(){this.killTimer(),this.timer=window.setTimeout(()=>this.reset(),SequenceTracker.CHORD_TIMEOUT)}};function u(e){return e.split(" ").map(e=>a(e)).join(" ")}function h(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==n&&"reset"!==n&&"checkbox"!==n&&"radio"!==n&&"file"!==n||e.isContentEditable}SequenceTracker.CHORD_TIMEOUT=1500;let d=new RadixTrie,f=new WeakMap,p=d,g=new SequenceTracker({onReset(){p=d}});function m(e){if(e.defaultPrevented||!(e.target instanceof Node))return;if(h(e.target)){let t=e.target;if(!t.id||!t.ownerDocument.querySelector(`[data-hotkey-scope="${t.id}"]`))return}let t=p.get(o(e));if(!t)return void g.reset();if(g.registerKeypress(e),p=t,t instanceof Leaf){let r,i=e.target,s=!1,o=h(i);for(let e=t.children.length-1;e>=0;e-=1){let n=(r=t.children[e]).getAttribute("data-hotkey-scope");if(!o&&!n||o&&i.id===n){s=!0;break}}if(r&&s){var n=r;let t=new CustomEvent("hotkey-fire",{cancelable:!0,detail:{path:g.path}});n.dispatchEvent(t)&&(h(n)?n.focus():n.click()),e.preventDefault()}g.reset()}}function v(e,t){0===Object.keys(d.children).length&&document.addEventListener("keydown",m);let n=(function(e){let t=[],n=[""],r=!1;for(let i=0;i<e.length;i++){if(r&&","===e[i]){t.push(n),n=[""],r=!1;continue}if(" "===e[i]){n.push(""),r=!1;continue}r="+"!==e[i],n[n.length-1]+=e[i]}return t.push(n),t.map(e=>e.map(e=>a(e)).filter(e=>""!==e)).filter(e=>e.length>0)})(t||e.getAttribute("data-hotkey")||"").map(t=>d.insert(t).add(e));f.set(e,n)}function y(e){let t=f.get(e);if(t&&t.length)for(let n of t)n&&n.delete(e);0===Object.keys(d.children).length&&document.removeEventListener("keydown",m)}}}]);
//# sourceMappingURL=vendors-node_modules_github_mini-throttle_dist_decorators_js-node_modules_delegated-events_di-e161aa-283d0fe3d51d.js.map