"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-900dde"],{97213:(e,t,n)=>{n.d(t,{VH:()=>A,df:()=>c,mT:()=>u});var s=n(22247),l=n(97797),r=n(72705),o=n(21403),i=n(12559),a=n(97325);function c(e){if(e.querySelector(".js-task-list-field")){for(let t of e.querySelectorAll("task-lists"))if(t instanceof r.A)for(let e of(t.disabled=!1,t.querySelectorAll("button")))e.disabled=!1}}function u(e){for(let t of e.querySelectorAll("task-lists"))if(t instanceof r.A)for(let e of(t.disabled=!0,t.querySelectorAll("button")))e.disabled=!0}function d(e,t,n){let s=e.querySelector(".js-comment-update");u(e),T(e);let l=s.elements.namedItem("task_list_track");l instanceof Element&&l.remove();let r=s.elements.namedItem("task_list_operation");r instanceof Element&&r.remove();let o=document.createElement("input");o.setAttribute("type","hidden"),o.setAttribute("name","task_list_track"),o.setAttribute("value",t),s.appendChild(o);let i=document.createElement("input");if(i.setAttribute("type","hidden"),i.setAttribute("name","task_list_operation"),i.setAttribute("value",JSON.stringify(n)),s.appendChild(i),!s.elements.namedItem("task_list_key")){let e=s.querySelector(".js-task-list-field").getAttribute("name").split("[")[0],t=document.createElement("input");t.setAttribute("type","hidden"),t.setAttribute("name","task_list_key"),t.setAttribute("value",e),s.appendChild(t)}e.classList.remove("is-comment-stale"),(0,a.k_)(s)}(0,o.lB)(".js-task-list-container .js-task-list-field",function(e){let t=e.closest(".js-task-list-container");c(t),T(t)}),(0,o.lB)(".js-convert-tasklist-to-block-enabled .contains-task-list",function(e){let t=_(e);if(!t||Array.from(t.children).some(e=>e.classList.contains("task-list-item-convert-container")))return;let n=e.ownerDocument.querySelector(".js-convert-to-block-template"),s=n?.content.cloneNode(!0);s&&t.appendChild(s)}),(0,l.on)("task-lists-move","task-lists",function(e){let{src:t,dst:n}=e.detail;d(e.currentTarget.closest(".js-task-list-container"),"reordered",{operation:"move",src:t,dst:n})}),(0,l.on)("task-lists-check","task-lists",function(e){let{position:t,checked:n}=e.detail;d(e.currentTarget.closest(".js-task-list-container"),`checked:${+!!n}`,{operation:"check",position:t,checked:n})}),(0,l.on)("click",".js-convert-to-block-button",function(e){let t=_(e.target);if(!t)return;if(!t.closest("task-lists"))throw Error("parent not found");let n=A(t);d(e.currentTarget.closest(".js-task-list-container"),"converted",{operation:"convert_to_block",position:n})}),(0,i.JW)(".js-task-list-container .js-comment-update",async function(e,t){let n,s=e.closest(".js-task-list-container"),l=e.elements.namedItem("task_list_track");l instanceof Element&&l.remove();let r=e.elements.namedItem("task_list_operation");r instanceof Element&&r.remove();try{n=await t.json()}catch(t){let e;try{e=JSON.parse(t.response.text)}catch{}if(e&&e.stale){let e=s.querySelector(".js-task-list-field");e.classList.add("session-resumable-canceled"),e.classList.remove("js-session-resumable")}else 422===t.response.status||window.location.reload()}n&&(r&&n.json.source&&(s.querySelector(".js-task-list-field").value=n.json.source),c(s),requestAnimationFrame(()=>T(s)))});let f=!1,m=!1,p=null;function h(e){f="insertLineBreak"===e.inputType}function b(e){(f||"insertLineBreak"===e.inputType)&&(function(e){let t=function(e,t){let n=t[0];if(!n||!e)return;let s=e.substring(0,n).split(`
`),l=s[s.length-2],r=l?.match(x);if(!r)return;let o=r[0],i=r[1],a=r[2],c=parseInt(r[3],10),u=!!r[4],d=!isNaN(c),f=d?`${c+1}.`:a,m=`${f} ${u?"[ ] ":""}`,p=e.indexOf(`
`,n);p<0&&(p=e.length);let h=e.substring(n,p);if(h.startsWith(m)&&(m=""),l.replace(o,"").trim().length>0||h.trim().length>0){let t=`${i}${m}`,s=e.substring(n),l=t.length,r=[null,null],o=e.substring(0,n)+t+s;if(d&&!e.substring(n).match(/^\s*$/g)){var b,g;let l;b=e.substring(n),g=c+1,t+=s=b.split(`
`).map(e=>{if(e.replace(/^\s+/,"").startsWith(`${g}.`)){let t=e.replace(`${g}`,`${g+1}`);return g+=1,t}return e}).join(`
`),r=[n,n+t.length],o=e.substring(0,n)+t}return{text:o,autocompletePrefix:t,selection:[n+l,n+l],commandId:E.insertText,writeSelection:r}}{let t=n-`
${o}`.length;return{autocompletePrefix:"",text:e.substring(0,t)+e.substring(n),selection:[t,t],commandId:E.delete,writeSelection:[null,null]}}}(e.value,[e.selectionStart,e.selectionEnd]);void 0!==t&&g(e,t)}(e.target),f=!1)}function g(e,t){if(null===p||!0===p){e.contentEditable="true";try{let n;f=!1,t.commandId===E.insertText?(n=t.autocompletePrefix,null!==t.writeSelection[0]&&null!==t.writeSelection[1]&&(e.selectionStart=t.writeSelection[0],e.selectionEnd=t.writeSelection[1])):e.selectionStart=t.selection[0],p=document.execCommand(t.commandId,!1,n)}catch{p=!1}e.contentEditable="false"}if(!p){try{document.execCommand("ms-beginUndoUnit")}catch{}e.value=t.text;try{document.execCommand("ms-endUndoUnit")}catch{}e.dispatchEvent(new CustomEvent("input",{bubbles:!0,cancelable:!0}))}null!=t.selection[0]&&null!=t.selection[1]&&(e.selectionStart=t.selection[0],e.selectionEnd=t.selection[1])}function v(e){if(!m&&"Enter"===e.key&&e.shiftKey&&!e.metaKey){let t=e.target,n=function(e,t){let n=t[0];if(!n||!e)return;let s=e.substring(0,n).split(`
`),l=s[s.length-1],r=l?.match(S);if(!r)return;let o=r[1]||"",i=`
${o}`;return{text:e.substring(0,n)+i+e.substring(n),autocompletePrefix:i,selection:[n+i.length,n+i.length],commandId:E.insertText,writeSelection:[null,null]}}(t.value,[t.selectionStart,t.selectionEnd]);if(void 0!==n)g(t,n),e.preventDefault(),(0,l.h)(t,"change")}}function k(){m=!0}function y(){m=!1}function w(e){if(m)return;if("Escape"===e.key)return void function(e){let t=e.target;"backward"===t.selectionDirection?t.selectionEnd=t.selectionStart:t.selectionStart=t.selectionEnd}(e);if("Tab"!==e.key)return;let t=e.target,n=function(e,t,n){let s=t[0]||0,l=t[1]||s;if(null===t[0]||s===l)return;let r=e.substring(0,s).lastIndexOf(`
`)+1,o=e.indexOf(`
`,l-1),i=o>0?o:e.length-1,a=e.substring(r,i).split(`
`),c=!1,u=0,d=0,f=[];for(let e of a){let t=e.match(/^\s*/);if(t){let s=t[0],l=e.substring(s.length);if(n){let e=s.length;s=s.slice(0,-2),u=c?u:s.length-e,c=!0,d+=s.length-e}else s+="  ",u=2,d+=2;f.push(s+l)}}let m=f.join(`
`);return{text:e.substring(0,r)+m+e.substring(i),selection:[Math.max(r,s+u),l+d],autocompletePrefix:m,commandId:E.insertText,writeSelection:[r,i]}}(t.value,[t.selectionStart,t.selectionEnd],e.shiftKey);void 0!==n&&(e.preventDefault(),g(t,n))}(0,o.lB)(".js-task-list-field",{subscribe:e=>(0,s.Zz)((0,s.Rt)(e,"keydown",w),(0,s.Rt)(e,"keydown",v),(0,s.Rt)(e,"beforeinput",h),(0,s.Rt)(e,"input",b),(0,s.Rt)(e,"compositionstart",k),(0,s.Rt)(e,"compositionend",y))});let E={insertText:"insertText",delete:"delete"},S=/^(\s*)?/,x=/^(\s*)([*-]|(\d+)\.)\s(\[[\sx]\]\s)?/;function A(e){let t=e.closest("task-lists");if(!t)throw Error("parent not found");return Array.from(t.querySelectorAll("ol, ul")).filter(e=>!e.closest("tracking-block")).indexOf(e)}function T(e){if(0===document.querySelectorAll("tracked-issues-progress").length||e.closest(".js-timeline-item"))return;let t=e.querySelectorAll(".js-comment-body [type=checkbox]"),n=t.length,s=Array.from(t).filter(e=>e.checked).length;for(let e of document.querySelectorAll("tracked-issues-progress[data-type=checklist]"))e.setAttribute("data-completed",String(s)),e.setAttribute("data-total",String(n))}function _(e){let t=e.closest(".contains-task-list"),n=t;for(;(n=n.parentElement.closest(".contains-task-list"))!==t&&null!==n;)t=n;return t}},88402:(e,t,n)=>{n.d(t,{A:()=>f});var s=n(24791),l=n(20451),r=n(21403),o=n(26559);function i(e){let t=document.querySelector(".sso-modal");t&&(t.classList.remove("success","error"),e?t.classList.add("success"):t.classList.add("error"))}async function a(){let e=document.querySelector("link[rel=sso-modal]"),t=await (0,s.r)({content:(0,l.Ts)(document,e.href),dialogClass:"sso-modal"}),n=null,r=window.external;if(r.ssoComplete=function(e){if(e.error)i(n=!1);else{i(n=!0);var t=e.expiresAround;let s=document.querySelector("meta[name=sso-expires-around]");s&&s.setAttribute("content",t),window.focus()}r.ssoComplete=null},await new Promise(e=>{t.addEventListener("dialog:remove",e,{once:!0})}),!n)throw Error("sso prompt canceled")}async function c(){let e=document.querySelector("link[rel=sso-session]"),t=document.querySelector("meta[name=sso-expires-around]");if(!(e instanceof HTMLLinkElement)||!function(e){if(!(e instanceof HTMLMetaElement))return!0;let t=parseInt(e.content);return Date.now()/1e3>t}(t))return!0;let n=e.href,s=await fetch(n,{headers:{Accept:"application/json",...(0,o.kt)()}});return await s.json()}(0,r.lB)(".js-sso-modal-complete",function(e){if(window.opener&&window.opener.external.ssoComplete){let t=e.getAttribute("data-error"),n=e.getAttribute("data-expires-around");window.opener.external.ssoComplete({error:t,expiresAround:n}),window.close()}else{let t=e.getAttribute("data-fallback-url");t&&(window.location.href=t)}});let u=null;function d(){u=null}async function f(){await c()||(u||(u=a().then(d).catch(d)),await u)}},24791:(e,t,n)=>{n.d(t,{r:()=>l});var s=n(97797);async function l(e){let t=document.querySelector("#site-details-dialog").content.cloneNode(!0),n=t.querySelector("details"),l=n.querySelector("details-dialog"),r=n.querySelector(".js-details-dialog-spinner");e.detailsClass&&n.classList.add(...e.detailsClass.split(" ")),e.dialogClass&&l.classList.add(...e.dialogClass.split(" ")),e.label?l.setAttribute("aria-label",e.label):e.labelledBy&&l.setAttribute("aria-labelledby",e.labelledBy),document.body.append(t);try{let t=await e.content;r.remove(),l.prepend(t)}catch{r.remove();let t=document.createElement("span");t.textContent=e.errorMessage||"Couldn't load the content",t.classList.add("my-6"),t.classList.add("mx-4"),l.prepend(t)}return n.addEventListener("toggle",()=>{n.hasAttribute("open")||((0,s.h)(l,"dialog:remove"),n.remove())}),l}},20451:(e,t,n)=>{n.d(t,{Ee:()=>i,b4:()=>a,Ts:()=>o});let s=class ResponseError extends Error{constructor(e,t){super(`${e} for HTTP ${t.status}`),function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"response",void 0),this.response=t,this.name="ResponseError"}};var l=n(1739),r=n(26559);async function o(e,t,n){let o=new Request(t,n);(0,r.tV)(o.headers);let i=await self.fetch(o);if(i.status<200||i.status>=300)throw Error(`HTTP ${i.status}${i.statusText||""}`);return!function(e,t,n=!1){let l=t.headers.get("content-type")||"";if(!n&&!l.startsWith("text/html"))throw new s(`expected response with text/html, but was ${l}`,t);if(n&&!(l.startsWith("text/html")||l.startsWith("application/json")))throw new s(`expected response with text/html or application/json, but was ${l}`,t);let r=t.headers.get("x-html-safe");if(r){if(!e.includes(r))throw new s("response X-HTML-Safe nonce did not match",t)}else throw new s("missing X-HTML-Safe nonce",t)}(function(e){let t=[...e.querySelectorAll("meta[name=html-safe-nonce]")].map(e=>e.content);if(t.length<1)throw Error("could not find html-safe-nonce on document");return t}(e),i),(0,l.B)(e,await i.text())}function i(e,t,n=1e3,s=[200],l=[202]){return async function n(o){let i=new Request(e,t);(0,r.tV)(i.headers);let a=await self.fetch(i);if(l.includes(a.status))return await new Promise(e=>setTimeout(e,o)),n(1.5*o);if(s.includes(a.status))return a;if(a.status<200||a.status>=300)throw Error(`HTTP ${a.status}${a.statusText||""}`);throw Error(`Unexpected ${a.status} response status from poll endpoint`)}(n)}async function a(e,t,n){let{wait:s=500,acceptedStatusCodes:l=[200],max:o=3,attempt:i=0}=n||{},c=async()=>new Promise((n,a)=>{setTimeout(async()=>{try{let s=new Request(e,t);(0,r.tV)(s.headers);let a=await self.fetch(s);if(l.includes(a.status)||i+1===o)return n(a);n("retry")}catch(e){a(e)}},s*i)}),u=await c();return"retry"!==u?u:a(e,t,{wait:s,acceptedStatusCodes:l,max:o,attempt:i+1})}},36175:(e,t,n)=>{n.d(t,{Ff:()=>a,eC:()=>c,uE:()=>i});var s=n(6986);let l=!1,r=new s.A;function o(e){let t=e.target;if(t instanceof HTMLElement&&t.nodeType!==Node.DOCUMENT_NODE)for(let e of r.matches(t))e.data.call(null,t)}function i(e,t){l||(l=!0,document.addEventListener("focus",o,!0)),r.add(e,t),document.activeElement instanceof HTMLElement&&document.activeElement.matches(e)&&t(document.activeElement)}function a(e,t,n){function s(t){let l=t.currentTarget;l&&(l.removeEventListener(e,n),l.removeEventListener("blur",s))}i(t,function(t){t.addEventListener(e,n),t.addEventListener("blur",s)})}function c(e,t){function n(e){let{currentTarget:s}=e;s&&(s.removeEventListener("input",t),s.removeEventListener("blur",n))}i(e,function(e){e.addEventListener("input",t),e.addEventListener("blur",n)})}},12559:(e,t,n)=>{n.d(t,{Ax:()=>l.Ax,JW:()=>r,ZV:()=>l.ZV});var s=n(26559),l=n(13937);function r(e,t){(0,l.JW)(e,async(e,n,l)=>((0,s.tV)(l.headers),t(e,n,l)))}},21715:(e,t,n)=>{n.d(t,{z:()=>s});let s=Object.freeze({INITIAL:"soft-nav:initial",START:"soft-nav:start",REPLACE_MECHANISM:"soft-nav:replace-mechanism",SUCCESS:"soft-nav:success",ERROR:"soft-nav:error",FRAME_UPDATE:"soft-nav:frame-update",END:"soft-nav:end",RENDER:"soft-nav:render",PROGRESS_BAR:{START:"soft-nav:progress-bar:start",END:"soft-nav:progress-bar:end"}})}}]);
//# sourceMappingURL=app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-900dde-2dfddc217d1c.js.map