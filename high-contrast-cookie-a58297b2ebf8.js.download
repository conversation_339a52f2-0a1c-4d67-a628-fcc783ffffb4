(()=>{"use strict";let e;!function(){if("undefined"!=typeof document){let t=document.getElementById("client-env");if(t)try{e=JSON.parse(t.textContent||"")}catch(e){console.error("Error parsing client-env",e)}}}();function t(e){let t=[];for(let n of function(){try{return document.cookie.split(";")}catch{return[]}}()){let[r,i]=n.trim().split("=");e===r&&void 0!==i&&t.push({key:r,value:i})}return t}!function(){if(function(){if(!e)throw Error("Client env was requested before it was loaded. This likely means you are attempting to use client env at the module level in SSR, which is not supported. Please move your client env usage into a function.");return e}().login)return;let n=t("increase_contrast_light")[0],r=t("increase_contrast_dark")[0];document.documentElement.setAttribute("data-light-theme",n?.value==="enabled"?"light_high_contrast":"light"),document.documentElement.setAttribute("data-dark-theme",r?.value==="enabled"?"dark_high_contrast":"dark")}()})();
//# sourceMappingURL=high-contrast-cookie-723168e4ac4c.js.map