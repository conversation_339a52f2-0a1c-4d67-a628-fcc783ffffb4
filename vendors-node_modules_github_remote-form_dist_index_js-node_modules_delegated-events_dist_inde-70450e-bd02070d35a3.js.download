"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-70450e"],{13937:(e,t,n)=>{let o;n.d(t,{Ax:()=>l,JW:()=>c,ZV:()=>a});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function r(){let e,t;return[new Promise(function(n,o){e=n,t=o}),e,t]}let s=[],i=[];function a(e){s.push(e)}function l(e){i.push(e)}function c(e,t){o||(o=new Map,"undefined"!=typeof document&&document.addEventListener("submit",u));let n=o.get(e)||[];o.set(e,[...n,t])}function u(e){let t;if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let n=e.target,a=function(e){let t=[],n=t=>"object"==typeof t?t===e:"string"==typeof t&&e.matches(t);for(let e of o.keys())if(n(e)){let n=o.get(e)||[];t.push(...n)}return t}(n);if(0===a.length)return;let l=function(e,t){let n={method:t?.formMethod||e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===n.method.toUpperCase()){let t=function(e){let t=new URLSearchParams;for(let[n,o]of[...new FormData(e).entries()])t.append(n,o.toString());return t.toString()}(e);t&&(n.url+=(~n.url.indexOf("?")?"&":"?")+t)}else n.body=new FormData(e);return n}(n,e instanceof SubmitEvent?e.submitter:null),[c,u,h]=r();e.preventDefault(),d(a,n,l,c).then(async e=>{if(e){for(let e of i)await e(n);f(l).then(u,h).catch(()=>{}).then(()=>{for(let e of s)e(n)})}else n.submit()},e=>{n.submit(),setTimeout(()=>{throw e})})}async function d(e,t,n,o){let s=!1;for(let i of e){let[e,a]=r(),l=()=>(s=!0,a(),o),c={text:l,json:()=>(n.headers.set("Accept","application/json"),l()),html:()=>(n.headers.set("Accept","text/html"),l())};await Promise.race([e,i(t,c,n)])}return s}async function f(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=JSON.parse(this.text);return delete this.json,this.json=e,this.json},get html(){return delete this.html,this.html=function(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}(document,this.text),this.html}};if(n.text=await t.text(),t.ok)return n;throw new ErrorWithResponse("request failed",n)}},97797:(e,t,n)=>{function o(){if(!(this instanceof o))return new o;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{h:()=>O,A:()=>C,on:()=>k});var r,s=window.document.documentElement,i=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector;o.prototype.matchesSelector=function(e,t){return i.call(e,t)},o.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},o.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);else if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),o.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},r="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var u=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var n,o,r,s,i,a,l=(e=e.slice(0).concat(e.default)).length,c=t,d=[];do if(u.exec(""),(r=u.exec(c))&&(c=r[3],r[2]||!c)){for(n=0;n<l;n++)if(i=(a=e[n]).selector(r[1])){for(o=d.length,s=!1;o--;)if(d[o].index===a&&d[o].key===i){s=!0;break}s||d.push({index:a,key:i});break}}while(r)return d}function f(e,t){return e.id-t.id}o.prototype.logDefaultIndexUsed=function(){},o.prototype.add=function(e,t){var n,o,s,i,a,l,c,u,f=this.activeIndexes,h=this.selectors,p=this.selectorObjects;if("string"==typeof e){for(o=0,p[(n={id:this.uid++,selector:e,data:t}).id]=n,c=d(this.indexes,e);o<c.length;o++)i=(u=c[o]).key,(a=function(e,t){var n,o,r;for(n=0,o=e.length;n<o;n++)if(r=e[n],t.isPrototypeOf(r))return r}(f,s=u.index))||((a=Object.create(s)).map=new r,f.push(a)),s===this.indexes.default&&this.logDefaultIndexUsed(n),(l=a.map.get(i))||(l=[],a.map.set(i,l)),l.push(n);this.size++,h.push(e)}},o.prototype.remove=function(e,t){if("string"==typeof e){var n,o,r,s,i,a,l,c,u=this.activeIndexes,f=this.selectors=[],h=this.selectorObjects,p={},m=1==arguments.length;for(r=0,n=d(this.indexes,e);r<n.length;r++)for(o=n[r],s=u.length;s--;)if(a=u[s],o.index.isPrototypeOf(a)){if(l=a.map.get(o.key))for(i=l.length;i--;)(c=l[i]).selector===e&&(m||c.data===t)&&(l.splice(i,1),p[c.id]=!0);break}for(r in p)delete h[r],this.size--;for(r in h)f.push(h[r].selector)}},o.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,o,r,s,i,a,l,c={},u=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,o=d.length;t<o;t++)for(n=0,s=d[t],r=(i=this.matches(s)).length;n<r;n++)c[(l=i[n]).id]?a=c[l.id]:(a={id:l.id,selector:l.selector,data:l.data,elements:[]},c[l.id]=a,u.push(a)),a.elements.push(s);return u.sort(f)},o.prototype.matches=function(e){if(!e)return[];var t,n,o,r,s,i,a,l,c,u,d,h=this.activeIndexes,p={},m=[];for(t=0,r=h.length;t<r;t++)if(l=(a=h[t]).element(e)){for(n=0,s=l.length;n<s;n++)if(c=a.map.get(l[n]))for(o=0,i=c.length;o<i;o++)!p[d=(u=c[o]).id]&&this.matchesSelector(e,u.selector)&&(p[d]=!0,m.push(u))}return m.sort(f)};var h={},p={},m=new WeakMap,g=new WeakMap,b=new WeakMap,y=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function w(e,t,n){var o=e[t];return e[t]=function(){return n.apply(e,arguments),o.apply(e,arguments)},e}function v(){m.set(this,!0)}function x(){m.set(this,!0),g.set(this,!0)}function A(){return b.get(this)||null}function E(e,t){y&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||y.get})}function S(e){if(function(e){try{return e.eventPhase,!0}catch(e){return!1}}(e)){var t=(1===e.eventPhase?p:h)[e.type];if(t){var n=function(e,t,n){var o=[],r=t;do{if(1!==r.nodeType)break;var s=e.matches(r);if(s.length){var i={node:r,observers:s};n?o.unshift(i):o.push(i)}}while(r=r.parentElement)return o}(t,e.target,1===e.eventPhase);if(n.length){w(e,"stopPropagation",v),w(e,"stopImmediatePropagation",x),E(e,A);for(var o=0,r=n.length;o<r&&!m.get(e);o++){var s=n[o];b.set(e,s.node);for(var i=0,a=s.observers.length;i<a&&!g.get(e);i++)s.observers[i].data.call(s.node,e)}b.delete(e),E(e)}}}}function k(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=!!r.capture,i=s?p:h,a=i[e];a||(a=new o,i[e]=a,document.addEventListener(e,S,s)),a.add(t,n)}function C(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=!!o.capture,s=r?p:h,i=s[e];i&&(i.remove(t,n),i.size||(delete s[e],document.removeEventListener(e,S,r)))}function O(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},39595:(e,t,n)=>{let o;n.d(t,{CF:()=>m,p_:()=>M,FB:()=>d,Se:()=>S,aC:()=>j,zV:()=>P});let r=new WeakSet,s=new WeakMap;function i(e=document){if(s.has(e))return s.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)u(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&a(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let o={get closed(){return t},unsubscribe(){t=!0,s.delete(e),n.disconnect()}};return s.set(e,o),o}function a(e){for(let t of e.querySelectorAll("[data-action]"))u(t);e instanceof Element&&e.hasAttribute("data-action")&&u(e)}function l(e){let t=e.currentTarget;for(let n of c(t))if(e.type===n.type){let o=t.closest(n.tag);r.has(o)&&"function"==typeof o[n.method]&&o[n.method](e);let s=t.getRootNode();if(s instanceof ShadowRoot&&r.has(s.host)&&s.host.matches(n.tag)){let t=s.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function u(e){for(let t of c(e))e.addEventListener(t.type,l)}function d(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let o of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!o.closest(n))return o}for(let o of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(o.closest(n)===e)return o}let f=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),h=(e,t="property")=>{let n=f(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},p="attr";function m(e,t){O(e,p).add(t)}let g=new WeakSet;function b(e,t){if(g.has(e))return;g.add(e);let n=Object.getPrototypeOf(e),o=n?.constructor?.attrPrefix??"data-";for(let r of(t||(t=O(n,p)),t)){let t=e[r],n=h(`${o}${r}`),s={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?s={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(s={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,r,s),r in e&&!e.hasAttribute(n)&&s.set.call(e,t)}}let y=new Map,w=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),v=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},o=()=>t.abort();document.addEventListener("mousedown",o,n),document.addEventListener("touchstart",o,n),document.addEventListener("keydown",o,n),document.addEventListener("pointerdown",o,n)}),x={ready:()=>w,firstInteraction:()=>v,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},A=new WeakMap;function E(e){cancelAnimationFrame(A.get(e)||0),A.set(e,requestAnimationFrame(()=>{for(let t of y.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let o=n?.getAttribute("data-load-on")||"ready",r=o in x?x[o]:x.ready;for(let e of y.get(t)||[])r(t).then(e);y.delete(t),A.delete(e)}}}))}function S(e,t){for(let[n,o]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))y.has(n)||y.set(n,new Set),y.get(n).add(o);k(document)}function k(e){o||(o=new MutationObserver(e=>{if(y.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&E(e)})),E(e),o.observe(e,{subtree:!0,childList:!0})}let C=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let o=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,o)};let r=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,o){t.attributeChangedCallback(this,e,n,o,r)};let s=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,s)},set(e){s=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",o=e=>h(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...O(e.prototype,p)].map(o).concat(t),set(e){t=e}})}(e),function(e){let t=f(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,o;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(b(e),r.add(e),e.shadowRoot&&(a(o=e.shadowRoot),i(o)),a(e),i(e.ownerDocument),t?.call(e),e.shadowRoot)&&(a(n=e.shadowRoot),i(n),k(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,o,r){b(e),"data-catalyst"!==t&&r&&r.call(e,t,n,o)}};function O(e,t){if(!Object.prototype.hasOwnProperty.call(e,C)){let t=e[C],n=e[C]=new Map;if(t)for(let[e,o]of t)n.set(e,new Set(o))}let n=e[C];return n.has(t)||n.set(t,new Set),n.get(t)}function j(e,t){O(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return d(this,t)}})}function P(e,t){O(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let o of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)||n.push(o);for(let o of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))o.closest(e)===this&&n.push(o);return n}})}function M(e){new CatalystDelegate(e)}}}]);
//# sourceMappingURL=vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-70450e-26305d3ad388.js.map