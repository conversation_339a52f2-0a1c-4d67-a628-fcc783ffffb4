"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["react-code-view"],{68048:(e,t,n)=>{n.d(t,{D:()=>r});function r(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e);let t=document.body;if(!t)return Promise.reject(Error());let n=function(e){let t=document.createElement("pre");return t.style.width="1px",t.style.height="1px",t.style.position="fixed",t.style.top="5px",t.textContent=e,t}(e);return t.appendChild(n),!function(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e.textContent||"");let t=getSelection();if(null==t)return Promise.reject(Error());t.removeAllRanges();let n=document.createRange();n.selectNodeContents(e),t.addRange(n),document.execCommand("copy"),t.removeAllRanges(),Promise.resolve()}(n),t.removeChild(n),Promise.resolve()}},70635:(e,t,n)=>{var r=n(16058),i=n(65144),o=n(74848),l=n(26033),a=n(88795),s=n(93955),c=n(53614),d=n(25772),u=n(68415),h=n(21325),m=n(96540);function p({children:e}){let t=(0,d.B)(),[n]=m.useState(t?.repo),[r]=m.useState(t?.currentUser),i=(0,m.useMemo)(()=>({}),[]);return(0,m.useEffect)(()=>{let e=document.querySelector(".footer");e&&(e.querySelector(".mt-6")?.classList.replace("mt-6","mt-0"),e.querySelector(".border-top")?.classList.remove("border-top"))},[]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("meta",{"data-hydrostats":"publish"}),(0,o.jsx)(u.LB,{initialValue:u.Gy.xxxlarge,children:(0,o.jsx)(l.y,{appName:"react-code-view",category:"",metadata:i,children:(0,o.jsx)(s.Q,{user:r,children:(0,o.jsx)(a.d,{repository:n,children:(0,o.jsx)(h.cp,{children:(0,o.jsx)(c.i,{children:e})})})})})})]})}try{p.displayName||(p.displayName="App")}catch{}var f=n(95782),x=n(21609),y=n(69098),g=n(51314),b=n(85579),v=n(141),j=n(61763),w=n(50104),N=n(30903),_=n(28408),k=n(21113),C=n(74474),A=n(18138),S=n(66871),B=n(42658),I=n(8447),L=n(14103),T=n(93653),E=n(75177),R=n(84217),O=n(70170),D=n(54763),F=n(98637),$=n(13617),M=n(30695),P=n(38621),z=n(87330),H=n(9591),W=n(52464),U=n(53110),V=n(36588),G=n(55864),Y=n(30631);function q({symbolKind:e,showFullSymbol:t}){return e?(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",position:"relative",...t?{}:{mr:2}},children:[(0,o.jsx)(E.A,{sx:{backgroundColor:e.plColor,opacity:.1,position:"absolute",borderRadius:5,alignItems:"stretch",display:"flex",width:"100%",height:"100%"}}),(0,o.jsx)(E.A,{sx:{color:e.plColor,borderRadius:5,fontWeight:600,...t?{fontSize:"small",px:2,py:"1px",mt:"2px"}:{fontSize:"smaller",px:1,py:"1px"}},children:t?e.fullName:e.shortName})]}):null}try{q.displayName||(q.displayName="SymbolIndicator")}catch{}let K=m.memo(function({treeSymbols:e,onTreeSymbolSelect:t}){let n=!e.some(e=>e.isParent||e.children.length>0);return(0,o.jsx)(E.A,{id:"filter-results",sx:{mb:-2,overflowY:"auto",maxHeight:"calc(100vh - 237px)",pl:3,pb:2,pt:1},children:(0,o.jsx)(G.G,{"aria-label":"Code Navigation",flat:n,children:e.map((e,n)=>(0,o.jsx)(X,{id:`${n}${e.symbol.name}`,symbol:e,depth:e.isParent?1:2,onSelect:t},`${n}${e.symbol.name}`))})})});function Q({symbol:e}){return(0,o.jsxs)(E.A,{sx:{display:"flex"},children:[(0,o.jsx)(q,{symbolKind:e.symbol.kind}),"  ",(0,o.jsx)(Y.A,{title:e.symbol.name,sx:{maxWidth:180,display:"block"},children:(0,o.jsx)("span",{children:e.symbol.name})})]})}function X({symbol:e,depth:t,onSelect:n,id:r}){let[i,l]=(0,m.useState)(t<=7);return(0,o.jsxs)(G.G.Item,{onSelect:()=>n(e.symbol),expanded:i,onExpandedChange:()=>l(!i),id:r,children:[(0,o.jsx)(Q,{symbol:e}),e.isParent&&e.children.length>0&&(0,o.jsx)(G.G.SubTree,{children:e.children.map((e,r)=>(0,o.jsx)(X,{symbol:e,depth:e.isParent?t+1:t,onSelect:n,id:`${r}${e.symbol.name}`},`${r}${e.symbol.name}`))})]})}try{K.displayName||(K.displayName="CodeNavSymbolTree")}catch{}try{Q.displayName||(Q.displayName="CodeNavTreeContent")}catch{}try{X.displayName||(X.displayName="CodeNavTreeItem")}catch{}var Z=n(52811),J=n(69676),ee=n(82678),et=n(15385),en=n(39461),er=n(34614);let ei=m.memo(function({symbol:e,filterText:t,onSelect:n,focused:r,index:i}){return(0,o.jsx)(et.l.Item,{role:"option",id:`jump-to-item-${i}`,"aria-selected":r,sx:{minWidth:0,...r?{backgroundColor:"var(--bgColor-muted, var(--color-canvas-subtle)) !important"}:{}},onSelect:()=>n(e),children:(0,o.jsx)(er.A,{href:e.href(),sx:{":hover":{textDecoration:"none"}},children:(0,o.jsxs)("div",{style:{display:"flex"},children:[(0,o.jsx)(q,{symbolKind:e.kind}),"  ",(0,o.jsx)("div",{style:{display:"flex",minWidth:0,alignItems:"flex-end"},children:(0,o.jsx)(en.z,{search:t,text:e.name,overflowWidth:175,hideOverflow:!0},e.fullyQualifiedName)})]})})})});try{ei.displayName||(ei.displayName="JumpToItem")}catch{}function eo(e){return(0,o.jsx)(el,{...e})}function el({codeSymbols:e,filterText:t,onSelect:n,focusedIndex:r}){let i=(0,m.useRef)(e.length),l=(0,m.useRef)(""),{containerRef:a}=(0,ee.G)({bindKeys:J.z0.ArrowVertical|J.z0.HomeAndEnd});return(0,m.useEffect)(()=>{e.length===i.current&&(l.current+="\u200B");let t=1===e.length?"symbol":"symbols";(0,Z.i)(`${e.length} ${t} found${l.current}`),i.current=e.length},[e]),(0,o.jsx)(et.l,{ref:a,role:"listbox",id:"filter-results","aria-orientation":"vertical",sx:{maxHeight:"68vh",overflowY:"auto"},children:e.map((e,i)=>{let{name:l,lineNumber:a}=e;return(0,o.jsx)(ei,{symbol:e,filterText:t,onSelect:n,focused:i===r,index:i},`${l}_${a}`)})})}try{eo.displayName||(eo.displayName="JumpToItemList")}catch{}try{el.displayName||(el.displayName="FullJumpToItemList")}catch{}var ea=n(96235);function es({filterText:e,isFindInFile:t}){let[n,r]=(0,m.useState)(t?"No matches found":"No symbols found"),i=(0,a.t)(),l=(0,m.useRef)(!0);return(0,m.useEffect)(()=>{if(l.current){l.current=!1;return}r(`${n}\u200B`)},[e]),(0,o.jsxs)(E.A,{sx:{justifyContent:"center",alignItems:"center",display:"flex",flexDirection:"column",pb:2},children:[""===e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(E.A,{sx:{bg:"canvas.subtle",borderRadius:6,p:"16px"},children:(0,o.jsxs)(E.A,{sx:{textAlign:"center"},children:[(0,o.jsx)(R.A,{as:"h3",sx:{fontSize:0,marginBottom:"4px"},children:"Symbol outline not available for this file"}),(0,o.jsx)(E.A,{sx:{justifyContent:"center",alignItems:"center",display:"flex",fontSize:"12px",color:"fg.muted"},children:"To inspect a symbol, try clicking on the symbol directly in the code view."})]})}),(0,o.jsxs)(E.A,{sx:{mt:"8px",fontSize:0,textAlign:"center",color:"fg.muted"},children:[" ","Code navigation supports a limited number of languages."," ",(0,o.jsx)(er.A,{inline:!0,href:"https://docs.github.com/repositories/working-with-files/using-files/navigating-code-on-github",children:"See which languages are supported."})]})]}),e&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(U.A,{icon:P.SearchIcon,size:24}),(0,o.jsx)(W.A,{as:"h3",sx:{textAlign:"center",fontWeight:600,fontSize:3,py:2},role:"alert","aria-relevant":"all",children:n})]}),e&&(0,o.jsxs)(W.A,{id:"filter-zero-state",sx:{textAlign:"center",px:3,mt:2,fontSize:0,color:"fg.subtle"},children:["No lines in this file contain that string.",(0,o.jsx)("br",{}),"Search in"," ",(0,o.jsxs)(er.A,{href:(0,ea.Y8Y)({owner:i.ownerLogin,repo:i.name,searchTerm:e}),inline:!0,children:[i.ownerLogin,"/",i.name]})," ","or"," ",(0,o.jsx)(er.A,{href:(0,ea.GGt)({searchTerm:e}),inline:!0,children:"all of GitHub"})]})]})}try{es.displayName||(es.displayName="SymbolZeroState")}catch{}let ec="symbols-pane-header";function ed({codeSymbols:e,onSymbolSelect:t,treeSymbols:n,autoFocusSearch:r,onClose:i}){let[l,a]=(0,m.useState)(""),[s,c]=(0,m.useState)(e);return(0,m.useEffect)(()=>{if(""===l)return void c(e);c(function(e,t){let n=e.replace(/\s/g,"");return(0,$.d)(t,e=>{let t=(0,M.dt)(e.name,n);return t>0?{score:t,text:e.name}:null},M.UD)}(l,e))},[l,e]),(0,o.jsx)(eu,{treeSymbols:n,codeSymbols:s,filterText:l,setFilterText:a,onSymbolSelect:t,autoFocusSearch:r,onClose:i})}function eu({codeSymbols:e,setFilterText:t,filterText:n,onSymbolSelect:r,treeSymbols:i,autoFocusSearch:l,onClose:a}){let s=e?.length>0,c=i.length>0,d=s||c,[u,h]=(0,m.useState)(-1),{sendRepoKeyDownEvent:p}=(0,F.T)(),f=(0,m.useRef)(null),x=!c||""!==n;(0,m.useEffect)(()=>{l&&f.current?.focus()},[l]),(0,V.Gp)(()=>{f.current?.focus()});let y=(0,m.useMemo)(()=>(0,O.s)(()=>{p("BLOB_SYMBOLS_MENU.FILTER_SYMBOLS")},400),[p]);return(0,o.jsxs)(E.A,{sx:{py:2,px:3},"aria-labelledby":ec,children:[(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",justifyContent:"space-between"},children:[(0,o.jsx)(E.A,{as:"h2",sx:{fontSize:1,order:1,display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",fontWeight:600},id:ec,tabIndex:-1,children:"Symbols"}),(0,o.jsx)(z.K,{"aria-label":"Close symbols",tooltipDirection:"w","data-hotkey":"Escape",icon:P.XIcon,sx:{order:3,color:"fg.default",mr:-2},onClick:a,variant:"invisible"})]}),d&&(0,o.jsx)(E.A,{sx:{fontSize:0,color:"fg.muted",pt:2},children:"Find definitions and references for functions and other symbols in this file by clicking a symbol below or in the code."}),(s||""!==n)&&(0,o.jsx)(H.A,{block:!0,leadingVisual:()=>(0,o.jsx)(U.A,{"aria-hidden":"true",icon:P.FilterIcon}),ref:f,trailingAction:n?(0,o.jsx)(H.A.Action,{onClick:()=>{t(""),h(-1)},icon:P.XCircleFillIcon,"aria-label":"Clear input","data-testid":"clear-search",sx:{color:"fg.subtle"}}):(0,o.jsx)(o.Fragment,{}),trailingVisual:n?void 0:()=>(0,o.jsx)(D.E,{children:(0,o.jsx)(E.A,{sx:{mr:"6px"},children:(0,o.jsx)("kbd",{children:"r"})})}),sx:{mt:2,borderRadius:2},placeholder:"Filter symbols",value:n,name:"Filter symbols","aria-label":"Filter symbols","aria-controls":!s&&x?"filter-zero-state":"filter-results","aria-expanded":"true","aria-autocomplete":"list","aria-activedescendant":-1===u?void 0:`jump-to-item-${u}`,onKeyDown:t=>{"ArrowDown"===t.key||("N"===t.key||"n"===t.key)&&t.ctrlKey?h(Math.min(u+1,e.length-1)):"ArrowUp"===t.key||("P"===t.key||"p"===t.key)&&t.ctrlKey?h(Math.max(u-1,0)):"Enter"===t.key&&e[u]?r(e[u]):"Escape"===t.key&&a()},role:"combobox",onChange:e=>{t(e.target.value),y(),h(-1)}}),!x&&(0,o.jsx)(E.A,{sx:{ml:-3,mb:-2},children:(0,o.jsx)(K,{treeSymbols:i,onTreeSymbolSelect:r})}),s&&x&&(0,o.jsx)(eo,{codeSymbols:e,filterText:n,onSelect:r,focusedIndex:u}),!s&&x&&(0,o.jsx)(W.A,{sx:{display:"flex",alignItems:"center",justifyContent:"center",mt:2},children:(0,o.jsx)(es,{filterText:n})})]})}try{ed.displayName||(ed.displayName="CodeNavSymbolNavigation")}catch{}try{eu.displayName||(eu.displayName="JumpToActionList")}catch{}var eh=n(87453),em=n(37902),ep=n(97146),ef=n(38007),ex=n(96679),ey=n(15305),eg=n(76087),eb=n(81675),ev=n(32687),ej=n(33253),ew=n(26807),eN=n(48234),e_=n(63205),ek=n(51595),eC=n(89276),eA=n(17264);function eS({data:e}){let{query:t,lines:n,currentCodeReferences:r}=e,i=[];return{ranges:r?(0,ev.KY)(r,n,(0,ev.tz)(t)):(0,ev.Kv)(n,(0,ev.tz)(t)),query:t}}var eB=n(92659);function eI(e){let[t,n]=(0,m.useState)(!1),r=(0,m.useCallback)(e=>{let r=e[e.length-1].intersectionRatio<1;r!==t&&n(r)},[t,n]);return(0,m.useEffect)(()=>{let t=e.current,n=new IntersectionObserver(r,{threshold:[1],rootMargin:"-1px 0px 0px 0px"});return e.current&&n.observe(e.current),()=>{t&&n.unobserve(t)}},[e,r]),t}function eL(){return m.useMemo(()=>({top:"0px",zIndex:4,background:"var(--bgColor-default, var(--color-canvas-default))",position:"sticky"}),[!0])}var eT=n(7799),eE=n(60039);async function eR(e,t,n){if(!(0,eT.M3)())return;let r=new FormData;r.set("tree_view_expanded_preference",null===e?"":e?"true":"false"),r.set("symbols_view_expanded_preference",null===t?"":t?"true":"false"),r.set("code_line_wrap_enabled",null===n?"":n?"true":"false"),(0,eE.DI)("/repos/preferences",{method:"PUT",body:r,headers:{Accept:"application/json"}})}var eO=n(52687);let eD=(0,m.createContext)({findInFileOpen:!1,setFindInFileOpen:()=>void 0});function eF({children:e,searchTerm:t,setSearchTerm:n,isBlame:r}){let i=(0,eO.fY)(r),l=(0,m.useRef)(""),[a,s]=(0,m.useState)(!1),c=(0,m.useCallback)(e=>{e&&""===t&&""!==l.current?n(l.current):e||""===t||(l.current=t,n("")),s(e)},[t,n]),d=(0,m.useMemo)(()=>({findInFileOpen:!i&&a,setFindInFileOpen:c}),[a,c,i]);return(0,o.jsx)(eD.Provider,{value:d,children:e})}function e$(){return(0,m.useContext)(eD)}try{eD.displayName||(eD.displayName="FindInFileOpenContext")}catch{}try{eF.displayName||(eF.displayName="FindInFileOpenProvider")}catch{}let eM={FileRenderer:"FileRenderer",Image:"Image",TooLargeError:"TooLargeError",CSV:"CSV",Markdown:"Markdown",IssueTemplate:"IssueTemplate",Code:"Code"};function eP(){let e=(0,eg.A)(),t=(0,ey.O)();if(e.renderedFileInfo&&!e.shortPath&&!t)return eM.FileRenderer;if(e.renderImageOrRaw)if(e.image)return eM.Image;else return eM.TooLargeError;if(e.csv&&!t)return eM.CSV;if(e.richText&&!t)return eM.Markdown;if((e.issueTemplate?.structured&&e.issueTemplate.valid||e.discussionTemplate&&e.discussionTemplate.valid)&&!t&&!e.isPlain)return eM.IssueTemplate;else return eM.Code}function ez(e){throw Error(`Unexpected object: ${e}`)}var eH=n(18338),eW=n(77378);function eU(e){let t=(0,m.useRef)(null);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(eH.s,{...e,ref:t}),(0,o.jsx)(eW.A,{containerRef:t})]})}try{eU.displayName||(eU.displayName="MarkdownContent")}catch{}var eV=n(93237),eG=n(85647),eY=n(85351),eq=n(80663);let eK=(0,m.createContext)([]);try{eK.displayName||(eK.displayName="SplitCodeownersErrorsContext")}catch{}var eQ=n(17606),eX=n(56297),eZ=n(22353),eJ=n(89681),e0=n(62578),e1=n(42490),e2=n(39459),e6=n(28391),e3=n(45968),e5=n(60040),e8=n(89169),e4=n(89985),e7=n(46996),e9=n(30391);let te=[.007,.014,.03,.049,.084,.14,.23,.38,.62,Number.MAX_VALUE],tt={"scale.orange.0":"#ffdfb6","scale.orange.1":"#ffc680","scale.orange.2":"#f0883e","scale.orange.3":"#f0883e","scale.orange.4":"#db6d28","scale.orange.5":"#bd561d","scale.orange.6":"#9b4215","scale.orange.7":"#762d0a","scale.orange.8":"#5a1e02","scale.orange.9":"#3d1300"};var tn=n(86565),tr=n(48869);let ti=m.forwardRef(tl);function to({copilotAccessAllowed:e,linesData:t,tabSize:n,onLineNumberClick:r}){let i=(0,m.useRef)(null),l=tc((0,ey.O)(),t);return(0,o.jsx)("div",{className:"d-flex flex-column",style:{tabSize:n},ref:i,"data-hpc":!0,children:l.map((t,n)=>(0,o.jsx)(ts,{...t,index:n,copilotAccessAllowed:e,onLineNumberClick:r},`blame-for-segment-${t.range?.start??t.linesData[0].lineNumber}`))})}function tl({copilotAccessAllowed:e,linesData:t,tabSize:n,materializeAllLines:r,onLineNumberClick:i},l){let a=(0,m.useRef)(null),s=(0,ey.O)(),{screenSize:c}=(0,u.lm)();(0,m.useImperativeHandle)(l,()=>({scrollToTop:()=>{(0,e_.si)(0)||h.scrollToIndex(0,{align:"start"})},scrollToLine:e=>{(0,e_.si)(e)||h.scrollToIndex(e,{align:"center"})}}));let d=(0,m.useCallback)(e=>{let n=t[e],r=s?.ranges[n.lineNumber];return r&&r.end===r.start?window.innerWidth>u.Gy.medium?31:41:20},[t,s]),h=(0,e5.hf)({parentRef:a,size:t.length,overscan:r?Number.MAX_SAFE_INTEGER:100,estimateSize:d}),p=tc(s,h.virtualItems.map(e=>({...t[e.index],virtualOffset:e.start})));return(0,o.jsx)("div",{style:{"--blame-segments-count":p.length,"--blame-virt-total-size":`${h.totalSize}px`,tabSize:n},className:"virtual-blame-wrapper",ref:a,"data-hpc":!0,children:p.map((t,n)=>{let r=t.linesData[0].virtualOffset??0;return c<u.Gy.large&&(r+=(n??0)*41),(0,o.jsx)(ts,{range:t.range,commit:t.commit,linesData:t.linesData,copilotAccessAllowed:e,onLineNumberClick:i,virtualOffset:r},`blame-for-segment-${t.range?.start??t.linesData[0].lineNumber}`)})})}function ta({range:e,commit:t,linesData:n,copilotAccessAllowed:r,onLineNumberClick:i}){return(0,o.jsxs)(o.Fragment,{children:[e&&t?(0,o.jsx)(td,{range:e,commit:t}):(0,o.jsx)("div",{className:"height-full"}),(0,o.jsx)("div",{className:"react-line-code-pairs d-flex flex-column",children:n.map(e=>(0,o.jsxs)("div",{className:"d-flex flex-row",children:[(0,o.jsx)("div",{className:`react-line-numbers ${n.length>1?"":"react-blame-no-line-data"}`,children:(0,o.jsx)(tr.Kn,{codeLineData:e,onClick:i},`line-number-${e.lineNumber}-content-${e.rawText?.substring(0,100)}`)}),(0,o.jsx)("div",{className:`react-code-line ${n.length>1?"":"react-blame-no-line-data"}`,children:(0,o.jsx)(tn.S,{stylingDirectivesLine:e.stylingDirectivesLine,id:`LC${e.lineNumber}`,codeLineData:e,copilotAccessAllowed:r,shouldUseInert:!1},`code-line=${e.lineNumber}-content-${e.rawText?.substring(0,100)}`)})]},`line-pair-${e.lineNumber}`))})]})}let ts=m.memo(function({range:e,commit:t,linesData:n,virtualOffset:r,copilotAccessAllowed:i,onLineNumberClick:l}){return(0,o.jsx)("div",{className:"react-blame-segment-wrapper",style:void 0!==r?{transform:`translateY(${r}px)`,position:"absolute",top:0}:void 0,children:(0,o.jsx)(ta,{range:e,commit:t,linesData:n,copilotAccessAllowed:i,onLineNumberClick:l})})},(e,t)=>e.range===t.range&&e.commit===t.commit&&e.virtualOffset===t.virtualOffset&&(0,e7.A)(e.linesData,t.linesData));function tc(e,t){if(!e)return[{linesData:t}];let n=[],r=null,i=!0;for(let o of t){r||(r={linesData:[]});let t=i?Object.values(e?.ranges??{}).find(e=>e.start<=o.lineNumber&&e.end>=o.lineNumber):e?.ranges[o.lineNumber];t&&(r.range=t,r.commit=e.commits[t.commitOid]),r.linesData.push(o),r.range?.end===o.lineNumber&&(n.push(r),r=null),i=!1}return r&&(n.push(r),r=null),n}let td=m.memo(tu);function tu({range:e,commit:t}){let n=(0,a.t)(),r=new Date(t.committedDate),i=new Date(n.createdAt),l=(0,o.jsx)("div",{className:"timestamp-ago",children:(0,o.jsx)(e8.A,{date:r,tense:"past",sx:{color:"fg.muted",whiteSpace:"nowrap",fontSize:"smaller"}})});return(0,o.jsxs)("div",{className:"react-blame-for-range d-flex",children:[(0,o.jsx)("div",{"aria-hidden":!0,className:"age-indicator",children:(0,o.jsx)(tm,{commitDate:r,repoCreationDate:i})}),(0,o.jsx)("div",{className:"pt-1 timestamp-wrapper-desktop",children:l}),(0,o.jsx)("div",{className:"author-avatar-wrapper",children:t.authorAvatarUrl&&(0,o.jsx)(e2.r,{src:t.authorAvatarUrl,size:18})}),(0,o.jsx)(E.A,{sx:{verticalAlign:"top",pt:[2,2,"6px"],pb:[2,2,0],minWidth:[0,0,170],flexGrow:[1,1,1]},children:(0,o.jsx)("div",{className:"d-flex",children:(0,o.jsx)(e3.JR,{html:t.shortMessageHtmlLink,sx:{whiteSpace:"nowrap",ml:2,overflowX:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontSize:[1,1,0]},"data-hovercard-url":(0,ea.fQd)({owner:n.ownerLogin,repo:n.name,commitish:t.oid})})})}),(0,o.jsxs)(E.A,{sx:{display:"flex",alignContent:"flex-start",justifySelf:"flex-end",verticalAlign:"top",pl:2,pt:["2px","2px","1px"],pb:[1,1,0],width:[150,150,34],pr:[2,2,0]},children:[(0,o.jsx)("div",{className:"pt-1 pr-3 timestamp-wrapper-mobile",children:l}),(0,o.jsx)(th,{range:e,commit:t})]})]})}function th({range:e,commit:t}){let n=(0,a.t)();if(!e.reblamePath)return null;let r=(0,ea.buO)({owner:n.ownerLogin,repo:n.name,commitish:t.firstParentOid,filePath:e.reblamePath}),i=new Intl.DateTimeFormat(void 0,{year:"numeric",month:"short",day:"numeric"}),l=t.oid.slice(0,7),s=i.format(new Date(t.committedDate)),c=`Blame prior to change ${l}, made on ${s}`,d=`reblame-${l}`;return(0,o.jsx)(e4.m,{text:c,type:"label",id:d,children:(0,o.jsx)(e6.N,{"aria-labelledby":d,to:r,className:"Button Button--iconOnly Button--invisible Button--small",children:(0,o.jsx)(P.VersionsIcon,{})})})}function tm({commitDate:e,repoCreationDate:t}){let n=function(e,t){let{resolvedColorScheme:n}=(0,e9.DP)(),r=n?.startsWith("dark"),i=r?tt["scale.orange.9"]:tt["scale.orange.0"];if(e<t)return i;let o=Date.now(),l=Math.min(o-t.getTime(),63072e6),a=(o-e.getTime())/l,s=0;for(let e of te){if(a<e)return r?tt[`scale.orange.${s}`]:tt[`scale.orange.${9-s}`];++s}return i}(e,t);return(0,o.jsx)("div",{className:"blame-age-indicator",style:{backgroundColor:n}})}try{ti.displayName||(ti.displayName="BlameLines")}catch{}try{to.displayName||(to.displayName="BlameLinesSSR")}catch{}try{tl.displayName||(tl.displayName="BlameLinesWithRef")}catch{}try{ta.displayName||(ta.displayName="BlameSegmentContent")}catch{}try{ts.displayName||(ts.displayName="BlameSegment")}catch{}try{ts.displayName||(ts.displayName="BlameSegment")}catch{}try{td.displayName||(td.displayName="BlameForRange")}catch{}try{tu.displayName||(tu.displayName="BlameForRangeUnmemoized")}catch{}try{th.displayName||(th.displayName="ReblameButton")}catch{}try{tm.displayName||(tm.displayName="BlameAgeIndicator")}catch{}var tp=n(75559),tf=n(95321);let tx=m.memo(ty);function ty({linesData:e,onLineNumberClick:t,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:i,tabSize:l,contentWidth:a,copilotAccessAllowed:s,onCollapseToggle:c}){let d=(0,h.ud)().codeWrappingOption.enabled,{rawBlobUrl:u}=(0,eg.A)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(E.A,{className:"react-code-file-contents",role:"presentation","aria-hidden":!0,"data-tab-size":l,"data-paste-markdown-skip":!0,sx:{tabSize:l,isolation:"isolate",position:"relative",width:a,overflow:"auto",maxWidth:d?"100%":"unset"},"data-hpc":!0,children:[(0,o.jsx)("div",{className:"react-line-numbers",style:{pointerEvents:"auto"},children:e.map(e=>(0,o.jsx)(tr.Kn,{codeLineData:e,onClick:t,ownedCodeSections:n,onLineStickOrUnstick:i,onCollapseToggle:c},`line-number-${e.lineNumber}-content:${e.rawText?.substring(0,100)}`))}),(0,o.jsx)("div",{className:"react-code-lines",children:e.map(e=>(0,o.jsx)(tn.S,{codeLineData:e,codeLineClassName:e.codeLineClassName,stylingDirectivesLine:e.stylingDirectivesLine,id:`LC${e.lineNumber}`,onLineStickOrUnstick:i,setIsCollapsed:c,codeLineToSectionMap:r,copilotAccessAllowed:s,measureRef:void 0,shouldUseInert:!1},`line-data-${e.lineNumber}-content:${e.rawText?.substring(0,100)}`))})]}),1e3===e.length&&(0,o.jsx)(E.A,{sx:{justifyContent:"center",display:"flex"},children:(0,o.jsx)(er.A,{href:u,children:"View remainder of file in raw view"})})]})}try{tx.displayName||(tx.displayName="CodeLinesSSR")}catch{}try{ty.displayName||(ty.displayName="CodeLinesSSRUnmemoized")}catch{}var tg=n(60638),tb=n(3124),tv=n(93783),tj=n(84228);let tw=m.memo(tN);function tN({linesData:e,onLineNumberClick:t,codeSections:n,nonTruncatedLinesData:r,colorizedLines:i,onLineStickOrUnstick:l,tabSize:a,contentWidth:s,copilotAccessAllowed:c,onCollapseToggle:d}){let u=(0,h.ud)().codeWrappingOption.enabled,{rawBlobUrl:m}=(0,eg.A)(),p=[...Array(Math.floor(e.length/tf.kd)+1).keys()];return(0,o.jsxs)(E.A,{className:"react-code-file-contents",role:"presentation","aria-hidden":!0,"data-tab-size":a,"data-paste-markdown-skip":!0,sx:{tabSize:a,isolation:"isolate",position:"relative",width:s,overflow:"auto",maxWidth:u?"100%":"unset"},"data-hpc":!0,children:[(0,o.jsx)("div",{className:"react-line-numbers-no-virtualization",style:{pointerEvents:"auto",position:"relative",zIndex:2},children:p.map(r=>{let i=e.slice(r*tf.kd,Math.min(r*tf.kd+tf.kd,e.length));return(0,o.jsx)("div",{className:"react-no-virtualization-wrapper-lines-ssr",children:i.map(e=>(0,o.jsx)(tv.m,{codeLineData:e,onClick:t,ownedCodeSections:n,onLineStickOrUnstick:l,onCollapseToggle:d},`line-number-${e.lineNumber}`))},`line-number-wrapper-${r}-content:${i[0]?.rawText?.substring(0,100)}`)})}),(0,o.jsxs)("div",{className:"react-code-lines",children:[(0,o.jsx)(tb.R,{linesData:e}),(0,o.jsx)(tg.n,{linesData:e,copilotAccessAllowed:c}),(0,o.jsx)(tj.Bg,{linesData:r,colorizedLines:i}),1e3===e.length&&(0,o.jsx)(E.A,{sx:{justifyContent:"center",display:"flex"},children:(0,o.jsx)(er.A,{href:m,children:"View remainder of file in raw view"})})]})]})}try{tw.displayName||(tw.displayName="CodeLinesSSRNoVirtualization")}catch{}try{tN.displayName||(tN.displayName="CodeLinesSSRNoVirtualizationUnmemoized")}catch{}var t_=n(1064),tk=n(24167);function tC({linesData:e,onCodeNavTokenSelected:t,onLineNumberClick:n,isBlame:r,isCursorVisible:i,isVirtualized:l,textAreaRef:s,shouldRenderOverlay:c,tabSize:d,optionalTestLeftOffsetFunction:u,textSelection:h,onCollapseToggle:p,onLineStickOrUnstick:f,optionalTestTopOffsetFunction:y,additionalTextAreaInstructions:g}){let b,[j,N]=(0,m.useState)(0),[_,k]=(0,m.useState)(0),[C,A]=(0,m.useState)(!1),S=(0,m.useRef)(""),B=(0,m.useRef)(null),I=(0,a.t)(),{refInfo:L,path:T}=(0,v.eu)(),{cursorNavigationHighlightLine:R,expandAndFocusLineContextMenu:O,cursorNavigationEnter:D,searchShortcut:F,cursorNavigationPageDown:$,cursorNavigationPageUp:M}=(0,w.wk)(),P=(0,eG.zy)();(0,m.useEffect)(()=>{N(0),k(0)},[P.key]),(0,m.useEffect)(()=>{function e(e){S.current=e.key}return window.oncontextmenu=function(e){if("ContextMenu"===S.current&&-1===e.button&&document.activeElement?.className.indexOf(e1.Dp)!==-1)return S.current="",e?.preventDefault(),e?.stopPropagation(),!1},window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e),window.oncontextmenu=null}},[]);let z=r?eO.cH:eO.M_,H=(0,m.useRef)(null),{onEnter:U,updateUrlForLineNumber:V,onPageUp:G,onPageDown:Y,currentStartLine:q,currentStartChar:K,currentEndLine:Q,currentEndChar:X,determineAndSetTextAreaCursorPosition:Z,getCorrectLineNumberWithCollapsedSections:J}=(0,eO.M1)(H,t,u??N,y??k,e,l,r,n,s,d,g,h),ee=(0,m.useRef)(!1);(0,m.useEffect)(()=>{ee.current=/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)},[]);let et=(0,eQ.o)("react-line-numbers"),en=i?{height:`${et}px`,width:"1.5px",backgroundColor:"fg.default",position:"absolute",visibility:ee.current?"hidden":"visible",zIndex:2}:{};return b=q.current===Q.current&&K.current===X.current?`#L${q.current+1}C${K.current}`:`#${(0,eN.Kn)({start:{line:q.current+1,column:K.current},end:{line:Q.current+1,column:X.current}})}`,(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(E.A,{"aria-hidden":!0,style:{top:_,left:z+j},sx:en,ref:H,"data-testid":"navigation-cursor",className:"code-navigation-cursor",children:" "}),c&&(0,o.jsx)("div",{style:{top:_+et,left:z+j,zIndex:2},className:"position-absolute border rounded-2 color-bg-subtle px-3 py-2",children:(0,o.jsxs)(W.A,{sx:{pointerEvents:"auto"},children:["Code view is read-only."," ",L.canEdit&&(0,o.jsx)(e6.N,{to:(0,ea.Nyd)({owner:I.ownerLogin,repo:I.name,filePath:T,commitish:L.name,hash:b}),children:"Switch to the editor."})]})}),C&&(0,o.jsx)(e1.Ay,{ref:B,rowBeginId:`LG${J(q.current)}`,rowBeginNumber:J(q.current),rowEndNumber:J(Q.current),rowEndId:`LG${J(Q.current)}`,openOnLoad:!0,cursorRef:H,onCollapseToggle:p,onLineStickOrUnstick:f,lineData:function(){if(q.current!==Q.current)return null;let t=e[q.current];if(!t)return null;if(t.isStartLine)return t;if(""===t.codeLineClassName)return null;let n=t.codeLineClassName?.split("child-of-line-");if(!n||t.codeLineClassName?.indexOf("child-of-line-")===-1)return null;let r=parseInt(n[n.length-1]?.trim()??"undefined");return r&&!Number.isNaN(r)?e[r-1]&&e[r-1]?.lineNumber===r?e[r-1]:e[(0,e_.N9)(r,e)]:null}(),onMenuClose:function(e,t){A(e),setTimeout(()=>{t&&Z(),s?.current?.focus()},300)}}),(0,o.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorEnter","data-hotkey":D.hotkey,onClick:U,"data-hotkey-scope":e_.wQ}),(0,o.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorSetHighlightedLine","data-hotkey":R.hotkey,onClick:V,"data-hotkey-scope":e_.wQ}),(0,o.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorSetHighlightAndExpandMenu","data-hotkey":O.hotkey,onClick:e=>(e.preventDefault(),e.stopPropagation(),A(!0),setTimeout(()=>{B.current?.setAnchor(H.current)},0),!1),"data-hotkey-scope":e_.wQ}),(0,o.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorPageDown","data-hotkey":$.hotkey,onClick:Y,"data-hotkey-scope":e_.wQ}),(0,o.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorPageUp","data-hotkey":M.hotkey,onClick:G,"data-hotkey-scope":e_.wQ}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:F.hotkey,onButtonClick:()=>{(0,tk.Z)({retainScrollPosition:!0,returnTarget:s?.current??void 0})},onlyAddHotkeyScopeButton:!0})]})}try{tC.displayName||(tC.displayName="NavigationCursor")}catch{}var tA=n(53491),tS=n(2724),tB=n(40961);function tI({onDismiss:e}){let{expandAndFocusLineContextMenu:t,cursorNavigationEnter:n,cursorNavigationHighlightLine:r}=(0,w.wk)();return(0,tB.createPortal)((0,o.jsx)(tS.l,{width:"large","aria-label":"Code Blob Focused Hotkeys",onClose:e,title:"Code Blob Focused Hotkeys",children:(0,o.jsx)("div",{children:(0,o.jsxs)("div",{children:[(0,o.jsxs)("div",{className:"p-1",children:["Select the line the cursor is on ",(0,o.jsx)("kbd",{children:r.text})]}),(0,o.jsxs)("div",{className:"p-1",children:["Select the symbol under the cursor ",(0,o.jsx)("kbd",{children:n.text})]}),(0,o.jsxs)("div",{className:"p-1",children:["Move focus to the highlighted line menu ",(0,o.jsx)("kbd",{children:t.text})]})]})})}),document.body)}try{tI.displayName||(tI.displayName="TextAreaHelpDialog")}catch{}function tL({textAreaRef:e,setTextOverlayShouldBeVisible:t,setTextSelection:n,setAdditionalTextAreaInstructions:r,cursorClickStartRef:i,parentRef:l,tabSize:a,plainTextLinesAsString:s,numLines:c,setIsTextAreaFocused:d}){let p=(0,eO.fY)(),f=(0,h.ud)().codeWrappingOption,y=(0,w.V3)(),g=(0,w._y)(),b=(0,eQ.o)("react-line-numbers"),[v,j]=(0,m.useState)(!1),{cursorNavigationOpenHelpDialog:N}=(0,w.wk)(),_=(0,e9.DP)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("textarea",{id:e_.wQ,"data-testid":e_.wQ,ref:e,onMouseUp:r=>(function(e,t,n,r,i,o,l,a){if(o&&!e.defaultPrevented&&i){if(2===e.button)e.preventDefault(),e.stopPropagation();else if(0===e.button){n(!1);let o=(0,u.nn)(window.innerWidth)<u.Gy.medium,s=l.current?.getBoundingClientRect().top?window.scrollY+l.current?.getBoundingClientRect().top:o?423:354;if(l.current&&e.pageY>s+l.current?.clientHeight){i.current={startX:-2,startY:-2};return}let c=(0,e_.t1)(e.pageY,s,a),d=l.current?.getBoundingClientRect().left||0,h=e.clientX-d-eO.M_,m=!1;(c<i.current.startY||c===i.current.startY&&h<i.current.startX)&&(m=!0),setTimeout(()=>{t&&t.current&&r({start:t.current.selectionStart,end:t.current.selectionEnd,keyboard:!1,displayStart:m})},0)}}})(r,e,t,n,i,p,l,b),onMouseDown:e=>(function(e,t,n,r,i){if(n&&!e.defaultPrevented&&r){if(2===e.button){e.preventDefault(),e.stopPropagation();return}else if(0===e.button){if(e.ctrlKey){e.preventDefault(),e.stopPropagation();return}let n=(0,u.nn)(window.innerWidth)<u.Gy.medium,o=t.current?.getBoundingClientRect().top?window.scrollY+t.current?.getBoundingClientRect().top:n?423:354;if(t.current&&e.pageY>o+t.current?.clientHeight){r.current={startX:-2,startY:-2};return}let l=(0,e_.t1)(e.pageY,o,i),a=t.current?.getBoundingClientRect().left||0;r.current={startX:e.clientX-a-eO.M_,startY:l}}}})(e,l,p,i,b),"aria-label":"file content","aria-readonly":!0,inputMode:"none",tabIndex:0,"aria-multiline":!0,"aria-haspopup":!1,"data-gramm":"false","data-gramm_editor":"false","data-enable-grammarly":"false",style:{resize:"none",marginTop:-2,paddingLeft:eO.M_,paddingRight:eO.di,display:"hidden",width:"100%",backgroundColor:"unset",boxSizing:"border-box",color:"transparent",position:"absolute",border:"none",tabSize:a,outline:"none",overflowX:"auto",height:b*(c+1),fontSize:"12px",lineHeight:"20px",overflowY:"hidden",overflowWrap:f.enabled?"anywhere":"normal",overscrollBehaviorX:"none",whiteSpace:f.enabled?"pre-wrap":"pre",zIndex:1},value:s,onKeyDown:function(i){y.includes(i.key)||g.includes(i.key)&&(i.getModifierState("Control")||i.getModifierState("Alt")||i.getModifierState("Shift")||i.getModifierState("Meta"))?(" "===i.key&&(i.preventDefault(),i.shiftKey?r(`PageUp${Date.now()}`):r(`PageDown${Date.now()}`)),i.altKey&&i.ctrlKey&&"\u02D9"===i.key&&j(!0),t(!1),setTimeout(()=>{e.current&&n({start:e.current.selectionStart,end:e.current.selectionEnd,keyboard:!0,displayStart:!1})},5)):!i.ctrlKey&&!i.metaKey&&!i.altKey&&!i.shiftKey&&(function(e,t){let n=e.exec(t);return n&&n[0]===t}(/[a-zA-Z0-9-_ ]{1,1}/,i.key)||"Backspace"===i.key||"Enter"===i.key)&&((0,Z.i)("Code view is read only."),t(!0),i.preventDefault())},spellCheck:!1,autoCorrect:"off",autoCapitalize:"off",autoComplete:"off","data-ms-editor":"false",onDrop:e=>{let t=e.dataTransfer.getData("Text");try{let e=new URL(t);window.open(e,"_blank")?.focus()}catch{}return!1},onPaste:e=>(e.preventDefault(),!1),onChange:()=>{},className:`react-blob-textarea react-blob-print-hide${_.resolvedColorScheme?.endsWith("contrast")?" select-contrast":""}`,onFocus:()=>{d(!0)}}),v&&(0,o.jsx)(tI,{onDismiss:()=>{j(!1)}}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:N.hotkey,onButtonClick:()=>{j(!0)},onlyAddHotkeyScopeButton:!0})]})}try{tL.displayName||(tL.displayName="TextArea")}catch{}let tT=(0,eY.A)("localStorage");function tE({blobLinesHandle:e,onCodeNavTokenSelected:t,codeSections:n,codeLineToSectionMap:r,validCodeNav:i,onLineStickOrUnstick:l,searchResults:a,focusedSearchResult:s}){let{rawLines:c,colorizedLines:d,stylingDirectives:u,tabSize:p}=(0,eg.A)(),f=!!(0,ey.O)(),[x,y]=(0,m.useState)(!1),g=(0,eO.fY)(),b=(0,eQ.o)("react-line-numbers"),[j,w]=(0,m.useState)(void 0),[N,_]=(0,m.useState)({start:-1,end:-1,keyboard:!0,displayStart:!1}),[k,C]=(0,m.useState)(""),[A,S]=(0,m.useState)(!1),B=(0,m.useRef)(null),[I,L]=(0,m.useState)(!1),T=(0,m.useRef)({startX:0,startY:0}),{hash:R}=(0,eG.zy)(),{refInfo:D,path:F,copilotAccessAllowed:$}=(0,v.eu)(),M=(0,m.useRef)(null),[P,z]=(0,m.useState)(void 0),[H]=(0,eq.I)(()=>!1,!0,[]),W=(0,m.useRef)(null),U=(0,h.ud)().codeWrappingOption.enabled,V=(0,eX.u)(),G=(0,eb.YP)(),Y=(0,m.useCallback)(()=>{w((0,eN.$c)(window.location.hash)?.blobRange)},[]),{findInFileOpen:q,setFindInFileOpen:K}=e$(),{lines:Q,plainTextLinesAsString:X,nonTruncatedLinesData:Z}=function(e,t,n,r,i){let o=function(e,t,n,r,i){let[o]=(0,eq.I)(()=>e.length,Math.min(e.length,1e3),[e]);return(0,m.useMemo)(()=>Array(o).fill(null).map((e,t)=>t+1).map(o=>{let l,a=t?.[o-1],s=!1,c=!1;for(let e of n?.get(o)??[])e.startLine===o&&(s=!0,l=e),e.endLine===o&&(c=!0);let d=e[o-1]?.replace(/[\n\r]/g,"")??"",u=i&&i.get(o)||[],h=(0,e_.Bn)(u,o,c,n);return{stylingDirectivesLine:a,lineNumber:o,codeLineClassName:h,isStartLine:s,isEndLine:c,ownedSection:l,rawText:d,hiddenUnicode:(0,eJ.Y)(d),codeownersLineError:r?.find(e=>e.line===o)}}),[o,t,e,i,n,r])}(e,t,n,r,i),l=(0,eJ.bp)(),a=(0,m.useRef)("");(0,e_.Wb)(e=>a.current=function(e){let t=[...e];return t.sort(),t.join(",")}(e));let s=(0,m.useMemo)(()=>(function(e,t){let n=new Set;for(let r=0;r<e.length;r++){if(n.has(r))continue;let e=t?.get(r)??[];for(let t=0;t<e.length;t++)if(e[t].collapsed){for(let r=e[t].startLine+1;r<=e[t].endLine;r++)n.add(r);e[t].startLine===r&&(r=e[t].endLine);break}}return e.filter(e=>!n.has(e.lineNumber))})(o,n),[o,n,a.current]),c=s.map(e=>l&&e.rawText?(0,eJ.TA)(e.rawText):e.rawText).join(`
`);return{lines:s,plainTextLinesAsString:c,nonTruncatedLinesData:o}}(c??[],u??null,n??null,(0,m.useContext)(eK),r),J=(0,tf.G)(Z.length)&&d&&0!==d.length,ee=(0,m.useRef)(Q);ee.current=Q,(0,m.useEffect)(()=>{(0,e_.X5)()},[D.currentOid,F]),(0,m.useEffect)(()=>{window.onbeforeprint=()=>y(!0),window.onafterprint=()=>y(!1)},[]),(0,m.useEffect)(()=>{eR(null,null,U)},[]),(0,m.useEffect)(()=>{"true"!==tT.getItem("heardHelpAnnouncement")&&((0,e_.kY)("While the code is focused, press Alt+F1 for a menu of operations.",2e3),tT.setItem("heardHelpAnnouncement","true"))},[]);var et=(0,m.useCallback)(e=>{if(e&&e?.node&&!f){if(!e.node.textContent||e.node.textContent.length<3)return;let n=e.node;!n||!n.hasAttribute||n.hasAttribute("clickadded")||(n.classList.add("pl-token"),n.setAttribute("clickadded","true"),n.addEventListener("click",function(n){let r=n.target.textContent?n.target.textContent:"";t&&e&&(t({selectedText:r,lineNumber:e.lineNumber,offset:e.offset}),q&&K(!1))}))}},[q,f,K,t]);let en=!!(0,ey.O)(),er=(0,eO.fY)();(0,m.useEffect)(()=>{let e;if(en||!i||er)return;let t=(0,O.s)(t=>{clearTimeout(e),e=setTimeout(()=>{!function(e,t){let n=function(e,t,n){let r,i;if(document.caretPositionFromPoint){let e=document.caretPositionFromPoint(t,n);e&&(r=e.offsetNode,i=e.offset)}else if(document.caretRangeFromPoint){let e=document.caretRangeFromPoint(t,n);e&&(r=e.startContainer,i=e.startOffset)}if(!r||"number"!=typeof i||r.nodeType!==Node.TEXT_NODE||!r.textContent)return null;let o=function(e,t,n){let r,i=null;for(;r=t.exec(e);){if(t.lastIndex===i){(0,eZ.N7)(Error("regexp did not advance in findNearestMatch()"));break}i=t.lastIndex;let e=r.index+r[0].length;if(r.index<=n&&n<=e)return[r[0],r.index,e]}return null}(r.textContent,e,i);if(!o)return null;let l=document.createRange();return l.setStart(r,o[1]),l.setEnd(r,o[2]),l}(/\w+[!?]?/g,e.clientX,e.clientY);if(!n)return;let r=n.commonAncestorContainer.parentElement;if(r)for(let e of r.classList){if(["pl-token","pl-c","pl-s","pl-k"].includes(e))return null;let r=n.toString();if(!r||r.match(/\n|\s|[();&.=",]/))return null;let{lineNumber:i,offset:o,node:l}=function(e){let t=e.startContainer,n=e.startOffset;for(;;){let e=t.previousSibling;for(;e;)n+=(e.textContent||"").length,e=e.previousSibling;let r=t.parentElement;if(!r)return{lineNumber:0,offset:0,node:null};if(r.classList.contains("react-file-line"))return{lineNumber:parseInt(r.getAttribute("data-line-number")||"1",10),offset:n,node:t};t=r}}(n);if(0===i&&0===o||!l)return null;return t({lineNumber:i,offset:o,node:l})}}(t,et)},15)},5);return window.addEventListener("mousemove",t),()=>{window.removeEventListener("mousemove",t)}},[et,en,i,er]);let ei=({line:t,column:n})=>{if(t<10)e.current?.scrollToTop();else if(e.current){if(-1===(0,e_.N9)(t,Q)){for(let e of r?.get(t)??[])e&&e.collapsed&&(e.collapsed=!1,(0,e_.ny)(e?.startLine));V()}setTimeout(()=>{let r=(0,e_.N9)(t,ee.current);e.current?.scrollToLine(r,n)},0),G&&!(0,e_.si)(t)&&(l(null,!0),(0,e_.CZ)(G,t,r,l))}};return(0,m.useEffect)(()=>{let e=(0,eN.$c)(R);if(!e.blobRange?.start?.line)return void w(void 0);w(e.blobRange)},[F,R,Q.length]),(0,m.useEffect)(()=>{let e=(0,eN.$c)(R);if(!e.blobRange?.start?.line)return;let t=window.setTimeout(()=>ei({line:e.blobRange.start.line}),0);return()=>{window.clearTimeout(t)}},[F,f]),(0,m.useEffect)(()=>{let e=M.current;if(!e||!g)return void z(void 0);z(e.scrollWidth>e.clientWidth?e.scrollWidth:void 0);let t=new ResizeObserver(e=>{for(let{target:t}of e)z(t.scrollWidth>t.clientWidth?t.scrollWidth:void 0)});return t.observe(e),()=>{t.disconnect()}},[g,F]),(0,ek.g)(ei),(0,o.jsx)(tA.gt,{searchResults:a,focusedSearchResult:s,children:(0,o.jsxs)(t_.iU,{highlightedLines:j,children:[Q.some(e=>e.hiddenUnicode)&&(0,o.jsx)(eJ.om,{}),(0,o.jsx)(E.A,{sx:{display:"flex",flex:1,py:2*!f,flexDirection:"column",justifyContent:"space-between",minWidth:0,position:"relative"},children:(0,o.jsx)(e1.AG,{children:(0,o.jsx)(e0.eT,{children:(0,o.jsxs)(E.A,{sx:{flex:1,position:"relative",minWidth:0,overflowX:f?"auto":void 0,overflowY:f?"hidden":void 0},ref:B,onBlur:e=>{e.currentTarget.contains(e.relatedTarget)||S(!1)},children:[g&&(0,o.jsx)(E.A,{ref:W,sx:{position:"absolute",width:"100%",overflow:"hidden"},children:(0,o.jsx)(E.A,{sx:{width:P,height:b*Q.length},children:(0,o.jsx)(tC,{linesData:Q,isBlame:!1,onCodeNavTokenSelected:t,onLineNumberClick:Y,isCursorVisible:!!A,isVirtualized:!0,textAreaRef:M,onCollapseToggle:V,onLineStickOrUnstick:l,tabSize:p,textSelection:N,shouldRenderOverlay:!!I,additionalTextAreaInstructions:k??""})})}),g&&(0,o.jsx)(tL,{textAreaRef:M,setTextOverlayShouldBeVisible:L,setTextSelection:_,setAdditionalTextAreaInstructions:C,cursorClickStartRef:T,parentRef:B,tabSize:p,plainTextLinesAsString:X,numLines:Q.length,setIsTextAreaFocused:S}),f?H?(0,o.jsx)(to,{linesData:Q,tabSize:p,copilotAccessAllowed:$,onLineNumberClick:Y}):(0,o.jsx)(ti,{ref:e,linesData:Q,tabSize:p,copilotAccessAllowed:$,onLineNumberClick:Y}):H?J?(0,o.jsx)(tw,{linesData:Q,nonTruncatedLinesData:Z,onLineNumberClick:Y,colorizedLines:d,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:l,tabSize:p,contentWidth:P,copilotAccessAllowed:$,onCollapseToggle:V}):(0,o.jsx)(tx,{linesData:Q,onLineNumberClick:Y,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:l,tabSize:p,contentWidth:P,copilotAccessAllowed:$,onCollapseToggle:V}):J?(0,o.jsx)(tf.P9,{ref:e,linesData:Q,nonTruncatedLinesData:Z,onLineNumberClick:Y,colorizedLines:d,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:l,tabSize:p,textAreaRef:M,cursorContainerRef:W,contentWidth:P??M.current?.clientWidth,onCollapseToggle:V,materializeAllLines:x,copilotAccessAllowed:$}):(0,o.jsx)(tp.FL,{ref:e,linesData:Q,onLineNumberClick:Y,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:l,tabSize:p,contentWidth:P,cursorContainerRef:W,onCollapseToggle:V,textAreaRef:M,materializeAllLines:x,copilotAccessAllowed:$})]})})})})]})})}try{tE.displayName||(tE.displayName="CodeBlob")}catch{}function tR({displayName:e,displayUrl:t}){return(0,o.jsx)(E.A,{sx:{display:"flex",justifyContent:"center",width:"100%"},children:(0,o.jsx)(E.A,{as:"img",alt:e,src:t,"data-hpc":!0,sx:{maxWidth:"100%"}})})}try{tR.displayName||(tR.displayName="ImageBlob")}catch{}var tO=n(10569),tD=n(19647),tF=n(19797),t$=n(73451),tM=n(65607);let tP={Issue:"issue",Discussion:"discussion"};function tz({issueTemplate:e,type:t}){return(0,o.jsxs)(E.A,{sx:{borderBottomLeftRadius:"6px",borderBottomRightRadius:"6px",p:5},children:[(0,o.jsxs)(E.A,{as:"table",sx:{mb:3},children:[(0,o.jsx)("thead",{children:(0,o.jsxs)("tr",{children:[t===tP.Issue?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(tH,{header:!0,children:"Name"}),(0,o.jsx)(tH,{header:!0,children:"About"})]}):(0,o.jsx)(tH,{header:!0,children:"Title"}),e.type&&(0,o.jsx)(tH,{header:!0,children:"Type"}),(0,o.jsx)(tH,{header:!0,children:"Labels"}),e.projects&&(0,o.jsx)(tH,{header:!0,children:"Projects"}),t===tP.Issue&&(0,o.jsx)(tH,{header:!0,children:"Assignees"})]})}),(0,o.jsx)("tbody",{children:(0,o.jsxs)("tr",{children:[t===tP.Issue?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(tH,{children:e.name}),(0,o.jsx)(tH,{children:e.about})]}):(0,o.jsx)(tH,{children:e.title}),e.type&&(0,o.jsx)(tH,{children:e.type}),(0,o.jsx)(tH,{children:e.labels}),e.projects&&(0,o.jsx)(tH,{children:e.projects}),t===tP.Issue&&(0,o.jsx)(tH,{children:e.assignees})]})})]}),e.inputs.map((e,t)=>(0,o.jsx)(tW,{input:e},t))]})}function tH({children:e,header:t}){return(0,o.jsx)(E.A,{as:t?"th":"td",sx:{p:"6px 13px",border:"1px solid var(--borderColor-default, var(--color-border-default))"},children:e})}function tW({input:e}){switch(e.type){case"markdown":return(0,o.jsx)(tU,{input:e});case"dropdown":return(0,o.jsx)(tV,{input:e});case"input":return(0,o.jsx)(tG,{input:e});case"textarea":return(0,o.jsx)(tY,{input:e});case"checkboxes":return(0,o.jsx)(tq,{input:e});default:return null}}function tU({input:e}){return e.value?(0,o.jsx)(e3.vb,{html:e.value}):null}function tV({input:e}){let t=e.options?.slice();e.required||t?.unshift("None");let n=e.multiple?"Selections: ":"Selection: ";return e.value&&(n+=e.value),(0,o.jsx)(tK,{input:e,sx:{alignItems:"start"},children:(0,o.jsxs)(tO.W,{children:[(0,o.jsx)(tO.W.Button,{children:n}),(0,o.jsx)(tO.W.Overlay,{width:"medium",children:(0,o.jsx)(et.l,{selectionVariant:e.multiple?"multiple":"single",children:t?.map((t,n)=>(0,o.jsx)(et.l.Item,{selected:t===e.value,disabled:!0,children:t},n))})})]})})}function tG({input:e}){return(0,o.jsx)(tK,{input:e,children:(0,o.jsx)(H.A,{placeholder:e.placeholder,value:e.value??""})})}function tY({input:e}){return(0,o.jsx)(tK,{input:e,children:(0,o.jsx)(tD.Ay,{placeholder:e.placeholder,value:e.value??"",sx:e.render?{fontFamily:"mono"}:{}})})}function tq({input:e}){return e.checkboxes?(0,o.jsxs)(tF.A,{disabled:!0,sx:{color:"var(--fgColor-default, var(--color-fg-default)) !important",my:"15px"},children:[(0,o.jsx)(tF.A.Label,{sx:{color:"var(--fgColor-default, var(--color-fg-default))",fontSize:["18px","18x","20px"],fontWeight:600},children:e.label}),e.description&&(0,o.jsx)(tF.A.Caption,{sx:{color:"var(--fgColor-muted, var(--color-fg-subtle))",fontSize:"12px"},children:(0,o.jsx)(e3.vb,{html:e.description})}),e.checkboxes.map((e,t)=>(0,o.jsxs)(t$.A,{disabled:!0,required:e.required,children:[(0,o.jsx)(tM.A,{}),(0,o.jsx)(t$.A.Label,{children:e.label})]},t))]}):null}function tK({children:e,input:t,sx:n}){return(0,o.jsxs)(t$.A,{disabled:!0,required:t.required,sx:{my:"15px",...n},children:[(0,o.jsx)(t$.A.Label,{sx:{color:"var(--fgColor-default, var(--color-fg-default))",fontSize:["18px","18x","20px"],"> span > span:last-of-type":{color:"var(--fgColor-danger, var(--color-danger-fg))"}},children:t.label}),t.description&&(0,o.jsx)(t$.A.Caption,{children:(0,o.jsx)(e3.vb,{html:t.description})}),e]})}try{tz.displayName||(tz.displayName="YamlTemplateContent")}catch{}try{tH.displayName||(tH.displayName="MarkdownTableCell")}catch{}try{tW.displayName||(tW.displayName="TemplateInput")}catch{}try{tU.displayName||(tU.displayName="MarkdownInput")}catch{}try{tV.displayName||(tV.displayName="DropdownInput")}catch{}try{tG.displayName||(tG.displayName="InputInput")}catch{}try{tY.displayName||(tY.displayName="TextareaInput")}catch{}try{tq.displayName||(tq.displayName="CheckboxesInput")}catch{}try{tK.displayName||(tK.displayName="InputWrapper")}catch{}let tQ=(0,m.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_CSV_CSVBlob_tsx").then(n.bind(n,27602))),tX=(0,m.lazy)(()=>n.e("ui_packages_file-renderer-blob_FileRendererBlob_tsx").then(n.bind(n,89323)));function tZ({setOpenPanel:e,codeNavInfo:t,validCodeNav:n,onCodeNavTokenSelected:r,onLineStickOrUnstick:i,searchResults:l,setSearchTerm:a,blobLinesHandle:s,focusedSearchResult:c}){let d=!!(0,ey.O)(),{rawLines:u}=(0,eg.A)(),{sendRepoKeyDownEvent:h}=(0,F.T)(),{findInFileShortcut:p}=(0,w.wk)(),f=eP(),{setFindInFileOpen:y}=e$(),g=f===eM.Code,b=g&&null!=u&&n?p.hotkey:"",v=(0,eO.fY)();(0,m.useEffect)(()=>{g||e(void 0)},[g,e]);let j=g||f===eM.CSV||f===eM.Markdown?{}:{overflow:"auto"},N=f===eM.Markdown?{justifyContent:"center"}:{};return(0,o.jsxs)(E.A,{as:"section","aria-labelledby":"file-name-id-wide file-name-id-mobile",sx:{backgroundColor:"var(--bgColor-default, var(--color-canvas-default))",border:"0px",borderWidth:0,borderRadius:"0px 0px 6px 6px",p:0,minWidth:0,mt:d?"92px":"46px",...N,...j},children:[(0,o.jsx)(tJ,{blobLinesHandle:s,onCodeNavTokenSelected:r,codeSections:d?void 0:t?.codeSections,codeLineToSectionMap:t?t.lineToSectionMap:void 0,validCodeNav:n,onLineStickOrUnstick:i,searchResults:l,focusedSearchResult:c}),g&&!v&&(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:b,onButtonClick:function(){y(!0),h("BLOB_FIND_IN_FILE_MENU.OPEN");let e=window.getSelection()?.toString();e&&a(e)},buttonTestLabel:"hotkey-button"})]})}let tJ=m.memo(function({onCodeNavTokenSelected:e,codeSections:t,codeLineToSectionMap:n,validCodeNav:r,onLineStickOrUnstick:i,searchResults:l,blobLinesHandle:a,focusedSearchResult:s}){let c=(0,eg.A)(),d=eP();switch(d){case eM.TooLargeError:return(0,o.jsxs)(E.A,{sx:{textAlign:"center"},"data-hpc":!0,children:[(0,o.jsx)(er.A,{href:c.rawBlobUrl,children:"View raw"}),c.large&&(0,o.jsx)("p",{children:"(Sorry about that, but we can\u2019t show files that are this big right now.)"})]});case eM.Code:return(0,o.jsx)(tE,{blobLinesHandle:a,onCodeNavTokenSelected:e,codeSections:t,codeLineToSectionMap:n,validCodeNav:r,onLineStickOrUnstick:i,searchResults:l,focusedSearchResult:s});case eM.Markdown:return(0,o.jsx)(eU,{richText:c.richText,sx:{borderBottomLeftRadius:"6px",borderBottomRightRadius:"6px",p:5,minWidth:0}});case eM.CSV:return(0,o.jsx)(m.Suspense,{fallback:(0,o.jsx)(eV.f,{}),children:(0,o.jsx)(tQ,{csv:c.csv})});case eM.FileRenderer:return(0,o.jsx)(m.Suspense,{fallback:(0,o.jsx)(eV.f,{}),children:(0,o.jsx)(tX,{identityUuid:c.renderedFileInfo.identityUUID,size:c.renderedFileInfo.size,type:c.renderedFileInfo.renderFileType,url:c.displayUrl},c.renderedFileInfo.identityUUID)});case eM.Image:return(0,o.jsx)(tR,{displayName:c.displayName,displayUrl:c.displayUrl});case eM.IssueTemplate:return(0,o.jsx)(tz,{issueTemplate:c.issueTemplate?c.issueTemplate:c.discussionTemplate,type:c.issueTemplate?tP.Issue:tP.Discussion,"data-hpc":!0});default:ez(d)}});try{tQ.displayName||(tQ.displayName="CSVBlob")}catch{}try{tX.displayName||(tX.displayName="FileRendererBlob")}catch{}try{tZ.displayName||(tZ.displayName="BlobContent")}catch{}try{tJ.displayName||(tJ.displayName="Blob")}catch{}var t0=n(73081),t1=n(7956),t2=n(24208),t6=n(99543),t3=n(91321),t5=n(10871),t8=n(34164),t4=n(68048),t7=n(99987);function t9(e,t,n){let[r,i]=(0,m.useState)(""),l=(0,m.useCallback)(()=>{i("")},[]);return[(0,m.useCallback)(e=>{i(e),t.current!==document.activeElement&&setTimeout(l,3e3)},[l,t]),l,(0,o.jsx)(ne,{message:r,id:e,contentRef:t,clearMessage:l,portalTooltipProps:n},e)]}function ne({message:e,id:t,contentRef:n,clearMessage:r,portalTooltipProps:i}){return e?(0,o.jsx)(t7.m,{id:t,contentRef:n,"aria-label":e,open:!!e,onMouseLeave:r,"aria-live":"assertive",...i}):null}try{ne.displayName||(ne.displayName="AlertTooltip")}catch{}var nt=n(13317),nn=n(9826);function nr({disabled:e,...t}){let n=e?{"aria-disabled":!0,onClick:e=>e.preventDefault()}:{};return(0,o.jsx)(z.K,{size:"small",...t,...n})}try{nr.displayName||(nr.displayName="AccessibleIconButton")}catch{}var ni=n(47139);let no=(0,m.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_CodeNav_ScrollMarks_tsx").then(n.bind(n,31513)));function nl({stickied:e,searchTerm:t,searchResults:n,setSearchTerm:r,focusedSearchResult:i,setFocusedSearchResult:l,onClose:a}){let s=(0,eO.fY)(),c=(0,m.useRef)(null),{findInFileShortcut:d,findSelectionShortcut:u,findNextShortcut:h,findPrevShortcut:p}=(0,w.wk)(),[f,y]=(0,m.useState)(!0),g=()=>{r(""),l(0)},{sendRepoKeyDownEvent:b}=(0,F.T)(),v=e=>{if(void 0===i)return void l(0);1===e?l(i===n.length-1?0:i+1):l(0===i?n.length-1:i-1)};(0,m.useEffect)(()=>{c.current?.focus(),c.current?.select()},[]);let j=()=>{let e=window.getSelection()?.toString();e?.length&&(r(e),l(0),b("BLOB_FIND_IN_FILE_MENU.FIND_IN_FILE_FROM_SELECTION")),c.current?.focus(),c.current?.select()};return((0,m.useEffect)(()=>{n.length>0&&void 0!==i&&(0,ek.f)({line:n[i].lineNumber,column:n[i].ident.start.column})},[n,i]),s)?null:(0,o.jsxs)("div",{className:`find-in-file-popover ${e?"find-in-file-popover-stickied":"find-in-file-popover-not-stickied"}`,children:[(0,o.jsxs)(E.A,{sx:{fontSize:0,py:2,pl:3,pr:2,borderBottom:"1px solid var(--borderColor-default, var(--color-border-default))",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",alignItems:"baseline"},children:[(0,o.jsx)(W.A,{as:"h5",sx:{color:"fg.default",pr:2,fontWeight:"bold"},children:"Find"}),(0,o.jsxs)(W.A,{className:"find-text-help-tooltip",sx:{color:"fg.subtle",visibility:f?"visible":"hidden"},children:["Press ",(0,o.jsx)(ni.A,{children:d.text})," again to open the browser's find menu"]})]}),(0,o.jsx)(E.A,{sx:{flex:1}}),(0,o.jsx)(z.K,{variant:"invisible",size:"small",onClick:a,icon:P.XIcon,sx:{color:"fg.subtle"},"aria-label":"Close find in file"})]}),(0,o.jsxs)(E.A,{sx:{px:2,py:"6px"},children:[(0,o.jsx)(H.A,{ref:c,sx:{pl:1,border:"none",boxShadow:"none"},validationStatus:n.length>1e3?"error":void 0,type:"text",leadingVisual:()=>(0,o.jsx)(U.A,{icon:P.SearchIcon,"aria-hidden":"true"}),"aria-labelledby":"find-in-file-label","aria-expanded":"true",autoComplete:"off",name:"Find in file input",placeholder:"Search this file",value:t,block:!0,onChange:e=>{e.target.value?(y(!1),r(e.target.value),void 0===i&&l(0)):(y(!0),g())},trailingAction:(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[(0,o.jsxs)(W.A,{className:"text-small",sx:{textAlign:"center",color:"fg.subtle",m:2},children:[0===n.length||void 0===i?0:i+1,"/",n.length]}),(0,o.jsx)(z.K,{size:"small",variant:"invisible",onClick:()=>{v(-1)},icon:P.ChevronUpIcon,"aria-label":"Up","data-testid":"up-search",sx:{color:"fg.subtle"}}),(0,o.jsx)(z.K,{size:"small",variant:"invisible",onClick:()=>{v(1)},icon:P.ChevronDownIcon,"aria-label":"Down","data-testid":"down-search",sx:{color:"fg.subtle"}})]}),onKeyDown:e=>{"Enter"===e.code||"NumpadEnter"===e.code?e.shiftKey?v(-1):v(1):(e.metaKey||e.ctrlKey)&&("g"===e.key||"G"===e.key)?(e.preventDefault(),e.shiftKey?v(-1):v(1)):(e.metaKey||e.ctrlKey)&&("f"===e.key||"F"===e.key)?f?(b("BLOB_FIND_IN_FILE_MENU.FALLBACK_TO_BROWSER_SEARCH"),a()):(y(!0),e.preventDefault(),c.current?.focus(),c.current?.select()):"Escape"===e.key&&a()}}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:d.hotkey,onButtonClick:j,buttonTestLabel:"hotkey-button"}),(0,o.jsx)("button",{hidden:!0,"data-hotkey":u.hotkey,onClick:j,"data-testid":"selection-hotkey"}),(0,o.jsx)("button",{hidden:!0,"data-hotkey":h.hotkey,onClick:()=>v(1),"data-testid":"find-next-button"}),(0,o.jsx)("button",{hidden:!0,"data-hotkey":p.hotkey,onClick:()=>v(-1),"data-testid":"find-prev-button"}),(0,o.jsx)(m.Suspense,{fallback:null,children:(0,o.jsx)(no,{definitionsOrReferences:n})})]})]})}try{no.displayName||(no.displayName="ScrollMarks")}catch{}try{nl.displayName||(nl.displayName="FindInFilePopover")}catch{}var na=n(99921),ns=n(40245),nc=n(70892),nd=n(12528);function nu({showTitle:e=!0}){let{sendRepoClickEvent:t}=(0,F.T)(),[n,r]=(0,m.useState)(!1),i=(0,a.t)(),{refInfo:l,path:s}=(0,v.eu)(),{contributors:c,loading:d,error:u}=(0,ns.u)(i.ownerLogin,i.name,l.name,s);if(u)return(0,o.jsx)(np,{});if(d)return(0,o.jsx)(na.r,{width:100,"data-testid":"contributors-skeleton"});if(!c||!c?.users.length)return null;let{users:h,totalCount:p}=c,f=nm(p," contributor","contributors");return(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",alignItems:"center"},children:[(0,o.jsx)(E.A,{sx:{display:"flex",flexDirection:"row"},children:(0,o.jsx)(nc.A,{children:h.slice(0,10).map((e,t)=>(0,o.jsx)(e2.r,{className:t>5?"AvatarShowLarge":"AvatarShowMedium",src:e.primaryAvatarUrl,alt:e.login,"data-testid":"contributor-avatar","data-hovercard-url":(0,ea.dCN)({owner:e.login})},e.login))})}),(0,o.jsxs)(er.A,{as:"button","aria-label":`Show ${f}"`,onClick:()=>{r(!0),t("CONTRIBUTORS.LIST.OPEN")},"data-testid":"contributors-count-button",sx:{ml:2,color:"fg.default"},children:[(0,o.jsx)(U.A,{icon:P.PeopleIcon}),e&&(0,o.jsx)(W.A,{className:"react-contributors-title",sx:{mx:1,fontSize:0},children:"Contributors"}),(0,o.jsx)(nd.A,{sx:{mx:1,px:2,py:1},children:p})]}),n&&(0,o.jsx)(tS.l,{title:f,onClose:()=>r(!1),width:"medium",height:c.totalCount>=12?"small":"auto",renderBody:()=>(0,o.jsx)(et.l,{sx:{overflowY:"auto",py:2},"data-testid":"contributor-dialog-list",children:h.map(e=>(0,o.jsx)(nh,{user:e},e.login))})})]})}function nh({user:e}){let{sendRepoClickEvent:t}=(0,F.T)(),{path:n,refInfo:r}=(0,v.eu)(),i=(0,a.t)();return(0,o.jsxs)(et.l.Item,{sx:{display:"flex",flexDirection:"row",fontSize:1,py:2,color:"fg.default","&:hover":{backgroundColor:"canvas.subtle"}},"data-testid":"contributor-dialog-row",onSelect:()=>t("CONTRIBUTORS.LIST.USER"),children:[(0,o.jsxs)(er.A,{as:e6.N,sx:{flex:1},muted:!0,to:e.profileLink,onClick:()=>t("CONTRIBUTORS.LIST.USER"),children:[(0,o.jsx)(e2.r,{src:e.primaryAvatarUrl,alt:e.login,sx:{mr:2},"aria-hidden":"true"}),(0,o.jsx)(Y.A,{inline:!0,title:e.login,children:e.login})]}),(0,o.jsx)(et.l.TrailingVisual,{children:(0,o.jsx)(er.A,{as:e6.N,muted:!0,to:(0,ea.MtY)({repo:i,branch:r.name,path:n,author:e.login}),onClick:()=>t("CONTRIBUTORS.LIST.COMMITS"),"aria-label":`${nm(e.commitsCount,"commit","commits")} by ${e.login}`,"data-testid":"commit-link",children:nm(e.commitsCount,"commit","commits")})})]})}function nm(e,t,n){return`${e} ${1===e?t:n}`}function np(){return(0,o.jsxs)(W.A,{sx:{color:"danger.fg"},children:[(0,o.jsx)(U.A,{icon:P.AlertFillIcon}),"\xa0Cannot retrieve contributors info at this time."]})}try{nu.displayName||(nu.displayName="ContributorAvatars")}catch{}try{nh.displayName||(nh.displayName="ContributorRow")}catch{}try{np.displayName||(np.displayName="ContributorsError")}catch{}let nf={Box:"BlobViewHeader-module__Box--pvsIA",Box_1:"BlobViewHeader-module__Box_1--PPihg",Box_2:"BlobViewHeader-module__Box_2--G_jCG",Box_3:"BlobViewHeader-module__Box_3--Kvpex",Box_4:"BlobViewHeader-module__Box_4--vFP89",IconButton:"BlobViewHeader-module__IconButton--uO1fA",ActionMenu_Overlay:"BlobViewHeader-module__ActionMenu_Overlay--NLYvM",Box_5:"BlobViewHeader-module__Box_5--jot5O",Box_6:"BlobViewHeader-module__Box_6--gPoMf",IconButton_1:"BlobViewHeader-module__IconButton_1--MzNlL",LinkButton:"BlobViewHeader-module__LinkButton--DMph4",IconButton_2:"BlobViewHeader-module__IconButton_2--KDy6i"};var nx=n(25925),ny=n(55847);let ng={Button:"FileNameStickyHeader-module__Button--SaiiH",Box_1:"FileNameStickyHeader-module__Box_1--HSpOJ",Box_2:"FileNameStickyHeader-module__Box_2--_pDx6",Box_3:"FileNameStickyHeader-module__Box_3--AsYoJ",Box_4:"FileNameStickyHeader-module__Box_4--IyPx8",GoToTopButton:"FileNameStickyHeader-module__GoToTopButton--9lB4x",Box_5:"FileNameStickyHeader-module__Box_5--xBJ2J"};var nb=n(4215);function nv({fileNameId:e="file-name-id",id:t,fontSize:n,showCopyPathButton:r}){let i=(0,a.t)(),{refInfo:l,path:s,action:c}=(0,v.eu)();return(0,o.jsx)(nb.Q,{path:s,repo:i,commitish:l.name,isFolder:"tree"===c,fileNameId:e,id:t,fontSize:n,showCopyPathButton:r&&""!==s&&"/"!==s})}try{nv.displayName||(nv.displayName="ReposHeaderBreadcrumb")}catch{}let nj=()=>(0,o.jsx)(nv,{id:"sticky-breadcrumb",fileNameId:"sticky-file-name-id",fontSize:1}),nw=({sx:e,className:t})=>(0,o.jsx)(ny.Q,{leadingVisual:P.ArrowUpIcon,variant:"invisible",size:"small",sx:e,onClick:e=>{e.preventDefault(),window.scrollTo({top:0,behavior:"smooth"})},className:(0,t8.$)(ng.Button,t),children:"Top"});function nN({isStickied:e,showTree:t,treeToggleElement:n}){return(0,o.jsx)(E.A,{sx:{display:e?"flex":"none",minWidth:0,py:2,...e?{backgroundColor:"canvas.subtle",borderLeft:"1px solid var(--borderColor-default, var(--color-border-default))",borderRight:"1px solid var(--borderColor-default, var(--color-border-default))"}:{}},children:t?(0,o.jsxs)("div",{className:ng.Box_5,children:[(0,o.jsx)(nj,{}),(0,o.jsx)(nw,{className:ng.GoToTopButton})]}):(0,o.jsxs)("div",{className:ng.Box_1,children:[(0,o.jsxs)("div",{className:ng.Box_2,children:[e&&n,(0,o.jsx)("div",{className:ng.Box_3,children:(0,o.jsx)(nx.R,{buttonClassName:"ref-selector-class"})}),(0,o.jsx)("div",{className:ng.Box_4,children:(0,o.jsx)(nj,{})})]}),(0,o.jsx)(nw,{className:ng.GoToTopButton})]})})}try{nj.displayName||(nj.displayName="StickyReposHeaderBreadcrumb")}catch{}try{nw.displayName||(nw.displayName="GoToTopButton")}catch{}try{nN.displayName||(nN.displayName="FileNameStickyHeader")}catch{}let n_={Box:"BlameAgeLegend-module__Box--dTotP",Text:"BlameAgeLegend-module__Text--qfxJQ",Box_1:"BlameAgeLegend-module__Box_1--n0gaX",Text_1:"BlameAgeLegend-module__Text_1--Ifgnh"},nk=m.memo(nC);function nC(){let e=function(){let{resolvedColorScheme:e}=(0,e9.DP)();return e?.startsWith("dark")?Array(10).fill(null).map((e,t)=>tt[`scale.orange.${9-t}`]):Array(10).fill(null).map((e,t)=>tt[`scale.orange.${t}`])}();return(0,o.jsxs)("div",{"aria-hidden":!0,className:n_.Box,children:[(0,o.jsx)("span",{className:n_.Text,children:"Older"}),e.map((e,t)=>(0,o.jsx)(E.A,{sx:{backgroundColor:e},className:n_.Box_1},`blame-recency-color-${t}`)),(0,o.jsx)("span",{className:n_.Text_1,children:"Newer"})]})}try{nC.displayName||(nC.displayName="BlameAgeLegend")}catch{}var nA=n(17515),nS=n(16945);let nB={SegmentedControl:"BlobTabButtons-module__SegmentedControl--JMGov"};function nI(){let{headerInfo:{isCSV:e,isRichtext:t,shortPath:n},renderedFileInfo:r,image:i,issueTemplate:l,discussionTemplate:a,viewable:s}=(0,eg.A)(),c=(0,ey.O)(),[d]=(0,ew.o)(),u="1"===d.get("plain")||!!d.get("short_path")?.length,h=r&&!s||i,p=t||l||a||e||r,f=r?`short_path=${n}`:"plain=1",{getUrl:y}=(0,k.Z)(),g=!p||u||c?c?2:1:0,[b,v]=(0,m.useState)(g);(0,nA.N)(()=>{v(g)},[g]);let j=(0,ew.Z)(),{viewCodeShortcut:N,viewPreviewShortcut:_,viewBlameShortcut:C}=(0,w.wk)(),A=e=>{if(p||(e+=1),v(e),b!==e)switch(e){case 0:j(y({action:"blob",params:"",hash:""}));break;case 1:j(y({action:"blob",params:p?f:"",hash:location.hash?.substring(1)??void 0}));break;case 2:j(y({action:"blame",params:"",hash:location.hash?.substring(1)??void 0}))}};if(h)return null;let S=[(0,o.jsx)(nS.I.Button,{selected:1===b,"data-hotkey":N.hotkey,children:"Code"},"raw"),(0,o.jsx)(nS.I.Button,{selected:2===b,"data-hotkey":C.hotkey,children:"Blame"},"blame")],B=(0,o.jsx)(nS.I.Button,{selected:0===b,"data-hotkey":_.hotkey,children:"Preview"},"preview'"),I=(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:N.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>(0,m.startTransition)(()=>A(+!!p))}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:C.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>(0,m.startTransition)(()=>A(p?2:1))}),p&&(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:_.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>(0,m.startTransition)(()=>A(0))})]}),L=p?h?[B]:[B,...S]:[...S];return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(nS.I,{"aria-label":"File view",size:"small",onChange:A,className:nB.SegmentedControl,children:L}),I]})}try{nI.displayName||(nI.displayName="BlobTabButtons")}catch{}var nL=n(94977),nT=n(90864);let nE={Box:"CodeSizeDetails-module__Box--QdxnQ",Box_1:"CodeSizeDetails-module__Box_1--_uFDs",Truncate:"CodeSizeDetails-module__Truncate--crycx",Text:"CodeSizeDetails-module__Text--j_Gbp",PrimerLink:"CodeSizeDetails-module__PrimerLink--Rhviw",Truncate_1:"CodeSizeDetails-module__Truncate_1--er0Uk",PrimerLink_1:"CodeSizeDetails-module__PrimerLink_1--ASUIG",Text_1:"CodeSizeDetails-module__Text_1--K8OQg",Tooltip:"CodeSizeDetails-module__Tooltip--wbw9i"};function nR({className:e}){let{codeownerInfo:t}=(0,nT.Y_)(),{helpUrl:n}=(0,v.sq)(),r=(0,eg.A)(),{path:i,refInfo:l}=(0,v.eu)(),s=(0,a.t)(),c=(0,ey.O)(),{headerInfo:{blobSize:d,isGitLfs:u,lineInfo:{truncatedLoc:h,truncatedSloc:m},mode:p},viewable:f,rawLines:x}=r,y="symbolic link"===p?function({rawLines:e,blame:t,repo:n,refInfo:r,path:i}){if(!e||!e[0])return null;let o=e[0];o.startsWith("/")||/^[a-zA-Z]:\\/.test(o)||(o=`${i}/../${o}`);let l={owner:n.ownerLogin,repo:n.name,commitish:r.name,filePath:o};return t?(0,ea.buO)(l):(0,ea.nD_)(l)}({rawLines:x,blame:c,repo:s,refInfo:l,path:i}):void 0;return(0,o.jsx)("div",{className:(0,t8.$)(e,nE.Box),children:(0,o.jsxs)("div",{className:(0,t8.$)("text-mono",nE.Box_1),children:["file"!==p&&!y&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(Y.A,{title:p,inline:!0,className:nE.Truncate,children:(0,o.jsx)("span",{children:p})}),f&&(0,o.jsx)("span",{className:nE.Text,children:"\xb7"})]}),f?(0,o.jsxs)(o.Fragment,{children:[y&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(er.A,{as:e6.N,muted:!0,to:y,className:nE.PrimerLink,children:"Symbolic Link"}),(0,o.jsx)("span",{className:nE.Text,children:"\xb7"})]}),(0,o.jsx)(Y.A,{maxWidth:"100%",title:d,inline:!0,"data-testid":"blob-size",className:nE.Truncate_1,children:(0,o.jsx)("span",{children:`${h} lines (${m} loc) \xb7 ${d}`})})]}):(0,o.jsx)("span",{children:d}),t&&(0,o.jsx)(nO,{codeownerInfo:t}),u&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:"file-info-divider"}),(0,o.jsx)(er.A,{muted:!0,href:`${n}/articles/versioning-large-files/`,"aria-label":"Learn more about Git LFS",className:nE.PrimerLink_1,children:(0,o.jsx)(U.A,{icon:P.QuestionIcon})}),(0,o.jsx)("span",{children:" Stored with Git LFS"})]})]})})}function nO({codeownerInfo:{codeownerPath:e,ownedByCurrentUser:t,ownersForFile:n,ruleForPathLine:r}}){var i,l,a;let s;if(!(t||n))return null;let c=(i=t,l=n,a=r,s="Owned by ",i&&(s+="you",l&&(s+=" along with ")),s+=l,a&&(s+=` (from CODEOWNERS line ${a})`),s),d=t?{color:"var(--fgColor-accent, var(--color-accent-fg))"}:{};return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:nE.Text_1,children:" \xb7 "}),(0,o.jsx)(nL.A,{id:"codeowners-tooltip","aria-label":c,wrap:!0,className:nE.Tooltip,children:e?(0,o.jsx)(er.A,{"aria-labelledby":"codeowners-tooltip",href:e,muted:!t,sx:d,children:(0,o.jsx)(U.A,{icon:P.ShieldLockIcon})}):(0,o.jsx)(U.A,{icon:P.ShieldLockIcon,sx:d})})]})}try{nR.displayName||(nR.displayName="CodeSizeDetails")}catch{}try{nO.displayName||(nO.displayName="CodeOwnersBadge")}catch{}var nD=n(33299);function nF({editEnabled:e,githubDevUrl:t,ghDesktopPath:n,onBranch:r}){let{sendRepoClickEvent:i}=(0,F.T)(),l=(0,nD.X)(["windows","mac"]),{openWithGitHubDevShortcut:a}=(0,w.wk)();return(0,o.jsxs)(et.l.Group,{children:[(0,o.jsx)(et.l.GroupHeading,{children:"Open with..."}),t?(0,o.jsxs)(et.l.LinkItem,{onClick:()=>i("BLOB_EDIT_DROPDOWN.DEV_LINK",{edit_enabled:e}),className:"js-blob-dropdown-click js-github-dev-shortcut",href:t,"data-hotkey":a.hotkey,children:["github.dev",(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",children:(0,o.jsx)(D.E,{children:(0,o.jsx)("kbd",{children:"."})})})]}):null,r&&l&&n?(0,o.jsx)(et.l.LinkItem,{onClick:()=>i("BLOB_EDIT_DROPDOWN.DESKTOP"),href:n,children:"GitHub Desktop"}):null]})}function n$(e,t,n){let r=(0,nD.X)(["windows","mac"]);return!!(e||t&&r&&n)}try{nF.displayName||(nF.displayName="OpenWithActionItems")}catch{}function nM({editAllowed:e,hasOpenWithItem:t}){let n=(0,eg.A)(),{refInfo:{canEdit:r}}=(0,v.eu)(),{githubDevUrl:i}=(0,v.sq)(),{sendRepoClickEvent:l}=(0,F.T)(),{getUrl:a}=(0,k.Z)(),{headerInfo:{ghDesktopPath:s,onBranch:c}}=n;return(0,o.jsxs)(o.Fragment,{children:[e&&(0,o.jsxs)(et.l.Group,{children:[(0,o.jsx)(et.l.GroupHeading,{children:"Edit file"}),(0,o.jsxs)(et.l.LinkItem,{onClick:()=>l("BLOB_EDIT_DROPDOWN.IN_PLACE"),href:a({action:"edit"}),"data-hotkey":"e",children:["Edit in place",(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",children:(0,o.jsx)(D.E,{children:(0,o.jsx)("kbd",{children:"e"})})})]})]}),e&&t&&(0,o.jsx)(et.l.Divider,{}),t&&(0,o.jsx)(nF,{editEnabled:r,githubDevUrl:i,ghDesktopPath:s,onBranch:c})]})}try{nM.displayName||(nM.displayName="EditMenuActionItems")}catch{}function nP({shortcut:e}){return(0,o.jsx)(o.Fragment,{children:e.text?.split(" ").map(e=>(0,o.jsxs)(m.Fragment,{children:[(0,o.jsx)("kbd",{children:e})," "]},e))})}try{nP.displayName||(nP.displayName="KeyboardVisual")}catch{}var nz=n(95776);function nH(){return(0,eg.A)().workflowRedirectUrl}function nW({onBlamePage:e,onDismiss:t,maxLineNumber:n}){let r=(0,ew.Z)(),i=(0,eO.fY)(),l=(0,eO.aM)(),s=(0,eO.ue)(),c=(0,m.useRef)(i?l:1),d=(0,m.useRef)(!0),[u,h]=(0,m.useState)(!0),p=(0,m.useRef)(l!==s&&i?s:null),{refInfo:f,path:x}=(0,v.eu)(),y=(0,a.t)(),g=(0,m.useRef)(null),b=(0,m.useRef)(c.current?`#L${c.current}${p.current?`-L${p.current}`:""}`:""),j=e?(0,ea.buO)({repo:y.name,owner:y.ownerLogin,filePath:x,commitish:f.name}):(0,ea.nD_)({repo:y.name,owner:y.ownerLogin,filePath:x,commitish:f.name});(0,m.useEffect)(()=>{g&&g.current&&(g.current.value=i?`${l}${s!==l?`-${s}`:""}`:"1",g.current.focus())},[]);let w=(e,n)=>{r(`${e}${n}`);let i=(0,eN.$c)(n);if(!i.blobRange?.start?.line||!d.current){h(d.current),setTimeout(()=>{g.current?.focus()},25);return}(0,ek.f)({line:i.blobRange.start.line}),t()};return(0,tB.createPortal)((0,o.jsxs)(nz.A,{isOpen:!0,onDismiss:t,children:[(0,o.jsx)(nz.A.Header,{children:"Jump to line"}),(0,o.jsxs)(E.A,{sx:{display:"flex",pl:3,pr:3,pt:3,pb:3*!!u},children:[(0,o.jsx)(E.A,{sx:{display:"flex",flexGrow:1,mr:2},children:(0,o.jsx)(H.A,{ref:g,"aria-invalid":!u,"aria-describedby":u?"":"goToLineErrorValidation",sx:{flexGrow:1,pr:2},placeholder:"Jump to line...",onChange:e=>{let t=e.target.value;g&&g.current&&(g.current.value=t),function(e){let t=!0;if(""===e.trim()&&(c.current=1),e.startsWith("-")&&n){let r=parseInt(e,10);if(!Number.isNaN(r)&&r<0){let e=n+r+1;e<=0&&(e=1,t=!1),c.current=e}}else if(e.includes("-")){let[r,i]=e.split("-"),o=parseInt(r,10),l=parseInt(i,10);!Number.isNaN(o)&&o>0&&(c.current=n?Math.min(o,n):o,t=void 0!==n&&o<=n),!Number.isNaN(l)&&l>0&&(p.current=n?Math.min(l,n):l,t=t&&void 0!==n&&l<=n)}else{let r=parseInt(e,10);!Number.isNaN(r)&&r>0?(c.current=n?Math.min(r,n):r,t=void 0!==n&&r<=n):t=""===e}d.current=t,t&&!u&&h(!0),b.current=`#L${c.current}${p.current?`-L${p.current}`:""}`}(t)},onFocus:()=>{g&&g.current&&g.current.select()},onKeyDown:e=>{if("Enter"===e.key){if("Enter"===e.key&&!d.current){h(d.current),setTimeout(()=>{g.current?.focus()},25);return}w(j,b.current)}}})}),(0,o.jsx)(t5.z,{href:u?j+b.current:void 0,onClick:e=>{e.preventDefault(),w(j,b.current)},sx:t0.E,children:"Go"})]}),!u&&(0,o.jsx)(E.A,{role:"alert",id:"goToLineErrorValidation",sx:{display:"flex",p:2,justifyContent:"center",color:"red"},children:"Invalid line number"})]}),document.body)}try{nW.displayName||(nW.displayName="GoToLineDialog")}catch{}var nU=n(72334),nV=n(2680);function nG(){let e=(0,a.t)(),{path:t,refInfo:n}=(0,v.eu)();return n.canEdit?(0,o.jsx)(et.l.LinkItem,{as:e6.N,to:(0,ea.IO9)({repo:e,path:t,commitish:n.name,action:"tree/delete"}),children:(0,o.jsx)(W.A,{sx:{color:"danger.fg"},children:"Delete directory"})}):null}try{nG.displayName||(nG.displayName="DeleteDirectoryItem")}catch{}let nY={Box:"CodeViewHeader-module__Box--PofRM",Box_1:"CodeViewHeader-module__Box_1--KpLzV",Box_2:"CodeViewHeader-module__Box_2--xzDOt",Box_3:"CodeViewHeader-module__Box_3--WnQ2n",Box_5:"CodeViewHeader-module__Box_5--KeXxF",Box_6:"CodeViewHeader-module__Box_6--iStzT",FileResultsList:"CodeViewHeader-module__FileResultsList--bglyC",Box_7:"CodeViewHeader-module__Box_7--FZfkg",IconButton:"CodeViewHeader-module__IconButton--EbF1J"},nq={Box:"DeleteHeaderButtons-module__Box--GOfJ9"},nK=(0,m.lazy)(()=>Promise.all([n.e("vendors-node_modules_tanstack_query-core_build_modern_queryObserver_js-node_modules_tanstack_-defd52"),n.e("vendors-node_modules_diff_lib_index_mjs"),n.e("vendors-node_modules_github_text-expander-element_dist_index_js"),n.e("ui_packages_copilot-chat_utils_copilot-chat-helpers_ts"),n.e("ui_packages_web-commit-dialog_WebCommitDialog_tsx"),n.e("app_assets_modules_react-code-view_components_blob-edit_WebCommitDialog_tsx"),n.e("ui_packages_flash-error_FlashError_module_css-ui_packages_text-expander_TextExpander_module_c-fee862")]).then(n.bind(n,53697)));function nQ({webCommitInfo:e,isBlob:t}){let[n,r]=(0,m.useState)("closed"),i=(0,m.useRef)(null),l=(0,a.t)(),{refInfo:s,path:c}=(0,v.eu)(),{helpUrl:d}=(0,v.sq)(),u=(0,ea.IO9)({repo:l,commitish:s.name,action:t?"blob":"tree",path:c});return e.shouldFork||e.shouldUpdate||e.lockedOnMigration?null:(0,o.jsxs)("div",{className:nq.Box,children:[(0,o.jsx)(ny.Q,{as:e6.N,to:u,children:"Cancel changes"}),(0,o.jsx)(ny.Q,{variant:"primary",ref:i,onClick:()=>{(0,m.startTransition)(()=>{r("pending")})},children:"Commit changes..."}),("pending"===n||"saving"===n)&&(0,o.jsx)(m.Suspense,{fallback:null,children:(0,o.jsx)(nK,{isNewFile:!1,isDelete:!0,helpUrl:d,ownerName:l.ownerLogin,dialogState:n,setDialogState:r,refName:s.name,placeholderMessage:`Delete ${c}${t?"":" directory"}`,webCommitInfo:e,returnFocusRef:i})})]})}try{nK.displayName||(nK.displayName="WebCommitDialog")}catch{}try{nQ.displayName||(nQ.displayName="DeleteHeaderButtons")}catch{}let nX=(0,eY.A)("localStorage");function nZ({symbolsEnabled:e}){let{codeFoldingOption:t,codeWrappingOption:n,codeCenterOption:r,openSymbolsOption:i}=(0,h.ud)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(nJ,{option:t}),(0,o.jsx)(nJ,{option:n}),(0,o.jsx)(nJ,{option:r}),e&&(0,o.jsx)(nJ,{option:i})]})}function nJ({option:e}){let t=(0,m.useCallback)(()=>{(0,m.startTransition)(()=>e.setEnabled(!e.enabled)),nX.setItem(e.name,String(!e.enabled)),e.name===h.T2&&eR(null,null,!e.enabled)},[e]);return(0,o.jsxs)(et.l.Item,{role:"menuitemcheckbox","aria-checked":e.enabled,onSelect:t,children:[(0,o.jsx)(et.l.LeadingVisual,{children:e.enabled&&(0,o.jsx)(P.CheckIcon,{})}),e.label]},e.name)}try{nZ.displayName||(nZ.displayName="SettingsMenuItems")}catch{}try{nJ.displayName||(nJ.displayName="OptionsElement")}catch{}function n0({payload:e,showTree:t,treeToggleElement:n,validCodeNav:r,onFindFilesShortcut:i}){let{codeCenterOption:l}=(0,h.ud)();return(0,o.jsx)("div",{className:(0,t8.$)("container",nY.Box),children:(0,o.jsx)("div",{className:"px-3 pt-3 pb-0",id:"StickyHeader",children:(0,o.jsx)("div",{className:nY.Box_1,children:(0,o.jsxs)("div",{className:nY.Box_2,children:[t?(0,o.jsx)("div",{className:nY.Box_6,children:(0,o.jsx)(nv,{id:"repos-header-breadcrumb--wide",fileNameId:"file-name-id-wide",showCopyPathButton:!0})}):(0,o.jsxs)("div",{className:(0,t8.$)("react-code-view-header-wrap--narrow",nY.Box_3),children:[(0,o.jsx)(E.A,{sx:{display:"block","@media screen and (min-width: 1360px)":{display:l.enabled?"none":"block"},mr:2},children:n}),(0,o.jsx)("div",{className:"react-code-view-header-mb--narrow mr-2",children:(0,o.jsx)(nx.R,{buttonClassName:"ref-selector-class",idEnding:"repos-header-ref-selector-wide"})}),(0,o.jsx)("div",{className:(0,t8.$)("react-code-view-header-mb--narrow",nY.Box_5),children:(0,o.jsx)(nv,{id:"repos-header-breadcrumb",fileNameId:"file-name-id",showCopyPathButton:!0})})]}),(0,o.jsx)("div",{className:"react-code-view-header-element--wide",children:(0,o.jsx)(n1,{payload:e,onFindFilesShortcut:i,narrow:!1,showTree:t,validCodeNav:r})}),(0,o.jsx)("div",{className:"react-code-view-header-element--narrow",children:(0,o.jsx)(n1,{payload:e,onFindFilesShortcut:i,narrow:!0,showTree:t,validCodeNav:r})})]})})})})}function n1({payload:e,onFindFilesShortcut:t,narrow:n,showTree:r,validCodeNav:i}){let l=(0,nt.y)(),{findFileWorkerPath:s,githubDevUrl:c}=(0,v.sq)(),d=(0,a.t)(),{refInfo:u,modelsRepoIntegrationEnabled:h}=(0,v.eu)(),{openWithGitHubDevShortcut:p,openWithGitHubDevInNewWindowShortcut:f}=(0,w.wk)(),x=(0,m.useRef)(null),y=!r&&(0,o.jsxs)("div",{children:[(0,o.jsx)(nV.default,{commitOid:u.currentOid,findFileWorkerPath:s,searchBoxRef:x,config:{enableOverlay:!0},className:nY.FileResultsList}),(0,o.jsx)(B.H,{inputRef:x,onFindFilesShortcut:t,textAreaId:e_.wQ})]});return(0,o.jsx)("div",{className:nY.Box_7,children:(0,o.jsxs)("div",{className:"d-flex gap-2",children:[(0,A.mM)(e)&&(0,o.jsxs)(eg.s,{blob:e.blob,children:[!n&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n3,{}),y]}),(0,o.jsx)(rt,{onCopy:l,narrow:n,validCodeNav:i,copilotAccessAllowed:e.copilotAccessAllowed??!1})]}),h&&e.path?.match(/\.prompt\.(yml|yaml)$/)&&(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(ny.Q,{as:e6.N,to:(0,ea.afT)({repo:d,path:e.path,commitish:u.name,action:"edit"}),children:"Edit prompt"})}),(0,A.Hf)(e)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(t2.W,{as:"h2",text:"Directory actions"}),!n&&(0,o.jsxs)(o.Fragment,{children:[y,(0,o.jsx)(nU.L,{})]}),(0,o.jsx)(n2,{narrow:n}),(0,o.jsx)(er.A,{className:"js-github-dev-shortcut d-none","data-hotkey":p.hotkey,href:c}),(0,o.jsx)(er.A,{className:"js-github-dev-new-tab-shortcut d-none","data-hotkey":f.hotkey,href:c,target:"_blank"})]}),(0,A.iS)(e)&&(0,o.jsx)(nQ,{webCommitInfo:e.webCommitInfo,isBlob:e.deleteInfo.isBlob})]})})}function n2({narrow:e}){let{refInfo:t,path:n}=(0,v.eu)(),r=(0,a.t)(),{sendRepoClickEvent:i}=(0,F.T)(),{addToast:l}=(0,t6.Y6)(),{createPermalink:s}=(0,k.Z)(),{copyFilePathShortcut:c,copyPermalinkShortcut:d}=(0,w.wk)(),{codeCenterOption:u}=(0,h.ud)(),p=(0,m.useRef)(null),[f,y,g]=t9("raw-copy-message-tooltip",p,{direction:"nw"});return(0,o.jsxs)(o.Fragment,{children:[c.hotkey&&(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:c.hotkey,onButtonClick:()=>{(0,t4.D)(n),l({type:"success",message:"Path copied!"})}}),d.hotkey&&(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:d.hotkey,onButtonClick:()=>{(0,t4.D)(s({absolute:!0})),l({type:"success",message:"Permalink copied!"})}}),(0,o.jsx)(t2.W,{as:"h2",text:"More options"}),g,(0,o.jsxs)(tO.W,{onOpenChange:e=>e&&i("MORE_OPTIONS_DROPDOWN"),anchorRef:p,children:[(0,o.jsx)(tO.W.Anchor,{children:(0,o.jsx)(z.K,{icon:P.KebabHorizontalIcon,"aria-label":"More options",size:"medium",title:"More options","data-testid":"tree-overflow-menu-anchor",onBlur:y,className:nY.IconButton})}),(0,o.jsx)(tO.W.Overlay,{width:"small",children:(0,o.jsxs)(et.l,{children:[e&&t.canEdit&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(et.l.LinkItem,{as:e6.N,onClick:()=>i("NEW_FILE_BUTTON"),to:(0,ea.IO9)({repo:r,path:n,commitish:t.name,action:"new"}),children:[(0,o.jsx)(et.l.LeadingVisual,{children:(0,o.jsx)(P.PlusIcon,{})}),"Create new file"]}),(0,o.jsxs)(et.l.LinkItem,{onClick:()=>i("UPLOAD_FILES_BUTTON"),href:(0,ea.IO9)({repo:r,path:n,commitish:t.name,action:"upload"}),children:[(0,o.jsx)(et.l.LeadingVisual,{children:(0,o.jsx)(P.UploadIcon,{})}),"Upload files"]}),(0,o.jsx)(et.l.Divider,{})]}),(0,o.jsx)(n6,{path:n,updateTooltipMessage:f}),t.canEdit&&(0,o.jsx)(et.l.Divider,{}),(0,o.jsx)(nG,{}),(0,o.jsx)(et.l.Divider,{}),(0,o.jsxs)(et.l.Group,{children:[(0,o.jsx)(et.l.GroupHeading,{children:"View options"}),(0,o.jsx)(nJ,{option:u})]})]})})]})]})}function n6({path:e,updateTooltipMessage:t}){let{copyFilePathShortcut:n}=(0,w.wk)(),{copyPermalinkShortcut:r}=(0,w.wk)(),{sendRepoClickEvent:i}=(0,F.T)(),{createPermalink:l}=(0,k.Z)();return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(et.l.Item,{onSelect:()=>{i("MORE_OPTIONS_DROPDOWN.COPY_PATH"),(0,t4.D)(e),t("Path copied!")},children:["Copy path",n.hotkey&&(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",className:"d-flex",children:(0,o.jsx)(nP,{shortcut:n})})]}),(0,o.jsxs)(et.l.Item,{onSelect:()=>{i("MORE_OPTIONS_DROPDOWN.COPY_PERMALINK"),(0,t4.D)(l({absolute:!0})),t("Permalink copied!")},children:["Copy permalink",r.hotkey&&(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",className:"d-flex",children:(0,o.jsx)(nP,{shortcut:r})})]})]})}function n3(){let e=nH();return e?(0,o.jsx)(ny.Q,{as:e6.N,to:e,children:"View Runs"}):null}try{n0.displayName||(n0.displayName="CodeViewHeader")}catch{}try{n1.displayName||(n1.displayName="PageActions")}catch{}try{n2.displayName||(n2.displayName="TreeOverflowMenu")}catch{}try{n6.displayName||(n6.displayName="CopyPathsActionItems")}catch{}try{n3.displayName||(n3.displayName="ViewRunsButton")}catch{}function n5({fileReference:e}){return(0,o.jsx)(et.l.Item,{"data-testid":"copilot-menu-items",onSelect:()=>(0,t1.Xu)(e),children:"Ask about this file"})}try{n5.displayName||(n5.displayName="CopilotMenuItems")}catch{}let n8={IconButton:"NavigationMenu-module__IconButton--NqJ_L",ActionMenu_Overlay:"NavigationMenu-module__ActionMenu_Overlay--OQ__q",Button:"NavigationMenu-module__Button--SJihq",ActionList_LinkItem:"NavigationMenu-module__ActionList_LinkItem--qaeAe"};function n4({viewable:e,onCopy:t,name:n,updateTooltipMessage:r,all:i}){let{sendRepoClickEvent:l}=(0,F.T)(),{rawBlobUrl:a}=(0,eg.A)(),{downloadRawContentShortcut:s}=(0,w.wk)(),c=(0,m.useCallback)(async()=>await n7(a,n),[n,a]);return(0,o.jsxs)(et.l.Group,{children:[(0,o.jsx)(et.l.GroupHeading,{children:"Raw file content"}),i&&(0,o.jsx)(n9,{viewable:e,onCopy:t,updateTooltipMessage:r}),i&&(0,o.jsx)(re,{onClick:()=>l("BLOB_RAW_DROPDOWN.VIEW"),rawHref:a}),(0,o.jsxs)(et.l.LinkItem,{onClick:c,children:["Download",s.text&&(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",className:"d-flex",children:(0,o.jsx)(nP,{shortcut:s})})]})]})}async function n7(e,t){let n=await fetch(e,{method:"get"}),r=await n.blob(),i=document.createElement("a");i.setAttribute("download",t);let o=URL.createObjectURL(r);i.href=o,i.setAttribute("target","_blank"),i.click(),URL.revokeObjectURL(o)}function n9({viewable:e,onCopy:t,updateTooltipMessage:n}){let{copyRawContentShortcut:r}=(0,w.wk)();return e?(0,o.jsxs)(et.l.Item,{onSelect:async()=>{let e=await t(),{ariaLabel:r}=(0,nn.t0)(e);n(r)},children:["Copy",r.text&&(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",className:"d-flex",children:(0,o.jsx)(nP,{shortcut:r})})]}):null}function re({onClick:e,rawHref:t}){let{viewRawContentShortcut:n}=(0,w.wk)();return(0,o.jsxs)(et.l.LinkItem,{onClick:e,href:t,children:["View",n.text&&(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",className:"d-flex",children:(0,o.jsx)(nP,{shortcut:n})})]})}try{n4.displayName||(n4.displayName="RawMenuActionItems")}catch{}try{n9.displayName||(n9.displayName="CopyActionItem")}catch{}try{re.displayName||(re.displayName="RawActionItem")}catch{}function rt({onCopy:e,validCodeNav:t,narrow:n,copilotAccessAllowed:r}){let i=(0,eg.A)(),l=i.symbolsEnabled,{action:c,path:d,refInfo:{canEdit:u,currentOid:h,name:p,refType:f}}=(0,v.eu)(),{githubDevUrl:y}=(0,v.sq)(),{sendRepoClickEvent:g}=(0,F.T)(),[b,j]=(0,m.useState)(!1),N=nH(),{search:_}=(0,eG.zy)(),C=new URLSearchParams(_),{setFindInFileOpen:A}=e$(),S=(0,eO.fY)(),B=!(i.richText&&"1"!==C.get("plain")||i.renderImageOrRaw||i.renderedFileInfo&&!C.get("short_path")||i.issueTemplate?.structured&&i.issueTemplate.valid),{headerInfo:{deleteTooltip:I,onBranch:L,siteNavLoginPath:T,lineInfo:{truncatedLoc:E}},viewable:R}=i,O=(0,s.i)(),{getUrl:$}=(0,k.Z)(),M=eP(),H=(0,m.useRef)("");(0,m.useEffect)(()=>{H.current=$({action:"blame"})},[$]);let{goToLineShortcut:W,findInFileShortcut:U,alternativeGoToLineShortcut:V}=(0,w.wk)(),G=(0,m.useRef)(null),[Y,q,K]=t9("raw-copy-message-tooltip",G,{direction:"nw"}),Q=(0,a.t)(),X=$(),[Z]=(0,eq.I)(()=>window.location.origin+X,X),J=(0,m.useMemo)(()=>({type:"file",url:Z,path:d,repoID:Q.id,repoOwner:Q.ownerLogin,repoName:Q.name,ref:ri(p,f??"branch"),commitOID:h}),[Z,d,Q.id,Q.ownerLogin,Q.name,p,f,h]);return(0,o.jsxs)(o.Fragment,{children:[B&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:W.hotkey,onButtonClick:()=>j(!0)}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:V.hotkey,onButtonClick:()=>j(!0)})]}),(0,o.jsx)(rn,{blameUrl:H.current,viewable:R,hidden:!0}),K,(0,o.jsxs)(tO.W,{onOpenChange:e=>e&&g("MORE_OPTIONS_DROPDOWN",{edit_enabled:u,github_dev_enabled:!!y}),anchorRef:G,children:[(0,o.jsx)(tO.W.Anchor,{children:(0,o.jsx)(z.K,{icon:P.KebabHorizontalIcon,"aria-label":"More file actions",tooltipDirection:"nw",className:(0,t8.$)("js-blob-dropdown-click",n8.IconButton),size:"medium",variant:"default","data-testid":`more-file-actions-button-nav-menu-${n?"narrow":"wide"}`,onBlur:q})}),(0,o.jsx)(tO.W.Overlay,{width:"small",className:n8.ActionMenu_Overlay,children:(0,o.jsxs)(et.l,{children:[n&&null!==N&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(et.l.LinkItem,{href:N,children:"View Runs"}),(0,o.jsx)(et.l.Divider,{})]}),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n4,{viewable:R,onCopy:e,name:i.displayName,updateTooltipMessage:Y}),(0,o.jsx)(et.l.Divider,{})]}),(0,o.jsxs)(et.l.Group,{children:[B&&(0,o.jsxs)(et.l.Item,{onSelect:()=>{g("MORE_OPTIONS_DROPDOWN.GO_TO_LINE"),j(!0)},"aria-keyshortcuts":W.hotkey,children:["Jump to line",(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",children:(0,o.jsx)(D.E,{children:(0,o.jsx)("kbd",{children:W.text})})})]}),M===eM.Code&&null!==i.rawLines&&t&&!S&&(0,o.jsxs)(et.l.Item,{onSelect:()=>{g("BLOB_FIND_IN_FILE_MENU.OPEN"),A(!0)},"aria-keyshortcuts":U.ariaKeyShortcuts,children:["Find in file",(0,o.jsx)(et.l.TrailingVisual,{"aria-hidden":"true",className:"d-flex",children:(0,o.jsx)(nP,{shortcut:U})})]})]}),(B||M===eM.Code&&null!==i.rawLines&&t)&&(0,o.jsx)(et.l.Divider,{}),(0,o.jsx)(n6,{path:d,updateTooltipMessage:Y}),(0,o.jsx)(et.l.Divider,{}),r&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(et.l.Group,{children:[(0,o.jsx)(et.l.GroupHeading,{children:"Copilot"}),(0,o.jsx)(n5,{fileReference:J})]}),(0,o.jsx)(et.l.Divider,{})]}),(0,o.jsxs)(et.l.Group,{children:[(0,o.jsx)(et.l.GroupHeading,{children:"View options"}),(0,o.jsx)(nZ,{symbolsEnabled:l})]}),(u&&L||!R&&L)&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(et.l.Divider,{}),(0,o.jsx)(rr,{deleteTooltip:I,loggedIn:!!O,siteNavLoginPath:T})]})]})})]}),b&&(0,o.jsx)(nW,{onBlamePage:"blame"===c,onDismiss:()=>{j(!1),setTimeout(()=>{let e=document.getElementById(e_.wQ);e?.focus()},0)},maxLineNumber:parseInt(E,10)??void 0})]})}function rn({blameUrl:e,viewable:t,hidden:n}){let{hash:r}=(0,eG.zy)(),i=(0,eG.Gy)(e+r),{viewBlameShortcut:l}=(0,w.wk)();return t?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(ny.Q,{"data-hotkey":l.hotkey,sx:{...n?{display:"none"}:void 0},onClick:i,className:n8.Button,children:"Blame"}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:l.hotkey,onButtonClick:i,onlyAddHotkeyScopeButton:!0})]}):null}function rr({deleteTooltip:e,loggedIn:t,siteNavLoginPath:n}){let{getUrl:r}=(0,k.Z)();return(0,o.jsx)(et.l.LinkItem,{as:e6.N,"aria-label":e,to:t?r({action:"delete"}):n,className:n8.ActionList_LinkItem,children:"Delete file"})}let ri=(e,t)=>"branch"===t?`refs/heads/${e}`:"tag"===t?`refs/tags/${e}`:e;try{rt.displayName||(rt.displayName="NavigationMenu")}catch{}try{rn.displayName||(rn.displayName="BlameButton")}catch{}try{rr.displayName||(rr.displayName="DeleteActionItem")}catch{}let ro={IconButton:"TableOfContents-module__IconButton--RCaNg"};function rl({toc:e,openPanel:t,setOpenPanel:n,isDirectoryReadme:r}){var i;let l=m.useRef(null);return(i=e)&&i.length>=2?(0,o.jsx)(z.K,{ref:l,tooltipDirection:"n",sx:{mr:2*!r},icon:P.ListUnorderedIcon,variant:"invisible","aria-label":"Outline","aria-pressed":"toc"===t,onClick:()=>{n?.("toc"===t?void 0:"toc",l.current)},size:"small",className:ro.IconButton}):null}try{rl.displayName||(rl.displayName="TableOfContents")}catch{}let ra={Box:"StickyLinesHeader-module__Box--X3qoe",Box_1:"StickyLinesHeader-module__Box_1--IClTl"};function rs({currentStickyLines:e,colorizedLines:t}){let n=Array.from(e.values());return(0,o.jsxs)("div",{className:ra.Box,children:[(0,o.jsx)("div",{className:(0,t8.$)("react-line-numbers",ra.Box_1),children:n.map(e=>(0,o.jsx)(tr.Kn,{codeLineData:e},`sticky-header-line-number-${e.lineNumber}`))}),(0,o.jsx)("div",{className:"react-code-lines",children:n.map(e=>t&&t[e.lineNumber-1]?(0,o.jsx)(e3.$6,{className:"react-code-text react-code-line-contents-no-virtualization react-file-line html-div",style:{paddingLeft:"18px"},html:t[e.lineNumber-1],onClick:()=>(0,ek.f)({line:e.lineNumber})},`sticky-header-line-${e.lineNumber}`):(0,o.jsx)(tn.S,{codeLineData:e,stylingDirectivesLine:e.stylingDirectivesLine,codeLineToSectionMap:void 0,copilotAccessAllowed:!1,onClick:()=>(0,ek.f)({line:e.lineNumber}),shouldUseInert:!1},`sticky-header-line-${e.lineNumber}`))})]})}try{rs.displayName||(rs.displayName="StickyLinesHeader")}catch{}function rc({openPanel:e,setOpenPanel:t,showTree:n,validCodeNav:r,treeToggleElement:i,searchTerm:l,setSearchTerm:s,currentStickyLines:c,focusedSearchResult:d,setFocusedSearchResult:u,searchResults:h,searchingText:p,stickyHeaderRef:f,copilotInfo:x,colorizedLines:y}){let g=(0,eg.A)(),b=g.symbolsEnabled,j=eI(f),{copilotAccessAllowed:N,refInfo:_,path:C}=(0,v.eu)(),A=eP(),{sendRepoClickEvent:S}=(0,F.T)(),B=(0,eB.ds)(),{copyFilePathShortcut:I}=(0,w.wk)(),{copyPermalinkShortcut:L}=(0,w.wk)(),T=(0,a.t)(),{githubDevUrl:R}=(0,v.sq)(),{headerInfo:{toc:O,onBranch:D,ghDesktopPath:$},viewable:M}=g,H=n$(R,D,$),W=(0,ey.O)(),U=(0,nt.y)(),{createPermalink:V}=(0,k.Z)(),{addToast:G}=(0,t6.Y6)(),{findInFileOpen:Y,setFindInFileOpen:q}=e$(),K=(0,m.useRef)(null),[Q,X,Z]=t9("raw-actions-message-tooltip",K,{direction:"nw"}),{getUrl:J}=(0,k.Z)(),ee=J(),[en]=(0,eq.I)(()=>window.location.origin+ee,ee),er=(0,m.useMemo)(()=>({type:"file",url:en,path:C,repoID:T.id,repoOwner:T.ownerLogin,repoName:T.name,ref:ri(_.name,_.refType??"branch"),commitOID:_.currentOid}),[en,C,T.id,T.ownerLogin,T.name,_.name,_.refType,_.currentOid]);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("div",{ref:eB.oM,className:nf.Box,children:[(0,o.jsx)("div",{className:"react-blob-sticky-header",children:(0,o.jsx)(nN,{isStickied:j,showTree:n,treeToggleElement:i})}),(0,o.jsxs)(E.A,{sx:{borderRadius:j?"0px":"6px 6px 0px 0px"},className:nf.Box_1,children:[(0,o.jsx)(t2.W,{as:"h2",text:"File metadata and controls"}),(0,o.jsxs)("div",{className:nf.Box_2,children:[(0,o.jsx)(nI,{}),(0,o.jsx)(nR,{className:"react-code-size-details-in-header"}),(0,o.jsx)(ep.E,{copilotInfo:x,className:"react-code-size-details-in-header",view:W?"blame":"preview"})]}),(0,o.jsxs)("div",{className:nf.Box_3,children:[I.hotkey&&(0,o.jsx)(rm,{hotkey:I.hotkey,onActivate:()=>{(0,t4.D)(C),G({type:"success",message:"Path copied!"})}}),L.hotkey&&(0,o.jsx)(rm,{hotkey:L.hotkey,onActivate:()=>{(0,t4.D)(V({absolute:!0})),G({type:"success",message:"Permalink copied!"})}}),(0,o.jsx)(t1.Ay,{copilotAccessAllowed:N,messageReference:er,hideDropdown:!0,id:"blob-view-header-copilot-icon"}),(0,o.jsxs)("div",{className:(0,t8.$)("react-blob-header-edit-and-raw-actions",nf.Box_4),children:[(0,o.jsx)(ru,{onCopy:U,fileName:g.displayName}),(0,o.jsx)(rd,{})]}),A===eM.Code&&!W&&r&&b&&(0,o.jsx)(rh,{isCodeNavOpen:"codeNav"===e,setCodeNavOpen:e=>{e&&S("BLOB_SYMBOLS_MENU.OPEN"),localStorage.setItem("codeNavOpen",e?"codeNav":""),eR(null,e,null),t(e?"codeNav":void 0)},size:"small",searchingText:p.selectedText}),!W&&(0,o.jsx)(rl,{toc:O,openPanel:e,setOpenPanel:t}),(0,o.jsxs)("div",{className:"react-blob-header-edit-and-raw-actions-combined",children:[Z,(0,o.jsxs)(tO.W,{anchorRef:K,children:[(0,o.jsx)(tO.W.Anchor,{children:(0,o.jsx)(z.K,{icon:P.KebabHorizontalIcon,tooltipDirection:"nw","aria-label":"Edit and raw actions",className:(0,t8.$)("js-blob-dropdown-click",nf.IconButton),size:"small",title:"More file actions",variant:"invisible","data-testid":"more-file-actions-button",onBlur:X})}),(0,o.jsx)(tO.W.Overlay,{className:(0,t8.$)("react-blob-header-edit-and-raw-actions-combined",nf.ActionMenu_Overlay),width:"small",children:(0,o.jsxs)(et.l,{children:[(_.canEdit&&M||H)&&(0,o.jsxs)("div",{className:"react-navigation-menu-edit-and-raw-actions",children:[(0,o.jsx)(nM,{editAllowed:_.canEdit&&M,hasOpenWithItem:H}),(0,o.jsx)(et.l.Divider,{})]}),(0,o.jsx)(o.Fragment,{children:(0,o.jsx)(n4,{viewable:M,onCopy:U,name:g.displayName,updateTooltipMessage:Q,all:!0})})]})})]})]})]})]}),W&&(0,o.jsxs)("div",{className:nf.Box_5,children:[(0,o.jsx)(nk,{}),(0,o.jsx)(nu,{})]})]}),Y&&(0,o.jsx)(nl,{stickied:j,searchTerm:l,focusedSearchResult:d,setFocusedSearchResult:u,setSearchTerm:s,searchResults:h,onClose:()=>{q(!1),"codeNav"===e&&s(p.selectedText)}}),(0,o.jsx)("div",{children:!W&&c.size>0&&(0,o.jsx)(E.A,{sx:{top:B},className:nf.Box_6,children:(0,o.jsx)(rs,{currentStickyLines:c,colorizedLines:y})})})]})}function rd(){let e=(0,eg.A)(),{getUrl:t}=(0,k.Z)(),{refInfo:{canEdit:n}}=(0,v.eu)(),{sendRepoClickEvent:r}=(0,F.T)(),{githubDevUrl:i}=(0,v.sq)(),l=(0,ew.Z)(),{editFileShortcut:a,openWithGitHubDevShortcut:s,openWithGitHubDevInNewWindowShortcut:c}=(0,w.wk)(),{headerInfo:{editTooltip:d,ghDesktopPath:u,onBranch:h}}=e,m=n$(i,h,u);return n||m?(0,o.jsxs)(o.Fragment,{children:[i&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(er.A,{className:"js-github-dev-shortcut d-none","data-hotkey":s.hotkey,href:i}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:s.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{l(i+window.location.pathname.substring(1))}}),(0,o.jsx)(er.A,{className:"js-github-dev-new-tab-shortcut d-none","data-hotkey":c.hotkey,href:i,target:"_blank"}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:c.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{window.open(i,"_blank")}})]}),(0,o.jsxs)(t3.A,{children:[n?(0,o.jsx)(z.K,{as:e6.N,tooltipDirection:"nw","aria-label":d,"data-hotkey":a.hotkey,icon:P.PencilIcon,to:t({action:"edit"}),size:"small",sx:{...t0.E},"data-testid":"edit-button",className:nf.IconButton_1}):(0,o.jsx)(nr,{icon:P.PencilIcon,"aria-label":d,disabled:!0,className:nf.IconButton_1}),(0,o.jsxs)(tO.W,{onOpenChange:e=>e&&r("BLOB_EDIT_DROPDOWN"),children:[(0,o.jsx)(tO.W.Anchor,{children:(0,o.jsx)(z.K,{tooltipDirection:"nw",icon:P.TriangleDownIcon,size:"small","aria-label":"More edit options","data-testid":"more-edit-button"})}),(0,o.jsx)(tO.W.Overlay,{align:"end",children:(0,o.jsx)(et.l,{children:(0,o.jsx)(nM,{editAllowed:n,hasOpenWithItem:m})})})]})]}),n&&(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:"e,Shift+E",onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{l(t({action:"edit"}))}})]}):null}function ru({onCopy:e,fileName:t}){let{viewRawContentShortcut:n,copyRawContentShortcut:r,downloadRawContentShortcut:i}=(0,w.wk)(),{renderImageOrRaw:l,renderedFileInfo:a,viewable:s,image:c,rawBlobUrl:d,headerInfo:{isGitLfs:u}}=(0,eg.A)(),h=(0,ew.Z)(),{addToast:p}=(0,t6.Y6)(),f=(0,m.useRef)(null),[y,g,b]=t9("raw-copy-message-tooltip",f),v=async()=>{await n7(d,t)},j=async()=>{let t=await e(),{ariaLabel:n}=(0,nn.t0)(t);y(n)},N=new URL(d,ex.fV.origin);N.searchParams.set("download","");let _={"aria-label":"Download raw file",tooltipDirection:"n",icon:P.DownloadIcon,size:"small",onClick:async()=>{u||await n7(d,t)},"data-testid":"download-raw-button","data-hotkey":i.hotkey,sx:{borderTopLeftRadius:0,borderBottomLeftRadius:0}};return!u&&(a&&!s||c)?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(z.K,{"aria-label":"Download raw file",tooltipDirection:"n",icon:P.DownloadIcon,size:"small",onClick:v,"data-testid":"download-raw-button","data-hotkey":i.hotkey}),(0,o.jsx)(x._,{buttonTestLabel:"download-raw-button-shortcut",buttonFocusId:e_.wQ,buttonHotkey:i.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:v})]}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(t3.A,{children:[(0,o.jsx)(t5.z,{href:d,download:l?"true":void 0,size:"small",sx:{linkButtonSx:t0.E},"data-testid":"raw-button","data-hotkey":n.hotkey,className:nf.LinkButton,children:"Raw"}),!u&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(z.K,{ref:f,"aria-label":"Copy raw file",tooltipDirection:"n",icon:P.CopyIcon,size:"small",onMouseLeave:g,onClick:j,"data-testid":"copy-raw-button","data-hotkey":r.hotkey,onBlur:g}),b]}),u?(0,o.jsx)(z.K,{as:"a","data-turbo":"false",href:N.toString(),..._}):(0,o.jsx)(z.K,{..._})]}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:n.hotkey,buttonTestLabel:"raw-button-shortcut",onlyAddHotkeyScopeButton:!0,onButtonClick:()=>h(d)}),!u&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(x._,{buttonTestLabel:"copy-raw-button-shortcut",buttonFocusId:e_.wQ,buttonHotkey:r.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:async()=>{let t=await e(),{ariaLabel:n,content:r}=(0,nn.t0)(t);p({message:n,icon:r})}}),(0,o.jsx)(x._,{buttonTestLabel:"download-raw-button-shortcut",buttonFocusId:e_.wQ,buttonHotkey:i.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:async()=>{await n7(d,t)}})]})]})}function rh({isCodeNavOpen:e,setCodeNavOpen:t,size:n,searchingText:r}){let{toggleSymbolsShortcut:i}=(0,w.wk)(),l=!(0,h.ud)().openSymbolsOption.enabled&&!e;return(0,o.jsx)(z.K,{"aria-label":e?"Close symbols panel":"Open symbols panel",tooltipDirection:"nw","aria-pressed":e,"aria-expanded":e,"aria-controls":"symbols-pane",icon:P.CodeSquareIcon,className:(0,t8.$)(l&&r?"react-button-with-indicator":"",nf.IconButton_2),"data-hotkey":i.hotkey,onClick:()=>{(0,eO.Ov)(!0),t(!e)},variant:"invisible","data-testid":"symbols-button",id:"symbols-button",size:n})}function rm({hotkey:e,onActivate:t}){return(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:e,onButtonClick:t})}try{rc.displayName||(rc.displayName="BlobViewHeader")}catch{}try{rd.displayName||(rd.displayName="EditMenu")}catch{}try{ru.displayName||(ru.displayName="RawGroup")}catch{}try{rh.displayName||(rh.displayName="SymbolsButton")}catch{}try{rm.displayName||(rm.displayName="KeyboardShortcut")}catch{}var rp=n(87183),rf=n(29943),rx=n(98075),ry=n(63867);let rg={definitions:void 0,localReferences:void 0,crossReferences:void 0,error:!1};var rb=n(42728),rv=n(56369);function rj({reference:e,isHighlighted:t,href:n,onClick:r,role:i,ariaLevel:l,symbol:a,index:s,focusElement:c}){let[d,u]=(0,m.useState)(null);(0,m.useEffect)(()=>{if(c){let e=document.getElementById(`find-in-file-item-${s}`);e&&e.focus()}},[c,s]);let{tabSize:h}=(0,eg.A)(),p=(0,eJ.bp)(),f=(0,rb.W)(e.highlightedText,e.stylingDirectives,e.bodyText,"plain",h,p);return(0,o.jsx)(er.A,{as:e6.N,to:n,role:i,sx:{display:"block",p:0,fontWeight:400,fontSize:"12px",":hover:not([disabled])":{bg:"canvas.default"},":hover":{textDecoration:"none"},'[data-component="text"]':{gridArea:"auto"},gridTemplateAreas:"text",whiteSpace:"break-spaces",verticalAlign:"top",lineHeight:"20px",position:"relative",overflowWrap:"anywhere",fontFamily:"mono",overflow:"visible"},onClick:e=>{e.ctrlKey||e.metaKey||r?.()},onSelect:r,id:`find-in-file-item-${s}`,"aria-current":t?"location":void 0,"aria-level":l,onKeyDown:e=>{"ArrowDown"===e.key?(rw("nextElementSibling"),e.preventDefault()):"ArrowUp"===e.key&&(rw("previousElementSibling"),e.preventDefault())},children:(0,o.jsx)(E.A,{sx:{p:1,py:"5px",...t?{background:"var(--bgColor-attention-muted, var(--color-attention-subtle))",boxShadow:"inset 2px 0 0 var(--bgColor-attention-emphasis, var(--color-attention-fg))"}:{}},children:(0,o.jsxs)("div",{className:"d-flex",children:[(0,o.jsx)(E.A,{className:"text-small blob-num color-fg-muted",sx:{width:"auto",minWidth:"auto"},children:e.lineNumber}),(0,o.jsxs)(E.A,{sx:{overflow:"hidden",whiteSpace:"pre",position:"relative"},children:[null!==d&&(0,o.jsxs)("div",{id:`offset-${e.href(!1)}`,style:{marginLeft:-d},children:[a.length>0&&(0,o.jsx)(rv.U,{symbols:[e],lineNumber:e.lineNumber,sx:{overflow:"initial"},isNotUsingWhitespace:!0}),(0,o.jsx)(e3.JR,{sx:{position:"relative",width:"100%",overflow:"hidden"},html:f,"aria-current":t?"location":void 0})]}),(0,o.jsx)("span",{ref:e=>{u(e?.offsetWidth??null)},style:{visibility:"hidden",position:"absolute",whiteSpace:"pre"},children:function(e,t){let n=34-e.length,r=t.bodyText.slice(0,t.ident.start.column);if(n<=0)return r;n=Math.max(n/2,n-t.bodyText.slice(t.ident.start.column+e.length).trimEnd().length);let i=r.split(" "),o=[];for(let e=i.length-1;e>=0;e--){let t=i[e];if(o.unshift(t),o.join(" ").length<=n)i.pop();else break}let l=i.join(" "),a=(r.slice(l.length).match(/^[ \t]*/)||[])[0]??"";return`${i.join(" ")}${a}`}(a,e)})]})]})})})}function rw(e){let{activeElement:t}=document,n=t?.[e];if(n)if("treeitem"!==n.role&&"nextElementSibling"===e){let e=n.querySelector('[role="treeitem"]');e?.focus()}else n.focus()}try{rj.displayName||(rj.displayName="CodeNavCell")}catch{}function rN({results:e,repo:t,filePath:n,highlightedIndex:r,isDefinition:i,onClick:l,offset:s,initiallyExpanded:c,enableExpandCollapse:d,symbol:u,setFocusOnFile:h}){let p=!!(0,ey.O)(),[f,x]=(0,m.useState)(c),y=(0,a.t)(),{path:g}=(0,v.eu)(),b=m.useRef(null),[j,w]=(0,m.useState)(!1),N=(0,eO.DC)(),_=e.slice(0,10),k=e.length>10?e.slice(10):[],{sendRepoClickEvent:C}=(0,F.T)(),A=y.ownerLogin===t.ownerLogin&&y.name===t.name,S=(0,m.useCallback)(e=>{"Enter"===e.key||" "===e.key?(x(!f),e.preventDefault()):"ArrowLeft"===e.key?x(!1):"ArrowRight"===e.key?(x(!0),f&&b.current?.focus()):"ArrowDown"===e.key?(rw("nextElementSibling"),e.preventDefault()):"ArrowUp"===e.key&&(rw("previousElementSibling"),e.preventDefault())},[f]);(0,m.useEffect)(()=>{r&&r>=10+s&&w(!0)},[r,s]),(0,m.useEffect)(()=>{h&&N&&b.current?.focus()},[h,N]),(0,m.useEffect)(()=>{N||document.getElementById(e_.wQ)?.focus()},[N]);let B=`${n}-${i?"definition":"reference"}-group`;return(0,o.jsxs)("div",{children:[(0,o.jsxs)(E.A,{sx:{fontSize:0,px:3,py:2,display:"flex",justifyContent:"space-between",borderTop:"1px solid",borderColor:"border.muted",cursor:d?"pointer":"auto"},onClick:d?()=>x(!f):void 0,onKeyDown:S,ref:b,children:[(0,o.jsxs)(E.A,{sx:{display:"flex"},children:[i&&y.id!==t.id&&(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(e2.r,{square:!0,src:t.ownerAvatar,sx:{mr:2,backgroundColor:"#FFFFFF"},size:16}),(0,o.jsx)(E.A,{sx:{fontWeight:"600",mr:1},children:t.name})]}),(0,o.jsxs)(E.A,{as:"button","aria-expanded":f,"aria-controls":B,sx:{fontWeight:"400",color:"fg.muted",display:"flex",flexDirection:"row",backgroundColor:"canvas.default",border:"none",padding:0},children:[d&&(0,o.jsx)(U.A,{"aria-hidden":"true",icon:f?P.ChevronDownIcon:P.ChevronRightIcon}),(0,o.jsx)(E.A,{sx:{display:"table",width:"100%",tableLayout:"fixed"},children:(0,o.jsxs)(Y.A,{"aria-label":`${i?"Definitions":"References"} in ${n!==g?n:"this file"}`,title:n,sx:{direction:"rtl",maxWidth:"100%",pl:2,display:"table-cell",textAlign:"left"},children:["\u200E",A&&n===g?"In this file":n,"\u200E"]})})]})]}),e&&!f&&(0,o.jsx)(nd.A,{sx:{ml:2},children:e.length})]}),f&&(0,o.jsxs)(E.A,{"aria-label":`Results in ${n!==g?n:"this file"}`,id:B,sx:{overflowX:"hidden"},role:"group",className:"code-nav-file-information",children:[_.map((e,t)=>(0,o.jsx)(rj,{reference:e,isHighlighted:r===t+s,href:e.href(p),onClick:()=>{l&&l(t+s),C("BLOB_SYMBOLS_MENU.SYMBOL_DEFINITION_CLICK")},symbol:u,index:t+s},`codeNavigation${t+s}`)),j&&k.map((e,t)=>(0,o.jsx)(rj,{reference:e,isHighlighted:r===t+s+10,href:e.href(p),onClick:()=>{l&&l(t+s+10),C("BLOB_SYMBOLS_MENU.SYMBOL_DEFINITION_CLICK")},symbol:u,index:t+s+10,focusElement:0===t},`codeNavigation${t+s+10}`)),k.length>0&&(0,o.jsx)(E.A,{sx:{px:3,pt:1,pb:2,fontSize:0,color:"fg.muted",borderColor:"border.muted"},children:(0,o.jsx)(ny.Q,{leadingVisual:j?P.FoldIcon:P.UnfoldIcon,onClick:()=>w(!j),sx:{color:"fg.default"},variant:"invisible",size:"small","aria-selected":!1,children:j?"Show less":"Show more"})})]})]},n)}try{rN.displayName||(rN.displayName="CodeNavFileInformation")}catch{}function r_({definitions:e,references:t,highlightedIndex:n,initiallyExpanded:r,enableExpandCollapse:i,onClick:l,symbol:a,setFocusOnFile:s}){let c=(0,m.useMemo)(()=>{let n={};if(e)for(let t of e){let e=t.pathKey();n[e]||(n[e]=[]),n[e].push(t)}else if(t)for(let e of t){let t=e.pathKey();n[t]||(n[t]=[]),n[t].push(e)}return n},[e,t]),d=0;return(0,o.jsx)(o.Fragment,{children:Object.keys(c).map((t,u)=>{let h=c[t],m=(0,o.jsx)(rN,{repo:h[0].repo,filePath:h[0].path,results:h,highlightedIndex:n,isDefinition:void 0!==e&&e.length>0,onClick:l,offset:d,initiallyExpanded:r,enableExpandCollapse:i,symbol:a,setFocusOnFile:0===u&&s},t);return d+=h.length,m})})}try{r_.displayName||(r_.displayName="CodeNavInfoPanelData")}catch{}let rk=(0,m.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_CodeNav_ScrollMarks_tsx").then(n.bind(n,31513)));function rC({codeNavInfo:e,selectedText:t,lineNumber:n,offset:r,onClose:i,onBackToSymbol:l,onSymbolSelect:s,isLoading:c,setSearchResults:d,setFocusedSearchResult:u}){let{findNextShortcut:h,findPrevShortcut:p}=(0,w.wk)(),f=!!(0,ey.O)(),{definitions:x,localReferences:y,crossReferences:g,error:b}=function(e,t,n,r){let[i,o]=(0,m.useState)(rg);return(0,m.useEffect)(()=>{(async()=>{if(n<0||r<0)return;let{definitions:i,localReferences:l,crossReferences:a,setLoading:s}=e.getDefinitionsAndReferences(t,n,r);o(rg);try{let e=await i;o({definitions:e,localReferences:void 0,crossReferences:void 0,error:!1}),s(!1);let[t,n]=await Promise.all([l,a]);o({definitions:e,localReferences:t,crossReferences:n,error:!1})}catch{o({...rg,error:!0})}finally{s(!1)}})()},[e,t,n,r]),i}(e,t,n,r),{copilotAccessAllowed:j}=(0,v.eu)(),[N,_]=(0,m.useState)(-1),[k,C]=(0,m.useState)(!1),A=(0,a.t)(),S=(0,m.useMemo)(()=>x?.definitions||[],[x?.definitions]),B=(0,m.useMemo)(()=>y?.references||[],[y]),I=(0,m.useMemo)(()=>g?.references||[],[g?.references]),L=S.length,T=B.length+I.length,R=(0,ew.Z)(),O=S.length>0?S[0]:void 0,D=I.map(e=>e.path).filter((e,t,n)=>n.indexOf(e)===t),F=k?T:B.length,$=(0,m.useRef)(null),{language:M,languageID:H}=(0,eg.A)(),W=(0,m.useMemo)(()=>({type:"symbol",kind:"codeNavSymbol",name:t,languageID:H,languageName:M,codeNavDefinitions:S.map(e=>({ident:e.ident,extent:e.extent,kind:e.kind.fullName,fullyQualifiedName:e.fullyQualifiedName,ref:rB(e.refInfo),commitOID:e.refInfo.currentOid,repoID:e.repo.id,repoName:e.repo.name,repoOwner:e.repo.ownerLogin,path:e.path})),codeNavReferences:[...B.map(e=>({ident:e.ident,path:e.path,ref:rB(e.refInfo),commitOID:e.refInfo.currentOid,repoID:e.repo.id,repoName:e.repo.name,repoOwner:e.repo.ownerLogin})),...I.map(e=>({ident:e.ident,path:e.path,ref:rB(e.refInfo),commitOID:e.refInfo.currentOid,repoID:e.repo.id,repoName:e.repo.name,repoOwner:e.repo.ownerLogin}))]}),[I,S,M,H,B,t]);(0,m.useEffect)(()=>{O&&O.repo.name===e.repo.name&&O.path===e.path?d([O,...B]):d(B),u(void 0),window.dispatchEvent(new rf.Xr(W))},[e.path,e.repo.name,O,B,t,d,u,W]),(0,V.Gp)(e=>{e||$.current?.focus()}),(0,m.useEffect)(()=>{_(-1)},[t]),(0,m.useEffect)(()=>{C(D.length>0&&D.length<=5)},[D.length]);let G=({index:e,direction:t,navigate:n})=>{if(void 0!==e){let t=e>=L?B[e-L]:S[e];_(e),n&&R(t.href(f)),(0,ek.f)({line:t.lineNumber,column:t.ident.start.column})}if(void 0!==t){let e=Math.max(L,N+t),r=B[e-L];e<B.length+S.length&&r&&(_(e),n&&R(r.href(f)),(0,ek.f)({line:r.lineNumber,column:r.ident.start.column}))}};return(0,o.jsxs)("div",{children:[(0,o.jsxs)(E.A,{as:"h2",sx:{fontSize:"12px",py:2,px:3,display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center"},role:"group","aria-roledescription":"Symbol Navigation Details",children:[(0,o.jsx)(ny.Q,{onClick:l,onSelect:l,id:"back-to-all-symbols","aria-label":"Back to All Symbols",ref:$,variant:"invisible",sx:{order:1,pr:3,pl:0,px:0,":hover:not([disabled])":{bg:"canvas.default"}},children:(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[(0,o.jsx)(U.A,{icon:P.ArrowLeftIcon,sx:{pr:1,fontWeight:600,color:"fg.muted"},size:20}),(0,o.jsx)(E.A,{sx:{fontSize:0,color:"fg.subtle",fontWeight:400},children:"All Symbols"})]})}),(0,o.jsx)(z.K,{"aria-label":"Close symbols",tooltipDirection:"w","data-hotkey":"Escape",icon:P.XIcon,sx:{order:3,mr:-2,color:"fg.default"},onClick:i,variant:"invisible"})]}),(0,o.jsx)(E.A,{sx:{alignItems:"center",display:"flex",justifyContent:"space-between",pb:3},children:(0,o.jsx)(rS,{currentSymbol:O,selectedText:t,codeNavInfo:e,onSymbolSelect:s,children:1===S.length?(0,o.jsx)(t1.Ay,{copilotAccessAllowed:j,messageReference:W}):void 0})}),c&&(0,o.jsx)(E.A,{sx:{display:"flex",flexDirection:"row",alignItems:"center",p:3,justifyContent:"center"},children:(0,o.jsx)(ry.A,{size:"small"})}),!b&&!c&&S&&S.length>0?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(rA,{headerText:S.length>1?"Definitions":"Definition",searchType:x?.backend==="precise"?"Precise":"Search"}),(0,o.jsx)("div",{children:S&&(0,o.jsx)(r_,{definitions:S,onClick:e=>{G({index:e})},highlightedIndex:N,initiallyExpanded:!0,enableExpandCollapse:S.length>1,symbol:t,setFocusOnFile:!0},"definitions")})]}):null,!c&&(B.length>0||I.length>0)&&(0,o.jsx)(rA,{headerText:`${F} ${F>1?"References":"Reference"}`,searchType:"Search",sx:{justifyContent:"space-between"},children:(0,o.jsxs)(E.A,{sx:{display:"float",float:"right",mr:"-6px"},children:[(0,o.jsx)(z.K,{unsafeDisableTooltip:!0,"aria-label":"Previous reference","data-hotkey":p.hotkey,onClick:()=>G({direction:-1,navigate:!0}),sx:{mr:2,cursor:"pointer",color:"fg.muted"},disabled:N<=S.length,icon:P.ChevronUpIcon,variant:"invisible",size:"small"}),(0,o.jsx)(z.K,{unsafeDisableTooltip:!0,"aria-label":"Next reference","data-hotkey":h.hotkey,onClick:()=>G({direction:1,navigate:!0}),sx:{cursor:"pointer",color:"fg.muted"},disabled:N>=B.length+S.length-1,icon:P.ChevronDownIcon,variant:"invisible",size:"small"}),(0,o.jsx)("button",{hidden:!0,"data-hotkey":h.hotkey,onClick:()=>G({direction:1,navigate:!0}),"data-testid":"find-next-button"}),(0,o.jsx)("button",{hidden:!0,"data-hotkey":p.hotkey,onClick:()=>G({direction:-1,navigate:!0}),"data-testid":"find-prev-button"})]})}),b&&(0,o.jsx)(E.A,{sx:{p:3,fontWeight:"400",color:"fg.muted"},children:"No references found"}),!c&&B.length>0&&(0,o.jsx)(r_,{initiallyExpanded:!0,enableExpandCollapse:!0,references:B,highlightedIndex:N-L,onClick:e=>{G({index:L+e})},symbol:t,setFocusOnFile:!(S&&S.length>0)},"referencesInfoBox"),!c&&k&&(0,o.jsx)(r_,{initiallyExpanded:!1,enableExpandCollapse:!0,references:I,symbol:t},"crossReferencesInfoBox"),0===B.length&&0===S.length&&!b&&!c&&(0,o.jsx)(E.A,{sx:{p:3,fontWeight:"400",color:"fg.muted"},children:"No definitions or references found"}),(0,o.jsxs)(E.A,{sx:{px:2,py:2,fontSize:0,color:"fg.muted",borderTop:"1px solid",borderColor:"border.muted"},children:[D.length>5&&(0,o.jsx)(ny.Q,{leadingVisual:k?P.FoldIcon:P.UnfoldIcon,sx:{color:"fg.default",mb:2},variant:"invisible",size:"small",onClick:()=>C(!k),children:k?"Show less":"Show more"}),(0,o.jsx)(ny.Q,{as:er.A,leadingVisual:P.SearchIcon,sx:{color:"fg.default"},variant:"invisible",size:"small",href:(0,ea.Y8Y)({owner:A.ownerLogin,repo:A.name,searchTerm:t}),children:"Search for this symbol"})]}),(0,o.jsx)(m.Suspense,{fallback:null,children:(0,o.jsx)(rk,{definitionsOrReferences:[...S,...B]})})]})}function rA({headerText:e,searchType:t,sx:n,children:r}){return(0,o.jsxs)(E.A,{sx:{fontSize:"14px",px:3,py:2,fontWeight:"600",backgroundColor:"canvas.subtle",borderTop:"1px solid",borderColor:"border.muted",height:"36px",display:"flex",flexDirection:"row",alignItems:"center",...n},children:[(0,o.jsxs)(R.A,{as:"h3",sx:{fontSize:"12px",fontWeight:"semibold",color:"fg.muted"},children:[e,(0,o.jsx)(W.A,{sx:{ml:2,fontWeight:"light"},children:t})]}),r]})}function rS({currentSymbol:e,selectedText:t,codeNavInfo:n,onSymbolSelect:r,children:i}){let l=e?.fullyQualifiedName??t,a=l.split(/(\W+)/).map(e=>{let t=/^\W+$/.test(e),r=t?[]:n.getLocalDefinitions(e,!0),i=1===r.length?r[0]:void 0,o=i?.kind.plColor;return{text:e,symbol:i,symbolColor:o,isSeparator:t}});return(0,o.jsxs)(E.A,{as:"h3",sx:{display:"flex",flexDirection:"column",flexGrow:1,alignContent:"start",fontWeight:400,fontSize:1,fontFamily:"mono",flexWrap:"wrap",minWidth:0,verticalAlign:"center",gap:2,px:3},"aria-label":`${e?.kind.fullName||""} ${l}`.trimStart(),children:[e&&(0,o.jsxs)(E.A,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",flexGrow:1,width:"100%"},children:[(0,o.jsx)(q,{symbolKind:e.kind,showFullSymbol:!0}),i]}),(0,o.jsxs)(Y.A,{title:l,sx:{maxWidth:290,mt:"3px",direction:"rtl",alignSelf:"start"},inline:!0,children:["\u200E",a.map((e,t)=>{let n=e.symbol?{all:"unset",cursor:"pointer","&:hover":{backgroundColor:"attention.muted"}}:{};return(0,o.jsx)(E.A,{as:"span",role:"button",tabIndex:e.isSeparator?-1:0,sx:{...n,color:e.symbolColor,direction:"ltr"},onClick:()=>e.symbol?r(e.symbol):void 0,onKeyDown:t=>{e.symbol&&["Enter","Space"].includes(t.code)&&r(e.symbol)},children:e.text},`${e.text}-${t}`)}),"\u200E"]})]})}function rB(e){return e.name===e.currentOid?e.currentOid:"tree"===e.refType?`refs/heads/${e.name}`:(0,rx.D7)(e.name,e.refType??"branch")}try{rk.displayName||(rk.displayName="ScrollMarks")}catch{}try{rC.displayName||(rC.displayName="CodeNavSymbolDetails")}catch{}try{rA.displayName||(rA.displayName="CodeNavSymbolSectionHeader")}catch{}try{rS.displayName||(rS.displayName="CodeNavSymbolDefinitionHeader")}catch{}function rI({selectedText:e,showCodeNavWithSymbol:t,lineNumber:n,offset:r,onClose:i,onClear:l,codeNavInfo:s,isLoading:c,setSearchResults:d,setFocusedSearchResult:u,autoFocusSearch:h}){function p(e){t(e),x(!1)}let[f,x]=(0,m.useState)(!e);(0,V.Gp)(e=>{e&&(l(),x(!0),d([]))});let{refInfo:y,path:g}=(0,v.eu)(),b=(0,a.t)(),{language:j,languageID:w,displayUrl:N}=(0,eg.A)(),_=(0,m.useMemo)(()=>({type:"file",languageID:w,languageName:j,path:g,ref:(0,rx.D7)(y.name,y.refType),commitOID:y.currentOid,repoID:b.id,repoName:b.name,repoOwner:b.ownerLogin,url:N}),[w,j,g,y.name,y.refType,y.currentOid,b.id,b.name,b.ownerLogin,N]);return(0,m.useEffect)(()=>{e?x(!1):f||(window.dispatchEvent(new rf.Xr(_)),x(!0))},[e,s]),(0,o.jsx)("div",{id:"symbols-pane",children:f?s?(0,o.jsx)(ed,{treeSymbols:s.symbolTree,onSymbolSelect:p,codeSymbols:s.symbols,onClose:i,autoFocusSearch:h}):(0,o.jsx)("div",{children:"Click on a symbol to see code navigation data"}):(0,o.jsx)(rC,{codeNavInfo:s,selectedText:e,lineNumber:n,offset:r,onBackToSymbol:()=>{l(),x(!0),d([]),window.dispatchEvent(new rf.Xr(_))},onClose:()=>{i(),l(),d([]),window.dispatchEvent(new rf.Xr(_))},onSymbolSelect:p,isLoading:c,setSearchResults:d,setFocusedSearchResult:u})})}try{rI.displayName||(rI.displayName="CodeNavInfoPanel")}catch{}var rL=n(13430);let rT=m.memo(rE);function rE(e){let{...t}=e,[n,r]=m.useState(!1),{screenSize:i}=(0,u.lm)();return m.useEffect(()=>{r(i<u.Gy.large)},[i]),(0,o.jsxs)(o.Fragment,{children:[!n&&(0,o.jsx)(rR,{className:"inner-panel-content-not-narrow",...e}),n&&(0,o.jsx)(tS.l,{onClose:()=>t.setOpenPanel(void 0),renderHeader:()=>null,renderBody:()=>rR({...t})})]})}function rR({stickySx:e,stickyHeaderRef:t,openPanel:n,isCodeNavLoading:r,codeNavInfo:i,setOpenPanel:l,showCodeNavWithSymbol:a,searchingText:s,setSearchingText:c,setSearchTerm:d,setSearchResults:u,setFocusedSearchResult:h,autoFocusSearch:m,className:p}){let{headerInfo:{toc:f}}=(0,eg.A)(),x=eI(t);return(0,o.jsx)(rL.Z,{sx:{...e,...x?{borderRadius:"0px 0px 6px 6px",borderTop:0}:{}},className:`panel-content-narrow-styles ${p||""}`,children:"toc"===n?(0,o.jsx)(rp.A,{toc:f,onClose:()=>{l(void 0)}}):"codeNav"===n&&(0,o.jsx)(rI,{codeNavInfo:i,showCodeNavWithSymbol:a,selectedText:s.selectedText,lineNumber:s.lineNumber-1,offset:s.offset,onClose:()=>{l(void 0),localStorage.setItem("codeNavOpen",""),eR(null,!1,null),document.getElementById("symbols-button")?.focus()},isLoading:r,onClear:()=>c({selectedText:"",lineNumber:0,offset:-1}),setSearchTerm:d,setSearchResults:u,setFocusedSearchResult:h,autoFocusSearch:m})})}try{rT.displayName||(rT.displayName="PanelContent")}catch{}try{rE.displayName||(rE.displayName="PanelContentUnmemoized")}catch{}try{rR.displayName||(rR.displayName="InnerPanelContent")}catch{}function rO({blame:e,blob:t,searchTerm:n,setSearchTerm:r,symbolsExpanded:i,setValidCodeNav:l,showTree:c,treeToggleElement:d,validCodeNav:p,copilotInfo:f}){let{path:x}=(0,v.eu)(),y=t.symbolsEnabled,g=(0,m.useRef)(null),b=eL(),[w,N]=(0,m.useState)(!1),{openPanel:_,setOpenPanel:k}=(0,j.Ak)(),C=(0,m.useRef)(null),A=(0,h.ud)().openSymbolsOption.enabled&&y,{screenSize:S}=(0,u.lm)();(0,V.Gp)(e=>{e&&N(!0)});let{sendRepoClickEvent:B}=(0,F.T)(),I=(0,m.useRef)(!0);function L(){window.scrollY<300&&O(null,!0)}(0,m.useEffect)(()=>{I.current?(I.current=!1,H.selectedText&&!_&&A&&i&&D()):(O(null,!0),r(""),N(!1),W({selectedText:"",lineNumber:-1,offset:0}))},[x]),(0,m.useEffect)(()=>(window.addEventListener("scroll",L),()=>{window.removeEventListener("scroll",L)}),[]);let T=!!(0,ey.O)(),{currentStickyLines:R,setStickyLines:O}=function(){let[e,t]=(0,m.useState)(()=>new Map),n=(0,m.useCallback)((n,r)=>{let i=!1;if(null===n&&0===e.size)return;if(null===n&&r){e.clear(),t(new Map(e));return}if(!n)return;let o=n.lineNumber;if(r&&e.has(o)?(e.delete(o),i=!0):r||e.has(o)||(e.set(o,n),i=!0),e.has(o))for(let[t]of e){let n=e.get(t);(!n.ownedSection||n.ownedSection.endLine<o||o<n.lineNumber)&&(e.delete(t),i=!0)}i&&t(new Map(e))},[]);return{currentStickyLines:e,setStickyLines:n}}(),D=(0,m.useCallback)(()=>{!T&&A&&(k("codeNav"),B("BLOB_SYMBOLS_MENU.OPEN_WITH_SYMBOL"),localStorage.setItem("codeNavOpen","codeNav"),eR(null,!0,null))},[T,A,k,B]),{isCodeNavLoading:$,codeNavInfo:M,showCodeNavWithSymbol:P,showCodeNavForToken:z,searchingText:H,setSearchingText:W}=function(e,t,n,r,i){let o=(0,ew.Z)(),l=(0,a.t)(),c=(0,s.i)(),{refInfo:d,path:u}=(0,v.eu)(),[h,p]=(0,m.useState)(!1),f=(0,m.useMemo)(()=>e.stylingDirectives?.map(e=>(0,ej.M)(e)),[e.stylingDirectives]),[x]=(0,ew.o)(),y="1"===x.get("plain"),g=(0,m.useMemo)(()=>{n(!0);try{return new ev.Op(l,d,u,!!c,e.rawLines||[],e.symbols?.symbols??[],f,e.language,y,p)}catch{n(!1)}},[n,l,d,u,c,e.rawLines,e.symbols?.symbols,e.language,f,y]),[b,j]=(0,m.useState)(()=>{let t=(0,eN.$c)(r);if(!t.blobRange?.start?.line)return{selectedText:"",lineNumber:-1,offset:0};if(!i&&t.blobRange.start.line===t.blobRange.end.line&&null!==t.blobRange.start.column&&null!==t.blobRange.end.column&&t.blobRange.end.column-t.blobRange.start.column>2&&e.stylingDirectives&&e.stylingDirectives[t.blobRange.start.line-1]?.length&&g?.blobLines[t.blobRange.start.line-1]){let e=g.blobLines[t.blobRange.start.line-1]?.substring(t.blobRange.start.column-1,t.blobRange.end.column-1),n=f[t.blobRange.start.line-1]?.find(e=>e.s===t.blobRange.start.column-1&&e.e===t.blobRange.end.column-1);return e&&n&&(0,e_.Bm)(e,n.c)?{selectedText:e,lineNumber:t.blobRange.start.line,offset:t.blobRange.start.column}:{selectedText:"",lineNumber:-1,offset:0}}if(!r||!g||i)return{selectedText:"",lineNumber:-1,offset:0};{let e=g.getSymbolOnLine(Number(r.substring(2)));return e?{selectedText:e.name,lineNumber:e.lineNumber,offset:e.ident.start.column}:{selectedText:"",lineNumber:-1,offset:0}}});return{isCodeNavLoading:h,codeNavInfo:g,showCodeNavWithSymbol:(0,m.useCallback)(e=>{j({selectedText:e.name,lineNumber:e.lineNumber,offset:e.ident.start.column}),t(),o(e.href()),(0,ek.f)({line:e.lineNumber})},[t,o]),showCodeNavForToken:(0,m.useCallback)(e=>{j(e),t()},[t]),setSearchingText:j,searchingText:b}}(t,D,l,ex.fV.hash,T),{searchStatus:U,searchResults:G,setSearchResults:Y,focusedSearchResult:q,setFocusedSearchResult:K}=function(e,t){let{findInFileWorkerPath:n}=(0,v.sq)(),[r,i]=(0,m.useState)([]),[o,l]=(0,m.useState)(void 0),[a,s]=(0,m.useState)("done"),c=m.useRef(),{refInfo:d,path:u}=(0,v.eu)();!c.current&&t&&(c.current=new eA.N(new eC.z(n,eS),200,e=>1!==e.query.length));let h=(0,m.useRef)(t);h.current=t;let p=(0,m.useRef)("");(0,m.useEffect)(()=>function(){c.current?.terminate()},[]);let f=(0,m.useRef)(void 0);return c.current&&e!==f.current&&(c.current.onResponse=t=>{t.query===h.current&&(l(0),i(e?.createReferences(t.ranges)||[]),s("done"),p.current=h.current)},f.current=e),(0,m.useEffect)(()=>{if(!e||!c.current||!p.current||""===p.current){i([]),l(0),s("done");return}i([]),l(0),s("pending"),c.current.post({query:p.current,lines:e.blobLines,currentCodeReferences:void 0})},[d.name,u]),(0,m.useEffect)(()=>{if(e&&c.current)if(""===t)i([]),l(0),s("done"),p.current="";else{var n;if(p.current===t||!((n=t).length>0&&n.length<=1e3))return;s("pending");let i=p.current.length>0&&t.startsWith(p.current);c.current.post({query:t,lines:e.blobLines,currentCodeReferences:i?r:void 0})}},[t]),{focusedSearchResult:o,setFocusedSearchResult:l,searchResults:r,setSearchResults:i,searchStatus:a}}(M,n),{headerInfo:{toc:Q}}=t,X=(0,m.useMemo)(()=>({value:!1}),[M]);X.value=!_||X.value;let Z=void 0!==_&&M&&p&&!(0===M.symbols.length&&"codeNav"===_&&!X.value)&&!(!Q&&"toc"===_)&&"edit"!==_&&!(!y&&"codeNav"===_),J=Z?_:void 0;return(0,m.useEffect)(()=>{_&&!Z&&k(void 0)},[_,k,Z]),(0,m.useEffect)(()=>{try{(0,ef.BI)("blob-size",{lines:t.stylingDirectives?.length,truncatedSloc:t.headerInfo?.lineInfo.truncatedSloc,truncatedLoc:t.headerInfo?.lineInfo.truncatedLoc,length:t.rawLines?.reduce((e,t)=>e+t.length,0)??0,humanLength:t.headerInfo?.blobSize})}catch{}},[t]),(0,o.jsx)(eg.s,{blob:t,children:(0,o.jsx)(ey.k,{blame:e,children:(0,o.jsxs)(eb.EN,{children:[M&&(0,o.jsx)(rD,{codeNavInfo:M}),(0,o.jsx)(em.D,{}),(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row"},children:[(0,o.jsxs)(E.A,{className:"container",sx:{width:"100%",height:"fit-content",minWidth:0,mr:J&&M&&S>u.Gy.medium?3:0},children:[(0,o.jsxs)(E.A,{sx:{height:"40px",pl:1,pb:3},className:"react-code-size-details-banner",children:[(0,o.jsx)(nR,{className:"react-code-size-details-banner"}),(0,o.jsx)(ep.E,{copilotInfo:f,className:"react-code-size-details-banner",view:e?"blame":"preview"})]}),(0,o.jsx)(E.A,{className:"react-blob-view-header-sticky",sx:b,id:eB.Us,ref:g,children:(0,o.jsx)(rc,{currentStickyLines:R,focusedSearchResult:q,openPanel:J,searchingText:H,searchResults:G,searchTerm:n,setFocusedSearchResult:K,setOpenPanel:k,setSearchTerm:r,showTree:c,stickyHeaderRef:g,treeToggleElement:d,validCodeNav:p,copilotInfo:f,colorizedLines:t.colorizedLines})}),(0,o.jsx)(E.A,{sx:{border:"1px solid",borderTop:"none",borderColor:"border.default",borderRadius:"0px 0px 6px 6px",minWidth:"273px"},children:(0,o.jsx)(tZ,{blobLinesHandle:C,setOpenPanel:k,validCodeNav:p,codeNavInfo:M,onCodeNavTokenSelected:z,onLineStickOrUnstick:O,searchResults:G,setSearchTerm:r,focusedSearchResult:q})})]}),J&&M?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(E.A,{sx:{pb:"33px"}}),(0,o.jsx)(rT,{stickySx:b,stickyHeaderRef:g,openPanel:J,isCodeNavLoading:$,codeNavInfo:M,setOpenPanel:k,showCodeNavWithSymbol:P,searchingText:H,setSearchingText:W,searchTerm:n,searchResults:G,searchStatus:U,setSearchResults:Y,setSearchTerm:r,setFocusedSearchResult:K,autoFocusSearch:w})]}):null]})]})})})}function rD({codeNavInfo:e}){let{sendRepoKeyDownEvent:t}=(0,F.T)(),{findSymbolShortcut:n}=(0,w.wk)(),{setOpenPanel:r}=(0,j.Ak)();return 0===e.symbols.length?null:(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:n.hotkey,onButtonClick:()=>{r("codeNav"),(0,V.DE)(),t("FIND_SYMBOL")}})}try{rO.displayName||(rO.displayName="BlobViewContent")}catch{}try{rD.displayName||(rD.displayName="FindSymbolShortcut")}catch{}var rF=n(6709),r$=n(93224),rM=n(6869);let rP={Flash:"BlameBanners-module__Flash--uicsZ",Box:"BlameBanners-module__Box--m11g9"};function rz(){let e=(0,d.B)(),t="blame"in e?e.blame:void 0;return(0,o.jsxs)(ey.k,{blame:t,children:[(0,o.jsx)(rH,{}),(0,o.jsx)(rU,{})]})}function rH(){let e=(0,a.t)(),t=(0,v.eu)().refInfo.name,n=(0,ey.O)(),[r,i]=m.useState(!0);if(!n)return null;let l=n?.ignoreRevs,s=n?.errorType;return s?(0,o.jsx)(o.Fragment,{children:r&&(0,o.jsxs)(rM.A,{variant:"warning",className:rP.Flash,children:[(0,o.jsx)(U.A,{icon:P.InfoIcon}),(0,o.jsx)(rW,{blameErrorType:s,renderIgnoreRefsLink:()=>(0,o.jsx)(e6.N,{to:(0,ea.nD_)({repo:e.name,owner:e.ownerLogin,commitish:t,filePath:l.path}),children:l.path})}),(0,o.jsx)("div",{onClick:()=>i(!1),className:rP.Box,children:(0,o.jsx)(U.A,{icon:P.XIcon})})]})}):null}function rW({blameErrorType:e,renderIgnoreRefsLink:t}){switch(e){case"invalid_ignore_revs":return(0,o.jsxs)("span",{children:["Your ",t()," file is invalid."]});case"ignore_revs_too_big":return(0,o.jsxs)("span",{children:["Your ",t()," file is too large."]});case"symlink_disallowed":return(0,o.jsx)("span",{children:"Symlinks are not supported."});case"blame_timeout":return(0,o.jsx)("span",{children:"Your blame took too long to compute."});default:ez(e)}}function rU(){let e=(0,a.t)(),t=(0,v.eu)().refInfo.name,n=(0,ey.O)()?.ignoreRevs,[r,i]=m.useState(!0);if(!n?.present)return null;let l=(0,o.jsx)(e6.N,{to:(0,ea.nD_)({repo:e.name,owner:e.ownerLogin,commitish:t,filePath:n.path}),children:n.path});return(0,o.jsx)(o.Fragment,{children:r&&(0,o.jsxs)(rM.A,{className:rP.Flash,children:[(0,o.jsx)(U.A,{icon:P.InfoIcon}),n.timedOut?(0,o.jsxs)("span",{children:["Failed to ignore revisions in ",l,"."]}):(0,o.jsxs)("span",{children:["Ignoring revisions in ",l,"."]}),(0,o.jsx)("div",{onClick:()=>i(!1),className:rP.Box,children:(0,o.jsx)(U.A,{icon:P.XIcon})})]})})}try{rz.displayName||(rz.displayName="BlameBanners")}catch{}try{rH.displayName||(rH.displayName="BlameErrorBanner")}catch{}try{rW.displayName||(rW.displayName="BlameErrorText")}catch{}try{rU.displayName||(rU.displayName="IgnoreRevsBanner")}catch{}var rV=n(56236);let rG={Flash:"BlobLowerBanners-module__Flash--idYKd"};var rY=n(96464);let rq={Flash:"DiscussionTemplateBanner-module__Flash--SIEaS"};function rK({errors:e}){if(!e||0===e.length)return null;let t=[];return 1===e.length?t.push("Learn more about this error."):e.map((e,n)=>{t.push(`Learn more about error ${n+1}.`)}),(0,o.jsxs)(rM.A,{variant:"danger",className:rq.Flash,children:[(0,o.jsxs)("p",{children:[(0,o.jsx)(U.A,{icon:P.AlertIcon}),(0,o.jsxs)("strong",{children:["There ",1===e.length?"is a problem":"are some problems"," with this template"]})]}),e.map((e,n)=>(0,o.jsxs)("p",{children:[(0,o.jsx)(e3.JR,{html:e.message}),". ",(0,o.jsx)(er.A,{href:e.link,target:"_blank",children:t[n]})]},`error-${n}`))]})}try{rK.displayName||(rK.displayName="DiscussionTemplateBanner")}catch{}var rQ=n(27851),rX=n(34700),rZ=n(95726);let rJ={Label:"IssueTemplateBanner-module__Label--GTasc",Banner:"IssueTemplateBanner-module__Banner--kvXxt"};function r0(e){let t,{issueTemplate:n,showIssueFormWarning:r,isValidLegacyIssueTemplate:i,helpUrl:l,isLoggedIn:a}=e;if(!a||!n&&!i&&!r)return null;let s=(0,rQ.G7)("lifecycle_label_name_updates"),c=null,d="Info",u=[];return n?!1===n.valid?(t="critical",d="Critical",n.errors&&(1===n.errors.length?u.push("Learn more about this error."):n.errors.map((e,t)=>{u.push(`Learn more about error ${t+1}.`)})),c=(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)("p",{children:[(0,o.jsx)(U.A,{icon:P.AlertIcon}),(0,o.jsxs)("strong",{children:["There ",n.errors?.length===1?"is a problem":"are some problems"," with this template"]})]}),n.errors?.map((e,t)=>(0,o.jsxs)("p",{children:[(0,o.jsx)(e3.JR,{html:e.message}),". ",(0,o.jsx)(er.A,{href:e.link,target:"_blank",children:u[t]})]},`error-${t}`))]})):c=n.structured?(0,o.jsxs)(o.Fragment,{children:[s?(0,o.jsx)(rX.X,{className:"mr-2"}):(0,o.jsx)(ni.A,{variant:"success",className:rJ.Label,children:"Beta"}),"This file is used as an Issue Form template."," ",(0,o.jsx)("a",{href:"https://github.com/orgs/community/discussions/categories/projects-and-issues",children:"Give Feedback."})]}):"This file is used as a markdown issue template.":r&&(c="Issue form templates are not supported on private repositories.",t="warning",d="Warning"),(0,o.jsxs)(o.Fragment,{children:[(n||r)&&(0,o.jsx)(rZ.l,{title:d,variant:t,className:rJ.Banner,children:c}),i&&(0,o.jsx)(r1,{helpUrl:l})]})}function r1({helpUrl:e}){return(0,o.jsxs)(rZ.l,{title:"Warning",variant:"warning",className:rJ.Banner,children:["You are using an old version of issue templates. Please update to the new issue template workflow."," ",(0,o.jsx)(er.A,{href:`${e}/articles/about-issue-and-pull-request-templates`,target:"_blank",inline:!0,children:"Learn more about issue templates."})]})}try{r0.displayName||(r0.displayName="IssueTemplateBanner")}catch{}try{r1.displayName||(r1.displayName="LegacyIssueTemplateBanner")}catch{}function r2(){let e=(0,a.t)(),t=(0,s.i)(),{refInfo:n,path:r}=(0,v.eu)(),{helpUrl:i}=(0,v.sq)(),{csvError:l,isCodeownersFile:c,publishBannersInfo:{showPublishActionBanner:d,releasePath:u,dismissActionNoticePath:h},discussionTemplate:p,issueTemplate:f,showIssueFormWarning:x,isValidLegacyIssueTemplate:y}=(0,eg.A)(),[g,b]=(0,m.useState)([]),[j,w]=(0,m.useState)(rY.bN.LOADING),N=(0,m.useRef)(0);return(0,m.useEffect)(()=>{if(!e.currentUserCanPush)return;N.current++;let t=async()=>{try{let t=N.current,i=await (0,rY.kz)(e,n,r);if(t<N.current)return;if(i.ok){let e=await i.json();b(e.map(e=>(0,rY.Ke)(e))),w(rY.bN.VALIDATED)}else w(rY.bN.ERROR)}catch{w(rY.bN.ERROR)}};c&&t()},[c,e,n,r]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(rV.A,{showPublishActionBanner:d,releasePath:u,dismissActionNoticePath:h}),(0,o.jsx)(r0,{issueTemplate:f,showIssueFormWarning:x,isValidLegacyIssueTemplate:y,isLoggedIn:!!t,helpUrl:i}),p?.errors&&p.errors.length>0&&(0,o.jsx)(rK,{...p}),(0,o.jsx)(r6,{}),c&&e.currentUserCanPush&&(0,o.jsx)(eK.Provider,{value:g,children:(0,o.jsx)(rY.$4,{errors:g,state:j})}),l&&(0,o.jsx)(r3,{csvError:l})]})}function r6(){let{truncated:e,large:t,image:n,renderedFileInfo:r,rawBlobUrl:i}=(0,eg.A)();return!e||t||n||r?null:(0,o.jsxs)(rM.A,{className:rG.Flash,children:["This file has been truncated, but you can"," ",(0,o.jsx)(er.A,{inline:!0,href:i,children:"view the full file"}),"."]})}function r3({csvError:e}){return(0,o.jsx)(rM.A,{variant:"warning",className:rG.Flash,children:(0,o.jsx)(e3.vb,{html:e})})}try{r2.displayName||(r2.displayName="BlobLowerBanners")}catch{}try{r6.displayName||(r6.displayName="TruncatedBanner")}catch{}try{r3.displayName||(r3.displayName="CSVErrorBanner")}catch{}let r5={Box:"BlobLicenseBanner-module__Box--F040L",Box_1:"BlobLicenseBanner-module__Box_1--NN6Gf",Box_2:"BlobLicenseBanner-module__Box_2--hiAjc",Box_3:"BlobLicenseBanner-module__Box_3--pQEZh",Box_4:"BlobLicenseBanner-module__Box_4--yVICK",Box_5:"BlobLicenseBanner-module__Box_5--cMmmH",VerifiedHTMLBox:"BlobLicenseBanner-module__VerifiedHTMLBox--qII3b",Box_6:"BlobLicenseBanner-module__Box_6--H28JJ",Box_7:"BlobLicenseBanner-module__Box_7--sz0ej",Box_8:"BlobLicenseBanner-module__Box_8--cq6OB",Box_9:"BlobLicenseBanner-module__Box_9--UXpn4",Octicon:"BlobLicenseBanner-module__Octicon--oXn77",Box_10:"BlobLicenseBanner-module__Box_10--qbVGR"};function r8(){let{license:e}=(0,nT.Y_)(),t=(0,a.t)(),{helpUrl:n}=(0,v.sq)(),r={permissions:{icon:P.CheckIcon,color:"success.fg"},limitations:{icon:P.XIcon,color:"danger.fg"},conditions:{icon:P.InfoIcon,color:"accent.fg"}};return e?(0,o.jsxs)("div",{className:r5.Box,children:[(0,o.jsxs)("div",{className:(0,t8.$)("blob-license-banner-outer",r5.Box_1),children:[(0,o.jsxs)("div",{className:r5.Box_2,children:[(0,o.jsxs)("div",{className:r5.Box_3,children:[(0,o.jsx)(U.A,{icon:P.LawIcon,size:32}),(0,o.jsxs)("div",{className:r5.Box_4,children:[(0,o.jsxs)("div",{className:r5.Box_5,children:[`${t.ownerLogin}/${t.name} is licensed under`," ",e.name.toLowerCase().startsWith("the ")?"":" the"]}),(0,o.jsx)("h3",{children:e.name})]})]}),(0,o.jsx)(e3.vb,{html:e.description,className:r5.VerifiedHTMLBox})]}),(0,o.jsx)("div",{className:r5.Box_6,children:Object.keys(e.rules).map((t,n)=>(0,o.jsxs)("div",{className:r5.Box_7,children:[(0,o.jsx)("h5",{className:r5.Box_8,children:t.charAt(0).toUpperCase()+t.substring(1)}),e.rules[t].map(e=>(0,o.jsxs)("div",{className:r5.Box_9,children:[(0,o.jsx)(U.A,{icon:r[t].icon,size:13,sx:{color:r[t].color},className:r5.Octicon}),e.label]},e.tag))]},n))})]}),(0,o.jsxs)("div",{className:r5.Box_10,children:["This is not legal advice.\xa0",(0,o.jsx)(er.A,{inline:!0,href:`${n}/articles/licensing-a-repository/#disclaimer`,children:"Learn more about repository licenses"})]})]}):null}try{r8.displayName||(r8.displayName="BlobLicenseBanner")}catch{}let r4={Flash:"InvalidCitationWarning-module__Flash--k8ISS"};function r7({citationHelpUrl:e}){return(0,o.jsxs)(rM.A,{variant:"warning",className:r4.Flash,children:["Your ",(0,o.jsx)("strong",{children:"CITATION.cff"}),"file cannot be parsed. Make sure the formatting is correct."," ",(0,o.jsx)(er.A,{inline:!0,href:e,children:"Learn more about CITATION files."})]})}try{r7.displayName||(r7.displayName="InvalidCitationWarning")}catch{}let r9={Flash:"OverridingGlobalFundingFileWarning-module__Flash--nU7J4"};function ie({globalPreferredFundingPath:e}){return(0,o.jsxs)(rM.A,{className:r9.Flash,children:["This file is overriding the organization-wide ",(0,o.jsx)("code",{children:"FUNDING.yml"}),"file. Removing ",(0,o.jsx)("code",{children:"FUNDING.yml"}),"in this repository will use the organization default.",(0,o.jsxs)(er.A,{inline:!0,href:e??void 0,children:[" ","View organization funding file."]})]})}try{ie.displayName||(ie.displayName="OverridingGlobalFundingFileWarning")}catch{}function it(){let{topBannersInfo:{overridingGlobalFundingFile:e,globalPreferredFundingPath:t,showInvalidCitationWarning:n,citationHelpUrl:r}}=(0,eg.A)(),{showLicenseMeta:i}=(0,nT.Y_)();return(0,o.jsxs)(o.Fragment,{children:[i&&(0,o.jsx)(r8,{}),n&&(0,o.jsx)(r7,{citationHelpUrl:r}),(0,o.jsx)(rF.I6,{}),e&&(0,o.jsx)(ie,{globalPreferredFundingPath:t})]})}try{it.displayName||(it.displayName="BlobMidBanners")}catch{}let ir={Box:"BlobTopBanners-module__Box--g_bGk"};var ii=n(32226);let io={Box:"DependabotConfigurationBanner-module__Box--vpfzX",PointerBox:"DependabotConfigurationBanner-module__PointerBox--L1ZfE",Text:"DependabotConfigurationBanner-module__Text--oG7Go",Text_1:"DependabotConfigurationBanner-module__Text_1--P_iN0",Box_1:"DependabotConfigurationBanner-module__Box_1--EeKqW",Button:"DependabotConfigurationBanner-module__Button--N3lHP"};function il(){let{defaultBranch:e}=(0,a.t)(),{refInfo:{name:t}}=(0,v.eu)(),{dependabotInfo:{showConfigurationBanner:n}}=(0,eg.A)();return n?e===t?(0,o.jsx)(ia,{}):(0,o.jsx)(is,{}):null}function ia(){let{dependabotInfo:{configFilePath:e,networkDependabotPath:t,dismissConfigurationNoticePath:n,configurationNoticeDismissed:r}}=(0,eg.A)(),i=(0,m.useCallback)(()=>(0,eE.DI)(n,{method:"POST"}),[n]);return r?null:(0,o.jsx)("div",{className:io.Box,children:(0,o.jsxs)(ii.A,{caret:"top",className:io.PointerBox,children:[(0,o.jsx)("h5",{className:io.Text,children:"Dependabot"}),(0,o.jsx)("p",{className:io.Text_1,children:"Dependabot creates pull requests to keep your dependencies secure and up-to-date."}),(0,o.jsxs)("p",{className:io.Text_1,children:["You can opt out at any time by removing the ",(0,o.jsx)("code",{children:e})," config file."]}),(0,o.jsxs)("div",{className:io.Box_1,children:[(0,o.jsx)(ny.Q,{as:"a",href:t,children:"View update status"}),(0,o.jsx)(ny.Q,{variant:"invisible",onClick:i,className:io.Button,children:"Dismiss"})]})]})})}function is(){let{defaultBranch:e,name:t,ownerLogin:n}=(0,a.t)(),{path:r}=(0,v.eu)(),i=(0,ea.nD_)({owner:n,repo:t,commitish:e,filePath:r});return(0,o.jsxs)(rM.A,{variant:"warning",children:[(0,o.jsxs)("h5",{children:[(0,o.jsx)(P.AlertIcon,{}),"Cannot configure Dependabot from this branch"]}),(0,o.jsxs)("p",{children:["To configure Dependabot, you must use"," ",(0,o.jsx)(er.A,{inline:!0,href:i,children:"this repository's default branch"})]})]})}try{il.displayName||(il.displayName="DependabotConfigurationBanner")}catch{}try{ia.displayName||(ia.displayName="DefaultBranchDependabotConfigurationBanner")}catch{}try{is.displayName||(is.displayName="DirectionsForNonDefaultBranch")}catch{}var ic=n(53904);let id={Box:"OrgOnboardingTip-module__Box--Pk3N1",Box_1:"OrgOnboardingTip-module__Box_1--f_CUO",Box_2:"OrgOnboardingTip-module__Box_2--ownDi",Breadcrumbs_Item:"OrgOnboardingTip-module__Breadcrumbs_Item--Fr7wA",Box_3:"OrgOnboardingTip-module__Box_3--JX0Hy",VerifiedHTMLBox:"OrgOnboardingTip-module__VerifiedHTMLBox--JF5OH",Box_4:"OrgOnboardingTip-module__Box_4--geNrU",Box_5:"OrgOnboardingTip-module__Box_5--_7M4f",Link:"OrgOnboardingTip-module__Link--VIyoj",Text:"OrgOnboardingTip-module__Text--Lzw1f",Box_6:"OrgOnboardingTip-module__Box_6--Wo1hi",Text_1:"OrgOnboardingTip-module__Text_1--_Ahmq",Box_7:"OrgOnboardingTip-module__Box_7--taCFv",Link_1:"OrgOnboardingTip-module__Link_1--Xsk1N",Octicon:"OrgOnboardingTip-module__Octicon--uIRNM"};function iu({children:e,mediaUrl:t,mediaPreviewSrc:n,iconSvg:r,taskTitle:i,taskPath:l,org:a}){return(0,o.jsxs)("section",{className:id.Box,children:[(0,o.jsxs)("div",{className:id.Box_1,children:[(0,o.jsx)(ih,{iconSvg:r}),(0,o.jsxs)("div",{className:id.Box_2,children:[(0,o.jsxs)(ic.A,{children:[(0,o.jsx)(ic.A.Item,{href:(0,ea.p3r)({owner:a}),children:"Tasks"}),(0,o.jsx)(ic.A.Item,{href:l,className:id.Breadcrumbs_Item,children:i})]}),e]})]}),(0,o.jsx)(ip,{mediaPreviewSrc:n,mediaUrl:t})]})}function ih({iconSvg:e}){return(0,o.jsxs)("div",{className:id.Box_3,children:[(0,o.jsx)(e3.vb,{html:e,className:id.VerifiedHTMLBox}),(0,o.jsx)(im,{size:6,color:"#6c84e9",bottom:-7,left:-7}),(0,o.jsx)(im,{size:4,color:"#9e7bff",top:-4,right:4}),(0,o.jsx)(im,{size:6,color:"#6c84e9",top:-7,right:-8})]})}function im({size:e,color:t,left:n,right:r,top:i,bottom:l}){return(0,o.jsx)(E.A,{sx:{width:e,height:e,left:`${n}px`,right:`${r}px`,top:`${i}px`,bottom:`${l}px`,backgroundColor:t},className:id.Box_4})}function ip({mediaUrl:e,mediaPreviewSrc:t}){return!((ex.cg?.innerWidth??0)<u.Gy.xlarge)&&e&&t?(0,o.jsx)("div",{className:(0,t8.$)("org-onboarding-tip-media",id.Box_5),children:(0,o.jsx)(er.A,{href:e,className:id.Link,children:(0,o.jsx)("img",{src:t,alt:"Guidance",loading:"lazy",style:{width:"50%",height:"50%"}})})}):null}function ix({repo:e,owner:t}){return(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:id.Text,children:"Auto-assign new issue with GitHub Actions"}),(0,o.jsxs)("div",{className:id.Box_6,children:[(0,o.jsxs)("p",{children:["The ",(0,o.jsx)("span",{className:id.Text_1,children:"auto-assign.yml"})," file below lives inside your"," ",(0,o.jsx)("span",{className:id.Text_1,children:"demo-repository"})," and defines when and how it\u2019s automatically triggered. This"," ",(0,o.jsx)("a",{href:"https://github.com/marketplace/actions/auto-assign-issues-prs",target:"_blank",rel:"noreferrer",children:"\u201CAuto Assign\u201D workflow"})," ","happens to add reviewers and assignees to issues and pull requests when they\u2019re opened in this repository."]}),(0,o.jsxs)("p",{children:["You can see the results of this workflow in any"," ",(0,o.jsx)("a",{href:(0,ea.SHX)({owner:t,repo:e,action:"issues"}),children:"issue"})," or"," ",(0,o.jsx)("a",{href:(0,ea.SHX)({owner:t,repo:e,action:"pulls"}),children:"pull request"})," that you create in this repository, as it\u2019ll assign them to the specified members. And you can see a log of any workflows you run in"," ",(0,o.jsx)("a",{href:(0,ea.SHX)({owner:t,repo:e,action:"actions"}),children:"your repository\u2019s \u201CActions\u201D tab."})]}),(0,o.jsxs)("div",{className:id.Box_7,children:[(0,o.jsx)(t5.z,{variant:"primary",href:(0,ea.SHX)({owner:t,repo:e,action:"issues/new"}),sx:t0.E,children:"Create new issue to see results"}),(0,o.jsxs)(er.A,{href:"https://docs.github.com/actions",target:"_blank",className:id.Link_1,children:[(0,o.jsx)(U.A,{icon:P.FileIcon,className:id.Octicon}),"Learn how automation works on GitHub"]})]})]})]})}try{iu.displayName||(iu.displayName="OrgOnboardingTip")}catch{}try{ih.displayName||(ih.displayName="SuggestIcon")}catch{}try{im.displayName||(im.displayName="Bubble")}catch{}try{ip.displayName||(ip.displayName="Media")}catch{}try{ix.displayName||(ix.displayName="ActionsOnboardingPrompt")}catch{}let iy={Flash:"PlanSupportBanner-module__Flash--IuApX",Box:"PlanSupportBanner-module__Box--ly6Nh",Text:"PlanSupportBanner-module__Text--rvwbA"};function ig({feature:e,featureName:t,repoIsFork:n,repoOwnedByCurrentUser:r,requestFullPath:i,showFreeOrgGatedFeatureMessage:l,showPlanSupportBanner:a,upgradeDataAttributes:s,upgradePath:c}){let d={};if(s)for(let e in s)d[`data-${e}`]=s[e];return a?(0,o.jsx)(rM.A,{variant:"warning",className:iy.Flash,children:r?n?`This repository is a fork, and inherits the features of the parent repository. Contact the owner of the root repository to enable ${t||"this feature"}`:(0,o.jsxs)("div",{className:iy.Box,children:[(0,o.jsx)("span",{className:iy.Text,children:`Upgrade to GitHub Pro or make this repository public to enable ${t||"this feature"}.`}),(0,o.jsx)(ib,{dataAttributes:d,individual:!0,requestFullPath:i,feature:e,upgradePath:c})]}):l?(0,o.jsxs)("div",{className:iy.Box,children:[(0,o.jsx)("span",{className:iy.Text,children:`Upgrade to GitHub Team or make this repository public to enable ${t||"this feature"}.`}),(0,o.jsx)(ib,{dataAttributes:d,individual:!1,requestFullPath:i,feature:e,upgradePath:c})]}):`Contact the owner of the repository to enable ${t||"this feature"}.`}):null}function ib({dataAttributes:e,individual:t,requestFullPath:n,feature:r,upgradePath:i}){return(0,o.jsx)(ny.Q,{...e,"data-ga-click":`Change ${t?"individual":"organization"}, click to upgrade, ref_page:${n};ref_cta:Upgrade now;ref_loc:${r};location:${r};text:Upgrade now`,onClick:()=>{location.href=i},children:"Upgrade now"})}try{ig.displayName||(ig.displayName="PlanSupportBanner")}catch{}try{ib.displayName||(ib.displayName="UpgradeButton")}catch{}function iv(){let{planSupportInfo:e,topBannersInfo:t}=(0,eg.A)(),{actionsOnboardingTip:n}=t,r=(0,a.t)();return(0,o.jsxs)("div",{className:ir.Box,children:[(0,o.jsx)(ig,{...e,feature:"codeowners",featureName:"CODEOWNERS"}),(0,o.jsx)(il,{}),n&&(0,o.jsx)(iu,{iconSvg:n.iconSvg,mediaPreviewSrc:n.mediaPreviewSrc,mediaUrl:n.mediaUrl,taskTitle:n.taskTitle,taskPath:n.taskPath,org:n.orgName,children:(0,o.jsx)(ix,{owner:r.ownerLogin,repo:r.name})})]})}try{iv.displayName||(iv.displayName="BlobTopBanners")}catch{}function ij({payload:e}){return(0,A.Hf)(e)?(0,o.jsx)(iw,{tree:e.tree}):(0,A.mM)(e)?(0,o.jsx)(iN,{blob:e.blob}):null}function iw({tree:e}){return(0,o.jsxs)(r$.X,{payload:e,children:[(0,o.jsx)(rF.I6,{}),(0,o.jsx)(i_,{}),(0,o.jsx)(ik,{})]})}function iN({blob:e}){return(0,o.jsxs)(eg.s,{blob:e,children:[(0,o.jsx)(iv,{}),(0,o.jsx)(it,{}),(0,o.jsx)(r2,{}),(0,o.jsx)(rz,{}),(0,o.jsx)(ik,{})]})}function i_(){let{items:e,totalCount:t}=(0,r$.d)(),n=t-e.length;return n>0?(0,o.jsxs)(rM.A,{variant:"warning","data-testid":"repo-truncation-warning",sx:{mt:3},children:["Sorry, we had to truncate this directory to ",e.length.toLocaleString()," files. ",n.toLocaleString()," ",1===n?"entry was":"entries were"," omitted from the list. Latest commit info may be omitted."]}):null}function ik(){let e=(0,g.x7)(),t="";for(let n of e)t+=n.message;return(0,e_.kY)(t),(0,o.jsx)(o.Fragment,{children:e.map((e,t)=>(0,o.jsx)(rM.A,{variant:e.variant,sx:{mt:3},children:e.message},t))})}try{ij.displayName||(ij.displayName="CodeViewBanners")}catch{}try{iw.displayName||(iw.displayName="TreeBanners")}catch{}try{iN.displayName||(iN.displayName="BlobBanners")}catch{}try{i_.displayName||(i_.displayName="TruncatedTreeBanner")}catch{}try{ik.displayName||(ik.displayName="CodeViewContextBanners")}catch{}var iC=n(91215),iA=n(94538),iS=n(68575),iB=n(8579),iI=n(69851);function iL({diff:e,index:t}){let[n,r]=(0,m.useState)(!1),[i,l]=(0,m.useState)(!1),[a,s]=(0,m.useState)(!1),c=[];for(let t=0;t<5;t++)e.deletions>t?c.push("deletion"):c.push("neutral");return e.diffHTML?(0,o.jsx)("div",{id:"readme",className:"readme prose-diff html-blob blob",children:(0,o.jsx)(e3.vb,{html:e.diffHTML,className:"markdown-body container-lg"})}):(0,o.jsxs)(E.A,{sx:{border:"1px solid",borderColor:"border.default",borderRadius:"6px",mt:3},id:`diff-entry-${t}`,children:[(0,o.jsxs)(E.A,{sx:{backgroundColor:"canvas.subtle",borderBottom:"1px solid",borderColor:"border.default",display:"flex",py:1,px:2,alignItems:"center",gap:2},children:[(0,o.jsx)(z.K,{"aria-label":n?"Expand diff":"Collapse diff",icon:n?P.ChevronRightIcon:P.ChevronDownIcon,size:"small",tooltipDirection:"s",variant:"invisible",onClick:()=>r(!n)}),(0,o.jsx)(W.A,{sx:{color:"fg.muted"},children:e.deletions}),(0,o.jsx)(iC.uP,{squares:c}),(0,o.jsx)(er.A,{sx:{color:"fg.default",cursor:"pointer"},underline:!1,href:`#diff-entry-${t}`,children:e.path}),(0,o.jsx)(iB.T,{textToCopy:e.path,ariaLabel:"Copy path to clipboard"})]}),n?null:(0,o.jsx)(E.A,{sx:{px:3*!i,py:4*!i,position:"relative"},tabIndex:-1,children:i?(0,o.jsx)(iI.x,{"data-testid":"delete-diff-fragment",src:e.loadDiffPath,onLoad:()=>s(!0),children:!a&&(0,o.jsx)(E.A,{sx:{display:"flex",alignItems:"center",justifyContent:"center",height:"137px"},children:(0,o.jsx)(ry.A,{})})}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(iT,{}),(0,o.jsxs)(E.A,{sx:{position:"absolute",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",top:0,left:0,height:"100%",width:"100%"},children:[(0,o.jsx)(er.A,{onClick:()=>l(!0),sx:{cursor:"pointer"},children:"Load diff"}),"This file was deleted."]})]})})]})}function iT(){return(0,o.jsx)(E.A,{as:"svg","aria-hidden":"true",className:"width-full",viewBox:"0 0 340 84",xmlns:"http://www.w3.org/2000/svg",sx:{height:"84",maxWidth:"340px"},children:(0,o.jsx)(E.A,{as:"path",className:"js-diff-placeholder",clipPath:"url(#diff-placeholder)",d:"M0 0h340v84H0z",fillRule:"evenodd",sx:{fill:"canvas.subtle"}})})}try{iL.displayName||(iL.displayName="DiffEntry")}catch{}try{iT.displayName||(iT.displayName="DiffPlaceholderSvg")}catch{}function iE({deleteInfo:e,webCommitInfo:t}){let{path:n}=(0,v.eu)(),{helpUrl:r}=(0,v.sq)(),i=(0,a.t)();return t.shouldFork||t.shouldUpdate||t.lockedOnMigration?(0,o.jsx)(iS.T,{binary:!1,helpUrl:r,webCommitInfo:t}):(0,o.jsxs)(E.A,{sx:{maxWidth:"1280px",mx:"auto"},children:[(0,o.jsx)(R.A,{as:"h1",className:"sr-only",children:`Deleting ${e.isBlob?"":"directory "}${i.name}/${n}. Commit changes to save.`}),(0,o.jsx)(iC.nq,{}),t.forkedRepo&&(0,o.jsx)(iA.r,{forkName:t.forkedRepo.name,forkOwner:t.forkedRepo.owner}),e.truncated&&(0,o.jsx)(rM.A,{variant:"warning",className:"mb-2",children:"The diff you're trying to view is too large. We only load the first 1000 changed files."}),e.diffs.map((e,t)=>(0,o.jsx)(iL,{diff:e,index:t},t))]})}try{iE.displayName||(iE.displayName="DeleteViewContent")}catch{}var iR=n(50436),iO=n(53861),iD=n(70179),iF=n(71748),i$=n(50144);function iM({openPanel:e,readme:t,setOpenPanel:n,stickyHeaderHeight:r}){let{displayName:i,errorMessage:l,richText:s,headerInfo:c,timedOut:d}=t,{toc:u}=c||{},h=(0,a.t)(),{refInfo:m,path:p}=(0,v.eu)(),f=p&&"/"!==p?`${p}/${i}`:i;return(0,o.jsxs)(E.A,{sx:{minWidth:0,display:"flex",flexDirection:"row",justifyContent:"space-between",gap:3},children:[(0,o.jsxs)(E.A,{id:"readme",sx:{borderColor:"border.default",borderWidth:1,borderStyle:"solid",borderRadius:2,width:"toc"===e?"65%":"100%"},children:[(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"row",alignItems:"center",gap:2,pr:2,pl:3,py:2,borderBottom:"1px solid",borderColor:"border.default"},children:[(0,o.jsx)(R.A,{as:"h2",sx:{fontSize:1,flexGrow:1},children:(0,o.jsx)(er.A,{sx:{color:"fg.default","&:hover":{color:"accent.fg"}},href:"#readme",children:i})}),m.canEdit&&(0,o.jsx)(i$.l,{editPath:(0,ea.IO9)({repo:h,commitish:m.name,action:"edit",path:f}),editTooltip:"Edit README"}),(0,o.jsx)(rl,{toc:u,openPanel:e,setOpenPanel:n,isDirectoryReadme:!0})]}),(0,o.jsx)(iF.e,{richText:s,errorMessage:l,path:f,stickyHeaderHeight:r,timedOut:d})]}),"toc"===e&&(0,o.jsx)(rL.Z,{sx:{height:"fit-content",width:"35%"},children:(0,o.jsx)(rp.A,{onClose:()=>{n(void 0)},toc:u})})]})}try{iM.displayName||(iM.displayName="DirectoryReadmePreview")}catch{}function iP({showTree:e,treeToggleElement:t}){let n=(0,m.useRef)(null),r=eI(n),i=eL();return(0,o.jsx)(E.A,{className:"react-blob-view-header-sticky",sx:{...i,zIndex:+!!r},ref:n,children:(0,o.jsx)(E.A,{sx:{display:"flex",flexDirection:"column",backgroundColor:"canvas.subtle",borderBottom:r?"1px solid var(--borderColor-default, var(--color-border-default))":"none",overflow:"hidden"},children:(0,o.jsx)(nN,{isStickied:r,showTree:e,treeToggleElement:t})})})}try{iP.displayName||(iP.displayName="FolderViewHeader")}catch{}function iz({tree:e,showTree:t,treeToggleElement:n}){let{openPanel:r,setOpenPanel:i}=(0,j.Ak)();return(0,o.jsxs)(r$.X,{payload:e,children:[(0,o.jsx)(em.D,{}),e.showBranchInfobar&&(0,o.jsx)(iD.t,{fallback:(0,o.jsx)(iR.o,{}),children:(0,o.jsx)(iR.k,{})}),(0,o.jsx)(iP,{showTree:t,treeToggleElement:n}),(0,o.jsxs)(E.A,{sx:{display:"flex",flexDirection:"column",gap:3},children:[(0,o.jsx)(iO.t,{}),e.readme&&(0,o.jsx)(iM,{openPanel:r,setOpenPanel:i,readme:e.readme,stickyHeaderHeight:50})]})]})}try{iz.displayName||(iz.displayName="FileTreeViewContent")}catch{}var iH=n(3147);let iW={SplitPageLayout_Content:"CodeView-module__SplitPageLayout_Content--qxR1C"},iU=(0,m.lazy)(()=>Promise.all([n.e("primer-react"),n.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),n.e("vendors-node_modules_tanstack_query-core_build_modern_queryObserver_js-node_modules_tanstack_-defd52"),n.e("vendors-node_modules_lit-html_lit-html_js"),n.e("vendors-node_modules_codemirror_autocomplete_dist_index_js-node_modules_codemirror_search_dis-aafe81"),n.e("vendors-node_modules_diff_lib_index_mjs"),n.e("vendors-node_modules_buffer_index_js"),n.e("vendors-node_modules_codemirror_lib_codemirror_js"),n.e("vendors-node_modules_js-yaml_dist_js-yaml_mjs"),n.e("vendors-node_modules_github_text-expander-element_dist_index_js"),n.e("vendors-node_modules_jsonc-parser_lib_esm_main_js"),n.e("vendors-node_modules_cronstrue_dist_cronstrue_js"),n.e("vendors-node_modules_codemirror_lint_dist_index_js"),n.e("vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_tanstack_react-d8f5f1"),n.e("ui_packages_copilot-chat_utils_copilot-chat-helpers_ts"),n.e("ui_packages_web-commit-dialog_WebCommitDialog_tsx"),n.e("app_assets_modules_github_editor_codemirror-linter-util_ts-app_assets_modules_github_editor_y-89a4a6"),n.e("app_assets_modules_github_editor_yaml-editors_workflow_workflow-rules_ts"),n.e("app_assets_modules_github_editor_yaml-editors_dependabot_dependabot-rules_ts"),n.e("app_assets_modules_react-code-view_components_blob-edit_WebCommitDialog_tsx"),n.e("app_assets_modules_secret-scanning_components_shared_shared_module_css-app_assets_modules_sec-cbff91"),n.e("app_assets_modules_react-code-view_components_blob-edit_BlobEditor_tsx-app_assets_modules_sec-a99ea8")]).then(n.bind(n,55718)));function iV({initialPayload:e}){let t,n=(0,_.P)(e),r=(0,a.t)(),{findFileWorkerPath:i}=(0,v.sq)(),{path:l}=n,s=(0,N.p)(n.refInfo),c=(0,A.di)(n),d=(0,A.mM)(n),p=(0,A.KR)(n),k=(0,A.iS)(n),[S,O]=(0,m.useState)(null),D=(0,nT.U6)(r,s,l,n.error?.httpStatus===404),F=(0,iH.TX)(r,s,l,n.error?.httpStatus===404,d||p?n.blob.rawLines?.length??1e5:1e5),$=m.useRef(null),M=m.useRef(!1),P=m.useRef(!1),z=m.useRef(null),H=m.useRef(null),W="repos-file-tree",U=m.useRef(),[V,G]=(0,m.useState)(""),{toggleFocusedPaneShortcut:Y}=(0,w.wk)();(0,I.s)(!1),(0,m.useEffect)(()=>()=>(0,I.s)(!0),[]),(0,L.L)(!0);let q=m.useMemo(()=>(0,C.aO)(n),[n.path,n.refInfo.currentOid]),K=(0,m.useCallback)(e=>{eR(e,null,null)},[]),{isTreeExpanded:Q,collapseTree:X,expandTree:Z,treeToggleElement:J,treeToggleRef:ee,searchBoxRef:et}=(0,B.qn)(W,U,n.treeExpanded,e_.wQ,K),en=(0,m.useCallback)(()=>{window.innerWidth<u.Gy.large&&X({focus:null})},[X]),[er,ei]=(0,m.useState)(!0);(0,m.useEffect)(()=>{if(!window.location.hash&&window.scrollY>0){let e=document.querySelector("#StickyHeader");e&&(e.style.position="relative",e.scrollIntoView(),e.style.position="sticky")}},[n.path]);let{codeCenterOption:eo}=(0,h.ud)(),el=m.useCallback(()=>{window.innerWidth<u.Gy.large&&Z({focus:"search"})},[Z]);return t=c?n.editInfo.isNewFile?"new":"edit":p?"blame":d?"blob":"tree",(0,o.jsx)(nT.mD,{...D,children:(0,o.jsx)(iH.Zj,{...F,children:(0,o.jsx)(v.qV,{refInfo:s,path:l,action:t,copilotAccessAllowed:n.copilotAccessAllowed??!1,modelsAccessAllowed:n.modelsAccessAllowed??!1,modelsRepoIntegrationEnabled:n.modelsRepoIntegrationEnabled??!1,children:(0,o.jsx)(y.$,{allShortcutsEnabled:n.allShortcutsEnabled,children:(0,o.jsxs)(B.Nq,{children:[(0,o.jsx)(iG,{}),(0,o.jsxs)("div",{children:[(0,o.jsx)(b.Ck,{children:(0,o.jsx)(j.cD,{payload:n,openPanelRef:U,children:(0,o.jsxs)(T.O7,{children:[(0,o.jsx)(E.A,{ref:$,tabIndex:0,sx:{width:["100%","100%","auto"]},children:(0,o.jsx)(B.c2,{id:W,repo:r,path:l,isFilePath:d||c||k,refInfo:s,collapseTree:X,showTree:Q,fileTree:q,onItemSelected:en,processingTime:n.fileTreeProcessingTime,treeToggleElement:J,treeToggleRef:ee,searchBoxRef:et,foldersToFetch:n.foldersToFetch,incompleteFileTree:n.incompleteFileTree,onFindFilesShortcut:el,textAreaId:e_.wQ,findFileWorkerPath:i,headerContent:(0,o.jsxs)(E.A,{sx:{display:"flex",width:"100%",mb:3,alignItems:"center"},children:[Q&&J,(0,o.jsx)(R.A,{as:"h2",sx:{fontSize:2,ml:2},children:"Files"})]})})}),(0,o.jsx)(T.O7.Content,{as:"div",padding:"none",width:eo.enabled?"xlarge":"full",hidden:{narrow:Q},className:iW.SplitPageLayout_Content,children:(0,o.jsx)(E.A,{sx:{marginLeft:"auto",marginRight:"auto",flexDirection:"column",pb:6,maxWidth:"100%",mt:0},ref:O,"data-selector":"repos-split-pane-content",tabIndex:0,children:(0,o.jsx)(eF,{searchTerm:V,setSearchTerm:G,isBlame:p,children:(0,o.jsxs)(g.lG,{children:[(0,o.jsx)(E.A,{sx:{display:c?"none":"inherit"},children:(0,o.jsx)(n0,{payload:n,showTree:Q,treeToggleElement:J,validCodeNav:er,onFindFilesShortcut:el})}),n.error?(0,o.jsx)(f.D,{...n.error}):(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(E.A,{className:"react-code-view-bottom-padding",sx:{mx:3},children:(0,o.jsx)(ij,{payload:n})}),(0,o.jsx)(E.A,{sx:{mx:3},children:(0,A.Hf)(n)?(0,o.jsx)(iz,{tree:n.tree,showTree:Q,treeToggleElement:J}):(0,A.di)(n)?(0,o.jsx)(m.Suspense,{fallback:(0,o.jsx)(eV.f,{}),children:(0,o.jsx)(iU,{collapseTree:X,editInfo:n.editInfo,repo:n.repo,showTree:Q,treeToggleElement:J,webCommitInfo:n.webCommitInfo,copilotInfo:n.copilotInfo,copilotAuthInfo:n.copilotAuthInfo,copilotAccessAllowed:n.copilotAccessAllowed},`${n.path}_${n.editInfo.fileName}_${n.editInfo.isNewFile}`)}):d?(0,o.jsx)(rO,{blame:n.blame,blob:n.blob,symbolsExpanded:n.symbolsExpanded,searchTerm:V,setSearchTerm:G,setValidCodeNav:ei,showTree:Q,treeToggleElement:J,validCodeNav:er,copilotInfo:n.copilotInfo}):k?(0,o.jsx)(iE,{deleteInfo:n.deleteInfo,webCommitInfo:n.webCommitInfo}):null})]})]})})})})]})})}),(0,o.jsx)(eh.t,{}),(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:Y.hotkey,onButtonClick:()=>(function(){let e=document.getElementById(ec),t=document.getElementById(e_.wQ);if(document.activeElement?.id===e_.wQ&&(P.current=!0),S?.contains(document.activeElement)&&!P.current?M.current=!0:$.current?.contains(document.activeElement)&&(M.current=!1),M.current||P.current)if(P.current){let t=z.current||e||S;M.current=!0,P.current=!1,t?.focus()}else{let e=H.current||$.current;z.current=S?.contains(document.activeElement)?document.activeElement:null,M.current=!1,P.current=!1,e?.focus()}else{let e=t||S;H.current=$.current?.contains(document.activeElement)?document.activeElement:null,M.current=!1,e?.focus()}})()})]})]})})})})})}function iG(){let e=(0,k.Z)(),{permalinkShortcut:t}=(0,w.wk)();return e.isCurrentPagePermalink()?(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:t.hotkey,buttonTestLabel:"header-permalink-button",onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{}}):(0,o.jsx)(x._,{buttonFocusId:e_.wQ,buttonHotkey:t.hotkey,buttonTestLabel:"header-permalink-button",onButtonClick:()=>{let t=e.createPermalink();0>window.location.href.indexOf(t)&&(0,S.kd)(t)}})}try{iU.displayName||(iU.displayName="BlobEditor")}catch{}try{iV.displayName||(iV.displayName="CodeView")}catch{}try{iG.displayName||(iG.displayName="PermalinkShortcut")}catch{}(0,i.o)("react-code-view",()=>({App:p,routes:[(0,r.a)({path:"/:owner/:repo/tree/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/blob/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/blame/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/edit/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/new/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/new/:branch/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/tree/delete/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/delete/:branch/:path/*",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo",Component:iV,shouldNavigateOnError:!0}),(0,r.a)({path:"/:owner/:repo/tree/:branch/*",Component:iV,shouldNavigateOnError:!0})]}))},91617:(e,t,n)=>{n.d(t,{L:()=>l});var r=n(96679),i=n(96540),o=n(81675);function l(e,t,n,l,a){let s=(0,i.useRef)(null),c=(0,o.pm)(),d=void 0===r.XC,u=(0,o.Px)(),h=(0,i.useMemo)(()=>{let{isEndLine:r,isStartLine:i,lineNumber:o}=e,s=0,c=1,h=r&&!d?new IntersectionObserver(e=>{for(let{target:t,isIntersecting:n,intersectionRatio:r}of e)if(t){let{currentY:e,currentRatio:i}=function(e,t,n,r,i,o,l,a){let s=n.getBoundingClientRect().y,c=i?o(i):void 0,d=window.innerHeight-n.getBoundingClientRect().bottom>0,u=n.getBoundingClientRect().bottom>0,h=n.getBoundingClientRect().top<150,m=d&&u,p=window.innerHeight-n.getBoundingClientRect().bottom>150&&window.innerHeight>300;for(let n of c||[])n&&m&&(s<e&&m?r>t&&l||a(n,!0):s>e&&l&&(r<t||p&&h&&a(n,!1)));return{currentY:s,currentRatio:r}}(s,c,t,r,o,u,n,l);s=e,c=i}},{root:null,rootMargin:`-${n}px 0px 0px 0px`,threshold:0}):void 0,m=i&&!d?new IntersectionObserver(t=>{for(let{target:n,isIntersecting:r,intersectionRatio:i}of t)if(n){let{currentY:t,currentRatio:o}=function(e,t,n,r,i,o,l){let a=r.getBoundingClientRect().y,s=window.innerHeight-r.getBoundingClientRect().bottom>0,c=r.getBoundingClientRect().bottom>0,d=r.getBoundingClientRect().top<150&&r.getBoundingClientRect().top>-300,u=0===r.getBoundingClientRect().bottom&&0===r.getBoundingClientRect().top&&0===r.getBoundingClientRect().height&&0===r.getBoundingClientRect().width&&0===r.getBoundingClientRect().x&&0===r.getBoundingClientRect().y;return!e.ownedSection||e.ownedSection?.collapsed||(a<=t&&(s&&c||d)&&!u?i>n&&o||d&&l(e,!1):a>t&&o&&(i<n||l(e,!0))),{currentY:a,currentRatio:i}}(e,s,c,n,i,r,l);s=t,c=o}},{root:null,rootMargin:`-${n+(a?20*a:0)}px 0px 0px 0px`,threshold:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1]}):void 0,p=t?i?m:r?h:void 0:void 0;return p&&(p.hasBeenDisconnected=!1,p.hasBeenObserved=!1),p},[e,t,n,u,l,a,d]),m=(0,i.useCallback)(e=>{s.current=e},[]);return(0,i.useEffect)(()=>{let{isStartLine:t,lineNumber:n,ownedSection:r}=e;return t&&s&&r&&h&&!h.hasBeenObserved&&!h.hasBeenDisconnected&&c(r.endLine,{lineNumber:n}),h&&s.current&&!h.hasBeenObserved&&(h.observe(s.current),h.hasBeenObserved=!0),()=>{h&&(h.disconnect(),h.hasBeenDisconnected=!0)}},[h,e]),m}},13317:(e,t,n)=>{n.d(t,{y:()=>s});var r=n(98637),i=n(96540),o=n(68048),l=n(9826),a=n(76087);function s(){let{sendRepoClickEvent:e}=(0,r.T)(),{rawBlobUrl:t}=(0,a.A)(),n=(0,i.useRef)(!1);return(0,i.useEffect)(()=>{n.current=navigator.userAgent.toLowerCase().indexOf("firefox")>-1},[]),(0,i.useCallback)(async()=>{e("BLOB_RAW_DROPDOWN.COPY");try{let e=(0,l.Ix)(t);if((0,l.j5)()&&navigator&&navigator.clipboard&&"write"in navigator.clipboard&&!n.current)await navigator.clipboard.write([new ClipboardItem({"text/plain":e})]);else{let t=await e;if(!t)return l.T9.Error;await (0,o.D)(await t.text())}}catch{return l.T9.Error}return l.T9.Success},[n,e,t])}},17606:(e,t,n)=>{n.d(t,{o:()=>s});var r=n(70170),i=n(85647),o=n(21325),l=n(17515),a=n(96540);function s(e,t=20){let[n,c]=(0,a.useState)(t),d=(0,a.useRef)(t),u=(0,o.ud)().codeWrappingOption,h=(0,i.zy)();return(0,l.N)(()=>{let n=document.getElementById("file-name-id-wide");if(!n)return;let i=new ResizeObserver((0,r.s)(()=>{let n=document.getElementsByClassName(e)[0]?.firstChild?.getBoundingClientRect().height??t;n>100&&(n=document.getElementsByClassName(e)[0]?.firstChild?.firstChild?.getBoundingClientRect().height),0===n||n===d.current||u.enabled||(c(n),d.current=n)}));return i.observe(n),()=>i.disconnect()},[h.key,u.enabled,e,t]),n}},52687:(e,t,n)=>{n.d(t,{DC:()=>S,Jo:()=>B,M1:()=>T,M_:()=>g,Ov:()=>I,QM:()=>y,aM:()=>C,cH:()=>b,di:()=>v,fY:()=>L,ue:()=>A});var r=n(11303),i=n(58779),o=n(85647),l=n(33253),a=n(80663),s=n(21325),c=n(33299),d=n(96540),u=n(48234),h=n(3147),m=n(63205),p=n(15305),f=n(17606),x=n(36588);let y=7.2293,g=92,b=92,v=70,j=new r.Lj(1),w=new r.Lj(0),N=new r.Lj(1),_=new r.Lj(0),k=new r.Lj(!1);function C(){return(0,i.HN)(j)}function A(){return(0,i.HN)(N)}function S(){return(0,i.HN)(k)}function B(){let e=(0,d.useRef)(null);function t(){e.current={start:{line:j.value,column:w.value+1},end:{line:N.value,column:_.value+1}}}return(0,i.Rs)(j,t),(0,i.Rs)(w,t),(0,i.Rs)(N,t),(0,i.Rs)(_,t),e}function I(e){k.value=e}function L(e){let t=(0,s.ud)().codeWrappingOption.enabled,n=!!(0,p.O)(),[r]=(0,a.I)(()=>!1,!0,[]);return!t&&!n&&!e&&!r}function T(e,t,n,r,i,a,s,p,C,A,S,B){let I=(0,d.useRef)(0),L=(0,d.useRef)(0),T=(0,d.useRef)(0),E=(0,d.useRef)(0),R=(0,d.useRef)(0),O=(0,d.useRef)(0),D=(0,d.useRef)(null),F=(0,d.useRef)(0),$=(0,d.useRef)(0),M=(0,d.useRef)(15),P=(0,d.useRef)(0),z=s?b:g,H=(0,c.X)(["windows"])?6.6:y,W=(0,f.o)("react-line-numbers"),{stylingDirectives:U}=(0,h.PL)(),V=(0,d.useMemo)(()=>{let e=[];for(let t=0;t<i.length;t++)0===t?e.push(i[t].rawText?.length??0):e.push((i[t].rawText?.length??0)+e[t-1]+1);return e},[i]);function G(e,t){n(e),O.current=t}function Y(e,t){r(e),R.current=t}let q=(0,d.useCallback)(e=>{let t=j.value!==N.value,n={start:{line:j.value,column:t?w.value+1:null},end:{line:N.value,column:t?_.value+1:null}},r={anchorPrefix:"L",blobRange:{start:n.start,end:n.end}},i=(0,u.JB)(r);window.location.hash=i,p?.(e)},[p]),{hash:K}=(0,o.zy)();function Q(e){let t=0,n=0,r=0,i=0,o=0,l=0,a=V.length-1;for(;l<=a;){let s=Math.floor((l+a)/2);if(r=V[s]+1,i=s>0?V[s-1]+1:0,o=s<V.length-1?V[s+1]+1:1/0,e>=i&&e<r){t=s,n=e-i;break}if(e<i)a=s-1;else if(e>=r&&e<o){t=s+1,n=e-r;break}else e>=o&&(l=s+1)}return{line:t,offset:n}}function X(e,t,n,r,i){I.current=e,L.current=n,T.current=t,E.current=r,R.current=i?e:t,O.current=i?n:r,j.value=es(e),w.value=n,N.value=es(t),_.value=r}function Z(){return I.current===R.current&&L.current===O.current&&(I.current!==T.current||L.current!==E.current)?"start":T.current===R.current&&E.current===O.current&&(I.current!==T.current||L.current!==E.current)?"end":"same"}function J(){if(C&&C.current){let e=C.current,t=I.current-1,n=T.current-1,r=(-1!==t?V[t]+1:0)+L.current,i=(-1!==n?V[n]+1:0)+E.current;e.selectionStart=r,e.selectionEnd=i,$.current=r,P.current=i}}function ee(){eo();let e=I.current,t=L.current;e+M.current>i.length?e=i.length-1:e+=M.current,t=er(e,t),I.current=e,L.current=t,en(),Y(e*W,e),el(v)}function et(){eo();let e=I.current,t=L.current;e<M.current?e=0:e-=M.current,t=er(e,t),I.current=e,L.current=t,en(),Y(e*W,e),el(v)}function en(){T.current=I.current,E.current=L.current}function er(e,t){let n=t;if(e>i.length||!i[e])return n;let r=i[e].rawText;return r&&(t>r.length?(n=r.length,G((0,m.m)(n,r,A),n)):n<F.current&&F.current<r.length?(n=F.current,G((0,m.m)(n,r,A),n)):n<F.current&&F.current>=r.length&&(n=r.length,G((0,m.m)(n,r,A),n))),n}function ei(){let e=i[R.current];if(!e)return;let{rawText:n}=e,r=U?U[e.lineNumber-1]:e.stylingDirectivesLine;if(!n||!r)return;let o=(0,l.M)(r),a=null;for(let e of o)if(!(e.s>O.current)&&!(e.e<O.current)&&e.c&&(a=e,!(0,m.Bm)(n.substring(e.s,e.e),e.c)))return;a&&t?.({selectedText:n.substring(a.s,a.e),lineNumber:es(R.current),offset:a.s})}function eo(){D.current||(a?D.current=e.current?.parentElement?.parentElement:D.current=e.current?.parentElement)}function el(e){D.current&&(ea(),function(e){let t=Z(),n=L.current;"end"===t&&(n=E.current),D.current&&D.current.scrollBy&&(n*H+z+50>=D.current.scrollLeft+D.current.clientWidth?D.current.scrollBy(n*H+z-D.current.scrollLeft-D.current.clientWidth+e,0):n*H+z<=D.current.scrollLeft&&D.current.scrollBy(n*H+z-D.current.scrollLeft-D.current.clientWidth,0))}(e)),J()}function ea(){let e=Z(),t=L.current,n=I.current;"end"===e&&(t=E.current,n=T.current);let r=Math.min(es(n+5),i.length);if(!(0,m.si)(r)){let e=(0,m.ap)(r);null===e&&window.scrollTo(0,n*W),e&&e.getBoundingClientRect().y<0||e&&e.getBoundingClientRect().y>window.innerHeight?(e.scrollIntoView({block:"center"}),window.scrollBy(-300,0)):e&&window.scrollBy(0,100),window.innerWidth<t*H+z&&window.scrollTo(0,0)}let o=Math.max(es(n-5),1);if((0,m.si)(o))n<=7&&window.scrollTo(0,0);else{let e=(0,m.ap)(o);null===e&&window.scrollTo(0,n*W),e&&e.getBoundingClientRect().y<0||e&&e.getBoundingClientRect().y>window.innerHeight?(e.scrollIntoView({block:"center"}),window.scrollBy(-300,0)):e&&window.scrollBy(0,-200)}}function es(e){return i[e]?i[e].lineNumber:e}return(0,d.useEffect)(()=>{M.current=Math.min(Math.max(Math.round((window.innerHeight-200>300?window.innerHeight-200:300)/W),1),100)},[W]),(0,d.useEffect)(()=>{S&&""!==S&&(S.includes("PageUp")?et():S.includes("PageDown")&&ee())},[S]),(0,d.useEffect)(()=>{if(!B||B.start<0&&B.end<0||$.current===B.start&&P.current===B.end)return;let e=Q(B.start),t=Q(B.end);($.current!==B.start||P.current===B.end)&&(B.keyboard||B.displayStart)?(G((0,m.m)(e.offset,i[e.line]?.rawText??"",A),e.offset),F.current=e.offset,Y(e.line*W,e.line),X(e.line,t.line,e.offset,t.offset,!0)):(G((0,m.m)(t.offset,i[t.line]?.rawText??"",A),t.offset),F.current=t.offset,Y(t.line*W,t.line),X(e.line,t.line,e.offset,t.offset,!1)),P.current=B.end,$.current=B.start,B.end!==B.start||B.keyboard||(k.value=!1,ei()),R.current<=5&&B.keyboard&&ea()},[i,A,B]),(0,d.useEffect)(()=>{let e=(0,u.$c)(K);if(!e.blobRange?.start?.line||e.blobRange.start.line>i.length)return;let t=e.blobRange.start.line-1;I.current=t,j.value=t,w.value=0,N.value=t,_.value=0,L.current=0,T.current=t,E.current=0,F.current=0,G(L.current,L.current),Y(I.current*W,I.current),el(v)},[K,i,n,r]),{onEnter:function(){k.value=!0,(0,x.FO)(),ei()},updateUrlForLineNumber:q,onPageDown:ee,onPageUp:et,currentStartLine:I,currentStartChar:L,currentEndLine:T,currentEndChar:E,determineAndSetTextAreaCursorPosition:J,getCorrectLineNumberWithCollapsedSections:es}}},36588:(e,t,n)=>{n.d(t,{DE:()=>a,FO:()=>l,Gp:()=>o});var r=n(96540);let i="react_blob_view_focus_symbol_pane";function o(e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]),(0,r.useEffect)(()=>{let t=t=>{e(t.detail?.focusSymbolSearch||!1)};return window.addEventListener(i,t),()=>{window.removeEventListener(i,t)}},[e])}function l(){window.dispatchEvent(new CustomEvent(i))}function a(){window.dispatchEvent(new CustomEvent(i,{detail:{focusSymbolSearch:!0}}))}},905:(e,t,n)=>{n.d(t,{S9:()=>s,sZ:()=>c,u9:()=>a,uU:()=>l,zM:()=>function e(t){let n=t?.textContent??"";if(""!==n)return n;if(t){let r=t.childNodes;for(let t=0;t<r.length;t++){let i=r[t];if(i){let t=i.getAttribute("data-code-text");null===t&&(t=e(i)),n+=t}}}return n}});var r=n(52811),i=n(96540),o=n(63205);let l="highlighted-line-menu-positioner",a=25;function s({lineData:e,onLineStickOrUnstick:t,onMenuClose:n,onCollapseToggle:l,setOpen:a}){function s(e,t=!1){a(e),n&&!e&&n(e,t)}let c=(0,i.useCallback)(()=>{if(!e)return;let{lineNumber:n,ownedSection:r}=e;r&&(r.collapsed=!1),l?.(),(0,o.ny)(n),t?.(e,!0)},[e,l,t]),d=(0,i.useCallback)(()=>{if(!e)return;let{lineNumber:t,ownedSection:n}=e;n&&(n.collapsed=!0),l?.(),(0,o.E8)(t)},[e,l]);return{setShouldBeOpen:s,expandOrCollapseSection:function(){if(!e)return;let{ownedSection:t}=e;t&&(t.collapsed?(c(),(0,o.kY)("Code section expanded")):(d(),(0,o.kY)("Code section collapsed"))),s(!1,!0)},openUpRefSelector:function(){let e=document.getElementsByClassName("ref-selector-class");e&&1===e.length?(e[0]?.click(),(0,r.i)("ref selector opened")):e&&2===e.length&&(e[1]?.click(),(0,r.i)("ref selector opened")),s(!1)}}}function c(e,t={x:0,y:0}){let n=document.getElementById(l);if(!e||!n)return{display:"none"};let{top:r,left:i,height:o}=e.getBoundingClientRect(),{top:s,left:d}=n.getBoundingClientRect();return{top:`${r-s-(a-o)/2+t.y}px`,left:`${Math.max(i-d+t.x,0)-13}px`}}},56297:(e,t,n)=>{n.d(t,{u:()=>i});var r=n(96540);function i(){let[e,t]=(0,r.useState)({});return(0,r.useCallback)(()=>t({}),[])}},51595:(e,t,n)=>{n.d(t,{f:()=>l,g:()=>o});var r=n(96540);let i="react_blob_view_scroll_line_into_view";function o(e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]),(0,r.useEffect)(()=>{let e=e=>t.current(e.detail);return window.addEventListener(i,e),()=>{window.removeEventListener(i,e)}},[])}function l(e){window.dispatchEvent(new CustomEvent(i,{detail:e}))}},92659:(e,t,n)=>{n.d(t,{Us:()=>i,ds:()=>d,oM:()=>c});var r=n(96540);let i="repos-sticky-header",o="code_view_update_sticky_header_height_event",l=null,a=null,s=null;function c(e){e&&a!==e&&(a=e,null===l?l=new ResizeObserver(e=>{for(let t of e)t.contentRect.height!==s&&(s=t.contentRect.height,window.dispatchEvent(new CustomEvent(o,{detail:t.contentRect.height})))}):l.disconnect(),l.observe(e))}function d(){let[e,t]=(0,r.useState)(void 0);return((0,r.useEffect)(()=>{function e(e){t(e.detail)}return window.addEventListener(o,e),a&&c(a),()=>{window.removeEventListener(o,e)}},[]),void 0!==e)?e:102}},63205:(e,t,n)=>{n.d(t,{$0:()=>f,Bm:()=>A,Bn:()=>v,CZ:()=>w,E8:()=>_,Gb:()=>g,N9:()=>R,Wb:()=>x,X5:()=>k,a2:()=>m,ap:()=>T,bP:()=>y,hO:()=>b,jH:()=>I,kY:()=>O,m:()=>B,ny:()=>N,si:()=>L,t1:()=>E,um:()=>S,wQ:()=>u,zS:()=>h});var r=n(11303),i=n(58779),o=n(96679),l=n(96540),a=n(48234),s=n(56369),c=n(52687);let d="collapse-show-rows-styles",u="read-only-cursor-text-area";function h(e,t){return document.querySelector(`#${e}LC${t}`)}function m(e,t){return document.querySelector(`main #${(0,s.p)(e,t)}`)}let p=new r.yy;function f(e){return(0,i.HN)(p.has(e))}function x(e){return(0,i.Rs)(p,e)}function y(){return(0,i.tQ)(p)}function g(){let e=b(),t=(0,l.useSyncExternalStore)(()=>()=>{},()=>(function(e){let t=e.toLowerCase().match(/firefox\/(\d+(\.\d+)?)/);return t&&t.length>=2&&t[1]?parseFloat(t[1]):null})(navigator.userAgent),()=>null)??0,n=(0,l.useSyncExternalStore)(()=>()=>{},()=>navigator.userAgent.toLowerCase().indexOf("chrome")>-1||navigator.userAgent.toLowerCase().indexOf("chromium")>-1||navigator.userAgent.toLowerCase().indexOf("edge")>-1,()=>!1),r=(0,l.useSyncExternalStore)(()=>()=>{},()=>(function(e){let t=e.match(/Chrom(e|ium)\/([0-9]+)\./);return t&&t.length>=3&&t[2]?parseFloat(t[2]):null})(navigator.userAgent),()=>null)??0;return(0,c.fY)()&&(e||n)&&(t>=124||r>=124)}function b(){return(0,l.useSyncExternalStore)(()=>()=>{},()=>navigator.userAgent.toLowerCase().indexOf("firefox")>-1,()=>!1)}function v(e,t,n,r){if(!r)return"";let i="";for(let t=0;t<e.length;t++)i+=`${j(e[t].startLine-1)} `;if(n&&r.has(t)){let e=r.get(t);if(e)for(let t=0;t<e.length;t++)i+=`${j(e[t].startLine-1)} `}return i}function j(e){return`child-of-line-${e}`}function w(e,t,n,r){if(!n)return;let i=n.get(t);if(i)for(let n of i){let i=e.get(n.endLine);if(i)for(let e of i)t>e.lineNumber&&r(e,!1)}}function N(e){C(e,!1),p.delete(e)}function _(e){C(e,!0),p.add(e)}function k(){let e=document.getElementById(d);e&&(e.textContent=""),p.clear()}function C(e,t){let n=`.${j(e-1)} { display: none; } `;if(document.getElementById(d)){let e=document.getElementById(d);if(t)e.textContent+=n;else{let t=e?.textContent||"";e.textContent=t=t.replace(n,"")}}else{let e=document.createElement("style");e.id=d,e.textContent=n,document.head.appendChild(e)}}function A(e,t){if(e.length<3)return!1;let n=t.split(" "),r=n.includes("pl-ent")?/\n|\s|[();&.=,]/:/\n|\s|[();&.=",]/;return!(e.match(r)||n.includes("pl-c")||n.includes("pl-k"))}function S(e,t){let n=null,r=null,i=null,o=t;if(e.parentElement?.classList.contains("react-file-line"))n=e.parentElement.getAttribute("data-line-number"),r=e.parentElement,i=e;else if(e.parentElement?.parentElement?.classList.contains("react-file-line"))n=e.parentElement.parentElement.getAttribute("data-line-number"),r=e.parentElement.parentElement,i=e.parentNode;else{if(!e.parentElement?.firstElementChild?.classList.contains("react-file-line")||!(n=e.parentElement.firstElementChild.getAttribute("data-line-number"))||!parseInt(n,10))return;return{line:parseInt(n,10)-1,column:null}}if(n&&parseInt(n,10)){for(let e of r.childNodes){if(e===i)break;o+=e.textContent?.length||0}return{line:parseInt(n,10),column:0!==o?o+1:null}}}function B(e,t,n){let r=document.createElement("div");r.style.position="absolute",r.style.visibility="hidden",r.style.fontFamily="ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace",r.style.fontSize="12px",r.style.lineHeight="20px",r.style.whiteSpace="pre",r.style.tabSize=n.toString(),r.textContent=t.slice(0,e),document.body.appendChild(r);let i=r.clientWidth;return(document.body.removeChild(r),0===i&&0!==e)?e*c.QM:i}function I(e,t,n,r,i){if(e?.start.line===n&&e?.start.column!==null){let o=(0,a.Py)({start:e.start,end:{line:e.start.line,column:e.end.line===n?e.end.column:null}},()=>t);if(o&&o.startContainer.parentElement)return{offset:o?.getBoundingClientRect().x-t.getBoundingClientRect().x+10,width:e.end.line===n?o.getBoundingClientRect().width:void 0};if(i){let t=B(e.start.column-1,i,r),o=e.end.line===n?e.end.column:null;return{offset:t+20,width:e.end.line===n?B(o?o-1:i.length-1,i,r)-t:void 0}}}else if(e?.end.line===n&&e?.end.column!==null){let n=(0,a.Py)({start:{line:e.end.line,column:0},end:e.end},()=>t);return n?{width:n.getBoundingClientRect().width+10}:{width:B(e.end.column-1,i,r)+10}}}function L(e){var t=h("",e);if(!t)return!1;let n=t.getBoundingClientRect();return n.top>=0&&n.left>=0&&n.bottom<=(window.innerHeight||document.documentElement.clientHeight)}function T(e){return h("",e)}function E(e,t,n){return Math.floor((e-t)/n)+1}function R(e,t){let n=0,r=t.length-1;for(;n<=r;){let i=Math.floor((n+r)/2),o=t[i];if(!o)break;if(o.lineNumber===e)return i;o.lineNumber<e?n=i+1:r=i-1}return -1}function O(e,t=0){if(void 0===o.XC)return;let n=o.XC.getElementById("screenReaderAnnouncementDiv");if(n||function(){if(void 0===o.XC)return;let e=o.XC.createElement("div");e.classList.add("sr-only","mt-n1"),e.id="screenReaderAnnouncementDiv",e.setAttribute("role","alert"),e.setAttribute("data-testid","screenReaderAnnouncement"),e.setAttribute("aria-live","assertive"),o.XC.body.appendChild(e)}(),!(n=o.XC.getElementById("screenReaderAnnouncementDiv")))return;let r=n.textContent===e?`${e}\u00A0`:e;setTimeout(()=>{n&&(n.textContent=r)},t)}},8447:(e,t,n)=>{n.d(t,{A:()=>o,s:()=>i});let r=!0;function i(e){r=e}function o(){return r}},17264:(e,t,n)=>{function r(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function i(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function o(e,t){var n=i(e,t,"get");return n.get?n.get.call(e):n.value}function l(e,t,n){r(e,t),t.set(e,n)}function a(e,t,n){var r=i(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}function s(e,t,n){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return n}n.d(t,{N:()=>DebouncedWorkerManager});var c=new WeakMap,d=new WeakMap,u=new WeakMap,h=new WeakMap,m=new WeakMap,p=new WeakMap,f=new WeakSet;let DebouncedWorkerManager=class DebouncedWorkerManager{post(e){if(o(this,p)&&o(this,p).call(this,e))return o(this,u)&&clearTimeout(o(this,u)),s(this,f,x).call(this,e);this.idle()?(o(this,u)&&clearTimeout(o(this,u)),a(this,u,setTimeout(()=>{s(this,f,x).call(this,e)},o(this,m)))):a(this,d,e)}idle(){return!o(this,c)}terminate(){o(this,h).terminate()}constructor(e,t=200,n){!function(e,t){r(e,t),t.add(e)}(this,f),l(this,c,{writable:!0,value:void 0}),l(this,d,{writable:!0,value:void 0}),function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"onResponse",void 0),l(this,u,{writable:!0,value:void 0}),l(this,h,{writable:!0,value:void 0}),l(this,m,{writable:!0,value:void 0}),l(this,p,{writable:!0,value:void 0}),a(this,h,e),a(this,m,t),a(this,p,n),o(this,h).onmessage=({data:e})=>{this.onResponse&&this.onResponse(e),o(this,d)?(s(this,f,x).call(this,o(this,d)),a(this,d,void 0)):a(this,c,void 0)}}};function x(e){a(this,c,e),o(this,h).postMessage(e)}},29943:(e,t,n)=>{function r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}n.d(t,{HS:()=>l,Oc:()=>o,Tv:()=>c,Xr:()=>SymbolChangedEvent,f7:()=>a,fD:()=>s,qR:()=>i});let OpenCopilotChatEvent=class OpenCopilotChatEvent extends Event{constructor(e){super("open-copilot-chat",{bubbles:!1,cancelable:!0}),r(this,"payload",void 0),this.payload=e}};Event;let AddCopilotChatReferenceEvent=class AddCopilotChatReferenceEvent extends Event{constructor(e,t=!1,n){super("add-copilot-chat-reference",{bubbles:!1,cancelable:!0}),r(this,"reference",void 0),r(this,"openPanel",void 0),r(this,"id",void 0),this.reference=e,this.openPanel=t,this.id=n}};let SymbolChangedEvent=class SymbolChangedEvent extends Event{constructor(e){super("symbol-changed",{bubbles:!1,cancelable:!0}),r(this,"context",void 0),this.context=e}};function i(e){window.dispatchEvent(new OpenCopilotChatEvent(e))}function o(e,t=!1,n){window.dispatchEvent(new AddCopilotChatReferenceEvent(e,t,n))}function l(e){return window.addEventListener("open-copilot-chat",e),()=>{window.removeEventListener("open-copilot-chat",e)}}function a(e){return window.addEventListener("add-copilot-chat-reference",e),()=>{window.removeEventListener("add-copilot-chat-reference",e)}}function s(e){return window.addEventListener("search-copilot-chat",e),()=>{window.removeEventListener("search-copilot-chat",e)}}function c(e){return window.addEventListener("symbol-changed",e),()=>{window.removeEventListener("symbol-changed",e)}}},99377:(e,t,n)=>{n.d(t,{C6:()=>i,UH:()=>s,Wp:()=>c,hs:()=>l,mF:()=>a,wh:()=>r,xP:()=>o});let r={explain:"explain",conversation:"conversation",suggest:"suggest",discussFileDiff:"discuss-file-diff",explainFileDiff:"explain-file-diff",reviewPr:"review-pull-request",actionsAgent:"actions-agent"},i=["exception","filtered","publicCode","contentTooLarge","rateLimit","agentUnauthorized","agentRequest","networkError","multipleAgentsAttempt"],o=["bing-search","codesearch","semantic-code-search","lexical-code-search","kb-search","getfile","getfilechanges","getdiscussion","get-actions-job-logs","getalert","planskill","get-github-data","support-search","get-figma","codesearchagentskill","draft-issue","repository-metadata"],l={Experiments:"experiements",Prompt:"prompt",None:"none"},a={Unlicensed:"unlicensed",LicensedFull:"licensed_full",LicensedLimited:"licensed_limited"},s={IndividualFree:"free",IndividualPro:"pro",IndividualProPlus:"pro_plus",Business:"business",Enterprise:"enterprise"},c="NULL_MESSAGE"},83829:(e,t,n)=>{n.d(t,{A0:()=>l,TA:()=>c,Y:()=>d,_n:()=>u,rE:()=>a,t6:()=>s});let r=/[\u202A-\u202E]|[\u2066-\u2069]|\u{E0001}|\u{E007F}/u,i=/[\u202A-\u202E]|[\u2066-\u2069]|\u{E0001}|\u{E007F}/gu,o=/([\u202A-\u202E]|[\u2066-\u2069]|\u{E0001}|\u{E007F})/gu,l=new Map(Object.entries({"\u202A":"U+202A","\u202B":"U+202B","\u202C":"U+202C","\u202D":"U+202D","\u202E":"U+202E","\u2066":"U+2066","\u2067":"U+2067","\u2068":"U+2068","\u2069":"U+2069","\u{E0001}":"U+E0001","\u{E007F}":"U+E007F"}));function a(e){return`<span class="hidden-unicode-replacement" data-code-text="${e}"></span>`}function s(e){return e.split(o)}function c(e){return d(e)?e.replaceAll(i,e=>l.get(e)??""):e}function d(e){return r.test(e)}function u(e){return l.get(e)}},11303:(e,t,n)=>{function r(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function i(e,t){var n=r(e,t,"get");return n.get?n.get.call(e):n.value}function o(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}function l(e,t,n){var i=r(e,t,"set");if(i.set)i.set.call(e,n);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=n}return n}n.d(t,{Es:()=>ObservableMap,Lj:()=>ObservableValue,yy:()=>ObservableSet});var a=new WeakMap;let s=class ObservableBase{subscribe(e){return i(this,a).add(e),()=>{i(this,a).delete(e)}}notify(e){for(let t of i(this,a))t(e)}constructor(){o(this,a,{writable:!0,value:new Set})}};var c=new WeakMap;let ObservableValue=class ObservableValue extends s{get value(){return i(this,c)}set value(e){var t,n;t=i(this,c),("object"==typeof(n=e)&&n&&"object"==typeof t&&t?function(e,t){for(let n of new Set(Object.keys(e).concat(Object.keys(t))))if(!Object.is(e[n],t[n]))return!0;return!1}(t,n):!Object.is(t,n))&&(l(this,c,e),this.notify(e))}setValue(e){this.value=e}constructor(e){super(),o(this,c,{writable:!0,value:void 0}),l(this,c,e)}};var d=new WeakMap,u=new WeakMap;let ObservableSet=class ObservableSet extends s{get value(){return i(this,d)}has(e){if(!i(this,u).has(e)){let t=new ObservableValue(i(this,d).has(e));i(this,u).set(e,t)}return i(this,u).get(e)}add(e){i(this,d).has(e)||(i(this,d).add(e),i(this,u).has(e)&&(i(this,u).get(e).value=!0),this.notify(i(this,d)))}delete(e){i(this,d).has(e)&&(i(this,d).delete(e),i(this,u).has(e)&&(i(this,u).get(e).value=!1),this.notify(i(this,d)))}clear(){if(0!==i(this,d).size){for(let e of(i(this,d).clear(),i(this,u).values()))e.value=!1;this.notify(i(this,d))}}constructor(...e){super(),o(this,d,{writable:!0,value:void 0}),o(this,u,{writable:!0,value:new Map}),l(this,d,new Set(...e))}};var h=new WeakMap,m=new WeakMap,p=new WeakMap;let ObservableMap=class ObservableMap extends s{get value(){return i(this,h)}has(e){if(!i(this,m).has(e)){let t=new ObservableValue(i(this,h).has(e));i(this,m).set(e,t)}return i(this,m).get(e)}get(e){if(!i(this,p).has(e)){let t=new ObservableValue(i(this,h).get(e));i(this,p).set(e,t)}return i(this,p).get(e)}set(e,t){i(this,h).get(e)!==t&&(i(this,h).set(e,t),i(this,m).has(e)&&(i(this,m).get(e).value=!0),i(this,p).has(e)&&(i(this,p).get(e).value=t),this.notify(i(this,h)))}delete(e){i(this,h).has(e)&&(i(this,h).delete(e),i(this,m).has(e)&&(i(this,m).get(e).value=!1),i(this,p).has(e)&&(i(this,p).get(e).value=void 0),this.notify(i(this,h)))}clear(){if(0!==i(this,h).size){for(let e of(i(this,h).clear(),i(this,m).values()))e.value=!1;for(let e of i(this,p).values())e.value=void 0;this.notify(i(this,h))}}constructor(...e){super(),o(this,h,{writable:!0,value:void 0}),o(this,m,{writable:!0,value:new Map}),o(this,p,{writable:!0,value:new Map}),l(this,h,new Map(...e))}}},58779:(e,t,n)=>{n.d(t,{AI:()=>o,HN:()=>d,R:()=>a,Rs:()=>c,Sk:()=>h,XG:()=>l,tQ:()=>u});var r=n(96540),i=n(11303);function o(e){let[t]=(0,r.useState)(()=>new i.Lj(e));return t}function l(e){let t=(0,r.useRef)(null);null===t.current&&(t.current=new i.Lj(e));let n=(0,r.useCallback)(e=>{null!==t.current&&(t.current.value=e)},[]);return[t,n]}function a(...e){let[t]=(0,r.useState)(()=>new i.Es(...e));return t}let s=Symbol("no value");function c(e,t){let n=(0,r.useRef)(e.value),i=(0,r.useRef)(t);(0,r.useEffect)(()=>{i.current=t}),(0,r.useEffect)(()=>(n.current!==s&&n.current!==e.value&&(i.current(e.value),n.current=s),e.subscribe(e=>i.current(e))),[e])}function d(e){let[t,n]=(0,r.useState)(e.value);return c(e,e=>n(e)),t}function u(e){let[t,n]=(0,r.useState)(e.value),[i,o]=(0,r.useState)({});return c(e,e=>{n(e),o({})}),t}function h(e,t){let n=o(t(e.value));return c(e,e=>{n.value=t(e)}),n}},98075:(e,t,n)=>{n.d(t,{D7:()=>i,MQ:()=>r,gE:()=>o});let r=e=>{if(e){if(e.startsWith("refs/tags/"))return"tag";if(e.startsWith("refs/heads/"))return"branch"}},i=(e,t)=>"branch"===t?`refs/heads/${e}`:"tag"===t?`refs/tags/${e}`:e,o=e=>{if(!e)return;if(!r(e))return e;let[,,...t]=e.split("/");return t.join("/")}},33253:(e,t,n)=>{n.d(t,{M:()=>o});let r=(e,t)=>e[({start:0,end:1,cssClass:2})[t]],i=e=>Array.isArray(e)?{s:r(e,"start"),e:r(e,"end"),c:r(e,"cssClass")}:e,o=e=>0===e.length?[]:Array.isArray(e[0])?e.map(i):e},42728:(e,t,n)=>{n.d(t,{W:()=>d});var r=n(96540),i=n(83829),o=n(33253),l=n(81351);function a(e,t,n,r,i,o,l){let a=t.substring(n,r);return{c:e,s:n,e:r,text:"plain"!==l?function(e,t,n){let r=[];for(let i of e)if("	"===i){let e=t-n.value%t;r.push(Array(e).fill(" ").join("")),n.value+=e}else r.push(i),n.value+=Array.from(i).length;return r.join("")}(a,o,i):a}}function s(e){return e.replace(/[&<>"']/g,c)}function c(e){switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"'":return"&#039;";default:return e}}function d(e,t,n,c="plain",u,h){return(0,r.useMemo)(()=>e??function(e,t,n,r,c){e||(e=`
`);let d=function(e,t,n,r){let i={value:0},o={nodes:[],s:0,e:e.length,c:""},l=t?.filter(e=>e.e>e.s);if(!l||0===l.length)return o.nodes.push(a("",e,0,e.length,i,r,n)),o;let s=[o];for(let t=0;t<l.length;t++){let c=l[t],d=l[t+1],u=s[s.length-1]??o,h=u.nodes[u.nodes.length-1];if(0===u.nodes.length&&c.s>u.s){let t=a("",e,u.s,c.s,i,r,n);u.nodes.push(t)}else if(h&&c.s>h.e){let t=a("",e,h.e,c.s,i,r,n);u.nodes.push(t)}if(d&&d.s<c.e){let e={...c,nodes:[]};u.nodes.push(e),s.push(e)}else{let t=a(c.c,e,c.s,c.e,i,r,n);u.nodes.push(t)}if(d&&d.s>=u.e){let t=c.e;if(u.e>t){let o=a("",e,t,u.e,i,r,n);u.nodes.push(o),t=u.e}for(;s.length>1&&d.s>=u.e;)if(s.pop(),u=s[s.length-1]??o,s.length>1&&d.s>=u.e&&u.e>t){let o=a("",e,t,u.e,i,r,n);t=u.e,u.nodes.push(o)}}}for(;s.length>0;){let t=s.pop(),o=t.nodes[t.nodes.length-1];if(o&&o.e<t.e){let l=a("",e,o.e,t.e,i,r,n);t.nodes.push(l)}}return o}(e,(0,o.M)(t??[]),n,r),u=[];return function e(t,n,r,o){for(let a of(t.c&&o.push(`<span class="${s(t.c)}">`),t.nodes))"nodes"in a?e(a,n,r,o):o.push(function e(t,n,r){switch(n){case"data-attribute":{let o=s(t.text);if(r&&(0,i.Y)(o)){let r=(0,i.t6)(o).map(r=>{let o=(0,i._n)(r);return o?(0,i.rE)(o):e({...t,text:r,c:""},n,!1)});return t.c?`<span class="${s(t.c)}">${r.join("")}</span>`:r.join("")}return t.c?`<span class="${s(t.c)}" data-code-text="${o}"></span>`:`<span data-code-text="${o}"></span>`}case"separated-characters-chunked":case"separated-characters":{if(t.text&&!t.text.trim())return e({...t},"data-attribute",r);let o=[...t.text];"separated-characters-chunked"!==n||r||(o=t.text.match(/.{1,2}/g)??o);let l=[...o].map(e=>{let t=r?(0,i._n)(e):void 0;return t?(0,i.rE)(t):`<span data-code-text="${s(e)}"></span>`}).join("");return t.c?`<span class="${s(t.c)}">${l}</span>`:l}default:{let e=s(t.text),n=r?(0,l.Gx)(e)??e:e;return t.c?`<span class="${s(t.c)}">${n}</span>`:n}}}(a,n,r));t.c&&o.push("</span>")}(d,n,c,u),u.join("")}(n,t,c,u,h),[n,e,t,c,u,h])}},98386:(e,t,n)=>{n.d(t,{G:()=>r});let r=e=>({})},40245:(e,t,n)=>{n.d(t,{u:()=>l});var r=n(96235),i=n(60039),o=n(96540);function l(e,t,n,l){let[a,s]=(0,o.useState)({loading:!0}),c=t&&e&&n&&l?(0,r.IO9)({repo:{name:t,ownerLogin:e},commitish:n,action:"file-contributors",path:l}):null;return(0,o.useEffect)(()=>{if(!c)return;let e=!1;return(async()=>{s({loading:!0});let t=await (0,i.lS)(c);if(!e)try{t.ok?s({contributors:await t.json()}):s({error:!0})}catch{s({error:!0})}})(),function(){e=!0}},[c]),a}},37433:(e,t,n)=>{n.d(t,{H:()=>i});var r=n(25772);function i(e,t){let{csrf_tokens:n}=(0,r.B)();return n?.[e]?.[t]}},14103:(e,t,n)=>{n.d(t,{L:()=>i});var r=n(96540);function i(e){(0,r.useEffect)(()=>{if(!e)return;let t=document.querySelector(".footer");if(t)return t.hidden=!0,()=>{t.hidden=!1}},[e])}},70979:(e,t,n)=>{n.d(t,{Y:()=>i});var r=n(96540);function i(){return(0,r.useSyncExternalStore)(a,o,l)}function o(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function l(){return!1}function a(){return()=>{}}},60040:(e,t,n)=>{n.d(t,{vp:()=>o.v,hf:()=>s,CK:()=>c});var r=n(17515),i=n(96540),o=n(35822);let l=e=>e;function a(e,t){return({additionalScrollOffset:n=0,scrollToFn:o,horizontal:l,parentRef:a,shouldUseScrollRef:s=!0,...c})=>{(0,i.useDebugValue)(t);let d=(0,i.useRef)(window),u=(0,i.useCallback)(()=>{let e=a.current?.getBoundingClientRect(),t=e?.top??0,n=e?.left??0;return l?-1*n:-1*t},[l,a]),h=(0,i.useCallback)(e=>{let t=e+((a.current?.getBoundingClientRect().top??0)+window.scrollY)+n;d.current?.scroll({top:l?0:t,left:l?t:0})},[n,l,a]);return e({...c,horizontal:l,parentRef:a,scrollToFn:o||h,onScrollElement:s?d:void 0,scrollOffsetFn:u,useObserver:()=>(function(e){let[t,n]=(0,i.useState)({height:0,width:0}),o=e.current;return(0,r.N)(()=>{if(!o)return;let e=()=>{let e={height:o.innerHeight,width:o.innerWidth};n(t=>t.height!==e.height||t.width!==e.width?e:t)};return e(),o.addEventListener("resize",e),()=>{o.removeEventListener("resize",e)}},[o]),t})(d)})}}let s=a(o.z,"useVirtualWindow"),c=a(function(e){let t=i.useRef({}),n=i.useRef({}),r=(e,n)=>{t.current[e](n)},a=i.useRef(r);a.current=r;let s=i.useRef(new ResizeObserver(e=>{for(let t of e){let e=t.target,n="data-key",r=e.getAttribute(n);if(null===r)throw Error(`Value not found, for '${n}' attribute`);a.current(r,e)}}));i.useEffect(()=>{let e=s.current;return()=>{e.disconnect()}},[]);let{size:c,keyExtractor:d=l}=e,u=i.useMemo(()=>{let e=e=>t=>{n.current[e]&&s.current.unobserve(n.current[e]),t&&(a.current(e,t),s.current.observe(t)),n.current[e]=t},t={};for(let n=0;n<c;n++){let r=d(n);t[r]=e(r)}return t},[c,d]),h=(0,o.z)(e),m=h.virtualItems.map(e=>(t.current[e.key]=e.measureRef,{...e,measureRef:u[e.key]}));return{...h,virtualItems:m}},"useVirtualWindowDynamic")},13430:(e,t,n)=>{n.d(t,{Z:()=>a});var r=n(74848),i=n(75177),o=n(34164);let l={Box:"Panel-module__Box--lC3LD"},a=({children:e,sx:t,className:n,...a})=>(0,r.jsx)(i.A,{sx:t,className:(0,o.$)(l.Box,n),...a,children:e});try{a.displayName||(a.displayName="Panel")}catch{}},93237:(e,t,n)=>{n.d(t,{f:()=>l});var r=n(74848),i=n(75177),o=n(63867);let l=()=>(0,r.jsx)(i.A,{sx:{display:"flex",flexDirection:"column",alignItems:"center",py:3},children:(0,r.jsx)(o.A,{"aria-label":"Loading"})});try{l.displayName||(l.displayName="LoadingFallback")}catch{}},68575:(e,t,n)=>{n.d(t,{T:()=>u});var r=n(74848),i=n(96679),o=n(37433),l=n(38621),a=n(55847),s=n(34614),c=n(53110);let d={Box:"EditIssues-module__Box--fgd2d",Octicon:"EditIssues-module__Octicon--LlC7t",Text:"EditIssues-module__Text--zkCQm",Text_1:"EditIssues-module__Text_1--tjv7I"};function u({binary:e,helpUrl:t,webCommitInfo:n}){let{shouldFork:u,lockedOnMigration:h,shouldUpdate:m,userOverRepositoryLimit:p}=n,f=i.fV.pathname;f.endsWith("/")&&(f=f.slice(0,-1));let x=f+i.fV.search,y=(0,o.H)(x,"post"),g=h?{message:"This repository is currently being migrated.",description:"Sorry, you\u2019re not able to edit this repository while the migration is in progress.",icon:l.LockIcon}:u?p?{message:"You are over your repository limit",description:"Sorry, you\u2019re not able to fork this repository.",icon:l.AlertIcon}:{message:"You need to fork this repository to propose changes.",description:"Sorry, you\u2019re not able to edit this repository directly\u2014you need to fork it and propose your changes from there instead.",icon:l.GitBranchIcon}:m?{message:"Sorry, it looks like your fork is outdated!",description:"You\u2019ll have to bring it up to date before you can propose changes.",icon:l.AlertIcon}:e?{message:"Binary file content is not editable.",description:"But you can still rename or move it.",icon:l.PencilIcon}:null;return g?(0,r.jsxs)("div",{className:d.Box,children:[(0,r.jsx)(c.A,{icon:g.icon,size:"medium",className:d.Octicon}),(0,r.jsx)("h3",{className:d.Text,children:g.message}),(0,r.jsx)("span",{className:d.Text_1,children:g.description}),(u&&!p||m)&&(0,r.jsxs)("form",{"data-turbo":"false",method:"post",action:x,"data-testid":"edit-issues-form",children:[(0,r.jsx)("input",{hidden:!0,name:"authenticity_token",value:y,readOnly:!0}),(0,r.jsx)(a.Q,{type:"submit",variant:"primary",children:u?"Fork this repository":"Update your fork"})]}),u&&p?(0,r.jsx)(s.A,{href:`${t}/repositories/creating-and-managing-repositories/repository-limits`,children:"Learn more repository limits"}):(0,r.jsx)(s.A,{href:`${t}/articles/fork-a-repo`,children:"Learn more about forks"})]}):null}try{u.displayName||(u.displayName="EditIssues")}catch{}},94538:(e,t,n)=>{n.d(t,{r:()=>l});var r=n(74848),i=n(6869);let o={Flash:"EditingForkBanner-module__Flash--BmTUB"};function l({forkName:e,forkOwner:t}){return(0,r.jsxs)(i.A,{"aria-live":"polite",className:o.Flash,children:["You\u2019re making changes in a project you don\u2019t have write access to. Submitting a change will write it to a new branch in your fork ",(0,r.jsx)("b",{children:`${t}/${e}`}),", so you can send a pull request."]})}try{l.displayName||(l.displayName="EditingForkBanner")}catch{}},96464:(e,t,n)=>{n.d(t,{bN:()=>m,$4:()=>p,pO:()=>y,kz:()=>g,Ke:()=>x});var r=n(74848),i=n(96235),o=n(60039),l=n(38621),a=n(6869),s=n(87330),c=n(53110),d=n(95776),u=n(96540);let h={Flash:"CodeownerFileBanner-module__Flash--dwY36",Flash_1:"CodeownerFileBanner-module__Flash_1--OS2TL",Box:"CodeownerFileBanner-module__Box--HgWQx",IconButton:"CodeownerFileBanner-module__IconButton--Yy_k4",Dialog:"CodeownerFileBanner-module__Dialog--arvdR",Box_1:"CodeownerFileBanner-module__Box_1--Rtx_4",Box_2:"CodeownerFileBanner-module__Box_2--X7HF6",Box_3:"CodeownerFileBanner-module__Box_3--KC_7L",Octicon:"CodeownerFileBanner-module__Octicon--eIlUl"},m={ERROR:"ERROR",LOADING:"LOADING",VALIDATED:"VALIDATED"};function p({errors:e,state:t}){let[n,i]=(0,u.useState)(!1),o=(0,u.useRef)(null);return t===m.ERROR?(0,r.jsx)(a.A,{variant:"warning",className:h.Flash,children:"Failed to validate this CODEOWNERS file"}):t===m.LOADING?(0,r.jsx)(a.A,{variant:"default",className:h.Flash,children:"Validating CODEOWNERS rules..."}):0===e.length?(0,r.jsx)(a.A,{variant:"success",className:h.Flash,children:"This CODEOWNERS file is valid."}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(a.A,{variant:"warning",className:h.Flash_1,children:[(0,r.jsx)(c.A,{icon:l.BugIcon}),(0,r.jsxs)("div",{onClick:()=>i(!n),className:h.Box,children:["This CODEOWNERS file contains errors",(0,r.jsx)(s.K,{"aria-label":"View CODEOWNERS errors",tooltipDirection:"s",ref:o,size:"small",icon:l.KebabHorizontalIcon,className:h.IconButton})]})]}),(0,r.jsxs)(d.A,{isOpen:n,onDismiss:()=>i(!1),returnFocusRef:o,className:h.Dialog,children:[(0,r.jsx)(d.A.Header,{children:"CODEOWNERS errors"}),(0,r.jsx)("div",{className:h.Box_1,children:e.map((e,t)=>(0,r.jsx)(f,{error:e},t))})]})]})}function f({error:e}){return(0,r.jsxs)("div",{className:h.Box_2,children:[`${e.kind} on line ${e.line}${e.suggestion?`: ${e.suggestion}`:""}`,(0,r.jsx)("pre",{className:h.Flash,children:(0,r.jsxs)("code",{children:[e.linePrefix,(0,r.jsx)("b",{className:h.Box_3,children:e.lineError}),e.lineSuffix]})})]})}function x(e){var t,n,r;let i=e.source.trim(),o=e.column-1,l=(t=o,n=e.end_column,r=i,n||(r.substring(t).indexOf(" ")>0?r.indexOf(" ",t):r.length));if(o>30){let e=o-30;i="\u2026"+i.slice(e),o-=e-1,l-=e-1}return{...e,linePrefix:i.substring(0,o),lineError:i.substring(o,l),lineSuffix:i.substring(l)}}function y(){return(0,r.jsx)(c.A,{icon:l.DotFillIcon,"aria-label":"This line contains CODEOWNERS errors",className:h.Octicon})}function g(e,t,n){return(0,o.lS)((0,i.$RX)({owner:e.ownerLogin,repo:e.name,commitish:t.name,filePath:n}),{method:"GET"})}try{p.displayName||(p.displayName="CodeownerFileBanner")}catch{}try{f.displayName||(f.displayName="CodeownersErrorDetails")}catch{}try{y.displayName||(y.displayName="CodeownersErrorLineIndicator")}catch{}},89681:(e,t,n)=>{n.d(t,{om:()=>y,T4:()=>g,Y:()=>w,bp:()=>N,$E:()=>j,TA:()=>v});var r=n(74848),i=n(96679),o=n(80663),l=n(38621),a=n(6869),s=n(34614),c=n(55847),d=n(53110),u=n(94977);let h={Flash:"HiddenUnicode-module__Flash--NH4EI",Button:"HiddenUnicode-module__Button--wjP7v",Octicon:"HiddenUnicode-module__Octicon--GY9_l"},m=/[\u202A-\u202E]|[\u2066-\u2069]|\u{E0001}|\u{E007F}/u,p=/[\u202A-\u202E]|[\u2066-\u2069]|\u{E0001}|\u{E007F}/gu,f=/([\u202A-\u202E]|[\u2066-\u2069]|\u{E0001}|\u{E007F})/gu,x=new Map(Object.entries({"\u202A":"U+202A","\u202B":"U+202B","\u202C":"U+202C","\u202D":"U+202D","\u202E":"U+202E","\u2066":"U+2066","\u2067":"U+2067","\u2068":"U+2068","\u2069":"U+2069","\u{E0001}":"U+E0001","\u{E007F}":"U+E007F"}));function y(){let[e]=(0,o.I)(()=>i.cg,i.cg,[]);if(!e)return null;let t=new URL(e.location.href,e.location.origin),n="1"===t.searchParams.get("h");return n?t.searchParams.delete("h"):t.searchParams.set("h","1"),(0,r.jsxs)(a.A,{full:!0,variant:"warning",className:h.Flash,children:[(0,r.jsx)(d.A,{icon:l.AlertIcon}),(0,r.jsxs)("span",{children:["This file contains bidirectional or hidden Unicode text that may be interpreted or compiled differently than what appears below. To review, open the file in an editor that reveals hidden Unicode characters."," ",(0,r.jsx)(s.A,{inline:!0,href:"https://github.co/hiddenchars",target:"_blank",rel:"noreferrer",children:"Learn more about bidirectional Unicode characters"})]}),(0,r.jsx)(c.Q,{as:"a",onClick:()=>{window.location.href=t.href},size:"small",className:h.Button,children:n?"Hide revealed characters":"Show hidden characters"})]})}function g(){return(0,r.jsx)(u.A,{direction:"e",text:"This line has hidden Unicode characters",children:(0,r.jsx)(d.A,{icon:l.AlertIcon,className:h.Octicon})})}function b({char:e}){return(0,r.jsx)("span",{className:"hidden-unicode-replacement padded",children:e})}function v(e){return w(e)?e.replaceAll(p,e=>x.get(e)??""):e}function j(e){return w(e)?e.split(f).map((e,t)=>{let n=x.get(e);return n?(0,r.jsx)(b,{char:n},t):e}):null}function w(e){return m.test(e)}function N(){return!!i.cg&&"1"===new URL(i.cg.location.href,i.cg.location.origin).searchParams.get("h")}try{y.displayName||(y.displayName="HiddenUnicodeAlert")}catch{}try{g.displayName||(g.displayName="HiddenUnicodeTooltip")}catch{}try{b.displayName||(b.displayName="HiddenUnicodeCharacter")}catch{}},62985:(e,t,n)=>{n.d(t,{M:()=>d,U:()=>c});var r=n(74848),i=n(38621),o=n(75177),l=n(17606),a=n(63205),s=n(86565);function c({linesData:e,onLineStickOrUnstick:t,setIsCollapsed:n,tabSize:i,extraLeftPadding:c,contentWidth:u}){let h=[...(0,a.bP)().keys()],m=(0,l.o)("react-line-numbers");return(0,r.jsx)(r.Fragment,{children:h.map(l=>{let h=(0,a.N9)(l,e);if(void 0===h||e[h]?.ownedSection===void 0)return null;let p=e[h],f=(0,a.m)(p?.rawText?.length??0,p?.rawText??"",i);return(0,r.jsxs)(o.A,{sx:{position:"absolute",top:m*h,pl:"10px",height:m,whiteSpace:"pre"},children:[u?(0,r.jsx)(s.I,{subtle:!0,lineNumber:l,highlightPosition:{offset:-82,width:u}}):null,(0,r.jsx)(d,{codeLineData:p,setIsCollapsed:n,onLineStickOrUnstick:t,leftOffset:f+(c??0)})]},`expand-row-ellipsis-${l}`)})})}function d({codeLineData:e,leftOffset:t,setIsCollapsed:n,onLineStickOrUnstick:o}){let{lineNumber:l,ownedSection:s}=e;return(0,a.$0)(l)?(0,r.jsx)("button",{"aria-label":"Expand row",className:"Button Button--iconOnly Button--invisible Button--small px-2 py-0 ml-1 border-0 expand-row-ellipsis",style:{left:t},onMouseDown:t=>{(0,a.ny)(l),n?.(!1),s&&(s.collapsed=!1,o?.(e,!0)),t.preventDefault()},children:(0,r.jsx)(i.EllipsisIcon,{})}):null}try{c.displayName||(c.displayName="CodeFoldingEllipsisOverlay")}catch{}try{d.displayName||(d.displayName="ExpandRowEllipsis")}catch{}},86565:(e,t,n)=>{n.d(t,{I:()=>_,S:()=>w});var r=n(74848),i=n(21325),o=n(17515),l=n(70979),a=n(75177),s=n(34164),c=n(96540),d=n(76087),u=n(91617),h=n(17606),m=n(92659),p=n(63205),f=n(62578),x=n(56369),y=n(62985),g=n(1064),b=n(53491),v=n(84228);let j=()=>{},w=c.memo(N);function N({codeLineData:e,codeLineClassName:t,id:n,onClick:l,setIsCollapsed:a,onLineStickOrUnstick:w,virtualOffset:N,codeLineToSectionMap:k,stylingDirectivesLine:C,virtualKey:A,forceVisible:S,measureRef:B,copilotAccessAllowed:I,shouldUseInert:L}){let{lineNumber:T,rawText:E}=e,R=(0,p.$0)(T),{tabSize:O}=(0,d.A)(),D=(0,g.kT)(T),F=(0,c.useRef)(null),$=D&&F.current?(0,p.jH)(D,F.current,T,O,E??""):void 0,M=k?.get(T)?.length??0,P=(0,m.ds)(),z=(0,u.L)(e,!0,P,w??j,M),H=(0,i.ud)().codeWrappingOption,W=(0,b.WB)(T),U=(0,b.e6)(T),V=(0,h.o)("react-line-numbers"),G=D?.start.line===T,Y=(0,c.useRef)(null),q=(0,c.useRef)(null);return(0,o.N)(()=>{G&&q.current?.setAnchor(Y.current)},[G]),(0,r.jsx)("div",{ref:e=>{B?.(e),z(e)},"data-key":A,className:(0,s.$)(t,"react-code-text react-code-line-contents",N&&"virtual",H.enabled&&B&&"react-code-text-cell"),style:{transform:N?`translateY(${N}px)`:void 0,minHeight:H.enabled?V:"auto"},onClick:l,children:(0,r.jsxs)("div",{ref:Y,children:[D&&(0,r.jsx)(_,{lineNumber:T,highlightPosition:$}),R&&!D&&(0,r.jsx)(_,{subtle:!0,lineNumber:T,highlightPosition:$}),W&&W.length>0&&(0,r.jsx)(x.U,{symbols:W,focusedSymbol:U,sx:{paddingLeft:"10px",width:"auto"},lineNumber:T}),(0,r.jsx)(v.FS,{id:n,lineNumber:T,stylingDirectivesLine:C,current:!!D,rawText:E,forceVisible:S,ref:F,shouldUseInert:L}),H.enabled&&(0,r.jsx)(y.M,{codeLineData:e,setIsCollapsed:a,onLineStickOrUnstick:w,leftOffset:0}),G&&I&&(0,r.jsx)(f.z8,{ref:q,rowBeginNumber:D.start.line,rowEndNumber:D.end.line,id:"code-line-copilot-button"})]})})}function _({lineNumber:e,highlightPosition:t,subtle:n}){let i=(0,l.Y)(),o=t?.offset!==void 0,s=t?.width!==void 0,c=t?.offset??-72,d=t?.width??0;return(0,r.jsx)(a.A,{sx:{position:"absolute",backgroundColor:n?"neutral.subtle":"var(--bgColor-attention-muted, var(--color-attention-subtle))",height:"100%",opacity:".6",boxShadow:n?"inset 2px 0 0 var(--fgColor-muted,  var(--color-fg-subtle))":"inset 2px 0 0 var(--fgColor-attention, var(--color-attention-fg))",top:i?"-3px":0,left:`${c}px`,width:s?`${o&&s?d:d+82}px`:"calc(100% + 72px)",pointerEvents:"none"}},`highlighted-line-${e}`)}try{w.displayName||(w.displayName="CodeLine")}catch{}try{N.displayName||(N.displayName="CodeLineUnmemoized")}catch{}try{_.displayName||(_.displayName="HighlighterElement")}catch{}},75559:(e,t,n)=>{n.d(t,{FL:()=>g,Ru:()=>j,_x:()=>v});var r=n(74848),i=n(21325),o=n(75177),l=n(96540),a=n(3147),s=n(52687),c=n(63205),d=n(77378),u=n(62985),h=n(86565),m=n(60040),p=n(17606);function f(e){window.scroll({top:e,left:0})}function x(e,t){return Array(t-e).fill(null).map((t,n)=>n+e)}var y=n(48869);let g=l.memo(l.forwardRef(b));function b({linesData:e,onLineNumberClick:t,codeSections:n,codeLineToSectionMap:g,onLineStickOrUnstick:b,tabSize:w,contentWidth:N,onCollapseToggle:_,cursorContainerRef:k,textAreaRef:C,materializeAllLines:A,copilotAccessAllowed:S},B){let I=(0,l.useRef)(null),L=(0,l.useRef)(null),T=(0,l.useRef)(null),E=(0,s.fY)(),R=(0,l.useRef)(!0),O=(0,l.useRef)(!0),D=(0,l.useRef)(!0),{stylingDirectives:F}=(0,a.PL)();(0,l.useEffect)(()=>{if(C&&C.current){C.current.onscroll=()=>{if(L.current&&C?.current){if(!D.current){D.current=!0;return}if(C.current.scrollLeft===L.current.scrollLeft)return;R.current=!(O.current&&D.current),L.current.scrollLeft=C.current.scrollLeft}k&&k.current&&C?.current&&(k.current.scrollLeft=C.current.scrollLeft)};let e=C.current;return()=>{e&&(e.onscroll=null)}}},[C,I,E,k]);let $=(0,i.ud)().codeWrappingOption.enabled,M=(0,c.Gb)(),P=function({parentRef:e,lineCount:t,materializeAllLines:n}){let r=(0,p.o)("react-line-numbers"),o=(0,l.useMemo)(()=>()=>r,[r]),a=(0,i.ud)().codeWrappingOption.enabled,s=(0,l.useMemo)(()=>{var e;return e=t,function(t){if(e<150)return x(0,e);let n=(0,m.vp)(t);if(0===n.length)return n;let r=n[0],i=Math.min(75,e-n[n.length-1]),o=x(0,Math.min(75,r)),l=x(e+1-i,e);return o.concat(n,l)}},[t]);return(0,m.CK)({parentRef:e,size:t,overscan:n?Number.MAX_SAFE_INTEGER:100,scrollToFn:f,estimateSize:o,rangeExtractor:s,measureSize:a?void 0:o})}({parentRef:I,lineCount:e.length,materializeAllLines:!!A});(0,l.useImperativeHandle)(B,()=>({scrollToTop:()=>{(0,c.si)(0)||P.scrollToIndex(0,{align:"start"})},scrollToLine:(e,t)=>{P.scrollToIndex(e,{align:"start"});let n=I.current;n&&n.scroll({left:j(n,e,t)})}}));let z=E?{overflowX:"overlay",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}:{overflowX:"auto"};return(0,r.jsxs)(o.A,{ref:I,sx:{pointerEvents:E?"none":"auto"},onScroll:e=>v(e,C),children:[(0,r.jsx)(o.A,{className:"react-code-line-container",ref:L,sx:z,tabIndex:0,onScroll:()=>{if(E&&L.current&&T.current){if(!O.current){O.current=!0;return}T.current.scrollLeft!==L.current.scrollLeft&&(D.current=!(O.current&&R.current),T.current.scrollLeft=L.current.scrollLeft)}},children:(0,r.jsxs)(o.A,{className:"react-code-file-contents",role:"presentation","aria-hidden":!0,"data-tab-size":w,"data-testid":"code-lines-container","data-paste-markdown-skip":!0,sx:{tabSize:w,position:"relative",width:N,maxWidth:$?"100%":"unset"},style:{height:P.totalSize},"data-hpc":!0,children:[(0,r.jsx)("div",{className:"react-line-numbers",style:{pointerEvents:"auto",height:P.totalSize,position:"relative",zIndex:2},children:P.virtualItems.map(i=>{let o=e[i.index];return(0,r.jsx)(y.Kn,{codeLineData:o,onClick:t,ownedCodeSections:n,onLineStickOrUnstick:b,onCollapseToggle:_,virtualOffset:i.start,copilotAccessAllowed:S},`line-number-${o.lineNumber}-content:${o.rawText?.substring(0,100)}`)})}),(0,r.jsx)("div",{className:"react-code-lines",style:{height:P.totalSize},children:P.virtualItems.map(t=>{let n=e[t.index];return(0,r.jsx)(h.S,{codeLineData:n,stylingDirectivesLine:n.stylingDirectivesLine??(F?F[n.lineNumber-1]:void 0),shouldUseInert:M,codeLineClassName:n.codeLineClassName,id:`LC${n.lineNumber}`,onLineStickOrUnstick:b,setIsCollapsed:_,codeLineToSectionMap:g,virtualOffset:t.start,virtualKey:t.key,measureRef:t.measureRef,copilotAccessAllowed:S},`line-number-${n.lineNumber}-content:${n.rawText?.substring(0,100)}`)})}),(0,r.jsx)(d.A,{shouldNotOverrideCopy:E,containerRef:E?C:I}),!$&&(0,r.jsx)(u.U,{linesData:e,onLineStickOrUnstick:b,setIsCollapsed:_,tabSize:w,extraLeftPadding:82})]})}),E&&N&&L.current&&L.current.clientWidth<N?(0,r.jsx)(o.A,{sx:{width:"100%",pointerEvents:"auto",overflowX:"auto",overflowY:"hidden",height:"17px",position:"sticky",bottom:0,zIndex:2},onScroll:()=>{if(T.current&&C?.current){if(!R.current){R.current=!0;return}T.current.scrollLeft!==C.current.scrollLeft&&(O.current=!(D.current&&R.current),C.current.scrollLeft=T.current.scrollLeft)}},ref:T,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onMouseUp:e=>e.preventDefault(),children:(0,r.jsx)(o.A,{sx:{width:N,height:"1px"}})}):null]})}function v(e,t){let n=e.target;t?.current?.scrollTo(n.scrollLeft,n.scrollTop)}function j(e,t,n){if(!n)return 0;let r=(0,c.a2)(t,n);if(!r)return 0;let i=e.getBoundingClientRect(),o=r.getBoundingClientRect();return i.left+i.width-e.scrollLeft-(o.left+o.width)>0?0:r.offsetLeft}try{g.displayName||(g.displayName="CodeLines")}catch{}try{b.displayName||(b.displayName="CodeLinesUnmemoized")}catch{}},95321:(e,t,n)=>{n.d(t,{P9:()=>_,OL:()=>C,kd:()=>k,G:()=>S});var r=n(74848),i=n(60183),o=n(21325),l=n(75177),a=n(96540),s=n(17606),c=n(52687),d=n(56297),u=n(63205),h=n(77378),m=n(62985),p=n(75559),f=n(60638),x=n(3124),y=n(93783),g=n(13233),b=n(91617),v=n(92659);function j({linesData:e,onLineStickOrUnstick:t,codeLineToSectionMap:n}){let i=(0,v.ds)(),o=(0,s.o)("react-line-numbers");return void 0===n?null:(0,r.jsx)(r.Fragment,{children:e.map(l=>{let a=l.lineNumber,s=n?.get(a)?.length??0;if(void 0===a||void 0===l.ownedSection)return null;let c=(0,u.N9)(a,e);return(0,r.jsx)(w,{className:"symbol-highlight react-code-text",lineData:e[c],stickyHeaderHeight:i,numParents:s,onLineStickOrUnstick:t??g.l,sx:{position:"absolute",top:o*(c-1),pl:"10px",height:o,whiteSpace:"pre"}},`observer-overlay-${c}-lineNumber-no-virtualization`)})})}function w({lineData:e,onLineStickOrUnstick:t,numParents:n,stickyHeaderHeight:i,className:o,sx:a}){let s=(0,b.L)(e,!0,i,t,n);return(0,r.jsx)(l.A,{ref:e=>{s(e)},className:o,sx:{mb:"-20px",color:"transparent",position:"absolute",maxHeight:"6rem",overflow:"hidden",width:"100%",display:"inline-block",userSelect:"none",...a},"data-testid":"sticky-line-observer"})}try{j.displayName||(j.displayName="StickyLineObserverOverlay")}catch{}try{w.displayName||(w.displayName="StickyLineObserverLine")}catch{}var N=n(84228);let _=a.memo(a.forwardRef(A)),k=60,C=3500;function A({linesData:e,onLineNumberClick:t,codeSections:n,nonTruncatedLinesData:i,codeLineToSectionMap:o,onLineStickOrUnstick:g,colorizedLines:b,tabSize:v,contentWidth:w,cursorContainerRef:_,onCollapseToggle:C,textAreaRef:A,copilotAccessAllowed:S},B){let I=(0,a.useRef)(null),L=(0,a.useRef)(null),T=(0,a.useRef)(null),E=(0,c.fY)(),R=(0,d.u)(),O=(0,a.useRef)(!0),D=(0,a.useRef)(!0),F=(0,a.useRef)(!0),$=(0,s.o)("react-line-numbers"),M=(0,a.useRef)([...Array(Math.floor(e.length/k)+1).keys()]);(0,a.useImperativeHandle)(B,()=>({scrollToTop:()=>{(0,u.si)(0)||window.scrollTo({left:0,top:0})},scrollToLine:(e,t)=>{let n=I.current;n&&setTimeout(()=>window.scrollTo({left:(0,p.Ru)(n,e,t),top:$*e}),0)}})),(0,a.useEffect)(()=>{if(A&&A.current){A.current.onscroll=()=>{if(L.current&&A?.current){if(!F.current){F.current=!0;return}if(A.current.scrollLeft===L.current.scrollLeft)return;O.current=!(D.current&&F.current),L.current.scrollLeft=A.current.scrollLeft}_&&_.current&&A?.current&&(_.current.scrollLeft=A.current.scrollLeft)};let e=A.current;return()=>{e&&(e.onscroll=null)}}},[A,I,E,_]),(0,a.useEffect)(()=>{M.current=[...Array(Math.floor(e.length/k)+1).keys()],R()},[e]);let P=E?{overflowX:"overlay",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}:{overflowX:"auto"};return(0,r.jsxs)(l.A,{ref:I,sx:{pointerEvents:E?"none":"auto"},onScroll:e=>(0,p._x)(e,A),children:[(0,r.jsx)(l.A,{ref:L,sx:P,tabIndex:0,onScroll:()=>{if(E&&L.current&&T.current){if(!D.current){D.current=!0;return}T.current.scrollLeft!==L.current.scrollLeft&&(F.current=!(D.current&&O.current),T.current.scrollLeft=L.current.scrollLeft)}},children:(0,r.jsxs)(l.A,{className:"react-code-file-contents",role:"presentation","aria-hidden":!0,"data-tab-size":v,"data-paste-markdown-skip":!0,sx:{tabSize:v,position:"relative",width:w,maxWidth:"unset"},"data-hpc":!0,children:[(0,r.jsx)("div",{className:"react-line-numbers-no-virtualization",style:{pointerEvents:"auto",position:"relative",zIndex:2},children:M.current.map(i=>{let l=e.slice(i*k,Math.min(i*k+k,e.length));return(0,r.jsx)("div",{className:"react-no-virtualization-wrapper-lines",children:l.map(e=>(0,r.jsx)(y.m,{codeLineData:e,onClick:t,ownedCodeSections:n,onLineStickOrUnstick:g,onCollapseToggle:C,codeLineToSectionMap:o,copilotAccessAllowed:S},`line-number-${e.lineNumber}-content:${e.rawText?.substring(0,100)}`))},`line-number-wrapper-${i}-content:${l[0]?.rawText?.substring(0,100)}`)})}),(0,r.jsxs)("div",{className:"react-code-lines",children:[(0,r.jsx)(x.R,{linesData:e}),(0,r.jsx)(f.n,{linesData:e,copilotAccessAllowed:S}),(0,r.jsx)(N.Bg,{colorizedLines:b,linesData:i}),(0,r.jsx)(j,{linesData:e,onLineStickOrUnstick:g,codeLineToSectionMap:o}),(0,r.jsx)(m.U,{linesData:e,onLineStickOrUnstick:g,setIsCollapsed:C,tabSize:v,contentWidth:w})]}),(0,r.jsx)(h.A,{shouldNotOverrideCopy:E,containerRef:E?A:I})]})}),E&&w&&L.current&&L.current.clientWidth<w?(0,r.jsx)(l.A,{sx:{width:"100%",pointerEvents:"auto",overflowX:"auto",overflowY:"hidden",height:"17px",position:"sticky",bottom:0,zIndex:2},onScroll:()=>{if(T.current&&A?.current){if(!O.current){O.current=!0;return}T.current.scrollLeft!==A.current.scrollLeft&&(D.current=!(F.current&&O.current),A.current.scrollLeft=T.current.scrollLeft)}},ref:T,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onMouseUp:e=>e.preventDefault(),children:(0,r.jsx)(l.A,{sx:{width:w,height:"1px"}})}):null]})}function S(e){let t=(0,o.ud)().codeWrappingOption.enabled,n=(0,i.u)("react_blob_overlay"),r=(0,u.Gb)(),l=(0,u.hO)();return n&&!t&&!(l&&!r)&&e<C}try{_.displayName||(_.displayName="CodeLinesNoVirtualization")}catch{}try{A.displayName||(A.displayName="CodeLinesNoVirtualizationUnmemoized")}catch{}},1064:(e,t,n)=>{n.d(t,{Df:()=>c,iU:()=>s,kT:()=>d});var r=n(74848),i=n(11303),o=n(58779),l=n(96540);let a=l.createContext(new i.Lj(void 0));function s({highlightedLines:e,children:t}){let n=(0,o.AI)(e);return(0,l.useEffect)(()=>{n.value=e},[n,e]),(0,r.jsx)(a.Provider,{value:n,children:t})}function c(){return(0,o.HN)(l.useContext(a))}function d(e){let t=l.useContext(a),n=(0,o.Sk)(t,t=>t&&e>=t.start.line&&e<=t.end.line?t:void 0);return(0,o.HN)(n)}try{a.displayName||(a.displayName="HighlightedLineContext")}catch{}try{s.displayName||(s.displayName="HighlightedLinesProvider")}catch{}},60638:(e,t,n)=>{n.d(t,{n:()=>m});var r=n(74848),i=n(17515),o=n(70979),l=n(75177),a=n(96540),s=n(76087),c=n(17606),d=n(63205),u=n(62578),h=n(1064);function m({linesData:e,copilotAccessAllowed:t}){let n=(0,h.Df)(),{tabSize:l}=(0,s.A)(),m=(0,c.o)("react-line-numbers"),f=(0,o.Y)(),x=(0,a.useRef)(null),y=(0,a.useRef)(null),g=n?(0,d.N9)(n.start.line,e):0,b=n?.end.line?(0,d.N9)(n.end.line,e):g,[v,j]=(0,a.useState)(f?g*m-3:g*m);if((0,i.N)(()=>{n&&(y.current?.setAnchor(x.current),j(f?g*m-3:g*m))},[n,f,m,g]),!n)return null;let w=[];for(let t=g;t<=b;t++){let r=(0,d.zS)("",t+1);if(!r)continue;let i=(0,d.jH)(n,r,t+1,l,e[t+1]?.rawText??"")??{};w.push({position:i,lineNumber:t})}return(0,r.jsxs)("div",{ref:x,style:{position:"relative",top:v+10},children:[w.map(({position:e,lineNumber:t})=>(0,r.jsx)(p,{highlightPosition:e,lineNumber:t,startingLineNumber:g},`line-${t}-highlight-${e.offset}`)),t&&(0,r.jsx)(u.z8,{ref:y,rowBeginNumber:n.start.line,rowEndNumber:n.end.line,recalcPosition:v,id:"code-line-copilot-button"})]})}function p({startingLineNumber:e,lineNumber:t,highlightPosition:n,subtle:i}){let o=(0,c.o)("react-line-numbers"),a=(t-e)*o;return(0,r.jsx)(l.A,{className:"line-highlight",sx:{position:"absolute",backgroundColor:i?"neutral.subtle":"var(--bgColor-attention-muted, var(--color-attention-subtle))",height:o,opacity:".6",boxShadow:i?"inset 2px 0 0 var(--borderColor-neutral-emphasis, var(--color-fg-subtle))":"inset 2px 0 0 var(--bgColor-attention-emphasis, var(--color-attention-fg))",left:n&&n.offset?`${n.offset-10}px`:"-82px",top:`${a-10}px`,width:n&&n.width?`${n.width+82}px`:"calc(100% + 82px)",pointerEvents:"none"}},`highlighted-line-${t}`)}try{m.displayName||(m.displayName="HighlightedLinesOverlay")}catch{}try{p.displayName||(p.displayName="LineHighlight")}catch{}},3124:(e,t,n)=>{n.d(t,{R:()=>s});var r=n(74848),i=n(17606),o=n(63205),l=n(56369),a=n(53491);function s({linesData:e}){let t=(0,a.VT)(),n=(0,a.Gp)(),s=(0,i.o)("react-line-numbers");return(0,r.jsx)(r.Fragment,{children:[...t.values()].map(t=>{let i=t[0]?.lineNumber;if(void 0===i)return null;let a=(0,o.N9)(i,e)+1;return(0,r.jsx)(l.U,{className:"symbol-highlight react-code-text",lineNumber:a,symbols:t,focusedSymbol:n,sx:{position:"absolute",top:s*(a-1),pl:"10px",height:s,whiteSpace:"pre"}},`overlay-${a}-lineNumber-no-virtualization`)})})}try{s.displayName||(s.displayName="HighlightedSymbolsOverlay")}catch{}},48869:(e,t,n)=>{n.d(t,{Kn:()=>j,_Y:()=>_,cw:()=>N});var r=n(74848),i=n(98637),o=n(66871),l=n(21325),a=n(17515),s=n(38621),c=n(75177),d=n(53110),u=n(34164),h=n(96540),m=n(48234),p=n(52687),f=n(63205),x=n(96464),y=n(89681),g=n(62578),b=n(42490),v=n(1064);let j=h.memo(w);function w({codeLineData:e,onClick:t,ownedCodeSections:n,onCollapseToggle:s,preventClick:c,onLineStickOrUnstick:d,virtualOffset:j,copilotAccessAllowed:w}){let{lineNumber:k,ownedSection:C,codeLineClassName:A,isStartLine:S,codeownersLineError:B,hiddenUnicode:I}=e,{sendRepoClickEvent:L}=(0,i.T)(),T=(0,p.Jo)(),[E,R]=(0,h.useState)(!1),O=(0,h.useCallback)(e=>{let n,r;if(e.defaultPrevented)return;let i=parseInt(e.currentTarget.getAttribute("data-line-number"),10),l=(0,m.$c)(`L${i}`),a=T.current;if(a&&(a.start.line!==a.end.line||a.start.column!==a.end.column))n=a.start,r=a.end;else{let e=window.getSelection()?.rangeCount?window.getSelection()?.getRangeAt(0):null;e&&(n=(0,f.um)(e.startContainer,e.startOffset),r=(0,f.um)(e.endContainer,e.endOffset))}let s=!1;n&&r&&n.line<=i&&r.line>=i&&(s=!0,l={anchorPrefix:"",blobRange:{start:n,end:r}});let{blobRange:c}=l,d=(0,m.eC)(window.location.hash);d&&e.shiftKey&&!s?(L("BLOB.MULTILINE"),l.blobRange={start:d.start,end:c.end}):L("BLOB.LINE");let u=(0,m.JB)(l);(0,o.Zu)(u),t?.(e)},[t,T,L]),D=(0,v.kT)(k),F=D?.start.line===k,$=D&&D.start.line<k&&D.end.line>=k||F&&D?.start.column===null,M=(0,h.useRef)(null),P=(0,h.useRef)(null),z=(0,h.useRef)(null);(0,a.N)(()=>{F&&P.current?.setAnchor(M.current)},[F]);let{codeFoldingOption:H}=(0,l.ud)(),W=H.enabled;(0,h.useEffect)(()=>{!H.enabled&&((0,f.X5)(),s?.(!1),C&&(C.collapsed=!1))},[H.enabled,C,s]);let U=e=>{w&&(R(e),setTimeout(()=>z.current?.setAnchor(e?M.current:null),0))};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{ref:M,"data-line-number":k,className:(0,u.$)(A,"react-line-number react-code-text",j&&"virtual",c&&"prevent-click",$&&"highlighted-line"),style:j?{paddingRight:"16px",transform:`translateY(${j}px)`}:{paddingRight:"16px"},onMouseDown:c?void 0:O,onMouseEnter:w?()=>U(!0):void 0,onMouseLeave:w?()=>U(!1):void 0,children:[k,B&&(0,r.jsx)(N,{children:(0,r.jsx)(x.pO,{})}),I&&(0,r.jsx)(N,{children:(0,r.jsx)(y.T4,{})}),W&&S&&C&&n&&(0,r.jsx)(N,{displayRight:!0,children:(0,r.jsx)(_,{codeLineData:e,onCollapseToggle:s,onLineStickOrUnstick:d})})]}),F&&(0,r.jsx)(b.Ay,{codeLineClassName:A,ref:P,rowBeginId:`LG${D.start.line}`,rowBeginNumber:D.start.line,rowEndNumber:D.end.line,rowEndId:`LG${D.end.line}`}),w&&E&&(0,r.jsx)("div",{children:(0,r.jsx)(g.z8,{ref:z,rowBeginNumber:k,rowEndNumber:k,id:"code-line-copilot-button"})})]})}function N({children:e,sx:t,displayRight:n}){return(0,r.jsx)(c.A,{as:"span",sx:{...n?void 0:{left:"-4px"},margin:`1px ${n?"8px":"1px"}`,position:"absolute",zIndex:"1",...t},children:e})}function _({codeLineData:e,onCollapseToggle:t,onLineStickOrUnstick:n}){let i=(0,f.$0)(e.lineNumber),o=(0,h.useCallback)(r=>{let{lineNumber:i,ownedSection:o}=e;o&&(o.collapsed=!1),t?.(!1),(0,f.ny)(i),n?.(e,!0),r.preventDefault()},[e,t,n]),l=(0,h.useCallback)(n=>{let{lineNumber:r,ownedSection:i}=e;i&&(i.collapsed=!0),t?.(!0),(0,f.E8)(r),n.preventDefault()},[e,t]);return i?(0,r.jsx)(c.A,{"aria-label":"Expand code section",onMouseDown:o,role:"button",sx:{position:"absolute"},children:(0,r.jsx)(d.A,{icon:s.ChevronRightIcon})}):(0,r.jsx)(c.A,{"aria-label":"Collapse code section",onMouseDown:l,role:"button",sx:{position:"absolute"},children:(0,r.jsx)(d.A,{icon:s.ChevronDownIcon})})}try{j.displayName||(j.displayName="LineNumber")}catch{}try{w.displayName||(w.displayName="LineNumberUnmemoized")}catch{}try{N.displayName||(N.displayName="CodeAlert")}catch{}try{_.displayName||(_.displayName="CodeFoldingChevron")}catch{}},93783:(e,t,n)=>{n.d(t,{m:()=>b});var r=n(74848),i=n(98637),o=n(66871),l=n(21325),a=n(17515),s=n(34164),c=n(96540),d=n(48234),u=n(52687),h=n(63205),m=n(96464),p=n(89681),f=n(62578),x=n(42490),y=n(1064),g=n(48869);let b=c.memo(v);function v({codeLineData:e,onClick:t,ownedCodeSections:n,onCollapseToggle:b,preventClick:v,onLineStickOrUnstick:j,virtualOffset:w,copilotAccessAllowed:N}){let{lineNumber:_,ownedSection:k,codeLineClassName:C,isStartLine:A,codeownersLineError:S,hiddenUnicode:B}=e,{sendRepoClickEvent:I}=(0,i.T)(),L=(0,u.Jo)(),T=(0,c.useCallback)(e=>{let n,r;if(e.defaultPrevented)return;let i=parseInt(e.currentTarget.getAttribute("data-line-number"),10),l=(0,d.$c)(`L${i}`),a=L.current;if(a&&(a.start.line!==a.end.line||a.start.column!==a.end.column))n=a.start,r=a.end;else{let e=window.getSelection()?.rangeCount?window.getSelection()?.getRangeAt(0):null;e&&(n=(0,h.um)(e.startContainer,e.startOffset),r=(0,h.um)(e.endContainer,e.endOffset))}let s=!1;n&&r&&n.line<=i&&r.line>=i&&(s=!0,l={anchorPrefix:"",blobRange:{start:n,end:r}});let{blobRange:c}=l,u=(0,d.eC)(window.location.hash);u&&e.shiftKey&&!s?(I("BLOB.MULTILINE"),l.blobRange={start:u.start,end:c.end}):I("BLOB.LINE");let m=(0,d.JB)(l);(0,o.Zu)(m),t?.(e)},[t,L,I]),E=(0,y.kT)(_),R=E?.start.line===_,O=E&&E.start.line<_&&E.end.line>=_||R&&E?.start.column===null,D=(0,c.useRef)(null),F=(0,c.useRef)(null),$=(0,c.useRef)(null),[M,P]=(0,c.useState)(!1);(0,a.N)(()=>{R&&F.current?.setAnchor(D.current)},[R]);let{codeFoldingOption:z}=(0,l.ud)(),H=z.enabled,W=e=>{N&&(P(e),setTimeout(()=>$.current?.setAnchor(e?D.current:null),0))};return(0,c.useEffect)(()=>{!z.enabled&&((0,h.X5)(),b?.(!1),k&&(k.collapsed=!1))},[z.enabled,k,b]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{ref:e=>{D.current=e},"data-line-number":_,className:(0,s.$)(C,"react-line-number react-code-text",w&&"virtual",v&&"prevent-click",O&&"highlighted-line"),style:w?{paddingRight:"16px",transform:`translateY(${w}px)`}:{paddingRight:"16px"},onMouseDown:v?void 0:T,onMouseEnter:N?()=>W(!0):void 0,onMouseLeave:N?()=>W(!1):void 0,children:[_,S&&(0,r.jsx)(g.cw,{children:(0,r.jsx)(m.pO,{})}),B&&(0,r.jsx)(g.cw,{children:(0,r.jsx)(p.T4,{})}),H&&A&&k&&n&&(0,r.jsx)(g.cw,{displayRight:!0,children:(0,r.jsx)(g._Y,{codeLineData:e,onCollapseToggle:b,onLineStickOrUnstick:j})})]}),R&&(0,r.jsx)(x.Ay,{codeLineClassName:C,ref:F,rowBeginId:`LG${E.start.line}`,rowBeginNumber:E.start.line,rowEndNumber:E.end.line,rowEndId:`LG${E.end.line}`}),N&&M&&(0,r.jsx)("div",{children:(0,r.jsx)(f.z8,{ref:$,rowBeginNumber:_,rowEndNumber:_,id:"code-line-copilot-button"})})]})}try{b.displayName||(b.displayName="LineNumberNoVirtualziation")}catch{}try{v.displayName||(v.displayName="LineNumberNoVirtualizationUnmemoized")}catch{}},53491:(e,t,n)=>{n.d(t,{Gp:()=>h,VT:()=>c,WB:()=>d,e6:()=>u,gt:()=>s});var r=n(74848),i=n(11303),o=n(58779),l=n(96540);let a=(0,l.createContext)({resultsByLineNumber:new i.Es,focusedResult:new i.Lj(void 0)});function s({searchResults:e,focusedSearchResult:t,children:n}){let i=(0,o.R)(),s=(0,o.AI)(void 0!==t?e[t]:void 0);(0,l.useEffect)(()=>{let t=new Map;for(let n of e){let e=n.lineNumber;t.has(e)?t.get(e).push(n):t.set(e,[n])}for(let[e,n]of(i.clear(),t))i.set(e,n)},[i,e]),(0,l.useEffect)(()=>{s.value=void 0!==t?e[t]:void 0},[e,s,t]);let c=(0,l.useMemo)(()=>({resultsByLineNumber:i,focusedResult:s}),[i,s]);return(0,r.jsx)(a.Provider,{value:c,children:n})}function c(){let{resultsByLineNumber:e}=(0,l.useContext)(a);return(0,o.tQ)(e)}function d(e){let{resultsByLineNumber:t}=(0,l.useContext)(a);return(0,o.HN)(t.get(e))}function u(e){let{focusedResult:t}=(0,l.useContext)(a),n=(0,o.Sk)(t,t=>t?.lineNumber===e?t:void 0);return(0,o.HN)(n)}function h(){let{focusedResult:e}=(0,l.useContext)(a);return(0,o.HN)(e)}try{s.displayName||(s.displayName="SearchResultsProvider")}catch{}},84228:(e,t,n)=>{n.d(t,{Bg:()=>f,FS:()=>m});var r=n(74848),i=n(45968),o=n(42728),l=n(80663),a=n(96540),s=n(76087),c=n(52687),d=n(63205),u=n(89681),h=n(95321);let m=a.memo(a.forwardRef(p));function p({id:e,stylingDirectivesLine:t,rawText:n,lineNumber:l,current:a,forceVisible:h,shouldUseInert:m},p){let f=function(){let e=(0,c.fY)(),t=(0,d.hO)(),n=(0,d.Gb)();return!e||n?"plain":t&&!n?"separated-characters-chunked":"data-attribute"}(),{tabSize:x}=(0,s.A)(),y=(0,u.bp)(),g=(0,o.W)(void 0,t,n,h?"plain":f,x,y);return(0,r.jsx)(i.$6,{id:e,className:"react-file-line html-div","data-testid":"code-cell","data-line-number":l,html:g,ref:p,style:{position:"relative"},"aria-current":a?"location":void 0,...m?{inert:"inert"}:{}})}let f=a.memo(x);function x({linesData:e,colorizedLines:t}){let n=function(e,t){let[n]=(0,l.I)(()=>!1,!0,[]);return(0,a.useMemo)(()=>t?t.map((r,i)=>{if(i>=e.length)return null;let o="",l="";return i%h.kd!=0||n||(o=n?"<div>":'<div class="react-no-virtualization-wrapper">'),(i+1)%h.kd!=0&&i!==t.length||n||(l="</div>"),`${o}<div id="LC${i+1}" class="react-code-text react-code-line-contents-no-virtualization react-file-line html-div ${e[i]?.codeLineClassName}">${0!==r.length?r:`
`}</div>${l}`}).join(`
`):"<div></div>",[e,t,n])}(e,t??null),o=(0,d.Gb)();return(0,r.jsx)(i.$6,{...o?{inert:"inert"}:{},html:n})}try{m.displayName||(m.displayName="SyntaxHighlightedLine")}catch{}try{p.displayName||(p.displayName="SyntaxHighlightedLineWithRef")}catch{}try{f.displayName||(f.displayName="SyntaxHighlightedOverlay")}catch{}try{x.displayName||(x.displayName="SyntaxHighlightedOverlayUnmemoed")}catch{}},87453:(e,t,n)=>{n.d(t,{a:()=>o,t:()=>l});var r=n(74848),i=n(75177);let o="find-result-marks-container";function l(){return(0,r.jsx)(i.A,{sx:{position:"fixed",top:0,right:0,height:"100%",width:"15px",transition:"transform 0.3s","&:hover":{transform:"scaleX(1.5)"},zIndex:1},id:o})}try{l.displayName||(l.displayName="ScrollMarksContainer")}catch{}},62578:(e,t,n)=>{n.d(t,{eT:()=>x,z8:()=>y});var r=n(74848),i=n(141),o=n(7956),l=n(88795),a=n(98075),s=n(17515),c=n(75177),d=n(96540),u=n(40961),h=n(76087),m=n(905);let p="copilot-button-positioner",f="copilot-button-container";function x({children:e}){return(0,r.jsxs)(c.A,{id:p,sx:{position:"relative"},children:[e,(0,r.jsx)("div",{id:f})]})}let y=d.memo(d.forwardRef(g));function g({rowBeginNumber:e,rowEndNumber:t,id:n,recalcPosition:c},x){let[y,g]=(0,d.useState)(null);(0,d.useImperativeHandle)(x,()=>({setAnchor:g}));let[b,v]=(0,d.useState)(void 0);(0,s.N)(()=>{let e=()=>requestAnimationFrame(()=>v(function(e,t={x:0,y:0}){let n=document.getElementById(p);if(!e||!n)return{display:"none"};let{top:r,height:i}=e.getBoundingClientRect(),{top:o}=n.getBoundingClientRect(),l=(m.u9-i)/2;return{top:`${r-o-l+t.y+1}px`,right:"37px"}}(y)));return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[y,c]);let{copilotAccessAllowed:j,refInfo:w,path:N}=(0,i.eu)(),_=(0,l.t)(),{language:k,languageID:C}=(0,h.A)(),A=(0,d.useMemo)(()=>({type:"snippet",languageID:C,languageName:k,path:N,range:{start:e,end:t},ref:(0,a.D7)(w.name,w.refType),commitOID:w.currentOid,repoID:_.id,repoName:_.name,repoOwner:_.ownerLogin,url:window.location.href}),[C,k,N,e,t,w,_]),S=void 0===b?null:(0,r.jsx)("div",{style:{alignSelf:"center",position:"absolute",lineHeight:"16px",height:"24px",width:"24px",zIndex:2,...b},children:(0,r.jsx)(o.Ay,{copilotAccessAllowed:j,messageReference:A,id:n})}),B=document.getElementById(f);return B?(0,u.createPortal)(S,B):null}try{x.displayName||(x.displayName="CopilotButtonContainer")}catch{}try{y.displayName||(y.displayName="CopilotButton")}catch{}try{g.displayName||(g.displayName="CopilotButtonWithRef")}catch{}},42490:(e,t,n)=>{n.d(t,{AG:()=>C,Ay:()=>S,Dp:()=>k});var r=n(74848),i=n(54763),o=n(141),l=n(50104),a=n(21113),s=n(88795),c=n(96235),d=n(96679),u=n(17515),h=n(26807),m=n(38621),p=n(10569),f=n(87330),x=n(15385),y=n(52464),g=n(96540),b=n(40961),v=n(68048),j=n(90864),w=n(905),N=n(63205);let _="highlighted-line-menu-container",k="highlighted-line-menu-first-option";function C({children:e}){return(0,r.jsxs)("div",{id:w.uU,className:"position-relative",children:[e,(0,r.jsx)("div",{id:_})]})}let A=g.memo(g.forwardRef(B)),S=A;function B({codeLineClassName:e,offset:t,lineData:n,onLineStickOrUnstick:C,onMenuClose:A,onCollapseToggle:S,openOnLoad:B=!1,cursorRef:I,rowBeginId:L,rowBeginNumber:T,rowEndId:E,rowEndNumber:R},O){let[D,F]=(0,g.useState)(null);(0,g.useImperativeHandle)(O,()=>({setAnchor:F}));let $=g.useRef(null),{githubDevUrl:M}=(0,o.sq)(),{modelsAccessAllowed:P,refInfo:z,path:H}=(0,o.eu)(),W=(0,s.t)(),[U,V]=(0,g.useState)(B?"hidden":"visible"),[G,Y]=(0,g.useState)(void 0);(0,u.N)(()=>{let e=()=>{V("hidden"),requestAnimationFrame(()=>{Y((0,w.sZ)(D,t)),V("visible")})};return e(),d.cg?.addEventListener("resize",e),()=>{d.cg?.removeEventListener("resize",e)}},[D,t]),(0,u.N)(()=>{if(B){I&&F(I.current);let e=window.setTimeout(()=>{Z(!0)},50);return()=>{window.clearTimeout(e)}}},[]);let{newDiscussionPath:q,newIssuePath:K}=(0,j.Y_)(),{refSelectorShortcut:Q}=(0,l.wk)(),[X,Z]=g.useState(!1),{createPermalink:J,getUrl:ee}=(0,a.Z)(),{setShouldBeOpen:et,expandOrCollapseSection:en,openUpRefSelector:er}=(0,w.S9)({lineData:n,onLineStickOrUnstick:C,onMenuClose:A,onCollapseToggle:S,setOpen:Z}),[ei]=(0,h.o)(),eo=J({absolute:!0,params:"1"===ei.get("plain")?"plain=1":void 0}),el=encodeURIComponent(eo),ea=`L${T}${L!==E?`-L${R}`:""}`,es=void 0===G?null:(0,r.jsxs)(p.W,{open:X,onOpenChange:et,children:[(0,r.jsx)(p.W.Anchor,{children:(0,r.jsx)(f.K,{className:e,size:"small",icon:m.KebabHorizontalIcon,"aria-label":`Line ${T} options`,"data-testid":"highlighted-line-menu-button",sx:{alignSelf:"center",zIndex:3,position:"absolute",lineHeight:"16px",height:"24px",width:"24px",visibility:U,...G}})}),(0,r.jsx)(p.W.Overlay,{width:"small",children:(0,r.jsxs)(x.l,{"data-testid":"highlighted-line-menu",children:[T===R&&(0,r.jsx)(x.l.Item,{onClick:()=>{(0,N.kY)(`Copied line ${T}.`);let e=(0,w.zM)(d.XC?.getElementById(`LC${T}`)??null);e&&(0,v.D)(e),et(!1)},onSelect:()=>{(0,N.kY)(`Copied line ${T}.`);let e=(0,w.zM)(d.XC?.getElementById(`LC${T}`)??null);e&&(0,v.D)(e),et(!1)},ref:$,className:k,children:"Copy line"}),T!==R&&(0,r.jsx)(x.l.Item,{onClick:()=>{(0,N.kY)(`Copied lines ${T}-${R}.`);let e="";for(let t=T;t<=R;t++)e+=`${(0,w.zM)(d.XC?.getElementById(`LC${t}`)??null)}${t!==R?`
`:""}`;e&&(0,v.D)(e),et(!1)},onSelect:()=>{(0,N.kY)(`Copied lines ${T}-${R}.`);let e="";for(let t=T;t<=R;t++)e+=`${(0,w.zM)(d.XC?.getElementById(`LC${t}`)??null)}${t!==R?`
`:""}`;e&&(0,v.D)(e),et(!1)},className:k,children:"Copy lines"}),eo&&(0,r.jsx)(x.l.Item,{onClick:()=>{(0,N.kY)("Copied permalink."),(0,v.D)(eo),et(!1)},onSelect:()=>{(0,N.kY)("Copied permalink."),(0,v.D)(eo),et(!1)},children:"Copy permalink"}),(0,r.jsx)(x.l.LinkItem,{href:ee({action:"blame",hash:ea}),children:"View git blame"}),K&&eo&&(0,r.jsx)(x.l.LinkItem,{href:`${K}?permalink=${el}`,children:"Reference in new issue"}),q&&eo&&(0,r.jsx)(x.l.LinkItem,{href:`${q}?permalink=${el}`,children:"Reference in new discussion"}),M&&(0,r.jsx)(x.l.LinkItem,{href:M+d.cg?.location.pathname.substring(1),children:"View file in GitHub.dev"}),T===R&&n&&(0,r.jsxs)(x.l.Item,{onClick:en,onSelect:en,children:[n.ownedSection&&n.ownedSection.collapsed?"Expand":"Collapse"," current section"]}),(0,r.jsxs)(x.l.Item,{onClick:er,onSelect:er,children:["View file in different branch/tag",(0,r.jsx)(x.l.TrailingVisual,{children:(0,r.jsx)(i.E,{children:Q.text})})]}),P&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.W.Divider,{}),(0,r.jsxs)(x.l.LinkItem,{href:(0,c.Da2)({registry:"azure-openai",name:"gpt-4o",commit:z.currentOid,filePath:H,repoOwner:W.ownerLogin,repoName:W.name,beginLine:T,endLine:R}),target:"_blank",children:[(0,r.jsx)("p",{className:"mb-1",children:"Open as prompt in GitHub Models"}),(0,r.jsx)(y.A,{sx:{color:"fg.muted",fontSize:0},children:"This tool uses AI to process your code"})]})]})]})})]}),ec=d.XC?.getElementById(_);return ec?(0,b.createPortal)(es,ec):null}try{C.displayName||(C.displayName="HighlightedLineMenuContainer")}catch{}try{A.displayName||(A.displayName="HighlightedLineMenu")}catch{}try{B.displayName||(B.displayName="HighlightedLineMenuWithRef")}catch{}},56369:(e,t,n)=>{n.d(t,{U:()=>l,p:()=>a});var r=n(74848),i=n(21325),o=n(52464);function l({symbols:e,focusedSymbol:t,lineNumber:n,sx:l,isNotUsingWhitespace:s,className:c}){let d=0,u=e.length>0?e[0].bodyText:"",h=`overlay-${n}-${l?"blob":"panel"}-${u}`,m=(0,i.ud)().codeWrappingOption,p=e.length>0&&!s?e[0].leadingWhitespace??0:0;return(0,r.jsxs)(o.A,{className:c,sx:{mb:"-20px",color:"transparent",position:"absolute",overflowWrap:m.enabled?"anywhere":"unset",maxWidth:m.enabled?"100%":"unset",maxHeight:"6rem",overflow:"hidden",width:"100%",display:"inline-block",userSelect:"none",...l},children:[e.map(e=>{let n=(0,r.jsxs)("span",{children:[(0,r.jsx)(o.A,{sx:{userSelect:"none",visibility:"hidden"},children:u.substring(d,e.ident.start.column+p)}),(0,r.jsx)(o.A,{sx:{bg:e===t?"#ff9632":"var(--bgColor-attention-muted)",zIndex:e===t?10:void 0,color:e===t?"black":void 0,position:e===t?"relative":void 0,userSelect:"none",pointerEvents:"none"},children:(0,r.jsx)(o.A,{sx:{visibility:e!==t?"hidden":void 0},id:a(e.lineNumber,e.ident.start.column+p),children:u.substring(e.ident.start.column+p,e.ident.end.column+p)})})]},`symbol-${e.ident.start.line}-${e.ident.start.column+p}`);return d=e.ident.end.column+p,n}),(0,r.jsx)(o.A,{sx:{visibility:"hidden",userSelect:"none"},children:u.substring(d)})]},h)}function a(e,t){return`match-${e}-${t}`}try{l.displayName||(l.displayName="HighlightedOverlay")}catch{}},3147:(e,t,n)=>{n.d(t,{PL:()=>h,TX:()=>m,Zj:()=>u});var r=n(74848),i=n(96235),o=n(60183),l=n(60039),a=n(96540),s=n(95321);let c={stylingDirectives:null},d=a.createContext(c);function u({children:e,...t}){return(0,r.jsx)(d.Provider,{value:t,children:e})}function h(){return a.useContext(d)}function m(e,t,n,r,d){let[u,h]=(0,a.useState)(c),m=(0,o.u)("react_blob_overlay"),p=t&&!r?(0,i.rzN)({repo:e,commitish:t.name,path:n}):null;return(0,a.useEffect)(()=>{if(!p)return;if(d>s.OL||!m)return void h(c);let e=!1;return(async()=>{h(c);let t=await (0,l.lS)(p);if(!e)try{if(t.ok){let e=await t.json();e&&Array.isArray(e.stylingDirectives)&&h(e)}else h(c)}catch{h(c)}})(),function(){e=!0}},[p,d,m]),u}try{d.displayName||(d.displayName="DeferredASTContext")}catch{}try{u.displayName||(u.displayName="DeferredASTProvider")}catch{}},90864:(e,t,n)=>{n.d(t,{U6:()=>u,Y_:()=>d,mD:()=>c});var r=n(74848),i=n(96235),o=n(60039),l=n(96540);let a={showLicenseMeta:!1,license:null,codeownerInfo:{codeownerPath:null,ownedByCurrentUser:null,ownersForFile:null,ruleForPathLine:null},newDiscussionPath:null,newIssuePath:null},s=l.createContext(a);function c({children:e,...t}){return(0,r.jsx)(s.Provider,{value:t,children:e})}function d(){return l.useContext(s)}function u(e,t,n,r){let[s,c]=(0,l.useState)(a),d=t&&!r?(0,i.ALm)({repo:e,commitish:t.name,path:n}):null;return(0,l.useEffect)(()=>{if(!d)return;let e=!1;return(async()=>{c(a);let t=await (0,o.lS)(d);if(!e)try{if(t.ok){let e=await t.json();e&&"boolean"==typeof e.showLicenseMeta&&"object"==typeof e.codeownerInfo&&c(e)}else c(a)}catch{c(a)}})(),function(){e=!0}},[d]),s}try{s.displayName||(s.displayName="DeferredMetadataContext")}catch{}try{c.displayName||(c.displayName="DeferredMetadataProvider")}catch{}},15305:(e,t,n)=>{n.d(t,{O:()=>a,k:()=>l});var r=n(74848),i=n(96540);let o=i.createContext(void 0);function l({blame:e,children:t}){return(0,r.jsxs)(o.Provider,{value:e,children:[" ",t," "]})}function a(){return i.useContext(o)}try{o.displayName||(o.displayName="CurrentBlameContext")}catch{}try{l.displayName||(l.displayName="CurrentBlameProvider")}catch{}},76087:(e,t,n)=>{n.d(t,{A:()=>a,s:()=>l});var r=n(74848),i=n(96540);let o=i.createContext({});function l({blob:e,children:t}){return(0,r.jsxs)(o.Provider,{value:e,children:[" ",t," "]})}function a(){return i.useContext(o)}try{o.displayName||(o.displayName="CurrentBlobContext")}catch{}try{l.displayName||(l.displayName="CurrentBlobProvider")}catch{}},81675:(e,t,n)=>{n.d(t,{EN:()=>s,Px:()=>d,YP:()=>c,pm:()=>u});var r=n(74848),i=n(96540),o=n(15305),l=n(76087);let a=i.createContext(null);function s({children:e}){let t=(0,l.A)(),n=(0,o.O)(),s=i.useMemo(()=>n?null:new Map,[t,n]);return(0,r.jsxs)(a.Provider,{value:s,children:[" ",e," "]})}function c(){return i.useContext(a)}function d(){let e=i.useContext(a);return(0,i.useCallback)(t=>e?.get(t),[e])}function u(){let e=i.useContext(a);return(0,i.useCallback)((t,n)=>{e&&(e.has(t)?e.get(t)?.push(n):e.set(t,[n]))},[e])}try{a.displayName||(a.displayName="CurrentLineRefMapContext")}catch{}try{s.displayName||(s.displayName="CurrentLineRefMapProvider")}catch{}},9826:(e,t,n)=>{n.d(t,{Ix:()=>c,T9:()=>a,j5:()=>s,t0:()=>d});var r=n(74848),i=n(38621),o=n(63867),l=n(53110);let a={Idle:"Idle",Fetching:"Fetching",Success:"Success",Error:"Error"};function s(){let e="undefined"!=typeof ClipboardItem;return"clipboard"in navigator&&e}async function c(e){let t=await fetch(e,{method:"get"});if(!t.ok)throw Error(`Failed to fetch ${e}: ${t.status} ${t.statusText}`);return new Blob([(await t.text()).replace(/\r?\n$/,"")],{type:"text/plain"})}function d(e){let t,n;switch(e){case a.Success:t="Copied!",n=(0,r.jsx)(l.A,{icon:i.CheckIcon});break;case a.Fetching:t="Copying",n=(0,r.jsx)(o.A,{size:"small"});break;case a.Error:t="Something went wrong. Try again.",n=(0,r.jsx)(l.A,{icon:i.AlertIcon});break;default:t="Copy",n=(0,r.jsx)(l.A,{icon:i.CopyIcon})}return{ariaLabel:t,content:n}}},77378:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(74848),i=n(50104),o=n(96540),l=n(13317);function a({containerRef:e,shouldNotOverrideCopy:t}){let{selectAllShortcut:n}=(0,i.wk)(),a=(0,o.useRef)(!1),s=(0,l.y)(),c=(0,o.useCallback)(()=>{a.current=!1,document.removeEventListener("selectionchange",c)},[]);(0,o.useEffect)(()=>()=>document.removeEventListener("selectionchange",c),[c]);let d=(0,o.useCallback)(t=>{e&&e.current&&(function(e){let t=document.createRange();t.selectNode(e);let n=window.getSelection();n?.removeAllRanges(),n?.addRange(t)}(e.current),t.preventDefault(),a.current=!0,setTimeout(()=>document.addEventListener("selectionchange",c),0))},[e,c]),u=(0,o.useCallback)(e=>{a.current&&(e.preventDefault(),s())},[s]);return(0,o.useEffect)(()=>{if(!t)return window.addEventListener("copy",u),()=>window.removeEventListener("copy",u)},[u,t]),(0,r.jsx)("button",{hidden:!0,"data-hotkey":n.hotkey,onClick:d})}try{a.displayName||(a.displayName="SelectAllShortcutButton")}catch{}},4215:(e,t,n)=>{n.d(t,{Q:()=>m,w:()=>x});var r=n(74848),i=n(8579),o=n(96235),l=n(28391),a=n(24208),s=n(75177),c=n(34614),d=n(52464),u=n(84217),h=n(96540);function m({id:e="breadcrumb",fileNameId:t,path:n,repo:o,commitish:l,isFolder:c,fontSize:d,showCopyPathButton:u}){let{fileName:m,segments:g}=(0,h.useMemo)(()=>(function(e){let t=e.split("/");return{fileName:t.pop(),segments:t.map((e,n)=>({directoryName:e,directoryPath:t.slice(0,n+1).join("/")}))}})(n),[n]),b=!n,v=c&&"/"===n;return(0,r.jsxs)(s.A,{sx:{display:"flex",flexDirection:"row",fontSize:d??2,minWidth:0,flexShrink:1,flexWrap:"wrap",maxWidth:"100%",alignItems:"center"},children:[(0,r.jsxs)(s.A,{as:"nav","data-testid":"breadcrumbs","aria-labelledby":`${e}-heading`,id:e,sx:{maxWidth:"100%"},children:[(0,r.jsx)(a.W,{id:`${e}-heading`,as:"h2",text:"Breadcrumbs"}),(0,r.jsxs)(s.A,{as:"ol",sx:{maxWidth:"100%",listStyle:"none",display:"inline-block"},children:[(0,r.jsxs)(s.A,{as:"li",sx:{display:"inline-block",maxWidth:"100%"},children:[(0,r.jsx)(p,{repo:o,commitish:l}),v&&(0,r.jsx)(a.W,{as:"h1",text:o.name})]}),g.map(({directoryName:e,directoryPath:t})=>(0,r.jsxs)(s.A,{as:"li",sx:{display:"inline-block",maxWidth:"100%"},children:[(0,r.jsx)(x,{fontSize:d}),e?(0,r.jsx)(f,{path:t,directoryName:e,repo:o,commitish:l}):null]},t))]})]}),m&&(0,r.jsxs)(s.A,{"data-testid":"breadcrumbs-filename",sx:{display:"inline-block",maxWidth:"100%"},children:[(0,r.jsx)(x,{fontSize:d}),(0,r.jsx)(y,{value:m,id:t,fontSize:d}),!b&&c&&(0,r.jsx)(x,{})]},m),u&&(0,r.jsx)(i.T,{ariaLabel:"Copy path",textToCopy:n,tooltipProps:{direction:"nw"},size:"small",className:"ml-2"})]})}function p({repo:e,commitish:t}){return(0,r.jsx)(c.A,{as:l.N,sx:{fontWeight:"bold"},to:(0,o.IO9)({repo:e,commitish:t,action:"tree"}),"data-testid":"breadcrumbs-repo-link",reloadDocument:!0,children:e.name})}function f({directoryName:e,path:t,repo:n,commitish:i}){return(0,r.jsx)(c.A,{as:l.N,to:(0,o.IO9)({repo:n,commitish:i,path:t,action:"tree"}),sx:{fontWeight:400},children:e})}function x({fontSize:e}){return(0,r.jsx)(d.A,{sx:{px:1,fontWeight:400,color:"fg.muted",fontSize:e??2},"aria-hidden":"true",children:"/"})}function y({value:e,id:t,fontSize:n}){return(0,r.jsx)(u.A,{as:"h1",tabIndex:-1,sx:{fontWeight:600,display:"inline-block",maxWidth:"100%",fontSize:n??2},id:t,children:e})}try{m.displayName||(m.displayName="Breadcrumb")}catch{}try{p.displayName||(p.displayName="RepoLink")}catch{}try{f.displayName||(f.displayName="DirectoryLink")}catch{}try{x.displayName||(x.displayName="Separator")}catch{}try{y.displayName||(y.displayName="FileName")}catch{}},48238:(e,t,n)=>{n.d(t,{c:()=>c});var r=n(74848),i=n(38621),o=n(87330),l=n(34164),a=n(96540);let s={square:"AskCopilotButton-module__square--o8kDO",muted:"AskCopilotButton-module__muted--QatcG"};function c({children:e,referenceType:t,...n}){let c=(0,a.useRef)(null);return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(o.K,{className:(0,l.$)(s.square,!!e&&s.muted),ref:c,icon:i.CopilotIcon,size:"small","aria-label":`Ask Copilot about this ${t}`,"data-testid":"copilot-ask-menu",...n})})}try{c.displayName||(c.displayName="AskCopilotButton")}catch{}},7956:(e,t,n)=>{n.d(t,{Ay:()=>f,Xu:()=>y});var r=n(74848),i=n(27851),o=n(38621),l=n(91321),a=n(10569),s=n(15385),c=n(96540),d=n(29943),u=n(99377),h=n(48238);let m={"menu-button":"CopilotCodeLinesMenu-module__menu-button--VNLPN"},p="code-line-dropdown-copilot-button";function f({copilotAccessAllowed:e,messageReference:t,hideDropdown:n,id:f}){let[g,b]=(0,c.useState)(!1),v=(0,i.G7)("copilot_chat_opening_thread_switch"),j=(0,c.useCallback)(()=>{let e={intent:u.wh.conversation,references:[t],id:f};(0,d.qR)(e),b(!1)},[f,t]);return e?(0,r.jsxs)(l.A,{className:n?"pr-0":"",children:[(0,r.jsx)(h.c,{referenceType:t.type,onClick:n?()=>y(t,!0,f):j,id:f}),n?void 0:(0,r.jsxs)(a.W,{open:g,onOpenChange:b,children:[(0,r.jsx)(a.W.Button,{id:p,trailingAction:o.TriangleDownIcon,size:"small","aria-label":"Copilot menu",className:m["menu-button"],children:(0,r.jsx)(r.Fragment,{})}),(0,r.jsx)(a.W.Overlay,{align:"end",onKeyDown:e=>{"Escape"===e.key&&(e?.stopPropagation(),b(!1))},children:(0,r.jsxs)(s.l,{children:[(0,r.jsx)(s.l.Item,{onSelect:()=>{(0,d.qR)({content:"Explain",intent:u.wh.explain,references:[t],id:p}),b(!1)},children:"Explain"}),(0,r.jsx)(s.l.Item,{onSelect:()=>{(0,d.qR)({content:"Suggest improvements to this code.",intent:u.wh.suggest,references:[t],id:p}),b(!1)},children:"Suggest improvements"}),(0,r.jsx)(s.l.Divider,{}),(0,r.jsx)(s.l.Item,{onSelect:()=>{v?x(t,p):y(t,!0,p),b(!1)},children:"Attach to current thread"})]})})]})]}):null}let x=(e,t)=>{(0,d.Oc)(e,!1,t),(0,d.qR)({intent:u.wh.conversation,id:t,attachThread:!0})},y=(e,t,n)=>{t?((0,d.Oc)(e,!0,n),(0,d.qR)({intent:u.wh.conversation,id:n})):(0,d.qR)({intent:u.wh.conversation,references:[e],id:n})};try{f.displayName||(f.displayName="CopilotCodeLinesMenu")}catch{}},97146:(e,t,n)=>{n.d(t,{E:()=>w});var r=n(74848),i=n(88795),o=n(84810),l=n(98386),a=n(75367),s=n(60039),c=n(38621),d=n(34614),u=n(10871),h=n(3971),m=n(55847),p=n(32226),f=n(84217),x=n(75177),y=n(96540),g=n(93955);let b={PointerBox:"CopilotPopover-module__PointerBox--fkCTe"},v={ORG_ADMIN:`For an organization, developers writing less boilerplate code means more productivity, while learning
  new technologies means delivering better customers solutions. Try it in Codespaces or your file editor.`,ORG_MEMBER:`We noticed that you're personally paying for GitHub Copilot. Instead, ask your organization admin
  to purchase the business version of GitHub Copilot.`,STANDARD:`Spend less time creating boilerplate and repetitive code patterns, and more time building great software.
  Try it in Codespaces or your favorite file editor.`},j=({featureRequestInfo:e,inProgress:t,toggleFeatureRequest:n,requested:i})=>e?.showFeatureRequest?i?(0,r.jsx)(o.c9,{inProgress:t,toggleFeatureRequest:n}):(0,r.jsx)(o.iU,{inProgress:t,toggleFeatureRequest:n,featureName:e?.featureName}):null,w=({view:e,copilotInfo:t,className:n})=>{let{documentationUrl:w,notices:N,userAccess:_}=t??{},{business:k,orgHasCFBAccess:C,userHasCFIAccess:A,userHasOrgs:S,userIsOrgAdmin:B,userIsOrgMember:I,featureRequestInfo:L}=_??{},{codeViewPopover:T}=N??{},{sendClickAnalyticsEvent:E}=(0,a.S)(),R=(0,g.i)(),{isOrgOwned:O,ownerLogin:D}=(0,i.t)(),{inProgress:F,requested:$,dismissed:M,dismissedAt:P,toggleFeatureRequest:z}=(0,o.tV)(L),[H,W]=(0,y.useState)(!1),[U,V]=(0,y.useState)(!1),G=(0,y.useCallback)(()=>W(!0),[W]),Y=(0,y.useCallback)(()=>W(!1),[W]),q=(0,y.useCallback)(()=>_&&O&&I&&!B&&(!C||A)?"Your organization can pay for GitHub Copilot":"Code 55% faster with GitHub Copilot",[C,O,_,A,B,I]),K=()=>R&&D===R.login?"owner":B?"admin":I?"member":"personal",Q=()=>{R&&E({category:"copilot_popover_code_view",action:`click_to_open_popover_${e}`,label:`ref_cta:open_copilot_popover;owner:${D};relationship:${K()}`})},X=(t,n)=>{E({category:"copilot_popover_code_view",action:t,label:`ref_cta:${n};ref_loc:code_view_${e}`})},Z=()=>{let t=`${O?"org_":""}code_view_${e}${B?"_org_admin":""}`;E({category:"copilot_popover_code_view",action:"click_to_dismiss_copilot_popover_forever",label:`ref_cta:dont_show_again;ref_loc:${t}`})},J=()=>{let e=_?.userHasOrgs??!1;E({category:"copilot_popover_code_view",action:`click_to_go_to_copilot_for_${e?"business":"individuals"}_info`,label:"ref_cta:learn_more;ref_loc:code_view"})};return U||!t?null:(0,r.jsx)("div",{className:n,children:(0,r.jsx)(h.T,{onOpen:G,onClose:Y,open:H,overlayProps:{role:"dialog",sx:{overflow:"inherit"}},focusZoneSettings:{disabled:!0},renderAnchor:e=>(0,r.jsx)(m.Q,{...e,...(0,l.G)("copilot-popover-button"),leadingVisual:c.CopilotIcon,onClick:()=>{W(!H),Q()},size:"small",sx:{color:"fg.default",display:["none","none","none","none","block"]},variant:"invisible",children:q()}),children:(0,r.jsxs)(p.A,{...(0,l.G)("copilot-popover-content"),caret:"top",className:b.PointerBox,children:[(0,r.jsx)(f.A,{as:"h2",sx:{fontSize:1,fontWeight:"bold",pb:3},children:"Code 55% faster with GitHub Copilot"}),(0,r.jsxs)(x.A,{sx:{fontSize:1,fontWeight:"normal",pb:3},children:[(0,r.jsx)("span",{...(0,l.G)("copilot-popover-body-text"),children:(()=>{if(M)return"";if(_&&O){if(B)return v.ORG_ADMIN;else if(I&&A)return v.ORG_MEMBER}return v.STANDARD})()}),M?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("p",{children:["Your request for Copilot Business was declined by an admin on ",P,"."]}),(0,r.jsxs)("p",{className:"mb-0",children:[(0,r.jsx)(d.A,{...(0,l.G)("contact-admin-link"),"aria-label":"Click this link to contact your admin.",target:"_blank",href:`/orgs/${D}/people?query=role:owner`,onClick:()=>J(),children:"Contact your admin"}),""," for more details around their decision."]})]}):(0,r.jsx)(d.A,{...(0,l.G)("copilot-popover-content-learn-more"),"aria-label":"Click this link to learn more about copilot. This action opens in a new tab.",target:"_blank",href:w,onClick:()=>J(),sx:{marginLeft:"8px"},children:"Learn more"})]}),(0,r.jsxs)(x.A,{sx:{alignItems:"center",display:"flex",flexDirection:"row"},children:[k||A||O&&(!O||I)?I&&!C&&B?(0,r.jsx)(u.z,{type:"button",href:`/github-copilot/business_signup/organization/payment?org=${D}`,onClick:()=>X("click_to_buy_copilot_for_business","get_github_copilot"),children:"Get GitHub Copilot"}):L&&!M?(0,r.jsx)(j,{requested:$,featureRequestInfo:L,inProgress:F,toggleFeatureRequest:z}):null:S?(0,r.jsx)(u.z,{type:"button",href:"/settings/copilot",onClick:()=>X("click_to_go_to_copilot_settings","get_github_copilot"),children:"Get GitHub Copilot"}):(0,r.jsx)(u.z,{type:"button",href:"/github-copilot/signup",onClick:()=>X("click_to_go_to_copilot_trial_signup","start_a_free_trial"),children:"Start a free trial"}),(0,r.jsx)(d.A,{...(0,l.G)("copilot-popover-dismiss-button"),onClick:()=>{T&&((0,s.DI)(T.dismissPath,{method:I?"DELETE":"POST"}),Z(),V(!0))},sx:{cursor:"pointer",fontSize:1,textDecorationLine:"none",marginLeft:M?0:"16px"},children:"Don't show again"})]})]})})})};try{j.displayName||(j.displayName="FeatureRequest")}catch{}try{w.displayName||(w.displayName="CopilotPopover")}catch{}},84810:(e,t,n)=>{n.d(t,{c9:()=>v,_$:()=>g,iU:()=>b,tV:()=>y});var r=n(74848),i=n(55847),o=n(34614),l=n(53110),a=n(38621),s=n(98386),c=n(75367),d=n(96540),u=n(99543),h=n(60039);let m=async(e,t,n)=>{try{return(await (0,h.DI)(e,{method:t,body:n})).ok}catch{return!1}},p=(e,t)=>m(e,"DELETE",x(t)),f=(e,t)=>m(e,"POST",x(t)),x=e=>{let t=new FormData;return t.append("feature",e),t};function y(e){let{alreadyRequested:t=!1,dismissed:n=!1,dismissedAt:r="",featureName:i="",requestPath:o=""}=e??{},[l,a]=(0,d.useState)(!1),[s,c]=(0,d.useState)(t),{addToast:h}=(0,u.Y6)(),m=async()=>{a(!0),await (s?p:f)(o,i)?c(!s):h({type:"error",message:"Something went wrong. Please try again later."}),a(!1)};return{inProgress:l,requested:s,dismissed:n,dismissedAt:r,toggleFeatureRequest:m}}function g({featureRequestInfo:e,learnMorePath:t,requestMessage:n,requestedMessage:i}){let{inProgress:o,requested:l,toggleFeatureRequest:a}=y(e);return e.showFeatureRequest?l?(0,r.jsx)(v,{inProgress:o,toggleFeatureRequest:a,requestedMessage:i}):(0,r.jsx)(b,{inProgress:o,toggleFeatureRequest:a,isEnterpriseRequest:e.isEnterpriseRequest,featureName:e.featureName,billingEntityId:e.billingEntityId,learnMorePath:t,requestMessage:n}):null}let b=({inProgress:e,toggleFeatureRequest:t,billingEntityId:n="",isEnterpriseRequest:i=!1,featureName:o,learnMorePath:l,requestMessage:a})=>{let{sendClickAnalyticsEvent:s}=(0,c.S)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j,{onClick:()=>{t();let e=i&&n?`ref_cta:ask_enterprise_owners_for_access;ref_loc:${o};enterprise_id:${n};`:`ref_cta:ask_admin_for_access;ref_loc:${o};`;s({category:"member_feature_request",action:`action.${o}`,label:e})},inProgress:e,isEnterpriseRequest:i}),a&&(0,r.jsx)(N,{message:a}),l&&(0,r.jsx)(w,{onClick:()=>{s({category:"suggestion",action:"click_to_read_docs",label:`ref_cta:learn_more;ref_loc:${o};`})},path:l})]})},v=({inProgress:e,toggleFeatureRequest:t,requestedMessage:n})=>(0,r.jsxs)(r.Fragment,{children:[n&&(0,r.jsx)(_,{message:n}),(0,r.jsx)(k,{onClick:t,inProgress:e})]}),j=({onClick:e,inProgress:t,isEnterpriseRequest:n})=>(0,r.jsx)(i.Q,{onClick:e,variant:"primary",disabled:t,...(0,s.G)("feature-request-request-button"),children:t?"Requesting...":n?"Ask enterprise owners for access":"Ask admin for access"}),w=({onClick:e,path:t})=>(0,r.jsx)(o.A,{href:t,onClick:e,...(0,s.G)("feature-request-learn-more-link"),children:"Learn more"}),N=({message:e})=>(0,r.jsx)("span",{children:e}),_=({message:e})=>(0,r.jsxs)("span",{className:"d-inline-block color-fg-subtle mr-1",children:[(0,r.jsx)(l.A,{icon:a.CheckIcon}),e]}),k=({onClick:e,inProgress:t})=>(0,r.jsx)(o.A,{className:"color-fg-danger text-semibold",as:"button",onClick:e,disabled:t,...(0,s.G)("feature-request-cancel-link"),children:t?"Cancelling...":"Remove request"});try{g.displayName||(g.displayName="FeatureRequest")}catch{}try{b.displayName||(b.displayName="RequestFeature")}catch{}try{v.displayName||(v.displayName="CancelFeatureRequest")}catch{}try{j.displayName||(j.displayName="RequestCTA")}catch{}try{w.displayName||(w.displayName="LearnMore")}catch{}try{N.displayName||(N.displayName="RequestMessage")}catch{}try{_.displayName||(_.displayName="RequestedMessage")}catch{}try{k.displayName||(k.displayName="RemoveRequestCTA")}catch{}},81351:(e,t,n)=>{n.d(t,{Gx:()=>a});var r=n(74848),i=n(21728),o=n(83829);function l(e){let t,n=(0,i.c)(2),{char:o}=e;return n[0]!==o?(t=(0,r.jsx)("span",{className:"hidden-unicode-replacement padded",children:o}),n[0]=o,n[1]=t):t=n[1],t}function a(e){return(0,o.Y)(e)?(0,o.t6)(e).map(e=>{let t=o.A0.get(e);return t?`<span class="hidden-unicode-replacement">${t}</span>`:e}).join(""):null}try{l.displayName||(l.displayName="HiddenUnicodeCharacter")}catch{}},69851:(e,t,n)=>{n.d(t,{x:()=>d});var r=n(74848),i=n(21728),o=n(88191),l=n(96540),a=n(80663);function s(e,t){let n,l,s,d,u,h,m=(0,i.c)(13);m[0]!==e?({children:n,src:s,...l}=e,m[0]=e,m[1]=n,m[2]=l,m[3]=s):(n=m[1],l=m[2],s=m[3]),m[4]===Symbol.for("react.memo_cache_sentinel")?(d=[],m[4]=d):d=m[4];let[p]=(0,a.I)(c,!0,d);return m[5]!==p||m[6]!==s?(u=p?{}:{src:s,"data-nonce":(0,o.M1)()},m[5]=p,m[6]=s,m[7]=u):u=m[7],m[8]!==n||m[9]!==l||m[10]!==t||m[11]!==u?(h=(0,r.jsx)("include-fragment",{...l,ref:t,...u,children:n}),m[8]=n,m[9]=l,m[10]=t,m[11]=u,m[12]=h):h=m[12],h}function c(){return!1}let d=(0,l.forwardRef)(s);try{s.displayName||(s.displayName="IncludeFragmentWithRef")}catch{}try{d.displayName||(d.displayName="IncludeFragment")}catch{}},34700:(e,t,n)=>{n.d(t,{X:()=>a});var r=n(74848),i=n(47139),o=n(56693),l=n(90735);let a=({children:e="Preview",feedbackUrl:t,className:n})=>(0,r.jsx)(l.V,{className:n,label:(0,r.jsx)(i.A,{variant:"success",children:e}),link:t?(0,r.jsx)(o.A,{feedbackUrl:t}):void 0});try{a.displayName||(a.displayName="BetaLabel")}catch{}},56693:(e,t,n)=>{n.d(t,{A:()=>l});var r=n(74848),i=n(34614);let o={link:"FeedbackLink-module__link--uY6Kq"},l=({feedbackUrl:e})=>(0,r.jsx)(i.A,{className:o.link,href:e,children:"Give feedback"});try{l.displayName||(l.displayName="FeedbackLink")}catch{}},90735:(e,t,n)=>{n.d(t,{V:()=>o});var r=n(74848),i=n(46309);let o=({label:e,link:t,className:n})=>t?(0,r.jsxs)(i.B,{direction:"horizontal",gap:"condensed",align:"baseline",className:n,children:[e," ",t]}):n?(0,r.jsx)("span",{className:n,children:e}):(0,r.jsx)(r.Fragment,{children:e});try{o.displayName||(o.displayName="LabelWithLink")}catch{}}},e=>{var t=t=>e(e.s=t);e.O(0,["primer-react","react-core","react-lib","octicons-react","vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52","vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483","vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6","vendors-node_modules_dompurify_dist_purify_es_mjs","vendors-node_modules_lodash-es__Stack_js-node_modules_lodash-es__Uint8Array_js-node_modules_l-4faaa6","vendors-node_modules_github_relative-time-element_dist_index_js","vendors-node_modules_lodash-es_isEqual_js","vendors-node_modules_tanstack_react-virtual_dist_esm_index_js","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-189aa3","vendors-node_modules_focus-visible_dist_focus-visible_js-node_modules_fzy_js_index_js-node_mo-296806","ui_packages_failbot_failbot_ts","ui_packages_document-metadata_document-metadata_ts-ui_packages_history_history_ts-ui_packages-417c81","ui_packages_paths_index_ts","ui_packages_ref-selector_RefSelector_tsx","ui_packages_commit-attribution_index_ts-ui_packages_commit-checks-status_index_ts-ui_packages-762eaa","ui_packages_app-uuid_app-uuid_ts-ui_packages_fetch-headers_fetch-headers_ts-ui_packages_repos-0cd8c2","ui_packages_diffs_diff-parts_ts","ui_packages_code-view-shared_hooks_use-canonical-object_ts-ui_packages_code-view-shared_hooks-6097ef","app_assets_modules_github_blob-anchor_ts-ui_packages_code-nav_code-nav_ts-ui_packages_filter--8253c1"],()=>t(70635)),e.O()}]);
//# sourceMappingURL=react-code-view-b4cda195a552.js.map