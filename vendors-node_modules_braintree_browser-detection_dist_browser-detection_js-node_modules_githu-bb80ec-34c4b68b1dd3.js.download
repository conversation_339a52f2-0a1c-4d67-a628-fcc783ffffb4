"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_braintree_browser-detection_dist_browser-detection_js-node_modules_githu-bb80ec"],{23683:(e,t,s)=>{t.nr=void 0,s(76091),s(70955),s(92112),s(60020),s(45744),s(20777),s(42189),s(16174),s(40691),s(67365),t.nr=s(32514),s(63969),s(76369),s(29572),s(45710),s(181),s(13735),s(10777),s(41464),s(2224),s(66787),s(46788),s(85571),s(62701),s(68861),s(4951)},62701:(e,t,s)=>{var n=s(76091),r=s(70955),i=s(63969);e.exports=function(){return n()||r()||i()}},76091:e=>{e.exports=function(e){return e=e||window.navigator.userAgent,/Android/i.test(e)}},70955:e=>{e.exports=function(e){return e=e||window.navigator.userAgent,/CrOS/i.test(e)}},92112:(e,t,s)=>{var n=s(40691),r=s(46788),i=s(60020),a=s(66787),o=s(85571);e.exports=function(e){return(-1!==(e=e||window.navigator.userAgent).indexOf("Chrome")||-1!==e.indexOf("CriOS"))&&!n(e)&&!r(e)&&!i(e)&&!a(e)&&!o(e)}},60020:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("DuckDuckGo/")}},40691:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("Edge/")||-1!==e.indexOf("Edg/")}},67365:e=>{e.exports=function(e){return e=e||window.navigator.userAgent,/Firefox/i.test(e)}},45744:(e,t,s)=>{var n=s(16174);e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("MSIE")||n(e)}},42189:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("MSIE 10")}},16174:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("Trident/7")}},20777:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("MSIE 9")}},76369:e=>{e.exports=function(e){return e=e||window.navigator.userAgent,/FxiOS/i.test(e)}},29572:(e,t,s)=>{var n=s(63969);e.exports=function(e){var t;return n(e=e||window.navigator.userAgent)&&(t=e,/\bGSA\b/.test(t))}},45710:(e,t,s)=>{var n=s(63969),r=s(76369),i=/webkit/i;e.exports=function(e){var t;return n(e=e||window.navigator.userAgent)&&(t=e,i.test(t))&&!(e.indexOf("CriOS")>-1)&&!r(e)&&!(e.indexOf("FBAN")>-1)}},181:(e,t,s)=>{var n=s(13735);e.exports=function(e,t){return t=void 0!==t?t:window.statusbar.visible,n(e)&&!t}},13735:(e,t,s)=>{var n=s(63969),r=s(29572);e.exports=function(e){return!!n(e=e||window.navigator.userAgent)&&(!!r(e)||/.+AppleWebKit(?!.*Safari)/i.test(e))}},10777:(e,t,s)=>{var n=s(13735);e.exports=function(e,t){return t=void 0!==t?t:window.statusbar.visible,n(e)&&t}},63969:(e,t,s)=>{var n=s(41464);e.exports=function(e,t,s){void 0===t&&(t=!0),e=e||window.navigator.userAgent;var r=/iPhone|iPod|iPad/i.test(e);return t?r||n(e,s):r}},41464:e=>{e.exports=function(e,t){return e=e||window.navigator.userAgent,t=t||window.document,/Mac|iPad/i.test(e)&&"ontouchend"in t}},2224:(e,t,s)=>{var n=s(76369),r=s(67365);e.exports=function(e){return n(e=e||window.navigator.userAgent)||/iPhone|iPod|iPad|Mobile|Tablet/i.test(e)&&r(e)}},66787:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("OPR/")||-1!==e.indexOf("Opera/")||-1!==e.indexOf("OPT/")}},32514:e=>{e.exports=function(e){return e=e||window.navigator.userAgent,/^Mozilla\/5\.0.*Safari\//.test(e)&&!/(iPhone|iPad|iPod|Android|SM-)/i.test(e)&&!/Chrome|CriOS|FxiOS|OPiOS|mercury/i.test(e)}},46788:e=>{e.exports=function(e){return e=e||window.navigator.userAgent,/SamsungBrowser/i.test(e)}},85571:e=>{e.exports=function(e){return -1!==(e=e||window.navigator.userAgent).indexOf("Silk/")}},4951:(e,t,s)=>{var n=s(92112);e.exports=function(e){var t;return e=e||window.navigator.userAgent,!!window.PaymentRequest&&(!n(e)||!!(t=e.match(/Chrome\/(\d+)\./))&&parseInt(t[1],10)>=61)}},68861:(e,t,s)=>{var n=s(76091),r=s(76369),i=s(13735),a=s(92112),o=s(46788),c=s(60020);function l(e){return(e=e||window.navigator.userAgent).indexOf("Opera Mini")>-1}e.exports=function(e){var t,s,h,u;return!(i(e=e||window.navigator.userAgent)||r(e)||n(t=(t=e)||window.navigator.userAgent)&&/Version\/[\d.]+/i.test(t)&&!l(t)&&!c(t)||l(e)||(h=(e||window.navigator.userAgent).match(/CriOS\/(\d+)\./))&&48>parseInt(h[1],10)||!a(u=e)&&!o(u)&&/samsung/i.test(u))}},86948:(e,t,s)=>{s.d(t,{i:()=>AliveSession});var n,r=s(77479),i=s(14845),a=s(28556),o=s(29406),c=s(23963),l=s(11193);!function(e){e.Deploy="Alive Redeploy",e.Reconnect="Alive Reconnect"}(n||(n={}));let AliveSession=class AliveSession{constructor(e,t,s,n,a=6e5,c={}){this.url=e,this.getUrl=t,this.inSharedWorker=s,this.notify=n,this.maxReconnectBackoff=a,this.options=c,this.subscriptions=new o.m,this.state="online",this.retrying=null,this.connectionCount=0,this.presence=new r.fI,this.presenceMetadata=new i.V,this.intentionallyDisconnected=!1,this.lastCameOnline=0,this.userId=function(e){let t=e.match(/\/u\/(\d+)\/ws/);return t?+t[1]:0}(e),this.presenceId=`${Math.round(0x7fffffff*Math.random())}_${Math.round(Date.now()/1e3)}`,this.presenceKey=(0,r.NW)(this.userId,this.presenceId),this.socket=this.connect()}subscribe(e){let t=this.subscriptions.add(...e);for(let s of(this.sendSubscribe(t),e)){let e=s.topic.name;(0,r.JR)(e)&&this.notifyCachedPresence(s.subscriber,e)}}unsubscribe(e){let t=this.subscriptions.delete(...e);this.sendUnsubscribe(t)}unsubscribeAll(...e){let t=this.subscriptions.drain(...e);this.sendUnsubscribe(t);let s=this.presenceMetadata.removeSubscribers(e);this.sendPresenceMetadataUpdate(s)}requestPresence(e,t){for(let s of t)this.notifyCachedPresence(e,s)}notifyCachedPresence(e,t){let s=this.presence.getChannelItems(t);0!==s.length&&this.notifyPresenceChannel(t,s)}updatePresenceMetadata(e){let t=new Set;for(let s of e)this.presenceMetadata.setMetadata(s),t.add(s.channelName);this.sendPresenceMetadataUpdate(t)}sendPresenceMetadataUpdate(e){if(!e.size)return;let t=[];for(let s of e){let e=this.subscriptions.topic(s);e&&t.push(e)}this.sendSubscribe(t)}online(){var e;this.lastCameOnline=Date.now(),this.state="online",null==(e=this.retrying)||e.abort(),this.socket.open()}offline(){var e;this.state="offline",null==(e=this.retrying)||e.abort(),this.socket.close()}shutdown(){this.inSharedWorker&&self.close()}get reconnectWindow(){let e=Date.now()-this.lastCameOnline<6e4;return 0===this.connectionCount||this.intentionallyDisconnected||e?0:1e4}socketDidOpen(){this.intentionallyDisconnected=!1,this.connectionCount++,this.socket.url=this.getUrlWithPresenceId(),this.sendSubscribe(this.subscriptions.topics())}socketDidClose(e,t,s){if(void 0!==this.redeployEarlyReconnectTimeout&&clearTimeout(this.redeployEarlyReconnectTimeout),"Alive Reconnect"===s)this.intentionallyDisconnected=!0;else if("Alive Redeploy"===s){this.intentionallyDisconnected=!0;let e=3+22*Math.random();this.redeployEarlyReconnectTimeout=setTimeout(()=>{this.intentionallyDisconnected=!0,this.socket.close(1e3,"Alive Redeploy Early Client Reconnect")},60*e*1e3)}}socketDidFinish(){"offline"!==this.state&&this.reconnect()}socketDidReceiveMessage(e,t){let s=JSON.parse(t);switch(s.e){case"ack":this.handleAck(s);break;case"msg":this.handleMessage(s)}}handleAck(e){for(let t of this.subscriptions.topics())t.offset=e.off}handleMessage(e){let t=e.ch,s=this.subscriptions.topic(t);if(s){if(s.offset=e.off,"e"in e.data){let s=this.presence.handleMessage(t,e.data);this.notifyPresenceChannel(t,s);return}e.data.wait||(e.data.wait=0),this.notify(this.subscriptions.subscribers(t),{channel:t,type:"message",data:e.data})}}notifyPresenceChannel(e,t){var s,n;let r=new Map;for(let e of t){let{userId:t,metadata:s,presenceKey:n}=e,a=r.get(t)||{userId:t,isOwnUser:t===this.userId,metadata:[]};if(n!==this.presenceKey){for(let e of s){if(i.n in e){!1!==a.isIdle&&(a.isIdle=!!e[i.n]);continue}a.metadata.push(e)}r.set(t,a)}}for(let t of this.subscriptions.subscribers(e)){let i=this.userId,a=Array.from(r.values()).filter(e=>e.userId!==i),o=null!=(n=null==(s=r.get(this.userId))?void 0:s.metadata)?n:[],c=this.presenceMetadata.getChannelMetadata(e,{subscriber:t,markAllAsLocal:!this.inSharedWorker});this.notify([t],{channel:e,type:"presence",data:[{userId:i,isOwnUser:!0,metadata:[...o,...c]},...a]})}}async reconnect(){if(!this.retrying)try{this.retrying=new AbortController;let e=await (0,l.L)(this.getUrl,1/0,this.maxReconnectBackoff,this.retrying.signal);e?(this.url=e,this.socket=this.connect()):this.shutdown()}catch(e){if("AbortError"!==e.name)throw e}finally{this.retrying=null}}getUrlWithPresenceId(){let e=new URL(this.url,self.location.origin);return e.searchParams.set("shared",this.inSharedWorker.toString()),e.searchParams.set("p",`${this.presenceId}.${this.connectionCount}`),e.toString()}connect(){let e=new a.Zl(this.getUrlWithPresenceId(),this,this.options.socketPolicy||{timeout:4e3,attempts:16,maxDelay:512e3});return e.open(),e}sendSubscribe(e){let t=Array.from(e);for(let e of(0,c.s)(t,25)){let t={};for(let s of e)(0,r.JR)(s.name)?t[s.signed]=JSON.stringify(this.presenceMetadata.getChannelMetadata(s.name)):t[s.signed]=s.offset;this.socket.send(JSON.stringify({subscribe:t}))}}sendUnsubscribe(e){let t=Array.from(e,e=>e.signed);for(let e of(0,c.s)(t,25))this.socket.send(JSON.stringify({unsubscribe:e}));for(let t of e)(0,r.JR)(t.name)&&this.presence.clearChannel(t.name)}}},11193:(e,t,s)=>{function n(e){return new Promise((t,s)=>{let n=Error("aborted");n.name="AbortError",e.aborted?s(n):e.addEventListener("abort",()=>s(n))})}async function r(e,t){let s,r=new Promise(t=>{s=self.setTimeout(t,e)});if(!t)return r;try{await Promise.race([r,n(t)])}catch(e){throw self.clearTimeout(s),e}}async function i(e,t,s=1/0,a){let o=a?n(a):null;for(let n=0;n<t;n++)try{let t=o?Promise.race([e(),o]):e();return await t}catch(o){if("AbortError"===o.name||n===t-1)throw o;let e=1e3*Math.pow(2,n),i=Math.floor(Math.random()*Math.floor(.1*e));await r(Math.min(s,e+i),a)}throw Error("retry failed")}s.d(t,{L:()=>i})},10204:(e,t,s)=>{s.d(t,{JR:()=>i.JR,KK:()=>o.K,VH:()=>r.V,ib:()=>n.i,m0:()=>a.m,nH:()=>r.n});var n=s(86948),r=s(14845),i=s(77479),a=s(29406),o=s(4295)},23963:(e,t,s)=>{s.d(t,{s:()=>n});function*n(e,t){for(let s=0;s<e.length;s+=t)yield e.slice(s,s+t)}},14845:(e,t,s)=>{s.d(t,{V:()=>PresenceMetadataSet,n:()=>n});let n="_i";function r(e){return Object.assign(Object.assign({},e),{isLocal:!0})}let PresenceMetadataForChannel=class PresenceMetadataForChannel{constructor(){this.subscriberMetadata=new Map}setMetadata(e,t){this.subscriberMetadata.set(e,t)}removeSubscribers(e){let t=!1;for(let s of e)t=this.subscriberMetadata.delete(s)||t;return t}getMetadata(e){if(!e){let e,t=[];for(let s of this.subscriberMetadata.values())for(let r of s)if(n in r){let t=!!r[n];e=void 0===e?t:t&&e}else t.push(r);return void 0!==e&&t.push({[n]:+!!e}),t}let t=[],{subscriber:s,markAllAsLocal:i}=e;for(let[e,n]of this.subscriberMetadata){let a=i||e===s?n.map(r):n;t.push(...a)}return t}hasSubscribers(){return this.subscriberMetadata.size>0}};let PresenceMetadataSet=class PresenceMetadataSet{constructor(){this.metadataByChannel=new Map}setMetadata({subscriber:e,channelName:t,metadata:s}){let n=this.metadataByChannel.get(t);n||(n=new PresenceMetadataForChannel,this.metadataByChannel.set(t,n)),n.setMetadata(e,s)}removeSubscribers(e){let t=new Set;for(let[s,n]of this.metadataByChannel)n.removeSubscribers(e)&&t.add(s),n.hasSubscribers()||this.metadataByChannel.delete(s);return t}getChannelMetadata(e,t){let s=this.metadataByChannel.get(e);return(null==s?void 0:s.getMetadata(t))||[]}}},77479:(e,t,s)=>{function n(e,t){return`${e}:${t}`}function r(e){let[t,s]=e.p.split(".");return{userId:e.u,presenceKey:n(e.u,t),connectionCount:Number(s),metadata:e.m||[]}}function i(e){return e.startsWith("presence-")}s.d(t,{JR:()=>i,NW:()=>n,fI:()=>AlivePresence});let PresenceChannel=class PresenceChannel{constructor(){this.presenceItems=new Map}shouldUsePresenceItem(e){let t=this.presenceItems.get(e.presenceKey);return!t||t.connectionCount<=e.connectionCount}addPresenceItem(e){this.shouldUsePresenceItem(e)&&this.presenceItems.set(e.presenceKey,e)}removePresenceItem(e){this.shouldUsePresenceItem(e)&&this.presenceItems.delete(e.presenceKey)}replacePresenceItems(e){for(let t of(this.presenceItems.clear(),e))this.addPresenceItem(t)}getPresenceItems(){return Array.from(this.presenceItems.values())}};let AlivePresence=class AlivePresence{constructor(){this.presenceChannels=new Map}getPresenceChannel(e){let t=this.presenceChannels.get(e)||new PresenceChannel;return this.presenceChannels.set(e,t),t}handleMessage(e,t){let s=this.getPresenceChannel(e);switch(t.e){case"pf":s.replacePresenceItems(t.d.map(r));break;case"pa":s.addPresenceItem(r(t.d));break;case"pr":s.removePresenceItem(r(t.d))}return this.getChannelItems(e)}getChannelItems(e){return this.getPresenceChannel(e).getPresenceItems()}clearChannel(e){this.presenceChannels.delete(e)}}},29406:(e,t,s)=>{s.d(t,{m:()=>SubscriptionSet});var n=s(43730);let SubscriptionSet=class SubscriptionSet{constructor(){this.subscriptions=new n.A,this.signatures=new Map}add(...e){let t=[];for(let{subscriber:s,topic:n}of e)this.subscriptions.has(n.name)||(t.push(n),this.signatures.set(n.name,n)),this.subscriptions.set(n.name,s);return t}delete(...e){let t=[];for(let{subscriber:s,topic:n}of e)this.subscriptions.delete(n.name,s)&&!this.subscriptions.has(n.name)&&(t.push(n),this.signatures.delete(n.name));return t}drain(...e){let t=[];for(let s of e)for(let e of this.subscriptions.drain(s)){let s=this.signatures.get(e);this.signatures.delete(e),t.push(s)}return t}topics(){return this.signatures.values()}topic(e){return this.signatures.get(e)||null}subscribers(e){return this.subscriptions.get(e).values()}}},4295:(e,t,s)=>{s.d(t,{K:()=>Topic});let Topic=class Topic{static parse(e){let[t,s]=e.split("--");if(!t||!s)return null;let n=JSON.parse(atob(t));return n.c&&n.t?new Topic(n.c,e):null}constructor(e,t){this.name=e,this.signed=t,this.offset=""}}},43730:(e,t,s)=>{s.d(t,{A:()=>MultiMap});let MultiMap=class MultiMap{constructor(e){if(this.map=new Map,e)for(let[t,s]of e)this.set(t,s)}get(e){return this.map.get(e)||new Set}set(e,t){let s=this.map.get(e);return s||(s=new Set,this.map.set(e,s)),s.add(t),this}has(e){return this.map.has(e)}delete(e,t){let s=this.map.get(e);if(!s)return!1;if(!t)return this.map.delete(e);let n=s.delete(t);return s.size||this.map.delete(e),n}drain(e){let t=[];for(let s of this.keys())this.delete(s,e)&&!this.has(s)&&t.push(s);return t}keys(){return this.map.keys()}values(){return this.map.values()}entries(){return this.map.entries()}[Symbol.iterator](){return this.entries()}clear(){this.map.clear()}get size(){return this.map.size}}},28556:(e,t,s)=>{async function n(e,t){let s,n=new Promise((t,n)=>{s=self.setTimeout(()=>n(Error("timeout")),e)});if(!t)return n;try{await Promise.race([n,a(t)])}catch(e){throw self.clearTimeout(s),e}}async function r(e,t){let s,n=new Promise(t=>{s=self.setTimeout(t,e)});if(!t)return n;try{await Promise.race([n,a(t)])}catch(e){throw self.clearTimeout(s),e}}async function i(e,t,s=1/0,n){let o=n?a(n):null;for(let i=0;i<t;i++)try{let t=o?Promise.race([e(),o]):e();return await t}catch(o){if("AbortError"===o.name||i===t-1)throw o;let e=1e3*Math.pow(2,i),a=Math.floor(Math.random()*Math.floor(.1*e));await r(Math.min(s,e+a),n)}throw Error("retry failed")}function a(e){return new Promise((t,s)=>{let n=Error("aborted");n.name="AbortError",e.aborted?s(n):e.addEventListener("abort",()=>s(n))})}async function o(e,t,s){var r;let i=new WebSocket(e),a=(r=i,new Promise((e,t)=>{r.readyState===WebSocket.OPEN?e(r):(r.onerror=()=>{r.onerror=null,r.onopen=null,t(Error("connect failed"))},r.onopen=()=>{r.onerror=null,r.onopen=null,e(r)})}));try{return await Promise.race([a,n(t,s)]),i}catch(e){throw c(a),e}}async function c(e){try{(await e).close()}catch(e){}}s.d(t,{Zl:()=>StableSocket});let StableSocket=class StableSocket{constructor(e,t,s){this.socket=null,this.opening=null,this.url=e,this.delegate=t,this.policy=s}async open(){if(this.opening||this.socket)return;this.opening=new AbortController;let e=Object.assign(Object.assign({},this.policy),{signal:this.opening.signal});try{var t;this.socket=await (t=this.url,i(()=>o(t,e.timeout,e.signal),e.attempts,e.maxDelay,e.signal))}catch(e){this.delegate.socketDidFinish(this);return}finally{this.opening=null}this.socket.onclose=e=>{var t,s;this.socket=null,this.delegate.socketDidClose(this,e.code,e.reason),(this.delegate.socketShouldRetry?this.delegate.socketShouldRetry(this,e.code):(t=e.code)!==l&&t!==h)?setTimeout(()=>this.open(),(s=100,Math.random()*(100+(this.delegate.reconnectWindow||50)-100)+s)):this.delegate.socketDidFinish(this)},this.socket.onmessage=e=>{this.delegate.socketDidReceiveMessage(this,e.data)},this.delegate.socketDidOpen(this)}close(e,t){this.opening?(this.opening.abort(),this.opening=null):this.socket&&(this.socket.onclose=null,this.socket.close(e,t),this.socket=null,this.delegate.socketDidClose(this,e,t),this.delegate.socketDidFinish(this))}send(e){this.socket&&this.socket.send(e)}isOpen(){return!!this.socket}};let l=1008,h=1011}}]);
//# sourceMappingURL=vendors-node_modules_braintree_browser-detection_dist_browser-detection_js-node_modules_githu-bb80ec-49d8fcaded97.js.map