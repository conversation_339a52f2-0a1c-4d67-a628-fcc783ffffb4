"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_diffs_diff-parts_ts"],{87591:(e,i,t)=>{function l(e){return`line-${e}`}function n(e,i,t){return`${e}${"left"===i?"L":"R"}${t}`}function r(e){return"DELETION"===e?"left":"right"}function a(e,i=!1,t=!1){if(t)return"var(--bgColor-attention-muted, var(--color-attention-subtle))";switch(e){case"ADDITION":return i?"var(--diffBlob-additionNum-bgColor, var(--diffBlob-addition-bgColor-num))":"var(--diffBlob-additionLine-bgColor, var(--diffBlob-addition-bgColor-line))";case"DELETION":return i?"var(--diffBlob-deletionNum-bgColor, var(--diffBlob-deletion-bgColor-num))":"var(--diffBlob-deletionLine-bgColor, var(--diffBlob-deletion-bgColor-line))";case"HUNK":return i?"var(--diffBlob-hunkNum-bgColor, var(--diffBlob-hunk-bgColor-num))":"var(--diffBlob-hunkLine-bgColor, var(--bgColor-accent-muted))";case"EMPTY":return i?"var(--diffBlob-emptyNum-bgColor, var(--diffBlob-hunk-bgColor-num))":"var(--diffBlob-emptyLine-bgColor, var(--bgColor-accent-muted))";default:return}}function d(e){let i=0;if(e)for(let t of e)i=Math.max(i,t?.left??0,t?.right??0);return Math.max(8*i.toString().length+20,40).toString()}t.d(i,{IQ:()=>n,Sq:()=>l,XU:()=>r,c7:()=>d,gK:()=>a})},91215:(e,i,t)=>{t.d(i,{Jg:()=>c,nq:()=>h,uP:()=>u.u,$e:()=>y,P3:()=>x,wj:()=>g,l4:()=>C,Qe:()=>b,Ah:()=>L});var l=t(74848),n=t(21728),r=t(34164),a=t(45968),d=t(87591);let s=e=>{let i,t,s,o,c,f,h,u,m,x,p=(0,n.c)(34),{dragging:y,isHighlighted:g,isLeftColumn:b,colSpan:j,line:N,lineAnchor:D}=e,_=N.html,C="";if(["ADDITION","DELETION"].includes(N.type)&&["+","-"].includes(_[0])){let e;C=_[0],p[0]!==_?(e=_.slice(1),p[0]=_,p[1]=e):e=p[1],_=e}let T=y&&g,L=b&&"HUNK"!==N.type;p[2]!==T||p[3]!==L?(i=(0,r.$)("diff-text-cell",{"border-left color-border-accent-emphasis":T,"border-right":L}),p[2]=T,p[3]=L,p[4]=i):i=p[4],p[5]!==D?(t=D?(0,d.Sq)(D):void 0,p[5]=D,p[6]=t):t=p[6],p[7]!==g||p[8]!==N.type?(s=(0,d.gK)(N.type,!1,g),p[7]=g,p[8]=N.type,p[9]=s):s=p[9],p[10]!==s?(o={backgroundColor:s},p[10]=s,p[11]=o):o=p[11];let v=C,S="ADDITION"===N.type,I="DELETION"===N.type;p[12]!==S||p[13]!==I?(c=(0,r.$)("diff-text syntax-highlighted-line",{addition:S,deletion:I}),p[12]=S,p[13]=I,p[14]=c):c=p[14];let k=e.lineChild,$="HUNK"===N.type;p[15]!==$?(f=(0,r.$)("diff-text-inner",{"color-fg-muted":$}),p[15]=$,p[16]=f):f=p[16];let H="CONTEXT"===N.type?"-7px":void 0;return p[17]!==H?(h={marginLeft:H},p[17]=H,p[18]=h):h=p[18],p[19]!==_||p[20]!==f||p[21]!==h?(u=(0,l.jsx)(a.$6,{className:f,html:_,style:h}),p[19]=_,p[20]=f,p[21]=h,p[22]=u):u=p[22],p[23]!==C||p[24]!==e.lineChild||p[25]!==u||p[26]!==c?(m=(0,l.jsxs)("code",{"data-code-marker":v,className:c,children:[k,u]}),p[23]=C,p[24]=e.lineChild,p[25]=u,p[26]=c,p[27]=m):m=p[27],p[28]!==j||p[29]!==m||p[30]!==i||p[31]!==t||p[32]!==o?(x=(0,l.jsx)("td",{className:i,colSpan:j,id:t,style:o,children:m}),p[28]=j,p[29]=m,p[30]=i,p[31]=t,p[32]=o,p[33]=x):x=p[33],x};try{s.displayName||(s.displayName="DiffText")}catch{}let o=e=>{let i,t,a,s,o,c,f,h,u,m,x,p,y,g=(0,n.c)(30);g[0]!==e?({ariaLabel:i,children:t,hasExpanderButton:s,lineType:f,colSpan:a,interactiveProps:o,isHighlighted:c,...h}=e,g[0]=e,g[1]=i,g[2]=t,g[3]=a,g[4]=s,g[5]=o,g[6]=c,g[7]=f,g[8]=h):(i=g[1],t=g[2],a=g[3],s=g[4],o=g[5],c=g[6],f=g[7],h=g[8]),g[9]!==t?(u=(0,l.jsx)("code",{className:"pr-2",children:t}),g[9]=t,g[10]=u):u=g[10];let b=u;if(o){let e;g[11]!==i||g[12]!==o||g[13]!==b?(e=(0,l.jsx)("button",{className:"diff-line-number-button",...o,"aria-label":i,children:b}),g[11]=i,g[12]=o,g[13]=b,g[14]=e):e=g[14],b=e}let j=o?void 0:i,N=!!o;g[15]!==s||g[16]!==N?(m=(0,r.$)("diff-line-number",{"has-expander":s,clickable:N}),g[15]=s,g[16]=N,g[17]=m):m=g[17];let D=a||1;return g[18]!==c||g[19]!==f?(x=(0,d.gK)(f,!0,c),g[18]=c,g[19]=f,g[20]=x):x=g[20],g[21]!==x?(p={backgroundColor:x},g[21]=x,g[22]=p):p=g[22],g[23]!==h||g[24]!==j||g[25]!==m||g[26]!==D||g[27]!==p||g[28]!==b?(y=(0,l.jsx)("td",{"aria-label":j,"data-line-number":!0,className:m,colSpan:D,style:p,...h,children:b}),g[23]=h,g[24]=j,g[25]=m,g[26]=D,g[27]=p,g[28]=b,g[29]=y):y=g[29],y};try{o.displayName||(o.displayName="LineNumber")}catch{}let c=e=>{let i,t,r,a,d=(0,n.c)(28),{dragging:c,isHighlighted:f,isLeftColumn:h,isSplit:u,lineAnchor:m,line:x,lineChild:p,onLineNumberClick:y}=e,g="CONTEXT"===x.type||"INJECTED_CONTEXT"===x.type,b="ADDITION"!==x.type,j=b||!u,N="DELETION"!==x.type,D=N&&!g||!u,_=u&&g?h?x.left:x.right:x.left;return d[0]!==_||d[1]!==f||d[2]!==x.left||d[3]!==x.right||d[4]!==x.type||d[5]!==y||d[6]!==b||d[7]!==j?(i=j&&(0,l.jsx)(o,{ariaLabel:`Line ${b?x.left?.toString():x.right?.toString()}`,lineType:x.type,interactiveProps:y?{onClick:y}:null,isHighlighted:f,children:b&&_}),d[0]=_,d[1]=f,d[2]=x.left,d[3]=x.right,d[4]=x.type,d[5]=y,d[6]=b,d[7]=j,d[8]=i):i=d[8],d[9]!==f||d[10]!==x.left||d[11]!==x.right||d[12]!==x.type||d[13]!==y||d[14]!==N||d[15]!==D?(t=D&&(0,l.jsx)(o,{ariaLabel:`Line ${N?x.right?.toString():x.left?.toString()}`,lineType:x.type,interactiveProps:y?{onClick:y}:null,isHighlighted:f,children:N&&x.right}),d[9]=f,d[10]=x.left,d[11]=x.right,d[12]=x.type,d[13]=y,d[14]=N,d[15]=D,d[16]=t):t=d[16],d[17]!==c||d[18]!==f||d[19]!==h||d[20]!==x||d[21]!==m||d[22]!==p?(r=(0,l.jsx)(s,{dragging:c,isHighlighted:f,isLeftColumn:h,line:x,lineAnchor:m,lineChild:p}),d[17]=c,d[18]=f,d[19]=h,d[20]=x,d[21]=m,d[22]=p,d[23]=r):r=d[23],d[24]!==i||d[25]!==t||d[26]!==r?(a=(0,l.jsxs)(l.Fragment,{children:[i,t,r]}),d[24]=i,d[25]=t,d[26]=r,d[27]=a):a=d[27],a};try{c.displayName||(c.displayName="DiffLinePart")}catch{}var f=t(75177);function h(){let e,i,t,r=(0,n.c)(3);return r[0]===Symbol.for("react.memo_cache_sentinel")?(e={bottom:"0 !important",clip:"rect(1px, 1px, 1px, 1px)",clipPath:"inset(50%)",height:"84px",position:"absolute",width:"320px"},r[0]=e):e=r[0],r[1]===Symbol.for("react.memo_cache_sentinel")?(i=(0,l.jsxs)("clipPath",{id:"diff-placeholder",children:[(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"67.0175439",x:"0",y:"0"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"100.701754",x:"18.9473684",y:"47.7194983"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"37.8947368",x:"0",y:"71.930126"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"53.3333333",x:"127.017544",y:"48.0703769"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"72.9824561",x:"187.719298",y:"48.0703769"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"140.350877",x:"76.8421053",y:"0"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"140.350877",x:"17.8947368",y:"23.8597491"}),(0,l.jsx)("rect",{height:"11.9298746",rx:"2",width:"173.684211",x:"166.315789",y:"23.8597491"})]}),r[1]=i):i=r[1],r[2]===Symbol.for("react.memo_cache_sentinel")?(t=(0,l.jsx)(f.A,{"aria-hidden":"true",as:"svg",version:"1.1",viewBox:"0 0 340 84",xmlns:"http://www.w3.org/2000/svg",sx:e,children:(0,l.jsxs)("defs",{children:[i,(0,l.jsxs)("linearGradient",{id:"animated-diff-gradient",spreadMethod:"reflect",x1:"0",x2:"0",y1:"0",y2:"1",children:[(0,l.jsx)("stop",{offset:"0",stopColor:"#eee"}),(0,l.jsx)("stop",{offset:"0.2",stopColor:"#eee"}),(0,l.jsx)("stop",{offset:"0.5",stopColor:"#ddd"}),(0,l.jsx)("stop",{offset:"0.8",stopColor:"#eee"}),(0,l.jsx)("stop",{offset:"1",stopColor:"#eee"}),(0,l.jsx)("animateTransform",{attributeName:"y1",dur:"1s",repeatCount:"3",values:"0%; 100%; 0"}),(0,l.jsx)("animateTransform",{attributeName:"y2",dur:"1s",repeatCount:"3",values:"100%; 200%; 0"})]})]})}),r[2]=t):t=r[2],t}try{h.displayName||(h.displayName="DiffPlaceholder")}catch{}var u=t(42597);t(24965);var m=t(38621);function x(e){let i,t,r,a=(0,n.c)(10),{currentLine:d,hunkButton:c,isLeftColumn:f,isSplit:h}=e;if(h&&!f)return null;a[0]!==d.type||a[1]!==c||a[2]!==h?(i=c?(0,l.jsx)(o,{colSpan:h?1:2,hasExpanderButton:!0,lineType:d.type,children:c}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(o,{lineType:d.type,children:(0,l.jsx)(m.KebabHorizontalIcon,{})}),!h&&(0,l.jsx)(o,{lineType:d.type,children:(0,l.jsx)(m.KebabHorizontalIcon,{})})]}),a[0]=d.type,a[1]=c,a[2]=h,a[3]=i):i=a[3];let u=h?3:void 0;return a[4]!==d||a[5]!==u?(t=(0,l.jsx)(s,{isHighlighted:!1,isLeftColumn:!0,colSpan:u,line:d}),a[4]=d,a[5]=u,a[6]=t):t=a[6],a[7]!==i||a[8]!==t?(r=(0,l.jsxs)(l.Fragment,{children:[i,t]}),a[7]=i,a[8]=t,a[9]=r):r=a[9],r}try{x.displayName||(x.displayName="HunkHeaderDiffLine")}catch{}function p(){let e,i=(0,n.c)(1);return i[0]===Symbol.for("react.memo_cache_sentinel")?(e=(0,l.jsx)("div",{className:"hunk-kebab-icon pr-2 pb-1",children:(0,l.jsx)(m.KebabHorizontalIcon,{})}),i[0]=e):e=i[0],e}try{p.displayName||(p.displayName="HunkKebabIcon")}catch{}function y(e){let i,t,a,d=(0,n.c)(5),{isLeftColumn:s}=e;return d[0]===Symbol.for("react.memo_cache_sentinel")?(i=(0,l.jsx)(o,{lineType:"EMPTY"}),d[0]=i):i=d[0],d[1]!==s?(t=(0,r.$)("empty-diff-line",{"border-right":s}),d[1]=s,d[2]=t):t=d[2],d[3]!==t?(a=(0,l.jsxs)(l.Fragment,{children:[i,(0,l.jsx)("td",{className:t,colSpan:1})]}),d[3]=t,d[4]=a):a=d[4],a}try{y.displayName||(y.displayName="EmptyDiffLine")}catch{}function g(e){let i,t,r,a,d,s,o,c,f=(0,n.c)(15);return f[0]===Symbol.for("react.memo_cache_sentinel")?(i=(0,l.jsx)("thead",{className:"sr-only",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{scope:"col",children:"Original file line number"}),(0,l.jsx)("th",{scope:"col",children:"Original file line"}),(0,l.jsx)("th",{scope:"col",children:"Diff line number"}),(0,l.jsx)("th",{scope:"col",children:"Diff line change"})]})}),f[0]=i):i=f[0],f[1]!==e.lineWidth?(t=(0,l.jsx)("col",{width:e.lineWidth}),f[1]=e.lineWidth,f[2]=t):t=f[2],f[3]===Symbol.for("react.memo_cache_sentinel")?(r=(0,l.jsx)("col",{}),f[3]=r):r=f[3],f[4]!==e.lineWidth?(a=(0,l.jsx)("col",{width:e.lineWidth}),f[4]=e.lineWidth,f[5]=a):a=f[5],f[6]===Symbol.for("react.memo_cache_sentinel")?(d=(0,l.jsx)("col",{}),f[6]=d):d=f[6],f[7]!==t||f[8]!==a?(s=(0,l.jsxs)("colgroup",{children:[t,r,a,d]}),f[7]=t,f[8]=a,f[9]=s):s=f[9],f[10]!==e.children?(o=(0,l.jsx)("tbody",{children:e.children}),f[10]=e.children,f[11]=o):o=f[11],f[12]!==s||f[13]!==o?(c=(0,l.jsxs)(l.Fragment,{children:[i,s,o]}),f[12]=s,f[13]=o,f[14]=c):c=f[14],c}try{g.displayName||(g.displayName="SplitDiffTable")}catch{}function b(e){let i,t,r,a,d,s,o,c=(0,n.c)(13);return c[0]===Symbol.for("react.memo_cache_sentinel")?(i=(0,l.jsx)("thead",{className:"sr-only",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{scope:"col",children:"Original file line number"}),(0,l.jsx)("th",{scope:"col",children:"Diff line number"}),(0,l.jsx)("th",{scope:"col",children:"Diff line change"})]})}),c[0]=i):i=c[0],c[1]!==e.lineWidth?(t=(0,l.jsx)("col",{width:e.lineWidth}),r=(0,l.jsx)("col",{width:e.lineWidth}),c[1]=e.lineWidth,c[2]=t,c[3]=r):(t=c[2],r=c[3]),c[4]===Symbol.for("react.memo_cache_sentinel")?(a=(0,l.jsx)("col",{width:"100%"}),c[4]=a):a=c[4],c[5]!==t||c[6]!==r?(d=(0,l.jsxs)("colgroup",{children:[t,r,a]}),c[5]=t,c[6]=r,c[7]=d):d=c[7],c[8]!==e.children?(s=(0,l.jsx)("tbody",{children:e.children}),c[8]=e.children,c[9]=s):s=c[9],c[10]!==d||c[11]!==s?(o=(0,l.jsxs)(l.Fragment,{children:[i,d,s]}),c[10]=d,c[11]=s,c[12]=o):o=c[12],o}try{b.displayName||(b.displayName="UnifiedDiffTable")}catch{}let j={diffTextCell:"UnifiedDiffLines-module__diffTextCell--UAuIr",diffHunkCell:"UnifiedDiffLines-module__diffHunkCell--qPkzg",diffHunkText:"UnifiedDiffLines-module__diffHunkText--Sfwuu",diffTextInner:"UnifiedDiffLines-module__diffTextInner--IuEAA",diffTextMarker:"UnifiedDiffLines-module__diffTextMarker--MdAo4",syntaxHighlightedAdditionLine:"UnifiedDiffLines-module__syntaxHighlightedAdditionLine--lU_S2",syntaxHighlightedDeletionLine:"UnifiedDiffLines-module__syntaxHighlightedDeletionLine--xgQwN",diffLineNumber:"UnifiedDiffLines-module__diffLineNumber--bzzxf",unifiedDiffLines:"UnifiedDiffLines-module__unifiedDiffLines--SSvV2"};function N(e){let i,t,r,a,s=(0,n.c)(9),{line:o,children:c}=e;return s[0]!==o.type?(i=(0,d.gK)(o.type,!0),s[0]=o.type,s[1]=i):i=s[1],s[2]!==i?(t={backgroundColor:i,textAlign:"center"},s[2]=i,s[3]=t):t=s[3],s[4]!==c?(r=(0,l.jsx)("code",{children:c}),s[4]=c,s[5]=r):r=s[5],s[6]!==t||s[7]!==r?(a=(0,l.jsx)("td",{className:j.diffLineNumber,style:t,children:r}),s[6]=t,s[7]=r,s[8]=a):a=s[8],a}function D(e){let i,t,d,s=(0,n.c)(4),{line:o}=e;s[0]===Symbol.for("react.memo_cache_sentinel")?(i=(0,l.jsx)(p,{}),s[0]=i):i=s[0],s[1]===Symbol.for("react.memo_cache_sentinel")?(t=(0,r.$)(j.diffTextInner,"color-fg-muted"),s[1]=t):t=s[1];let c=o.html;return s[2]!==c?(d=(0,l.jsx)("td",{colSpan:4,className:j.diffHunkCell,valign:"top",children:(0,l.jsxs)("div",{className:"d-flex flex-row",children:[i,(0,l.jsx)("code",{className:j.diffHunkText,children:(0,l.jsx)(a.$6,{className:t,html:c})})]})}),s[2]=c,s[3]=d):d=s[3],d}function _(e){let i,t,s,o=(0,n.c)(13),{line:c}=e,f="HUNK"===c.type,h="ADDITION"!==c.type,u="DELETION"!==c.type,m="ADDITION"===c.type?"+":"DELETION"===c.type?"-":void 0,x=!!m;return o[0]!==f||o[1]!==c?(i=f&&(0,l.jsx)(D,{line:c}),o[0]=f,o[1]=c,o[2]=i):i=o[2],o[3]!==f||o[4]!==c||o[5]!==m||o[6]!==h||o[7]!==x||o[8]!==u?(t=!f&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(N,{line:c,children:h&&c.left}),(0,l.jsx)(N,{line:c,children:u&&c.right}),(0,l.jsx)("td",{className:j.diffTextCell,style:{backgroundColor:(0,d.gK)(c.type,!1)},children:(0,l.jsxs)("code",{className:(0,r.$)("ADDITION"===c.type&&j.syntaxHighlightedAdditionLine,"DELETION"===c.type&&j.syntaxHighlightedDeletionLine),children:[x&&(0,l.jsx)("span",{className:j.diffTextMarker,children:m}),(0,l.jsx)(a.$6,{className:j.diffTextInner,html:c.html,style:{backgroundColor:(0,d.gK)(c.type,!1)}})]})})]}),o[3]=f,o[4]=c,o[5]=m,o[6]=h,o[7]=x,o[8]=u,o[9]=t):t=o[9],o[10]!==i||o[11]!==t?(s=(0,l.jsxs)("tr",{children:[i,t]}),o[10]=i,o[11]=t,o[12]=s):s=o[12],s}function C(e){let i,t,a,d,s=(0,n.c)(14),{className:o,lines:c,lineWidth:f,tabSize:h}=e;if(s[0]!==c||s[1]!==h){let e;s[3]!==h?(e=(e,i)=>(0,l.jsx)(_,{line:e,tabSize:h},i),s[3]=h,s[4]=e):e=s[4],i=c.map(e),s[0]=c,s[1]=h,s[2]=i}else i=s[2];let u=i;return s[5]!==o?(t=(0,r.$)(j.unifiedDiffLines,o,"tab-size"),s[5]=o,s[6]=t):t=s[6],s[7]!==u||s[8]!==f?(a=(0,l.jsx)(b,{lineWidth:f,children:u}),s[7]=u,s[8]=f,s[9]=a):a=s[9],s[10]!==t||s[11]!==a||s[12]!==h?(d=(0,l.jsx)("table",{className:t,"data-tab-size":h,children:a}),s[10]=t,s[11]=a,s[12]=h,s[13]=d):d=s[13],d}try{N.displayName||(N.displayName="LineNumberCell")}catch{}try{D.displayName||(D.displayName="HunkCell")}catch{}try{_.displayName||(_.displayName="UnifiedDiffRow")}catch{}try{C.displayName||(C.displayName="UnifiedDiffLines")}catch{}function T(e){return"LEFT"===e?"-":"RIGHT"===e?"+":""}function L({startDiffSide:e,endDiffSide:i,originalStartLine:t,originalEndLine:l}){return e&&i&&"number"==typeof t&&"number"==typeof l?0===t&&1===l&&e===i?"-1 to +1":e===i&&t===l?`${T(e)}${t}`:`${T(e)}${t} to ${T(i)}${l}`:""}},42597:(e,i,t)=>{t.d(i,{u:()=>d});var l=t(74848),n=t(21728),r=t(34164);let a={diffSquare:"DiffSquares-module__diffSquare--h5kjy",addition:"DiffSquares-module__addition--jeNtt",deletion:"DiffSquares-module__deletion--hKV3q",neutral:"DiffSquares-module__neutral--VlyoP"};function d(e){let i,t,r=(0,n.c)(4),{squares:a}=e;return r[0]!==a?(i=a.map(s),r[0]=a,r[1]=i):i=r[1],r[2]!==i?(t=(0,l.jsx)("div",{className:"d-flex",children:i}),r[2]=i,r[3]=t):t=r[3],t}function s(e,i){return(0,l.jsx)("div",{"data-testid":`${e} diffstat`,className:(0,r.$)(a.diffSquare,a[e])},i)}try{d.displayName||(d.displayName="DiffSquares")}catch{}},24965:(e,i,t)=>{t.d(i,{z:()=>d});var l=t(74848),n=t(21728),r=t(42597);let a={addition:"addition",deletion:"deletion",neutral:"neutral"};function d(e){let i,t,d,s,o,c,f,h=(0,n.c)(20),{linesAdded:u,linesDeleted:m,linesChanged:x}=e,p=void 0===u?0:u,y=void 0===m?0:m,g=void 0===x?0:x;if(!p&&!y&&!g||0===g)return null;if(h[0]!==p||h[1]!==g||h[2]!==y){let{greenSquares:e,redSquares:t,graySquares:l}=function(e,i,t){let l=t>5?5/t:1,n=Math.floor(e*l),r=Math.floor(i*l);return{greenSquares:n,redSquares:r,graySquares:5-n-r}}(p,y,g);i=[...Array(e).fill(a.addition),...Array(t).fill(a.deletion),...Array(l).fill(a.neutral)],h[0]=p,h[1]=g,h[2]=y,h[3]=i}else i=h[3];let b=i;return h[4]!==p?(t=p>0&&(0,l.jsxs)("span",{"aria-hidden":"true",className:"f6 text-bold fgColor-success",children:["+",p.toLocaleString()]}),h[4]=p,h[5]=t):t=h[5],h[6]!==y?(d=y>0&&(0,l.jsxs)("span",{"aria-hidden":"true",className:"f6 text-bold fgColor-danger",children:["-",y.toLocaleString()]}),h[6]=y,h[7]=d):d=h[7],h[8]!==p||h[9]!==y?(s=function({linesAdded:e,linesDeleted:i}){let t="Lines changed: ";return t+`${e} ${1===e?"addition":"additions"} & ${i} ${1===i?"deletion":"deletions"}`}({linesAdded:p,linesDeleted:y}),h[8]=p,h[9]=y,h[10]=s):s=h[10],h[11]!==s?(o=(0,l.jsx)("span",{className:"sr-only",children:s}),h[11]=s,h[12]=o):o=h[12],h[13]!==b?(c=(0,l.jsx)(r.u,{squares:b}),h[13]=b,h[14]=c):c=h[14],h[15]!==t||h[16]!==d||h[17]!==o||h[18]!==c?(f=(0,l.jsxs)("div",{className:"d-flex flex-items-center gap-1 pl-1",children:[t,d,o,c]}),h[15]=t,h[16]=d,h[17]=o,h[18]=c,h[19]=f):f=h[19],f}try{d.displayName||(d.displayName="DiffStats")}catch{}}}]);
//# sourceMappingURL=ui_packages_diffs_diff-parts_ts-ceee561bf244.js.map