[data-color-mode="light"][data-light-theme="light_high_contrast"],
[data-color-mode="light"][data-light-theme="light_high_contrast"] ::backdrop,
[data-color-mode="auto"][data-light-theme="light_high_contrast"],
[data-color-mode="auto"][data-light-theme="light_high_contrast"] ::backdrop {
  --button-outline-bgColor-active: #033f9d;
  --button-primary-bgColor-active: #03501b;
  --button-primary-bgColor-disabled: #85cb97;
  --button-primary-bgColor-hover: #04571e;
  --button-primary-borderColor-disabled: #85cb97;
  --color-ansi-cyan: #1b7c83;
  --color-ansi-cyan-bright: #3192aa;
  --control-checked-bgColor-active: #033f9d;
  --control-checked-bgColor-hover: #0344a8;
  --control-checked-borderColor-active: #033f9d;
  --control-checked-borderColor-hover: #0344a8;
  --control-danger-bgColor-active: #8c0b1d;
  --reactionButton-selected-bgColor-hover: #c7e9ff;
  --avatarStack-fade-bgColor-default: #c8d1da;
  --avatarStack-fade-bgColor-muted: #dae0e7;
  --bgColor-accent-emphasis: #0349b4;
  --bgColor-accent-muted: #dff7ff;
  --bgColor-attention-emphasis: #744500;
  --bgColor-attention-muted: #fcf7be;
  --bgColor-danger-emphasis: #a0111f;
  --bgColor-danger-muted: #fff0ee;
  --bgColor-disabled: #e0e6eb;
  --bgColor-done-emphasis: #622cbc;
  --bgColor-done-muted: #faf0fe;
  --bgColor-emphasis: #25292e;
  --bgColor-inset: #eff2f5;
  --bgColor-inverse: #25292e;
  --bgColor-muted: #e6eaef;
  --bgColor-neutral-emphasis: #454c54;
  --bgColor-neutral-muted: #e0e6eb;
  --bgColor-severe-emphasis: #873800;
  --bgColor-severe-muted: #fff2d5;
  --bgColor-sponsors-emphasis: #971368;
  --bgColor-sponsors-muted: #feeff7;
  --bgColor-success-emphasis: #055d20;
  --bgColor-success-muted: #d2fedb;
  --bgColor-transparent: #ffffff00;
  --borderColor-accent-emphasis: #0349b4;
  --borderColor-accent-muted: #368cf9;
  --borderColor-attention-emphasis: #744500;
  --borderColor-attention-muted: #b58407;
  --borderColor-danger-emphasis: #a0111f;
  --borderColor-danger-muted: #ee5a5d;
  --borderColor-default: #454c54;
  --borderColor-disabled: #59636e1f;
  --borderColor-done-emphasis: #622cbc;
  --borderColor-done-muted: #a371f7;
  --borderColor-neutral-emphasis: #59636e;
  --borderColor-severe-emphasis: #873800;
  --borderColor-severe-muted: #dc6d1a;
  --borderColor-sponsors-emphasis: #971368;
  --borderColor-sponsors-muted: #ed4baf;
  --borderColor-success-emphasis: #055d20;
  --borderColor-success-muted: #26a148;
  --borderColor-translucent: #59636e;
  --borderColor-transparent: #ffffff00;
  --button-danger-bgColor-active: #86061d;
  --button-danger-borderColor-hover: #6e011a;
  --button-danger-shadow-selected: inset 0px 1px 0px 0px #43001133;
  --button-inactive-fgColor: #454c54;
  --button-invisible-bgColor-disabled: #ffffff00;
  --button-invisible-borderColor-disabled: #ffffff00;
  --button-invisible-fgColor-hover: #393f46;
  --button-invisible-iconColor-hover: #393f46;
  --button-outline-borderColor-hover: #022f7a;
  --button-outline-shadow-selected: inset 0px 1px 0px 0px #021a4a33;
  --button-primary-bgColor-rest: #055d20;
  --button-primary-borderColor-rest: #013d14;
  --button-primary-shadow-selected: inset 0px 1px 0px 0px #00230b4d;
  --button-star-iconColor: #d5a824;
  --buttonCounter-default-bgColor-rest: #c8d1da;
  --buttonCounter-outline-fgColor-rest: #023b95;
  --buttonCounter-primary-bgColor-rest: #00230b33;
  --codeMirror-syntax-fgColor-constant: #023b95;
  --codeMirror-syntax-fgColor-entity: #622cbc;
  --codeMirror-syntax-fgColor-keyword: #a0111f;
  --codeMirror-syntax-fgColor-storage: #a0111f;
  --codeMirror-syntax-fgColor-string: #032563;
  --codeMirror-syntax-fgColor-support: #023b95;
  --codeMirror-syntax-fgColor-variable: #702c00;
  --color-ansi-black-bright: #393f46;
  --color-ansi-blue: #0349b4;
  --color-ansi-blue-bright: #1168e3;
  --color-ansi-gray: #59636e;
  --color-ansi-green: #024c1a;
  --color-ansi-green-bright: #055d20;
  --color-ansi-magenta: #622cbc;
  --color-ansi-magenta-bright: #844ae7;
  --color-ansi-red: #a0111f;
  --color-ansi-red-bright: #86061d;
  --color-ansi-white: #59636e;
  --color-ansi-white-bright: #818b98;
  --color-ansi-yellow: #3f2200;
  --color-ansi-yellow-bright: #4e2c00;
  --color-prettylights-syntax-brackethighlighter-angle: #59636e;
  --color-prettylights-syntax-brackethighlighter-unmatched: #6e011a;
  --color-prettylights-syntax-carriage-return-bg: #a0111f;
  --color-prettylights-syntax-carriage-return-text: #f6f8fa;
  --color-prettylights-syntax-comment: #59636e;
  --color-prettylights-syntax-constant: #023b95;
  --color-prettylights-syntax-constant-other-reference-link: #032563;
  --color-prettylights-syntax-entity: #512598;
  --color-prettylights-syntax-entity-tag: #023b95;
  --color-prettylights-syntax-invalid-illegal-bg: #6e011a;
  --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
  --color-prettylights-syntax-keyword: #a0111f;
  --color-prettylights-syntax-markup-changed-bg: #ffc67b;
  --color-prettylights-syntax-markup-changed-text: #702c00;
  --color-prettylights-syntax-markup-deleted-bg: #fff0ee;
  --color-prettylights-syntax-markup-deleted-text: #6e011a;
  --color-prettylights-syntax-markup-heading: #023b95;
  --color-prettylights-syntax-markup-ignored-bg: #023b95;
  --color-prettylights-syntax-markup-ignored-text: #d1d9e0;
  --color-prettylights-syntax-markup-inserted-bg: #d2fedb;
  --color-prettylights-syntax-markup-inserted-text: #024c1a;
  --color-prettylights-syntax-markup-list: #2e1800;
  --color-prettylights-syntax-meta-diff-range: #622cbc;
  --color-prettylights-syntax-string: #032563;
  --color-prettylights-syntax-string-regexp: #024c1a;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #818b98;
  --color-prettylights-syntax-variable: #702c00;
  --contribution-default-bgColor-1: #82e596;
  --contribution-default-bgColor-2: #26a148;
  --contribution-default-bgColor-3: #117f32;
  --contribution-default-bgColor-4: #024c1a;
  --contribution-default-borderColor-0: #010409;
  --contribution-halloween-bgColor-1: #f0db3d;
  --contribution-halloween-bgColor-2: #ffd642;
  --contribution-halloween-bgColor-3: #f68c41;
  --contribution-halloween-bgColor-4: #010409;
  --contribution-winter-bgColor-1: #9cd7ff;
  --contribution-winter-bgColor-2: #368cf9;
  --contribution-winter-bgColor-3: #0349b4;
  --contribution-winter-bgColor-4: #032563;
  --control-bgColor-active: #d1d9e0;
  --control-bgColor-hover: #dae0e7;
  --control-bgColor-rest: #e0e6eb;
  --control-fgColor-placeholder: #454c54;
  --control-fgColor-rest: #25292e;
  --control-transparent-bgColor-active: #d1d9e0;
  --control-transparent-bgColor-hover: #dae0e7;
  --control-transparent-bgColor-rest: #ffffff00;
  --control-transparent-bgColor-selected: #dae0e7;
  --control-transparent-borderColor-rest: #ffffff00;
  --controlTrack-bgColor-active: #c8d1da;
  --controlTrack-bgColor-hover: #d1d9e0;
  --controlTrack-bgColor-rest: #dae0e7;
  --controlTrack-fgColor-rest: #59636e;
  --data-auburn-color-emphasis: #9d615c;
  --data-auburn-color-muted: #f2e9e9;
  --data-blue-color-emphasis: #006edb;
  --data-blue-color-muted: #d1f0ff;
  --data-brown-color-emphasis: #856d4c;
  --data-brown-color-muted: #eeeae2;
  --data-coral-color-emphasis: #d43511;
  --data-coral-color-muted: #ffe5db;
  --data-gray-color-emphasis: #808fa3;
  --data-gray-color-muted: #e8ecf2;
  --data-green-color-emphasis: #30a147;
  --data-green-color-muted: #caf7ca;
  --data-lemon-color-emphasis: #866e04;
  --data-lemon-color-muted: #f7eea1;
  --data-lime-color-emphasis: #527a29;
  --data-lime-color-muted: #e3f2b5;
  --data-olive-color-emphasis: #64762d;
  --data-olive-color-muted: #f0f0ad;
  --data-orange-color-emphasis: #eb670f;
  --data-orange-color-muted: #ffe7d1;
  --data-pine-color-emphasis: #167e53;
  --data-pine-color-muted: #bff8db;
  --data-pink-color-emphasis: #ce2c85;
  --data-pink-color-muted: #ffe5f1;
  --data-plum-color-emphasis: #a830e8;
  --data-plum-color-muted: #f8e5ff;
  --data-purple-color-emphasis: #894ceb;
  --data-purple-color-muted: #f1e5ff;
  --data-red-color-emphasis: #df0c24;
  --data-red-color-muted: #ffe2e0;
  --data-teal-color-emphasis: #179b9b;
  --data-teal-color-muted: #c7f5ef;
  --data-yellow-color-emphasis: #b88700;
  --data-yellow-color-muted: #ffec9e;
  --diffBlob-additionNum-bgColor: #82e596;
  --diffBlob-additionWord-bgColor: #055d20;
  --diffBlob-deletionNum-bgColor: #ffc1bc;
  --diffBlob-deletionWord-bgColor: #a0111f;
  --diffBlob-hunkNum-bgColor-rest: #9cd7ff;
  --display-auburn-bgColor-emphasis: #744744;
  --display-auburn-bgColor-muted: #f2e9e9;
  --display-auburn-borderColor-emphasis: #8a5551;
  --display-auburn-borderColor-muted: #e6d6d5;
  --display-auburn-fgColor: #5d3937;
  --display-auburn-scale-0: #f2e9e9;
  --display-auburn-scale-1: #e6d6d5;
  --display-auburn-scale-2: #d4b7b5;
  --display-auburn-scale-3: #c59e9b;
  --display-auburn-scale-4: #b4827e;
  --display-auburn-scale-5: #9d615c;
  --display-auburn-scale-6: #8a5551;
  --display-auburn-scale-7: #744744;
  --display-auburn-scale-8: #5d3937;
  --display-auburn-scale-9: #432928;
  --display-blue-bgColor-emphasis: #004db3;
  --display-blue-bgColor-muted: #d1f0ff;
  --display-blue-borderColor-emphasis: #005fcc;
  --display-blue-borderColor-muted: #ade1ff;
  --display-blue-fgColor: #003d99;
  --display-blue-scale-0: #d1f0ff;
  --display-blue-scale-1: #ade1ff;
  --display-blue-scale-2: #75c8ff;
  --display-blue-scale-3: #47afff;
  --display-blue-scale-4: #0f8fff;
  --display-blue-scale-5: #006edb;
  --display-blue-scale-6: #005fcc;
  --display-blue-scale-7: #004db3;
  --display-blue-scale-8: #003d99;
  --display-blue-scale-9: #002b75;
  --display-brown-bgColor-emphasis: #64513a;
  --display-brown-bgColor-muted: #eeeae2;
  --display-brown-borderColor-emphasis: #755f43;
  --display-brown-borderColor-muted: #dfd7c8;
  --display-brown-fgColor: #51412f;
  --display-brown-scale-0: #eeeae2;
  --display-brown-scale-1: #dfd7c8;
  --display-brown-scale-2: #cbbda4;
  --display-brown-scale-3: #b8a484;
  --display-brown-scale-4: #a68b64;
  --display-brown-scale-5: #856d4c;
  --display-brown-scale-6: #755f43;
  --display-brown-scale-7: #64513a;
  --display-brown-scale-8: #51412f;
  --display-brown-scale-9: #3a2e22;
  --display-coral-bgColor-emphasis: #9b2712;
  --display-coral-bgColor-muted: #ffe5db;
  --display-coral-borderColor-emphasis: #ba2e12;
  --display-coral-borderColor-muted: #fecebe;
  --display-coral-fgColor: #7e2011;
  --display-coral-scale-0: #ffe5db;
  --display-coral-scale-1: #fecebe;
  --display-coral-scale-2: #fcab92;
  --display-coral-scale-3: #f88768;
  --display-coral-scale-4: #f25f3a;
  --display-coral-scale-5: #d43511;
  --display-coral-scale-6: #ba2e12;
  --display-coral-scale-7: #9b2712;
  --display-coral-scale-8: #7e2011;
  --display-coral-scale-9: #5d180e;
  --display-cyan-bgColor-emphasis: #00596b;
  --display-cyan-bgColor-muted: #bdf4ff;
  --display-cyan-borderColor-emphasis: #006a80;
  --display-cyan-borderColor-muted: #7ae9ff;
  --display-cyan-fgColor: #004857;
  --display-cyan-scale-0: #bdf4ff;
  --display-cyan-scale-1: #7ae9ff;
  --display-cyan-scale-2: #00d0fa;
  --display-cyan-scale-3: #00b7db;
  --display-cyan-scale-4: #0099b8;
  --display-cyan-scale-5: #007b94;
  --display-cyan-scale-6: #006a80;
  --display-cyan-scale-7: #00596b;
  --display-cyan-scale-8: #004857;
  --display-cyan-scale-9: #003742;
  --display-gray-bgColor-emphasis: #4e535a;
  --display-gray-bgColor-muted: #e8ecf2;
  --display-gray-borderColor-emphasis: #5c6570;
  --display-gray-borderColor-muted: #d2dae4;
  --display-gray-fgColor: #424448;
  --display-gray-scale-0: #e8ecf2;
  --display-gray-scale-1: #d2dae4;
  --display-gray-scale-2: #b4c0cf;
  --display-gray-scale-3: #9ba9bb;
  --display-gray-scale-4: #808fa3;
  --display-gray-scale-5: #647182;
  --display-gray-scale-6: #5c6570;
  --display-gray-scale-7: #4e535a;
  --display-gray-scale-8: #424448;
  --display-gray-scale-9: #303031;
  --display-green-bgColor-emphasis: #285c3b;
  --display-green-bgColor-muted: #caf7ca;
  --display-green-borderColor-emphasis: #2b6e3f;
  --display-green-borderColor-muted: #9ceda0;
  --display-green-fgColor: #254b34;
  --display-green-scale-0: #caf7ca;
  --display-green-scale-1: #9ceda0;
  --display-green-scale-2: #54d961;
  --display-green-scale-3: #31bf46;
  --display-green-scale-4: #30a147;
  --display-green-scale-5: #2c8141;
  --display-green-scale-6: #2b6e3f;
  --display-green-scale-7: #285c3b;
  --display-green-scale-8: #254b34;
  --display-green-scale-9: #1d3528;
  --display-indigo-bgColor-emphasis: #393cd5;
  --display-indigo-bgColor-muted: #e5e9ff;
  --display-indigo-borderColor-emphasis: #494edf;
  --display-indigo-borderColor-muted: #d2d7fe;
  --display-indigo-fgColor: #2d2db4;
  --display-indigo-scale-0: #e5e9ff;
  --display-indigo-scale-1: #d2d7fe;
  --display-indigo-scale-2: #b1b9fb;
  --display-indigo-scale-3: #979ff7;
  --display-indigo-scale-4: #7a82f0;
  --display-indigo-scale-5: #5a61e7;
  --display-indigo-scale-6: #494edf;
  --display-indigo-scale-7: #393cd5;
  --display-indigo-scale-8: #2d2db4;
  --display-indigo-scale-9: #25247b;
  --display-lemon-bgColor-emphasis: #654f01;
  --display-lemon-bgColor-muted: #f7eea1;
  --display-lemon-borderColor-emphasis: #786002;
  --display-lemon-borderColor-muted: #f0db3d;
  --display-lemon-fgColor: #523f00;
  --display-lemon-scale-0: #f7eea1;
  --display-lemon-scale-1: #f0db3d;
  --display-lemon-scale-2: #d8bd0e;
  --display-lemon-scale-3: #c2a60a;
  --display-lemon-scale-4: #a68c07;
  --display-lemon-scale-5: #866e04;
  --display-lemon-scale-6: #786002;
  --display-lemon-scale-7: #654f01;
  --display-lemon-scale-8: #523f00;
  --display-lemon-scale-9: #3d2e00;
  --display-lime-bgColor-emphasis: #3a5b25;
  --display-lime-bgColor-muted: #e3f2b5;
  --display-lime-borderColor-emphasis: #476c28;
  --display-lime-borderColor-muted: #c7e580;
  --display-lime-fgColor: #2f4a21;
  --display-lime-scale-0: #e3f2b5;
  --display-lime-scale-1: #c7e580;
  --display-lime-scale-2: #9bd039;
  --display-lime-scale-3: #80b530;
  --display-lime-scale-4: #6c9d2f;
  --display-lime-scale-5: #527a29;
  --display-lime-scale-6: #476c28;
  --display-lime-scale-7: #3a5b25;
  --display-lime-scale-8: #2f4a21;
  --display-lime-scale-9: #213319;
  --display-olive-bgColor-emphasis: #495a2b;
  --display-olive-bgColor-muted: #f0f0ad;
  --display-olive-borderColor-emphasis: #56682c;
  --display-olive-borderColor-muted: #dbe170;
  --display-olive-fgColor: #3b4927;
  --display-olive-scale-0: #f0f0ad;
  --display-olive-scale-1: #dbe170;
  --display-olive-scale-2: #b9c832;
  --display-olive-scale-3: #9bae32;
  --display-olive-scale-4: #819532;
  --display-olive-scale-5: #64762d;
  --display-olive-scale-6: #56682c;
  --display-olive-scale-7: #495a2b;
  --display-olive-scale-8: #3b4927;
  --display-olive-scale-9: #2a331f;
  --display-orange-bgColor-emphasis: #8d3c11;
  --display-orange-bgColor-muted: #ffe7d1;
  --display-orange-borderColor-emphasis: #a24610;
  --display-orange-borderColor-muted: #fecfaa;
  --display-orange-fgColor: #70300f;
  --display-orange-scale-0: #ffe7d1;
  --display-orange-scale-1: #fecfaa;
  --display-orange-scale-2: #fbaf74;
  --display-orange-scale-3: #f68c41;
  --display-orange-scale-4: #eb670f;
  --display-orange-scale-5: #b8500f;
  --display-orange-scale-6: #a24610;
  --display-orange-scale-7: #8d3c11;
  --display-orange-scale-8: #70300f;
  --display-orange-scale-9: #54230d;
  --display-pine-bgColor-emphasis: #135d41;
  --display-pine-bgColor-muted: #bff8db;
  --display-pine-borderColor-emphasis: #156f4b;
  --display-pine-borderColor-muted: #80efb9;
  --display-pine-fgColor: #114b36;
  --display-pine-scale-0: #bff8db;
  --display-pine-scale-1: #80efb9;
  --display-pine-scale-2: #1dd781;
  --display-pine-scale-3: #1dbf76;
  --display-pine-scale-4: #1aa267;
  --display-pine-scale-5: #167e53;
  --display-pine-scale-6: #156f4b;
  --display-pine-scale-7: #135d41;
  --display-pine-scale-8: #114b36;
  --display-pine-scale-9: #0d3627;
  --display-pink-bgColor-emphasis: #8e2e66;
  --display-pink-bgColor-muted: #ffe5f1;
  --display-pink-borderColor-emphasis: #b12f79;
  --display-pink-borderColor-muted: #fdc9e2;
  --display-pink-fgColor: #6e2b53;
  --display-pink-scale-0: #ffe5f1;
  --display-pink-scale-1: #fdc9e2;
  --display-pink-scale-2: #f8a5cf;
  --display-pink-scale-3: #f184bc;
  --display-pink-scale-4: #e55da5;
  --display-pink-scale-5: #ce2c85;
  --display-pink-scale-6: #b12f79;
  --display-pink-scale-7: #8e2e66;
  --display-pink-scale-8: #6e2b53;
  --display-pink-scale-9: #4d233d;
  --display-plum-bgColor-emphasis: #7d1eb8;
  --display-plum-bgColor-muted: #f8e5ff;
  --display-plum-borderColor-emphasis: #961edc;
  --display-plum-borderColor-muted: #f0cdfe;
  --display-plum-fgColor: #651d96;
  --display-plum-scale-0: #f8e5ff;
  --display-plum-scale-1: #f0cdfe;
  --display-plum-scale-2: #e2a7fb;
  --display-plum-scale-3: #d487f7;
  --display-plum-scale-4: #c264f2;
  --display-plum-scale-5: #a830e8;
  --display-plum-scale-6: #961edc;
  --display-plum-scale-7: #7d1eb8;
  --display-plum-scale-8: #651d96;
  --display-plum-scale-9: #471769;
  --display-purple-bgColor-emphasis: #6223d7;
  --display-purple-bgColor-muted: #f1e5ff;
  --display-purple-borderColor-emphasis: #783ae4;
  --display-purple-borderColor-muted: #e6d2fe;
  --display-purple-fgColor: #4f21ab;
  --display-purple-scale-0: #f1e5ff;
  --display-purple-scale-1: #e6d2fe;
  --display-purple-scale-2: #d1b1fc;
  --display-purple-scale-3: #bc91f8;
  --display-purple-scale-4: #a672f3;
  --display-purple-scale-5: #894ceb;
  --display-purple-scale-6: #783ae4;
  --display-purple-scale-7: #6223d7;
  --display-purple-scale-8: #4f21ab;
  --display-purple-scale-9: #391b79;
  --display-red-bgColor-emphasis: #a60c29;
  --display-red-bgColor-muted: #ffe2e0;
  --display-red-borderColor-emphasis: #c50d28;
  --display-red-borderColor-muted: #fecdcd;
  --display-red-fgColor: #880c27;
  --display-red-scale-0: #ffe2e0;
  --display-red-scale-1: #fecdcd;
  --display-red-scale-2: #fda5a7;
  --display-red-scale-3: #fb8389;
  --display-red-scale-4: #f85461;
  --display-red-scale-5: #df0c24;
  --display-red-scale-6: #c50d28;
  --display-red-scale-7: #a60c29;
  --display-red-scale-8: #880c27;
  --display-red-scale-9: #610a20;
  --display-teal-bgColor-emphasis: #0d5b63;
  --display-teal-bgColor-muted: #c7f5ef;
  --display-teal-borderColor-emphasis: #106e75;
  --display-teal-borderColor-muted: #89ebe1;
  --display-teal-fgColor: #0a4852;
  --display-teal-scale-0: #c7f5ef;
  --display-teal-scale-1: #89ebe1;
  --display-teal-scale-2: #22d3c7;
  --display-teal-scale-3: #1db9b4;
  --display-teal-scale-4: #179b9b;
  --display-teal-scale-5: #127e81;
  --display-teal-scale-6: #106e75;
  --display-teal-scale-7: #0d5b63;
  --display-teal-scale-8: #0a4852;
  --display-teal-scale-9: #073740;
  --display-yellow-bgColor-emphasis: #704d00;
  --display-yellow-bgColor-muted: #ffec9e;
  --display-yellow-borderColor-emphasis: #805900;
  --display-yellow-borderColor-muted: #ffd642;
  --display-yellow-fgColor: #5c3d00;
  --display-yellow-scale-0: #ffec9e;
  --display-yellow-scale-1: #ffd642;
  --display-yellow-scale-2: #ebb400;
  --display-yellow-scale-3: #d19d00;
  --display-yellow-scale-4: #b88700;
  --display-yellow-scale-5: #946a00;
  --display-yellow-scale-6: #805900;
  --display-yellow-scale-7: #704d00;
  --display-yellow-scale-8: #5c3d00;
  --display-yellow-scale-9: #422b00;
  --fgColor-accent: #023b95;
  --fgColor-attention: #603700;
  --fgColor-danger: #86061d;
  --fgColor-disabled: #59636e;
  --fgColor-done: #512598;
  --fgColor-muted: #454c54;
  --fgColor-neutral: #393f46;
  --fgColor-severe: #702c00;
  --fgColor-sponsors: #7d0c57;
  --fgColor-success: #024c1a;
  --header-bgColor: #25292e;
  --header-borderColor-divider: #c8d1da;
  --headerSearch-bgColor: #25292e;
  --headerSearch-borderColor: #818b98;
  --highlight-neutral-bgColor: #fcf7be;
  --label-auburn-bgColor-active: #d4b7b5;
  --label-auburn-bgColor-hover: #e6d6d5;
  --label-auburn-bgColor-rest: #f2e9e9;
  --label-auburn-fgColor-active: #5d3937;
  --label-auburn-fgColor-hover: #744744;
  --label-auburn-fgColor-rest: #8a5551;
  --label-blue-bgColor-active: #75c8ff;
  --label-blue-bgColor-hover: #ade1ff;
  --label-blue-bgColor-rest: #d1f0ff;
  --label-blue-fgColor-active: #003d99;
  --label-blue-fgColor-hover: #004db3;
  --label-blue-fgColor-rest: #005fcc;
  --label-brown-bgColor-active: #cbbda4;
  --label-brown-bgColor-hover: #dfd7c8;
  --label-brown-bgColor-rest: #eeeae2;
  --label-brown-fgColor-active: #51412f;
  --label-brown-fgColor-hover: #64513a;
  --label-brown-fgColor-rest: #755f43;
  --label-coral-bgColor-active: #fcab92;
  --label-coral-bgColor-hover: #fecebe;
  --label-coral-bgColor-rest: #ffe5db;
  --label-coral-fgColor-active: #7e2011;
  --label-coral-fgColor-hover: #9b2712;
  --label-coral-fgColor-rest: #ba2e12;
  --label-cyan-bgColor-active: #00d0fa;
  --label-cyan-bgColor-hover: #7ae9ff;
  --label-cyan-bgColor-rest: #bdf4ff;
  --label-cyan-fgColor-active: #004857;
  --label-cyan-fgColor-hover: #00596b;
  --label-cyan-fgColor-rest: #006a80;
  --label-gray-bgColor-active: #b4c0cf;
  --label-gray-bgColor-hover: #d2dae4;
  --label-gray-bgColor-rest: #e8ecf2;
  --label-gray-fgColor-active: #424448;
  --label-gray-fgColor-hover: #4e535a;
  --label-gray-fgColor-rest: #5c6570;
  --label-green-bgColor-active: #54d961;
  --label-green-bgColor-hover: #9ceda0;
  --label-green-bgColor-rest: #caf7ca;
  --label-green-fgColor-active: #254b34;
  --label-green-fgColor-hover: #285c3b;
  --label-green-fgColor-rest: #2b6e3f;
  --label-indigo-bgColor-active: #b1b9fb;
  --label-indigo-bgColor-hover: #d2d7fe;
  --label-indigo-bgColor-rest: #e5e9ff;
  --label-indigo-fgColor-active: #2d2db4;
  --label-indigo-fgColor-hover: #393cd5;
  --label-indigo-fgColor-rest: #494edf;
  --label-lemon-bgColor-active: #d8bd0e;
  --label-lemon-bgColor-hover: #f0db3d;
  --label-lemon-bgColor-rest: #f7eea1;
  --label-lemon-fgColor-active: #523f00;
  --label-lemon-fgColor-hover: #654f01;
  --label-lemon-fgColor-rest: #786002;
  --label-lime-bgColor-active: #9bd039;
  --label-lime-bgColor-hover: #c7e580;
  --label-lime-bgColor-rest: #e3f2b5;
  --label-lime-fgColor-active: #2f4a21;
  --label-lime-fgColor-hover: #3a5b25;
  --label-lime-fgColor-rest: #476c28;
  --label-olive-bgColor-active: #b9c832;
  --label-olive-bgColor-hover: #dbe170;
  --label-olive-bgColor-rest: #f0f0ad;
  --label-olive-fgColor-active: #3b4927;
  --label-olive-fgColor-hover: #495a2b;
  --label-olive-fgColor-rest: #56682c;
  --label-orange-bgColor-active: #fbaf74;
  --label-orange-bgColor-hover: #fecfaa;
  --label-orange-bgColor-rest: #ffe7d1;
  --label-orange-fgColor-active: #70300f;
  --label-orange-fgColor-hover: #8d3c11;
  --label-orange-fgColor-rest: #a24610;
  --label-pine-bgColor-active: #1dd781;
  --label-pine-bgColor-hover: #80efb9;
  --label-pine-bgColor-rest: #bff8db;
  --label-pine-fgColor-active: #114b36;
  --label-pine-fgColor-hover: #135d41;
  --label-pine-fgColor-rest: #156f4b;
  --label-pink-bgColor-active: #f8a5cf;
  --label-pink-bgColor-hover: #fdc9e2;
  --label-pink-bgColor-rest: #ffe5f1;
  --label-pink-fgColor-active: #6e2b53;
  --label-pink-fgColor-hover: #8e2e66;
  --label-pink-fgColor-rest: #b12f79;
  --label-plum-bgColor-active: #e2a7fb;
  --label-plum-bgColor-hover: #f0cdfe;
  --label-plum-bgColor-rest: #f8e5ff;
  --label-plum-fgColor-active: #651d96;
  --label-plum-fgColor-hover: #7d1eb8;
  --label-plum-fgColor-rest: #961edc;
  --label-purple-bgColor-active: #d1b1fc;
  --label-purple-bgColor-hover: #e6d2fe;
  --label-purple-bgColor-rest: #f1e5ff;
  --label-purple-fgColor-active: #4f21ab;
  --label-purple-fgColor-hover: #6223d7;
  --label-purple-fgColor-rest: #783ae4;
  --label-red-bgColor-active: #fda5a7;
  --label-red-bgColor-hover: #fecdcd;
  --label-red-bgColor-rest: #ffe2e0;
  --label-red-fgColor-active: #880c27;
  --label-red-fgColor-hover: #a60c29;
  --label-red-fgColor-rest: #c50d28;
  --label-teal-bgColor-active: #22d3c7;
  --label-teal-bgColor-hover: #89ebe1;
  --label-teal-bgColor-rest: #c7f5ef;
  --label-teal-fgColor-active: #0a4852;
  --label-teal-fgColor-hover: #0d5b63;
  --label-teal-fgColor-rest: #106e75;
  --label-yellow-bgColor-active: #ebb400;
  --label-yellow-bgColor-hover: #ffd642;
  --label-yellow-bgColor-rest: #ffec9e;
  --label-yellow-fgColor-active: #5c3d00;
  --label-yellow-fgColor-hover: #704d00;
  --label-yellow-fgColor-rest: #805900;
  --menu-bgColor-active: #ffffff00;
  --overlay-backdrop-bgColor: #393f4666;
  --reactionButton-selected-bgColor-rest: #dff7ff;
  --reactionButton-selected-fgColor-hover: #023b95;
  --selectMenu-bgColor-active: #9cd7ff;
  --selectMenu-borderColor: #ffffff00;
  --shadow-floating-legacy: 0px 6px 12px -3px #25292e0a, 0px 6px 18px 0px #25292e1f;
  --shadow-resting-medium: 0px 1px 1px 0px #25292e1a, 0px 3px 6px 0px #25292e1f;
  --skeletonLoader-bgColor: #dae0e7;
  --treeViewItem-leadingVisual-iconColor-rest: #368cf9;
  --underlineNav-borderColor-active: #cd3425;
  --avatar-bgColor: #ffffff;
  --avatar-shadow: 0px 0px 0px 2px #ffffffcc;
  --bgColor-black: #010409;
  --bgColor-closed-emphasis: var(--bgColor-danger-emphasis);
  --bgColor-closed-muted: var(--bgColor-danger-muted);
  --bgColor-default: #ffffff;
  --bgColor-open-emphasis: var(--bgColor-success-emphasis);
  --bgColor-open-muted: var(--bgColor-success-muted);
  --bgColor-upsell-emphasis: var(--bgColor-done-emphasis);
  --bgColor-upsell-muted: var(--bgColor-done-muted);
  --bgColor-white: #ffffff;
  --border-accent-emphasis: 0.0625rem solid #0349b4;
  --border-accent-muted: 0.0625rem solid #368cf9;
  --border-attention-emphasis: 0.0625rem solid #744500;
  --border-attention-muted: 0.0625rem solid #b58407;
  --border-danger-emphasis: 0.0625rem solid #a0111f;
  --border-danger-muted: 0.0625rem solid #ee5a5d;
  --border-default: 0.0625rem solid #454c54;
  --border-disabled: 0.0625rem solid #59636e1f;
  --border-done-emphasis: 0.0625rem solid #622cbc;
  --border-done-muted: 0.0625rem solid #a371f7;
  --border-neutral-emphasis: 0.0625rem solid #59636e;
  --border-severe-emphasis: 0.0625rem solid #873800;
  --border-severe-muted: 0.0625rem solid #dc6d1a;
  --border-sponsors-emphasis: 0.0625rem solid #971368;
  --border-sponsors-muted: 0.0625rem solid #ed4baf;
  --border-success-emphasis: 0.0625rem solid #055d20;
  --border-success-muted: 0.0625rem solid #26a148;
  --border-transparent: 0.0625rem solid #ffffff00;
  --borderColor-closed-emphasis: var(--borderColor-danger-emphasis);
  --borderColor-closed-muted: var(--borderColor-danger-muted);
  --borderColor-emphasis: var(--borderColor-default);
  --borderColor-muted: var(--borderColor-default);
  --borderColor-open-emphasis: var(--borderColor-success-emphasis);
  --borderColor-open-muted: var(--borderColor-success-muted);
  --borderColor-upsell-emphasis: var(--borderColor-done-emphasis);
  --borderColor-upsell-muted: var(--borderColor-done-muted);
  --button-danger-bgColor-hover: var(--bgColor-danger-emphasis);
  --button-danger-bgColor-rest: var(--control-bgColor-rest);
  --button-danger-borderColor-active: var(--button-danger-borderColor-hover);
  --button-danger-fgColor-active: #ffffff;
  --button-danger-fgColor-disabled: #86061d80;
  --button-danger-fgColor-hover: #ffffff;
  --button-danger-fgColor-rest: var(--fgColor-danger);
  --button-danger-iconColor-hover: #ffffff;
  --button-default-bgColor-active: var(--control-bgColor-active);
  --button-default-bgColor-hover: var(--control-bgColor-hover);
  --button-default-bgColor-rest: var(--control-bgColor-rest);
  --button-default-bgColor-selected: var(--control-bgColor-active);
  --button-default-fgColor-rest: var(--control-fgColor-rest);
  --button-default-shadow-resting: 0px 1px 0px 0px #0104090a;
  --button-inactive-bgColor: var(--control-bgColor-rest);
  --button-invisible-bgColor-active: var(--control-transparent-bgColor-active);
  --button-invisible-bgColor-hover: var(--control-transparent-bgColor-hover);
  --button-invisible-bgColor-rest: var(--control-transparent-bgColor-rest);
  --button-invisible-borderColor-rest: var(--control-transparent-borderColor-rest);
  --button-invisible-fgColor-active: var(--control-fgColor-rest);
  --button-invisible-fgColor-rest: var(--control-fgColor-rest);
  --button-invisible-iconColor-rest: var(--fgColor-muted);
  --button-outline-bgColor-hover: var(--bgColor-accent-emphasis);
  --button-outline-bgColor-rest: var(--control-bgColor-rest);
  --button-outline-borderColor-active: var(--button-outline-borderColor-hover);
  --button-outline-fgColor-active: #ffffff;
  --button-outline-fgColor-disabled: #023b9580;
  --button-outline-fgColor-hover: #ffffff;
  --button-outline-fgColor-rest: var(--fgColor-accent);
  --button-primary-borderColor-active: var(--button-primary-borderColor-rest);
  --button-primary-borderColor-hover: var(--button-primary-borderColor-rest);
  --button-primary-fgColor-disabled: #ffffffcc;
  --buttonCounter-danger-bgColor-disabled: #a0111f0d;
  --buttonCounter-danger-bgColor-hover: #ffffff33;
  --buttonCounter-danger-bgColor-rest: #a0111f1a;
  --buttonCounter-danger-fgColor-disabled: #86061d80;
  --buttonCounter-danger-fgColor-hover: #ffffff;
  --buttonCounter-danger-fgColor-rest: var(--fgColor-danger);
  --buttonCounter-invisible-bgColor-rest: var(--bgColor-neutral-muted);
  --buttonCounter-outline-bgColor-disabled: #0349b40d;
  --buttonCounter-outline-bgColor-hover: #ffffff33;
  --buttonCounter-outline-bgColor-rest: #0349b41a;
  --buttonCounter-outline-fgColor-disabled: #023b9580;
  --buttonCounter-outline-fgColor-hover: #ffffff;
  --codeMirror-activeline-bgColor: var(--bgColor-neutral-muted);
  --codeMirror-gutterMarker-fgColor-muted: var(--fgColor-muted);
  --codeMirror-lineNumber-fgColor: var(--fgColor-muted);
  --codeMirror-selection-bgColor: var(--borderColor-accent-muted);
  --codeMirror-syntax-fgColor-comment: #010409;
  --color-ansi-black: #010409;
  --color-prettylights-syntax-markup-bold: #010409;
  --color-prettylights-syntax-markup-italic: #010409;
  --color-prettylights-syntax-storage-modifier-import: #010409;
  --contribution-default-bgColor-0: #ffffff;
  --contribution-default-borderColor-1: var(--contribution-default-borderColor-0);
  --contribution-default-borderColor-2: var(--contribution-default-borderColor-0);
  --contribution-default-borderColor-3: var(--contribution-default-borderColor-0);
  --contribution-default-borderColor-4: var(--contribution-default-borderColor-0);
  --control-bgColor-disabled: var(--bgColor-disabled);
  --control-bgColor-selected: var(--control-bgColor-rest);
  --control-borderColor-danger: var(--borderColor-danger-emphasis);
  --control-borderColor-disabled: var(--borderColor-disabled);
  --control-borderColor-rest: var(--borderColor-default);
  --control-borderColor-success: var(--borderColor-success-emphasis);
  --control-borderColor-warning: var(--borderColor-attention-emphasis);
  --control-checked-bgColor-disabled: var(--fgColor-disabled);
  --control-checked-bgColor-rest: var(--bgColor-accent-emphasis);
  --control-danger-bgColor-hover: var(--bgColor-danger-emphasis);
  --control-danger-fgColor-rest: var(--fgColor-danger);
  --control-fgColor-disabled: var(--fgColor-disabled);
  --control-iconColor-rest: var(--fgColor-muted);
  --control-transparent-bgColor-disabled: var(--bgColor-disabled);
  --control-transparent-borderColor-active: var(--borderColor-default);
  --control-transparent-borderColor-hover: var(--borderColor-default);
  --controlKnob-bgColor-checked: #ffffff;
  --controlKnob-bgColor-rest: #ffffff;
  --controlTrack-bgColor-disabled: var(--fgColor-disabled);
  --controlTrack-borderColor-disabled: var(--fgColor-disabled);
  --counter-bgColor-emphasis: var(--bgColor-neutral-emphasis);
  --counter-bgColor-muted: var(--bgColor-neutral-muted);
  --counter-borderColor: var(--borderColor-default);
  --diffBlob-additionLine-bgColor: var(--bgColor-success-muted);
  --diffBlob-deletionLine-bgColor: var(--bgColor-danger-muted);
  --diffBlob-emptyLine-bgColor: var(--bgColor-muted);
  --diffBlob-emptyNum-bgColor: var(--bgColor-muted);
  --diffBlob-hunkLine-bgColor: var(--bgColor-accent-muted);
  --diffBlob-hunkLine-fgColor: var(--fgColor-muted);
  --diffBlob-hunkNum-bgColor-hover: var(--bgColor-accent-emphasis);
  --fgColor-black: #010409;
  --fgColor-closed: var(--fgColor-danger);
  --fgColor-default: #010409;
  --fgColor-link: var(--fgColor-accent);
  --fgColor-onEmphasis: #ffffff;
  --fgColor-onInverse: #ffffff;
  --fgColor-open: var(--fgColor-success);
  --fgColor-upsell: var(--fgColor-done);
  --fgColor-white: #ffffff;
  --focus-outlineColor: var(--borderColor-accent-emphasis);
  --header-fgColor-default: #ffffffb3;
  --header-fgColor-logo: #ffffff;
  --overlay-bgColor: #ffffff;
  --overlay-borderColor: var(--borderColor-default);
  --page-header-bgColor: var(--bgColor-muted);
  --selection-bgColor: #0349b433;
  --shadow-inset: inset 0px 1px 0px 0px #0104090a;
  --shadow-resting-small: 0px 1px 1px 0px #0104090f, 0px 1px 3px 0px #0104090f;
  --shadow-resting-xsmall: 0px 1px 1px 0px #0104090f;
  --sideNav-bgColor-selected: #ffffff;
  --timelineBadge-bgColor: var(--bgColor-muted);
  --tooltip-bgColor: var(--bgColor-emphasis);
  --topicTag-borderColor: var(--borderColor-accent-emphasis);
  --underlineNav-iconColor-rest: var(--fgColor-muted);
  --avatar-borderColor: var(--borderColor-emphasis);
  --border-closed-emphasis: var(--border-danger-emphasis);
  --border-closed-muted: var(--border-danger-muted);
  --border-emphasis: 0.0625rem solid #454c54;
  --border-muted: 0.0625rem solid #454c54;
  --border-open-emphasis: var(--border-success-emphasis);
  --border-open-muted: var(--border-success-muted);
  --border-upsell-emphasis: 0.0625rem solid #622cbc;
  --border-upsell-muted: 0.0625rem solid #a371f7;
  --borderColor-neutral-muted: var(--borderColor-muted);
  --button-danger-bgColor-disabled: var(--control-bgColor-disabled);
  --button-danger-borderColor-rest: var(--control-borderColor-rest);
  --button-danger-iconColor-rest: var(--button-danger-fgColor-rest);
  --button-default-bgColor-disabled: var(--control-bgColor-disabled);
  --button-default-borderColor-active: var(--control-borderColor-rest);
  --button-default-borderColor-disabled: var(--control-borderColor-disabled);
  --button-default-borderColor-rest: var(--control-borderColor-rest);
  --button-invisible-borderColor-hover: var(--control-transparent-borderColor-hover);
  --button-invisible-fgColor-disabled: var(--control-fgColor-disabled);
  --button-invisible-iconColor-disabled: var(--control-fgColor-disabled);
  --button-outline-bgColor-disabled: var(--control-bgColor-disabled);
  --button-primary-fgColor-rest: var(--fgColor-white);
  --button-primary-iconColor-rest: #ffffffcc;
  --card-bgColor: var(--bgColor-default);
  --codeMirror-bgColor: var(--bgColor-default);
  --codeMirror-cursor-fgColor: var(--fgColor-default);
  --codeMirror-fgColor: var(--fgColor-default);
  --codeMirror-gutterMarker-fgColor-default: var(--bgColor-default);
  --codeMirror-gutters-bgColor: var(--bgColor-default);
  --codeMirror-lines-bgColor: var(--bgColor-default);
  --codeMirror-matchingBracket-fgColor: var(--fgColor-default);
  --control-borderColor-emphasis: var(--borderColor-emphasis);
  --control-borderColor-selected: var(--control-bgColor-selected);
  --control-checked-borderColor-disabled: var(--control-checked-bgColor-disabled);
  --control-checked-borderColor-rest: var(--control-checked-bgColor-rest);
  --control-checked-fgColor-disabled: var(--fgColor-onEmphasis);
  --control-checked-fgColor-rest: var(--fgColor-onEmphasis);
  --control-danger-fgColor-hover: var(--fgColor-onEmphasis);
  --controlKnob-bgColor-disabled: var(--control-bgColor-disabled);
  --controlKnob-borderColor-checked: var(--control-checked-bgColor-rest);
  --controlKnob-borderColor-disabled: var(--control-bgColor-disabled);
  --controlTrack-borderColor-rest: var(--borderColor-emphasis);
  --controlTrack-fgColor-disabled: var(--fgColor-onEmphasis);
  --diffBlob-additionLine-fgColor: var(--fgColor-default);
  --diffBlob-additionNum-fgColor: var(--fgColor-default);
  --diffBlob-additionWord-fgColor: var(--fgColor-onEmphasis);
  --diffBlob-deletionLine-fgColor: var(--fgColor-default);
  --diffBlob-deletionNum-fgColor: var(--fgColor-default);
  --diffBlob-deletionWord-fgColor: var(--fgColor-onEmphasis);
  --diffBlob-expander-iconColor: var(--fgColor-default);
  --diffBlob-hunkNum-fgColor-hover: var(--fgColor-onEmphasis);
  --diffBlob-hunkNum-fgColor-rest: var(--fgColor-default);
  --focus-outline: 2px solid #0349b4;
  --reactionButton-selected-fgColor-rest: var(--fgColor-link);
  --shadow-floating-large: 0px 0px 0px 1px #454c54, 0px 40px 80px 0px #25292e3d;
  --shadow-floating-medium: 0px 0px 0px 1px #454c54, 0px 8px 16px -4px #25292e14, 0px 4px 32px -4px #25292e14, 0px 24px 48px -12px #25292e14, 0px 48px 96px -24px #25292e14;
  --shadow-floating-small: 0px 0px 0px 1px #454c5480, 0px 6px 12px -3px #25292e0a, 0px 6px 18px 0px #25292e1f;
  --shadow-floating-xlarge: 0px 0px 0px 1px #454c54, 0px 56px 112px 0px #25292e52;
  --tooltip-fgColor: var(--fgColor-onEmphasis);
  --underlineNav-borderColor-hover: var(--borderColor-muted);
  --border-neutral-muted: 0.0625rem solid #454c54;
  --button-default-borderColor-hover: var(--button-default-borderColor-rest);
  --controlKnob-borderColor-rest: var(--control-borderColor-emphasis);
}
@media (prefers-color-scheme: dark) {
  [data-color-mode][data-color-mode="auto"][data-dark-theme="light_high_contrast"],
  [data-color-mode][data-color-mode="auto"][data-dark-theme="light_high_contrast"] ::backdrop {
    --button-outline-bgColor-active: #033f9d;
    --button-primary-bgColor-active: #03501b;
    --button-primary-bgColor-disabled: #85cb97;
    --button-primary-bgColor-hover: #04571e;
    --button-primary-borderColor-disabled: #85cb97;
    --color-ansi-cyan: #1b7c83;
    --color-ansi-cyan-bright: #3192aa;
    --control-checked-bgColor-active: #033f9d;
    --control-checked-bgColor-hover: #0344a8;
    --control-checked-borderColor-active: #033f9d;
    --control-checked-borderColor-hover: #0344a8;
    --control-danger-bgColor-active: #8c0b1d;
    --reactionButton-selected-bgColor-hover: #c7e9ff;
    --avatarStack-fade-bgColor-default: #c8d1da;
    --avatarStack-fade-bgColor-muted: #dae0e7;
    --bgColor-accent-emphasis: #0349b4;
    --bgColor-accent-muted: #dff7ff;
    --bgColor-attention-emphasis: #744500;
    --bgColor-attention-muted: #fcf7be;
    --bgColor-danger-emphasis: #a0111f;
    --bgColor-danger-muted: #fff0ee;
    --bgColor-disabled: #e0e6eb;
    --bgColor-done-emphasis: #622cbc;
    --bgColor-done-muted: #faf0fe;
    --bgColor-emphasis: #25292e;
    --bgColor-inset: #eff2f5;
    --bgColor-inverse: #25292e;
    --bgColor-muted: #e6eaef;
    --bgColor-neutral-emphasis: #454c54;
    --bgColor-neutral-muted: #e0e6eb;
    --bgColor-severe-emphasis: #873800;
    --bgColor-severe-muted: #fff2d5;
    --bgColor-sponsors-emphasis: #971368;
    --bgColor-sponsors-muted: #feeff7;
    --bgColor-success-emphasis: #055d20;
    --bgColor-success-muted: #d2fedb;
    --bgColor-transparent: #ffffff00;
    --borderColor-accent-emphasis: #0349b4;
    --borderColor-accent-muted: #368cf9;
    --borderColor-attention-emphasis: #744500;
    --borderColor-attention-muted: #b58407;
    --borderColor-danger-emphasis: #a0111f;
    --borderColor-danger-muted: #ee5a5d;
    --borderColor-default: #454c54;
    --borderColor-disabled: #59636e1f;
    --borderColor-done-emphasis: #622cbc;
    --borderColor-done-muted: #a371f7;
    --borderColor-neutral-emphasis: #59636e;
    --borderColor-severe-emphasis: #873800;
    --borderColor-severe-muted: #dc6d1a;
    --borderColor-sponsors-emphasis: #971368;
    --borderColor-sponsors-muted: #ed4baf;
    --borderColor-success-emphasis: #055d20;
    --borderColor-success-muted: #26a148;
    --borderColor-translucent: #59636e;
    --borderColor-transparent: #ffffff00;
    --button-danger-bgColor-active: #86061d;
    --button-danger-borderColor-hover: #6e011a;
    --button-danger-shadow-selected: inset 0px 1px 0px 0px #43001133;
    --button-inactive-fgColor: #454c54;
    --button-invisible-bgColor-disabled: #ffffff00;
    --button-invisible-borderColor-disabled: #ffffff00;
    --button-invisible-fgColor-hover: #393f46;
    --button-invisible-iconColor-hover: #393f46;
    --button-outline-borderColor-hover: #022f7a;
    --button-outline-shadow-selected: inset 0px 1px 0px 0px #021a4a33;
    --button-primary-bgColor-rest: #055d20;
    --button-primary-borderColor-rest: #013d14;
    --button-primary-shadow-selected: inset 0px 1px 0px 0px #00230b4d;
    --button-star-iconColor: #d5a824;
    --buttonCounter-default-bgColor-rest: #c8d1da;
    --buttonCounter-outline-fgColor-rest: #023b95;
    --buttonCounter-primary-bgColor-rest: #00230b33;
    --codeMirror-syntax-fgColor-constant: #023b95;
    --codeMirror-syntax-fgColor-entity: #622cbc;
    --codeMirror-syntax-fgColor-keyword: #a0111f;
    --codeMirror-syntax-fgColor-storage: #a0111f;
    --codeMirror-syntax-fgColor-string: #032563;
    --codeMirror-syntax-fgColor-support: #023b95;
    --codeMirror-syntax-fgColor-variable: #702c00;
    --color-ansi-black-bright: #393f46;
    --color-ansi-blue: #0349b4;
    --color-ansi-blue-bright: #1168e3;
    --color-ansi-gray: #59636e;
    --color-ansi-green: #024c1a;
    --color-ansi-green-bright: #055d20;
    --color-ansi-magenta: #622cbc;
    --color-ansi-magenta-bright: #844ae7;
    --color-ansi-red: #a0111f;
    --color-ansi-red-bright: #86061d;
    --color-ansi-white: #59636e;
    --color-ansi-white-bright: #818b98;
    --color-ansi-yellow: #3f2200;
    --color-ansi-yellow-bright: #4e2c00;
    --color-prettylights-syntax-brackethighlighter-angle: #59636e;
    --color-prettylights-syntax-brackethighlighter-unmatched: #6e011a;
    --color-prettylights-syntax-carriage-return-bg: #a0111f;
    --color-prettylights-syntax-carriage-return-text: #f6f8fa;
    --color-prettylights-syntax-comment: #59636e;
    --color-prettylights-syntax-constant: #023b95;
    --color-prettylights-syntax-constant-other-reference-link: #032563;
    --color-prettylights-syntax-entity: #512598;
    --color-prettylights-syntax-entity-tag: #023b95;
    --color-prettylights-syntax-invalid-illegal-bg: #6e011a;
    --color-prettylights-syntax-invalid-illegal-text: #f6f8fa;
    --color-prettylights-syntax-keyword: #a0111f;
    --color-prettylights-syntax-markup-changed-bg: #ffc67b;
    --color-prettylights-syntax-markup-changed-text: #702c00;
    --color-prettylights-syntax-markup-deleted-bg: #fff0ee;
    --color-prettylights-syntax-markup-deleted-text: #6e011a;
    --color-prettylights-syntax-markup-heading: #023b95;
    --color-prettylights-syntax-markup-ignored-bg: #023b95;
    --color-prettylights-syntax-markup-ignored-text: #d1d9e0;
    --color-prettylights-syntax-markup-inserted-bg: #d2fedb;
    --color-prettylights-syntax-markup-inserted-text: #024c1a;
    --color-prettylights-syntax-markup-list: #2e1800;
    --color-prettylights-syntax-meta-diff-range: #622cbc;
    --color-prettylights-syntax-string: #032563;
    --color-prettylights-syntax-string-regexp: #024c1a;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #818b98;
    --color-prettylights-syntax-variable: #702c00;
    --contribution-default-bgColor-1: #82e596;
    --contribution-default-bgColor-2: #26a148;
    --contribution-default-bgColor-3: #117f32;
    --contribution-default-bgColor-4: #024c1a;
    --contribution-default-borderColor-0: #010409;
    --contribution-halloween-bgColor-1: #f0db3d;
    --contribution-halloween-bgColor-2: #ffd642;
    --contribution-halloween-bgColor-3: #f68c41;
    --contribution-halloween-bgColor-4: #010409;
    --contribution-winter-bgColor-1: #9cd7ff;
    --contribution-winter-bgColor-2: #368cf9;
    --contribution-winter-bgColor-3: #0349b4;
    --contribution-winter-bgColor-4: #032563;
    --control-bgColor-active: #d1d9e0;
    --control-bgColor-hover: #dae0e7;
    --control-bgColor-rest: #e0e6eb;
    --control-fgColor-placeholder: #454c54;
    --control-fgColor-rest: #25292e;
    --control-transparent-bgColor-active: #d1d9e0;
    --control-transparent-bgColor-hover: #dae0e7;
    --control-transparent-bgColor-rest: #ffffff00;
    --control-transparent-bgColor-selected: #dae0e7;
    --control-transparent-borderColor-rest: #ffffff00;
    --controlTrack-bgColor-active: #c8d1da;
    --controlTrack-bgColor-hover: #d1d9e0;
    --controlTrack-bgColor-rest: #dae0e7;
    --controlTrack-fgColor-rest: #59636e;
    --data-auburn-color-emphasis: #9d615c;
    --data-auburn-color-muted: #f2e9e9;
    --data-blue-color-emphasis: #006edb;
    --data-blue-color-muted: #d1f0ff;
    --data-brown-color-emphasis: #856d4c;
    --data-brown-color-muted: #eeeae2;
    --data-coral-color-emphasis: #d43511;
    --data-coral-color-muted: #ffe5db;
    --data-gray-color-emphasis: #808fa3;
    --data-gray-color-muted: #e8ecf2;
    --data-green-color-emphasis: #30a147;
    --data-green-color-muted: #caf7ca;
    --data-lemon-color-emphasis: #866e04;
    --data-lemon-color-muted: #f7eea1;
    --data-lime-color-emphasis: #527a29;
    --data-lime-color-muted: #e3f2b5;
    --data-olive-color-emphasis: #64762d;
    --data-olive-color-muted: #f0f0ad;
    --data-orange-color-emphasis: #eb670f;
    --data-orange-color-muted: #ffe7d1;
    --data-pine-color-emphasis: #167e53;
    --data-pine-color-muted: #bff8db;
    --data-pink-color-emphasis: #ce2c85;
    --data-pink-color-muted: #ffe5f1;
    --data-plum-color-emphasis: #a830e8;
    --data-plum-color-muted: #f8e5ff;
    --data-purple-color-emphasis: #894ceb;
    --data-purple-color-muted: #f1e5ff;
    --data-red-color-emphasis: #df0c24;
    --data-red-color-muted: #ffe2e0;
    --data-teal-color-emphasis: #179b9b;
    --data-teal-color-muted: #c7f5ef;
    --data-yellow-color-emphasis: #b88700;
    --data-yellow-color-muted: #ffec9e;
    --diffBlob-additionNum-bgColor: #82e596;
    --diffBlob-additionWord-bgColor: #055d20;
    --diffBlob-deletionNum-bgColor: #ffc1bc;
    --diffBlob-deletionWord-bgColor: #a0111f;
    --diffBlob-hunkNum-bgColor-rest: #9cd7ff;
    --display-auburn-bgColor-emphasis: #744744;
    --display-auburn-bgColor-muted: #f2e9e9;
    --display-auburn-borderColor-emphasis: #8a5551;
    --display-auburn-borderColor-muted: #e6d6d5;
    --display-auburn-fgColor: #5d3937;
    --display-auburn-scale-0: #f2e9e9;
    --display-auburn-scale-1: #e6d6d5;
    --display-auburn-scale-2: #d4b7b5;
    --display-auburn-scale-3: #c59e9b;
    --display-auburn-scale-4: #b4827e;
    --display-auburn-scale-5: #9d615c;
    --display-auburn-scale-6: #8a5551;
    --display-auburn-scale-7: #744744;
    --display-auburn-scale-8: #5d3937;
    --display-auburn-scale-9: #432928;
    --display-blue-bgColor-emphasis: #004db3;
    --display-blue-bgColor-muted: #d1f0ff;
    --display-blue-borderColor-emphasis: #005fcc;
    --display-blue-borderColor-muted: #ade1ff;
    --display-blue-fgColor: #003d99;
    --display-blue-scale-0: #d1f0ff;
    --display-blue-scale-1: #ade1ff;
    --display-blue-scale-2: #75c8ff;
    --display-blue-scale-3: #47afff;
    --display-blue-scale-4: #0f8fff;
    --display-blue-scale-5: #006edb;
    --display-blue-scale-6: #005fcc;
    --display-blue-scale-7: #004db3;
    --display-blue-scale-8: #003d99;
    --display-blue-scale-9: #002b75;
    --display-brown-bgColor-emphasis: #64513a;
    --display-brown-bgColor-muted: #eeeae2;
    --display-brown-borderColor-emphasis: #755f43;
    --display-brown-borderColor-muted: #dfd7c8;
    --display-brown-fgColor: #51412f;
    --display-brown-scale-0: #eeeae2;
    --display-brown-scale-1: #dfd7c8;
    --display-brown-scale-2: #cbbda4;
    --display-brown-scale-3: #b8a484;
    --display-brown-scale-4: #a68b64;
    --display-brown-scale-5: #856d4c;
    --display-brown-scale-6: #755f43;
    --display-brown-scale-7: #64513a;
    --display-brown-scale-8: #51412f;
    --display-brown-scale-9: #3a2e22;
    --display-coral-bgColor-emphasis: #9b2712;
    --display-coral-bgColor-muted: #ffe5db;
    --display-coral-borderColor-emphasis: #ba2e12;
    --display-coral-borderColor-muted: #fecebe;
    --display-coral-fgColor: #7e2011;
    --display-coral-scale-0: #ffe5db;
    --display-coral-scale-1: #fecebe;
    --display-coral-scale-2: #fcab92;
    --display-coral-scale-3: #f88768;
    --display-coral-scale-4: #f25f3a;
    --display-coral-scale-5: #d43511;
    --display-coral-scale-6: #ba2e12;
    --display-coral-scale-7: #9b2712;
    --display-coral-scale-8: #7e2011;
    --display-coral-scale-9: #5d180e;
    --display-cyan-bgColor-emphasis: #00596b;
    --display-cyan-bgColor-muted: #bdf4ff;
    --display-cyan-borderColor-emphasis: #006a80;
    --display-cyan-borderColor-muted: #7ae9ff;
    --display-cyan-fgColor: #004857;
    --display-cyan-scale-0: #bdf4ff;
    --display-cyan-scale-1: #7ae9ff;
    --display-cyan-scale-2: #00d0fa;
    --display-cyan-scale-3: #00b7db;
    --display-cyan-scale-4: #0099b8;
    --display-cyan-scale-5: #007b94;
    --display-cyan-scale-6: #006a80;
    --display-cyan-scale-7: #00596b;
    --display-cyan-scale-8: #004857;
    --display-cyan-scale-9: #003742;
    --display-gray-bgColor-emphasis: #4e535a;
    --display-gray-bgColor-muted: #e8ecf2;
    --display-gray-borderColor-emphasis: #5c6570;
    --display-gray-borderColor-muted: #d2dae4;
    --display-gray-fgColor: #424448;
    --display-gray-scale-0: #e8ecf2;
    --display-gray-scale-1: #d2dae4;
    --display-gray-scale-2: #b4c0cf;
    --display-gray-scale-3: #9ba9bb;
    --display-gray-scale-4: #808fa3;
    --display-gray-scale-5: #647182;
    --display-gray-scale-6: #5c6570;
    --display-gray-scale-7: #4e535a;
    --display-gray-scale-8: #424448;
    --display-gray-scale-9: #303031;
    --display-green-bgColor-emphasis: #285c3b;
    --display-green-bgColor-muted: #caf7ca;
    --display-green-borderColor-emphasis: #2b6e3f;
    --display-green-borderColor-muted: #9ceda0;
    --display-green-fgColor: #254b34;
    --display-green-scale-0: #caf7ca;
    --display-green-scale-1: #9ceda0;
    --display-green-scale-2: #54d961;
    --display-green-scale-3: #31bf46;
    --display-green-scale-4: #30a147;
    --display-green-scale-5: #2c8141;
    --display-green-scale-6: #2b6e3f;
    --display-green-scale-7: #285c3b;
    --display-green-scale-8: #254b34;
    --display-green-scale-9: #1d3528;
    --display-indigo-bgColor-emphasis: #393cd5;
    --display-indigo-bgColor-muted: #e5e9ff;
    --display-indigo-borderColor-emphasis: #494edf;
    --display-indigo-borderColor-muted: #d2d7fe;
    --display-indigo-fgColor: #2d2db4;
    --display-indigo-scale-0: #e5e9ff;
    --display-indigo-scale-1: #d2d7fe;
    --display-indigo-scale-2: #b1b9fb;
    --display-indigo-scale-3: #979ff7;
    --display-indigo-scale-4: #7a82f0;
    --display-indigo-scale-5: #5a61e7;
    --display-indigo-scale-6: #494edf;
    --display-indigo-scale-7: #393cd5;
    --display-indigo-scale-8: #2d2db4;
    --display-indigo-scale-9: #25247b;
    --display-lemon-bgColor-emphasis: #654f01;
    --display-lemon-bgColor-muted: #f7eea1;
    --display-lemon-borderColor-emphasis: #786002;
    --display-lemon-borderColor-muted: #f0db3d;
    --display-lemon-fgColor: #523f00;
    --display-lemon-scale-0: #f7eea1;
    --display-lemon-scale-1: #f0db3d;
    --display-lemon-scale-2: #d8bd0e;
    --display-lemon-scale-3: #c2a60a;
    --display-lemon-scale-4: #a68c07;
    --display-lemon-scale-5: #866e04;
    --display-lemon-scale-6: #786002;
    --display-lemon-scale-7: #654f01;
    --display-lemon-scale-8: #523f00;
    --display-lemon-scale-9: #3d2e00;
    --display-lime-bgColor-emphasis: #3a5b25;
    --display-lime-bgColor-muted: #e3f2b5;
    --display-lime-borderColor-emphasis: #476c28;
    --display-lime-borderColor-muted: #c7e580;
    --display-lime-fgColor: #2f4a21;
    --display-lime-scale-0: #e3f2b5;
    --display-lime-scale-1: #c7e580;
    --display-lime-scale-2: #9bd039;
    --display-lime-scale-3: #80b530;
    --display-lime-scale-4: #6c9d2f;
    --display-lime-scale-5: #527a29;
    --display-lime-scale-6: #476c28;
    --display-lime-scale-7: #3a5b25;
    --display-lime-scale-8: #2f4a21;
    --display-lime-scale-9: #213319;
    --display-olive-bgColor-emphasis: #495a2b;
    --display-olive-bgColor-muted: #f0f0ad;
    --display-olive-borderColor-emphasis: #56682c;
    --display-olive-borderColor-muted: #dbe170;
    --display-olive-fgColor: #3b4927;
    --display-olive-scale-0: #f0f0ad;
    --display-olive-scale-1: #dbe170;
    --display-olive-scale-2: #b9c832;
    --display-olive-scale-3: #9bae32;
    --display-olive-scale-4: #819532;
    --display-olive-scale-5: #64762d;
    --display-olive-scale-6: #56682c;
    --display-olive-scale-7: #495a2b;
    --display-olive-scale-8: #3b4927;
    --display-olive-scale-9: #2a331f;
    --display-orange-bgColor-emphasis: #8d3c11;
    --display-orange-bgColor-muted: #ffe7d1;
    --display-orange-borderColor-emphasis: #a24610;
    --display-orange-borderColor-muted: #fecfaa;
    --display-orange-fgColor: #70300f;
    --display-orange-scale-0: #ffe7d1;
    --display-orange-scale-1: #fecfaa;
    --display-orange-scale-2: #fbaf74;
    --display-orange-scale-3: #f68c41;
    --display-orange-scale-4: #eb670f;
    --display-orange-scale-5: #b8500f;
    --display-orange-scale-6: #a24610;
    --display-orange-scale-7: #8d3c11;
    --display-orange-scale-8: #70300f;
    --display-orange-scale-9: #54230d;
    --display-pine-bgColor-emphasis: #135d41;
    --display-pine-bgColor-muted: #bff8db;
    --display-pine-borderColor-emphasis: #156f4b;
    --display-pine-borderColor-muted: #80efb9;
    --display-pine-fgColor: #114b36;
    --display-pine-scale-0: #bff8db;
    --display-pine-scale-1: #80efb9;
    --display-pine-scale-2: #1dd781;
    --display-pine-scale-3: #1dbf76;
    --display-pine-scale-4: #1aa267;
    --display-pine-scale-5: #167e53;
    --display-pine-scale-6: #156f4b;
    --display-pine-scale-7: #135d41;
    --display-pine-scale-8: #114b36;
    --display-pine-scale-9: #0d3627;
    --display-pink-bgColor-emphasis: #8e2e66;
    --display-pink-bgColor-muted: #ffe5f1;
    --display-pink-borderColor-emphasis: #b12f79;
    --display-pink-borderColor-muted: #fdc9e2;
    --display-pink-fgColor: #6e2b53;
    --display-pink-scale-0: #ffe5f1;
    --display-pink-scale-1: #fdc9e2;
    --display-pink-scale-2: #f8a5cf;
    --display-pink-scale-3: #f184bc;
    --display-pink-scale-4: #e55da5;
    --display-pink-scale-5: #ce2c85;
    --display-pink-scale-6: #b12f79;
    --display-pink-scale-7: #8e2e66;
    --display-pink-scale-8: #6e2b53;
    --display-pink-scale-9: #4d233d;
    --display-plum-bgColor-emphasis: #7d1eb8;
    --display-plum-bgColor-muted: #f8e5ff;
    --display-plum-borderColor-emphasis: #961edc;
    --display-plum-borderColor-muted: #f0cdfe;
    --display-plum-fgColor: #651d96;
    --display-plum-scale-0: #f8e5ff;
    --display-plum-scale-1: #f0cdfe;
    --display-plum-scale-2: #e2a7fb;
    --display-plum-scale-3: #d487f7;
    --display-plum-scale-4: #c264f2;
    --display-plum-scale-5: #a830e8;
    --display-plum-scale-6: #961edc;
    --display-plum-scale-7: #7d1eb8;
    --display-plum-scale-8: #651d96;
    --display-plum-scale-9: #471769;
    --display-purple-bgColor-emphasis: #6223d7;
    --display-purple-bgColor-muted: #f1e5ff;
    --display-purple-borderColor-emphasis: #783ae4;
    --display-purple-borderColor-muted: #e6d2fe;
    --display-purple-fgColor: #4f21ab;
    --display-purple-scale-0: #f1e5ff;
    --display-purple-scale-1: #e6d2fe;
    --display-purple-scale-2: #d1b1fc;
    --display-purple-scale-3: #bc91f8;
    --display-purple-scale-4: #a672f3;
    --display-purple-scale-5: #894ceb;
    --display-purple-scale-6: #783ae4;
    --display-purple-scale-7: #6223d7;
    --display-purple-scale-8: #4f21ab;
    --display-purple-scale-9: #391b79;
    --display-red-bgColor-emphasis: #a60c29;
    --display-red-bgColor-muted: #ffe2e0;
    --display-red-borderColor-emphasis: #c50d28;
    --display-red-borderColor-muted: #fecdcd;
    --display-red-fgColor: #880c27;
    --display-red-scale-0: #ffe2e0;
    --display-red-scale-1: #fecdcd;
    --display-red-scale-2: #fda5a7;
    --display-red-scale-3: #fb8389;
    --display-red-scale-4: #f85461;
    --display-red-scale-5: #df0c24;
    --display-red-scale-6: #c50d28;
    --display-red-scale-7: #a60c29;
    --display-red-scale-8: #880c27;
    --display-red-scale-9: #610a20;
    --display-teal-bgColor-emphasis: #0d5b63;
    --display-teal-bgColor-muted: #c7f5ef;
    --display-teal-borderColor-emphasis: #106e75;
    --display-teal-borderColor-muted: #89ebe1;
    --display-teal-fgColor: #0a4852;
    --display-teal-scale-0: #c7f5ef;
    --display-teal-scale-1: #89ebe1;
    --display-teal-scale-2: #22d3c7;
    --display-teal-scale-3: #1db9b4;
    --display-teal-scale-4: #179b9b;
    --display-teal-scale-5: #127e81;
    --display-teal-scale-6: #106e75;
    --display-teal-scale-7: #0d5b63;
    --display-teal-scale-8: #0a4852;
    --display-teal-scale-9: #073740;
    --display-yellow-bgColor-emphasis: #704d00;
    --display-yellow-bgColor-muted: #ffec9e;
    --display-yellow-borderColor-emphasis: #805900;
    --display-yellow-borderColor-muted: #ffd642;
    --display-yellow-fgColor: #5c3d00;
    --display-yellow-scale-0: #ffec9e;
    --display-yellow-scale-1: #ffd642;
    --display-yellow-scale-2: #ebb400;
    --display-yellow-scale-3: #d19d00;
    --display-yellow-scale-4: #b88700;
    --display-yellow-scale-5: #946a00;
    --display-yellow-scale-6: #805900;
    --display-yellow-scale-7: #704d00;
    --display-yellow-scale-8: #5c3d00;
    --display-yellow-scale-9: #422b00;
    --fgColor-accent: #023b95;
    --fgColor-attention: #603700;
    --fgColor-danger: #86061d;
    --fgColor-disabled: #59636e;
    --fgColor-done: #512598;
    --fgColor-muted: #454c54;
    --fgColor-neutral: #393f46;
    --fgColor-severe: #702c00;
    --fgColor-sponsors: #7d0c57;
    --fgColor-success: #024c1a;
    --header-bgColor: #25292e;
    --header-borderColor-divider: #c8d1da;
    --headerSearch-bgColor: #25292e;
    --headerSearch-borderColor: #818b98;
    --highlight-neutral-bgColor: #fcf7be;
    --label-auburn-bgColor-active: #d4b7b5;
    --label-auburn-bgColor-hover: #e6d6d5;
    --label-auburn-bgColor-rest: #f2e9e9;
    --label-auburn-fgColor-active: #5d3937;
    --label-auburn-fgColor-hover: #744744;
    --label-auburn-fgColor-rest: #8a5551;
    --label-blue-bgColor-active: #75c8ff;
    --label-blue-bgColor-hover: #ade1ff;
    --label-blue-bgColor-rest: #d1f0ff;
    --label-blue-fgColor-active: #003d99;
    --label-blue-fgColor-hover: #004db3;
    --label-blue-fgColor-rest: #005fcc;
    --label-brown-bgColor-active: #cbbda4;
    --label-brown-bgColor-hover: #dfd7c8;
    --label-brown-bgColor-rest: #eeeae2;
    --label-brown-fgColor-active: #51412f;
    --label-brown-fgColor-hover: #64513a;
    --label-brown-fgColor-rest: #755f43;
    --label-coral-bgColor-active: #fcab92;
    --label-coral-bgColor-hover: #fecebe;
    --label-coral-bgColor-rest: #ffe5db;
    --label-coral-fgColor-active: #7e2011;
    --label-coral-fgColor-hover: #9b2712;
    --label-coral-fgColor-rest: #ba2e12;
    --label-cyan-bgColor-active: #00d0fa;
    --label-cyan-bgColor-hover: #7ae9ff;
    --label-cyan-bgColor-rest: #bdf4ff;
    --label-cyan-fgColor-active: #004857;
    --label-cyan-fgColor-hover: #00596b;
    --label-cyan-fgColor-rest: #006a80;
    --label-gray-bgColor-active: #b4c0cf;
    --label-gray-bgColor-hover: #d2dae4;
    --label-gray-bgColor-rest: #e8ecf2;
    --label-gray-fgColor-active: #424448;
    --label-gray-fgColor-hover: #4e535a;
    --label-gray-fgColor-rest: #5c6570;
    --label-green-bgColor-active: #54d961;
    --label-green-bgColor-hover: #9ceda0;
    --label-green-bgColor-rest: #caf7ca;
    --label-green-fgColor-active: #254b34;
    --label-green-fgColor-hover: #285c3b;
    --label-green-fgColor-rest: #2b6e3f;
    --label-indigo-bgColor-active: #b1b9fb;
    --label-indigo-bgColor-hover: #d2d7fe;
    --label-indigo-bgColor-rest: #e5e9ff;
    --label-indigo-fgColor-active: #2d2db4;
    --label-indigo-fgColor-hover: #393cd5;
    --label-indigo-fgColor-rest: #494edf;
    --label-lemon-bgColor-active: #d8bd0e;
    --label-lemon-bgColor-hover: #f0db3d;
    --label-lemon-bgColor-rest: #f7eea1;
    --label-lemon-fgColor-active: #523f00;
    --label-lemon-fgColor-hover: #654f01;
    --label-lemon-fgColor-rest: #786002;
    --label-lime-bgColor-active: #9bd039;
    --label-lime-bgColor-hover: #c7e580;
    --label-lime-bgColor-rest: #e3f2b5;
    --label-lime-fgColor-active: #2f4a21;
    --label-lime-fgColor-hover: #3a5b25;
    --label-lime-fgColor-rest: #476c28;
    --label-olive-bgColor-active: #b9c832;
    --label-olive-bgColor-hover: #dbe170;
    --label-olive-bgColor-rest: #f0f0ad;
    --label-olive-fgColor-active: #3b4927;
    --label-olive-fgColor-hover: #495a2b;
    --label-olive-fgColor-rest: #56682c;
    --label-orange-bgColor-active: #fbaf74;
    --label-orange-bgColor-hover: #fecfaa;
    --label-orange-bgColor-rest: #ffe7d1;
    --label-orange-fgColor-active: #70300f;
    --label-orange-fgColor-hover: #8d3c11;
    --label-orange-fgColor-rest: #a24610;
    --label-pine-bgColor-active: #1dd781;
    --label-pine-bgColor-hover: #80efb9;
    --label-pine-bgColor-rest: #bff8db;
    --label-pine-fgColor-active: #114b36;
    --label-pine-fgColor-hover: #135d41;
    --label-pine-fgColor-rest: #156f4b;
    --label-pink-bgColor-active: #f8a5cf;
    --label-pink-bgColor-hover: #fdc9e2;
    --label-pink-bgColor-rest: #ffe5f1;
    --label-pink-fgColor-active: #6e2b53;
    --label-pink-fgColor-hover: #8e2e66;
    --label-pink-fgColor-rest: #b12f79;
    --label-plum-bgColor-active: #e2a7fb;
    --label-plum-bgColor-hover: #f0cdfe;
    --label-plum-bgColor-rest: #f8e5ff;
    --label-plum-fgColor-active: #651d96;
    --label-plum-fgColor-hover: #7d1eb8;
    --label-plum-fgColor-rest: #961edc;
    --label-purple-bgColor-active: #d1b1fc;
    --label-purple-bgColor-hover: #e6d2fe;
    --label-purple-bgColor-rest: #f1e5ff;
    --label-purple-fgColor-active: #4f21ab;
    --label-purple-fgColor-hover: #6223d7;
    --label-purple-fgColor-rest: #783ae4;
    --label-red-bgColor-active: #fda5a7;
    --label-red-bgColor-hover: #fecdcd;
    --label-red-bgColor-rest: #ffe2e0;
    --label-red-fgColor-active: #880c27;
    --label-red-fgColor-hover: #a60c29;
    --label-red-fgColor-rest: #c50d28;
    --label-teal-bgColor-active: #22d3c7;
    --label-teal-bgColor-hover: #89ebe1;
    --label-teal-bgColor-rest: #c7f5ef;
    --label-teal-fgColor-active: #0a4852;
    --label-teal-fgColor-hover: #0d5b63;
    --label-teal-fgColor-rest: #106e75;
    --label-yellow-bgColor-active: #ebb400;
    --label-yellow-bgColor-hover: #ffd642;
    --label-yellow-bgColor-rest: #ffec9e;
    --label-yellow-fgColor-active: #5c3d00;
    --label-yellow-fgColor-hover: #704d00;
    --label-yellow-fgColor-rest: #805900;
    --menu-bgColor-active: #ffffff00;
    --overlay-backdrop-bgColor: #393f4666;
    --reactionButton-selected-bgColor-rest: #dff7ff;
    --reactionButton-selected-fgColor-hover: #023b95;
    --selectMenu-bgColor-active: #9cd7ff;
    --selectMenu-borderColor: #ffffff00;
    --shadow-floating-legacy: 0px 6px 12px -3px #25292e0a, 0px 6px 18px 0px #25292e1f;
    --shadow-resting-medium: 0px 1px 1px 0px #25292e1a, 0px 3px 6px 0px #25292e1f;
    --skeletonLoader-bgColor: #dae0e7;
    --treeViewItem-leadingVisual-iconColor-rest: #368cf9;
    --underlineNav-borderColor-active: #cd3425;
    --avatar-bgColor: #ffffff;
    --avatar-shadow: 0px 0px 0px 2px #ffffffcc;
    --bgColor-black: #010409;
    --bgColor-closed-emphasis: var(--bgColor-danger-emphasis);
    --bgColor-closed-muted: var(--bgColor-danger-muted);
    --bgColor-default: #ffffff;
    --bgColor-open-emphasis: var(--bgColor-success-emphasis);
    --bgColor-open-muted: var(--bgColor-success-muted);
    --bgColor-upsell-emphasis: var(--bgColor-done-emphasis);
    --bgColor-upsell-muted: var(--bgColor-done-muted);
    --bgColor-white: #ffffff;
    --border-accent-emphasis: 0.0625rem solid #0349b4;
    --border-accent-muted: 0.0625rem solid #368cf9;
    --border-attention-emphasis: 0.0625rem solid #744500;
    --border-attention-muted: 0.0625rem solid #b58407;
    --border-danger-emphasis: 0.0625rem solid #a0111f;
    --border-danger-muted: 0.0625rem solid #ee5a5d;
    --border-default: 0.0625rem solid #454c54;
    --border-disabled: 0.0625rem solid #59636e1f;
    --border-done-emphasis: 0.0625rem solid #622cbc;
    --border-done-muted: 0.0625rem solid #a371f7;
    --border-neutral-emphasis: 0.0625rem solid #59636e;
    --border-severe-emphasis: 0.0625rem solid #873800;
    --border-severe-muted: 0.0625rem solid #dc6d1a;
    --border-sponsors-emphasis: 0.0625rem solid #971368;
    --border-sponsors-muted: 0.0625rem solid #ed4baf;
    --border-success-emphasis: 0.0625rem solid #055d20;
    --border-success-muted: 0.0625rem solid #26a148;
    --border-transparent: 0.0625rem solid #ffffff00;
    --borderColor-closed-emphasis: var(--borderColor-danger-emphasis);
    --borderColor-closed-muted: var(--borderColor-danger-muted);
    --borderColor-emphasis: var(--borderColor-default);
    --borderColor-muted: var(--borderColor-default);
    --borderColor-open-emphasis: var(--borderColor-success-emphasis);
    --borderColor-open-muted: var(--borderColor-success-muted);
    --borderColor-upsell-emphasis: var(--borderColor-done-emphasis);
    --borderColor-upsell-muted: var(--borderColor-done-muted);
    --button-danger-bgColor-hover: var(--bgColor-danger-emphasis);
    --button-danger-bgColor-rest: var(--control-bgColor-rest);
    --button-danger-borderColor-active: var(--button-danger-borderColor-hover);
    --button-danger-fgColor-active: #ffffff;
    --button-danger-fgColor-disabled: #86061d80;
    --button-danger-fgColor-hover: #ffffff;
    --button-danger-fgColor-rest: var(--fgColor-danger);
    --button-danger-iconColor-hover: #ffffff;
    --button-default-bgColor-active: var(--control-bgColor-active);
    --button-default-bgColor-hover: var(--control-bgColor-hover);
    --button-default-bgColor-rest: var(--control-bgColor-rest);
    --button-default-bgColor-selected: var(--control-bgColor-active);
    --button-default-fgColor-rest: var(--control-fgColor-rest);
    --button-default-shadow-resting: 0px 1px 0px 0px #0104090a;
    --button-inactive-bgColor: var(--control-bgColor-rest);
    --button-invisible-bgColor-active: var(--control-transparent-bgColor-active);
    --button-invisible-bgColor-hover: var(--control-transparent-bgColor-hover);
    --button-invisible-bgColor-rest: var(--control-transparent-bgColor-rest);
    --button-invisible-borderColor-rest: var(--control-transparent-borderColor-rest);
    --button-invisible-fgColor-active: var(--control-fgColor-rest);
    --button-invisible-fgColor-rest: var(--control-fgColor-rest);
    --button-invisible-iconColor-rest: var(--fgColor-muted);
    --button-outline-bgColor-hover: var(--bgColor-accent-emphasis);
    --button-outline-bgColor-rest: var(--control-bgColor-rest);
    --button-outline-borderColor-active: var(--button-outline-borderColor-hover);
    --button-outline-fgColor-active: #ffffff;
    --button-outline-fgColor-disabled: #023b9580;
    --button-outline-fgColor-hover: #ffffff;
    --button-outline-fgColor-rest: var(--fgColor-accent);
    --button-primary-borderColor-active: var(--button-primary-borderColor-rest);
    --button-primary-borderColor-hover: var(--button-primary-borderColor-rest);
    --button-primary-fgColor-disabled: #ffffffcc;
    --buttonCounter-danger-bgColor-disabled: #a0111f0d;
    --buttonCounter-danger-bgColor-hover: #ffffff33;
    --buttonCounter-danger-bgColor-rest: #a0111f1a;
    --buttonCounter-danger-fgColor-disabled: #86061d80;
    --buttonCounter-danger-fgColor-hover: #ffffff;
    --buttonCounter-danger-fgColor-rest: var(--fgColor-danger);
    --buttonCounter-invisible-bgColor-rest: var(--bgColor-neutral-muted);
    --buttonCounter-outline-bgColor-disabled: #0349b40d;
    --buttonCounter-outline-bgColor-hover: #ffffff33;
    --buttonCounter-outline-bgColor-rest: #0349b41a;
    --buttonCounter-outline-fgColor-disabled: #023b9580;
    --buttonCounter-outline-fgColor-hover: #ffffff;
    --codeMirror-activeline-bgColor: var(--bgColor-neutral-muted);
    --codeMirror-gutterMarker-fgColor-muted: var(--fgColor-muted);
    --codeMirror-lineNumber-fgColor: var(--fgColor-muted);
    --codeMirror-selection-bgColor: var(--borderColor-accent-muted);
    --codeMirror-syntax-fgColor-comment: #010409;
    --color-ansi-black: #010409;
    --color-prettylights-syntax-markup-bold: #010409;
    --color-prettylights-syntax-markup-italic: #010409;
    --color-prettylights-syntax-storage-modifier-import: #010409;
    --contribution-default-bgColor-0: #ffffff;
    --contribution-default-borderColor-1: var(--contribution-default-borderColor-0);
    --contribution-default-borderColor-2: var(--contribution-default-borderColor-0);
    --contribution-default-borderColor-3: var(--contribution-default-borderColor-0);
    --contribution-default-borderColor-4: var(--contribution-default-borderColor-0);
    --control-bgColor-disabled: var(--bgColor-disabled);
    --control-bgColor-selected: var(--control-bgColor-rest);
    --control-borderColor-danger: var(--borderColor-danger-emphasis);
    --control-borderColor-disabled: var(--borderColor-disabled);
    --control-borderColor-rest: var(--borderColor-default);
    --control-borderColor-success: var(--borderColor-success-emphasis);
    --control-borderColor-warning: var(--borderColor-attention-emphasis);
    --control-checked-bgColor-disabled: var(--fgColor-disabled);
    --control-checked-bgColor-rest: var(--bgColor-accent-emphasis);
    --control-danger-bgColor-hover: var(--bgColor-danger-emphasis);
    --control-danger-fgColor-rest: var(--fgColor-danger);
    --control-fgColor-disabled: var(--fgColor-disabled);
    --control-iconColor-rest: var(--fgColor-muted);
    --control-transparent-bgColor-disabled: var(--bgColor-disabled);
    --control-transparent-borderColor-active: var(--borderColor-default);
    --control-transparent-borderColor-hover: var(--borderColor-default);
    --controlKnob-bgColor-checked: #ffffff;
    --controlKnob-bgColor-rest: #ffffff;
    --controlTrack-bgColor-disabled: var(--fgColor-disabled);
    --controlTrack-borderColor-disabled: var(--fgColor-disabled);
    --counter-bgColor-emphasis: var(--bgColor-neutral-emphasis);
    --counter-bgColor-muted: var(--bgColor-neutral-muted);
    --counter-borderColor: var(--borderColor-default);
    --diffBlob-additionLine-bgColor: var(--bgColor-success-muted);
    --diffBlob-deletionLine-bgColor: var(--bgColor-danger-muted);
    --diffBlob-emptyLine-bgColor: var(--bgColor-muted);
    --diffBlob-emptyNum-bgColor: var(--bgColor-muted);
    --diffBlob-hunkLine-bgColor: var(--bgColor-accent-muted);
    --diffBlob-hunkLine-fgColor: var(--fgColor-muted);
    --diffBlob-hunkNum-bgColor-hover: var(--bgColor-accent-emphasis);
    --fgColor-black: #010409;
    --fgColor-closed: var(--fgColor-danger);
    --fgColor-default: #010409;
    --fgColor-link: var(--fgColor-accent);
    --fgColor-onEmphasis: #ffffff;
    --fgColor-onInverse: #ffffff;
    --fgColor-open: var(--fgColor-success);
    --fgColor-upsell: var(--fgColor-done);
    --fgColor-white: #ffffff;
    --focus-outlineColor: var(--borderColor-accent-emphasis);
    --header-fgColor-default: #ffffffb3;
    --header-fgColor-logo: #ffffff;
    --overlay-bgColor: #ffffff;
    --overlay-borderColor: var(--borderColor-default);
    --page-header-bgColor: var(--bgColor-muted);
    --selection-bgColor: #0349b433;
    --shadow-inset: inset 0px 1px 0px 0px #0104090a;
    --shadow-resting-small: 0px 1px 1px 0px #0104090f, 0px 1px 3px 0px #0104090f;
    --shadow-resting-xsmall: 0px 1px 1px 0px #0104090f;
    --sideNav-bgColor-selected: #ffffff;
    --timelineBadge-bgColor: var(--bgColor-muted);
    --tooltip-bgColor: var(--bgColor-emphasis);
    --topicTag-borderColor: var(--borderColor-accent-emphasis);
    --underlineNav-iconColor-rest: var(--fgColor-muted);
    --avatar-borderColor: var(--borderColor-emphasis);
    --border-closed-emphasis: var(--border-danger-emphasis);
    --border-closed-muted: var(--border-danger-muted);
    --border-emphasis: 0.0625rem solid #454c54;
    --border-muted: 0.0625rem solid #454c54;
    --border-open-emphasis: var(--border-success-emphasis);
    --border-open-muted: var(--border-success-muted);
    --border-upsell-emphasis: 0.0625rem solid #622cbc;
    --border-upsell-muted: 0.0625rem solid #a371f7;
    --borderColor-neutral-muted: var(--borderColor-muted);
    --button-danger-bgColor-disabled: var(--control-bgColor-disabled);
    --button-danger-borderColor-rest: var(--control-borderColor-rest);
    --button-danger-iconColor-rest: var(--button-danger-fgColor-rest);
    --button-default-bgColor-disabled: var(--control-bgColor-disabled);
    --button-default-borderColor-active: var(--control-borderColor-rest);
    --button-default-borderColor-disabled: var(--control-borderColor-disabled);
    --button-default-borderColor-rest: var(--control-borderColor-rest);
    --button-invisible-borderColor-hover: var(--control-transparent-borderColor-hover);
    --button-invisible-fgColor-disabled: var(--control-fgColor-disabled);
    --button-invisible-iconColor-disabled: var(--control-fgColor-disabled);
    --button-outline-bgColor-disabled: var(--control-bgColor-disabled);
    --button-primary-fgColor-rest: var(--fgColor-white);
    --button-primary-iconColor-rest: #ffffffcc;
    --card-bgColor: var(--bgColor-default);
    --codeMirror-bgColor: var(--bgColor-default);
    --codeMirror-cursor-fgColor: var(--fgColor-default);
    --codeMirror-fgColor: var(--fgColor-default);
    --codeMirror-gutterMarker-fgColor-default: var(--bgColor-default);
    --codeMirror-gutters-bgColor: var(--bgColor-default);
    --codeMirror-lines-bgColor: var(--bgColor-default);
    --codeMirror-matchingBracket-fgColor: var(--fgColor-default);
    --control-borderColor-emphasis: var(--borderColor-emphasis);
    --control-borderColor-selected: var(--control-bgColor-selected);
    --control-checked-borderColor-disabled: var(--control-checked-bgColor-disabled);
    --control-checked-borderColor-rest: var(--control-checked-bgColor-rest);
    --control-checked-fgColor-disabled: var(--fgColor-onEmphasis);
    --control-checked-fgColor-rest: var(--fgColor-onEmphasis);
    --control-danger-fgColor-hover: var(--fgColor-onEmphasis);
    --controlKnob-bgColor-disabled: var(--control-bgColor-disabled);
    --controlKnob-borderColor-checked: var(--control-checked-bgColor-rest);
    --controlKnob-borderColor-disabled: var(--control-bgColor-disabled);
    --controlTrack-borderColor-rest: var(--borderColor-emphasis);
    --controlTrack-fgColor-disabled: var(--fgColor-onEmphasis);
    --diffBlob-additionLine-fgColor: var(--fgColor-default);
    --diffBlob-additionNum-fgColor: var(--fgColor-default);
    --diffBlob-additionWord-fgColor: var(--fgColor-onEmphasis);
    --diffBlob-deletionLine-fgColor: var(--fgColor-default);
    --diffBlob-deletionNum-fgColor: var(--fgColor-default);
    --diffBlob-deletionWord-fgColor: var(--fgColor-onEmphasis);
    --diffBlob-expander-iconColor: var(--fgColor-default);
    --diffBlob-hunkNum-fgColor-hover: var(--fgColor-onEmphasis);
    --diffBlob-hunkNum-fgColor-rest: var(--fgColor-default);
    --focus-outline: 2px solid #0349b4;
    --reactionButton-selected-fgColor-rest: var(--fgColor-link);
    --shadow-floating-large: 0px 0px 0px 1px #454c54, 0px 40px 80px 0px #25292e3d;
    --shadow-floating-medium: 0px 0px 0px 1px #454c54, 0px 8px 16px -4px #25292e14, 0px 4px 32px -4px #25292e14, 0px 24px 48px -12px #25292e14, 0px 48px 96px -24px #25292e14;
    --shadow-floating-small: 0px 0px 0px 1px #454c5480, 0px 6px 12px -3px #25292e0a, 0px 6px 18px 0px #25292e1f;
    --shadow-floating-xlarge: 0px 0px 0px 1px #454c54, 0px 56px 112px 0px #25292e52;
    --tooltip-fgColor: var(--fgColor-onEmphasis);
    --underlineNav-borderColor-hover: var(--borderColor-muted);
    --border-neutral-muted: 0.0625rem solid #454c54;
    --button-default-borderColor-hover: var(--button-default-borderColor-rest);
    --controlKnob-borderColor-rest: var(--control-borderColor-emphasis);
  }
}


/*# sourceMappingURL=light_high_contrast.scss.map */

/*# sourceMappingURL=light_high_contrast-79653a44c38b.css.map*/