"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78"],{96907:(t,e,i)=>{i.d(e,{Ay:()=>E});let Combobox=class Combobox{constructor(t,e,{tabInsertsSuggestions:i,defaultFirstOption:n,scrollIntoViewOptions:s}={}){this.input=t,this.list=e,this.tabInsertsSuggestions=null==i||i,this.defaultFirstOption=null!=n&&n,this.scrollIntoViewOptions=null!=s?s:{block:"nearest",inline:"nearest"},this.isComposing=!1,e.id||(e.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=t=>(function(t,e){if(!t.shiftKey&&!t.metaKey&&!t.altKey&&(e.ctrlBindings||!t.ctrlKey)&&!e.isComposing)switch(t.key){case"Enter":o(e.input,e.list)&&t.preventDefault();break;case"Tab":e.tabInsertsSuggestions&&o(e.input,e.list)&&t.preventDefault();break;case"Escape":e.clearSelection();break;case"ArrowDown":e.navigate(1),t.preventDefault();break;case"ArrowUp":e.navigate(-1),t.preventDefault();break;case"n":e.ctrlBindings&&t.ctrlKey&&(e.navigate(1),t.preventDefault());break;case"p":e.ctrlBindings&&t.ctrlKey&&(e.navigate(-1),t.preventDefault());break;default:if(t.ctrlKey)break;e.clearSelection()}})(t,this),this.compositionEventHandler=t=>(function(t,e){e.isComposing="compositionstart"===t.type,document.getElementById(e.input.getAttribute("aria-controls")||"")&&e.clearSelection()})(t,this),this.inputHandler=this.clearSelection.bind(this),t.setAttribute("role","combobox"),t.setAttribute("aria-controls",e.id),t.setAttribute("aria-expanded","false"),t.setAttribute("aria-autocomplete","list"),t.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",n),this.indicateDefaultOption()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",n)}indicateDefaultOption(){var t;this.defaultFirstOption&&(null==(t=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(s)[0])||t.setAttribute("data-combobox-option-default","true"))}navigate(t=1){let e=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(s)[0],i=Array.from(this.list.querySelectorAll('[role="option"]')).filter(s),n=i.indexOf(e);if(n===i.length-1&&1===t||0===n&&-1===t){this.clearSelection(),this.input.focus();return}let o=1===t?0:i.length-1;if(e&&n>=0){let e=n+t;e>=0&&e<i.length&&(o=e)}let r=i[o];if(r)for(let t of i)t.removeAttribute("data-combobox-option-default"),r===t?(this.input.setAttribute("aria-activedescendant",r.id),r.setAttribute("aria-selected","true"),r.dispatchEvent(new Event("combobox-select",{bubbles:!0})),r.scrollIntoView(this.scrollIntoViewOptions)):t.removeAttribute("aria-selected")}clearSelection(){for(let t of(this.input.removeAttribute("aria-activedescendant"),this.list.querySelectorAll('[aria-selected="true"]')))t.removeAttribute("aria-selected");this.indicateDefaultOption()}};function n(t){if(!(t.target instanceof Element))return;let e=t.target.closest('[role="option"]');if(e){var i,n;"true"!==e.getAttribute("aria-disabled")&&(i=e,n={event:t},i.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:n})))}}function o(t,e){let i=e.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!i&&("true"===i.getAttribute("aria-disabled")||(i.click(),!0))}function s(t){return!t.hidden&&!(t instanceof HTMLInputElement&&"hidden"===t.type)&&(t.offsetWidth>0||t.offsetHeight>0)}let r=window.testScreenReaderDelay||100;let Autocomplete=class Autocomplete{constructor(t,e,i,n=!1){var o;if(this.container=t,this.input=e,this.results=i,this.combobox=new Combobox(e,i,{defaultFirstOption:n}),this.feedback=t.getRootNode().getElementById(`${this.results.id}-feedback`),this.autoselectEnabled=n,this.clearButton=t.getRootNode().getElementById(`${this.input.id||this.input.name}-clear`),this.clientOptions=i.querySelectorAll("[role=option]"),this.feedback&&(this.feedback.setAttribute("aria-live","polite"),this.feedback.setAttribute("aria-atomic","true")),this.clearButton&&!this.clearButton.getAttribute("aria-label")){let t=document.querySelector(`label[for="${this.input.name}"]`);this.clearButton.setAttribute("aria-label","clear:"),this.clearButton.setAttribute("aria-labelledby",`${this.clearButton.id} ${(null==t?void 0:t.id)||""}`)}this.input.getAttribute("aria-expanded")||this.input.setAttribute("aria-expanded","false"),this.results.popover?this.results.matches(":popover-open")&&this.results.hidePopover():this.results.hidden=!0,this.results.getAttribute("aria-label")||this.results.setAttribute("aria-label","results"),this.input.setAttribute("autocomplete","off"),this.input.setAttribute("spellcheck","false"),this.interactingWithList=!1,this.onInputChange=function(t,e=0){let i;return function(...n){clearTimeout(i),i=window.setTimeout(()=>{clearTimeout(i),t(...n)},e)}}(this.onInputChange.bind(this),300),this.onResultsMouseDown=this.onResultsMouseDown.bind(this),this.onInputBlur=this.onInputBlur.bind(this),this.onInputFocus=this.onInputFocus.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onCommit=this.onCommit.bind(this),this.handleClear=this.handleClear.bind(this),this.input.addEventListener("keydown",this.onKeydown),this.input.addEventListener("focus",this.onInputFocus),this.input.addEventListener("blur",this.onInputBlur),this.input.addEventListener("input",this.onInputChange),this.results.addEventListener("mousedown",this.onResultsMouseDown),this.results.addEventListener("combobox-commit",this.onCommit),null==(o=this.clearButton)||o.addEventListener("click",this.handleClear)}destroy(){this.input.removeEventListener("keydown",this.onKeydown),this.input.removeEventListener("focus",this.onInputFocus),this.input.removeEventListener("blur",this.onInputBlur),this.input.removeEventListener("input",this.onInputChange),this.results.removeEventListener("mousedown",this.onResultsMouseDown),this.results.removeEventListener("combobox-commit",this.onCommit)}handleClear(t){t.preventDefault(),"true"===this.input.getAttribute("aria-expanded")&&(this.input.setAttribute("aria-expanded","false"),this.updateFeedbackForScreenReaders("Results hidden.")),this.input.value="",this.container.value="",this.input.focus(),this.input.dispatchEvent(new Event("change")),this.close()}onKeydown(t){if("Escape"===t.key&&this.container.open)this.close(),t.stopPropagation(),t.preventDefault();else if(t.altKey&&"ArrowUp"===t.key&&this.container.open)this.close(),t.stopPropagation(),t.preventDefault();else if(t.altKey&&"ArrowDown"===t.key&&!this.container.open){if(!this.input.value.trim())return;this.open(),t.stopPropagation(),t.preventDefault()}}onInputFocus(){this.interactingWithList||this.fetchResults()}onInputBlur(){this.interactingWithList||this.close()}onCommit({target:t}){if(!(t instanceof HTMLElement)||(this.close(),t instanceof HTMLAnchorElement))return;let e=t.getAttribute("data-autocomplete-value")||t.textContent;this.updateFeedbackForScreenReaders(`${t.textContent||""} selected.`),this.container.value=e,e||this.updateFeedbackForScreenReaders("Results hidden.")}onResultsMouseDown(){this.interactingWithList=!0}onInputChange(){this.feedback&&this.feedback.textContent&&(this.feedback.textContent=""),this.container.removeAttribute("value"),this.fetchResults()}identifyOptions(){let t=0;for(let e of this.results.querySelectorAll('[role="option"]:not([id])'))e.id=`${this.results.id}-option-${t++}`}updateFeedbackForScreenReaders(t){setTimeout(()=>{this.feedback&&(this.feedback.textContent=t)},r)}fetchResults(){let t=this.input.value.trim();if(!t&&!this.container.fetchOnEmpty)return void this.close();let e=this.container.src;if(!e)return;let i=new URL(e,window.location.href),n=new URLSearchParams(i.search.slice(1));n.append("q",t),i.search=n.toString(),this.container.dispatchEvent(new CustomEvent("loadstart")),this.container.fetchResult(i).then(t=>{this.results.innerHTML=t,this.identifyOptions(),this.combobox.indicateDefaultOption();let e=this.results.querySelectorAll('[role="option"]'),i=!!e.length||!!this.results.querySelectorAll('[data-no-result-found="true"]').length,n=e.length,[o]=e,s=null==o?void 0:o.textContent;this.autoselectEnabled&&s?this.updateFeedbackForScreenReaders(`${n} results. ${s} is the top result: Press Enter to activate.`):this.updateFeedbackForScreenReaders(`${n||"No"} results.`),i?this.open():this.close(),this.container.dispatchEvent(new CustomEvent("load")),this.container.dispatchEvent(new CustomEvent("loadend"))}).catch(()=>{this.container.dispatchEvent(new CustomEvent("error")),this.container.dispatchEvent(new CustomEvent("loadend"))})}open(){(this.results.popover?!this.results.matches(":popover-open"):this.results.hidden)&&(this.combobox.start(),this.results.popover?this.results.showPopover():this.results.hidden=!1),this.container.open=!0,this.interactingWithList=!0}close(){(this.results.popover?this.results.matches(":popover-open"):!this.results.hidden)&&(this.combobox.stop(),this.results.popover?this.results.hidePopover():this.results.hidden=!0),this.container.open=!1,this.interactingWithList=!1}};var a,l,u,c,d,h=function(t,e,i,n){if("a"===i&&!n)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!n:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?n:"a"===i?n.call(t):n?n.value:e.get(t)},p=function(t,e,i,n,o){if("m"===n)throw TypeError("Private method is not writable");if("a"===n&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===n?o.call(t,i):o?o.value=i:e.set(t,i),i},f=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&0>e.indexOf(n)&&(i[n]=t[n]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(t);o<n.length;o++)0>e.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(t,n[o])&&(i[n[o]]=t[n[o]]);return i};let b=globalThis.HTMLElement||null;let AutoCompleteEvent=class AutoCompleteEvent extends Event{constructor(t,e){var{relatedTarget:i}=e;super(t,f(e,["relatedTarget"])),this.relatedTarget=i}};let m=new WeakMap,g=null;let auto_complete_element_AutoCompleteElement=class auto_complete_element_AutoCompleteElement extends b{constructor(){super(...arguments),a.add(this),l.set(this,null),u.set(this,null),d.set(this,void 0)}static define(t="auto-complete",e=customElements){return e.define(t,this),this}static setCSPTrustedTypesPolicy(t){g=null===t?t:Promise.resolve(t)}get forElement(){var t;if(null==(t=h(this,l,"f"))?void 0:t.isConnected)return h(this,l,"f");let e=this.getAttribute("for"),i=this.getRootNode();return e&&(i instanceof Document||i instanceof ShadowRoot)?i.getElementById(e):null}set forElement(t){p(this,l,t,"f"),this.setAttribute("for","")}get inputElement(){var t;return(null==(t=h(this,u,"f"))?void 0:t.isConnected)?h(this,u,"f"):this.querySelector("input")}set inputElement(t){p(this,u,t,"f"),h(this,a,"m",c).call(this)}connectedCallback(){this.isConnected&&(h(this,a,"m",c).call(this),new MutationObserver(()=>{m.get(this)||h(this,a,"m",c).call(this)}).observe(this,{subtree:!0,childList:!0}))}disconnectedCallback(){let t=m.get(this);t&&(t.destroy(),m.delete(this))}get src(){return this.getAttribute("src")||""}set src(t){this.setAttribute("src",t)}get value(){return this.getAttribute("value")||""}set value(t){this.setAttribute("value",t)}get open(){return this.hasAttribute("open")}set open(t){t?this.setAttribute("open",""):this.removeAttribute("open")}get fetchOnEmpty(){return this.hasAttribute("fetch-on-empty")}set fetchOnEmpty(t){this.toggleAttribute("fetch-on-empty",t)}async fetchResult(t){var e;null==(e=h(this,d,"f"))||e.abort();let{signal:i}=p(this,d,new AbortController,"f"),n=await fetch(t.toString(),{signal:i,headers:{Accept:"text/fragment+html"}});if(!n.ok)throw Error(await n.text());return g?(await g).createHTML(await n.text(),n):await n.text()}static get observedAttributes(){return["open","value","for"]}attributeChangedCallback(t,e,i){var n,o;if(e===i)return;let s=m.get(this);if(s)switch((this.forElement!==(null==(n=m.get(this))?void 0:n.results)||this.inputElement!==(null==(o=m.get(this))?void 0:o.input))&&h(this,a,"m",c).call(this),t){case"open":null===i?s.close():s.open();break;case"value":null!==i&&(s.input.value=i),this.dispatchEvent(new AutoCompleteEvent("auto-complete-change",{bubbles:!0,relatedTarget:s.input}))}}};l=new WeakMap,u=new WeakMap,d=new WeakMap,a=new WeakSet,c=function(){var t;null==(t=m.get(this))||t.destroy();let{forElement:e,inputElement:i}=this;if(!e||!i)return;let n="true"===this.getAttribute("data-autoselect");m.set(this,new Autocomplete(this,i,e,n)),e.setAttribute("role","listbox")};let v="undefined"!=typeof globalThis?globalThis:window;try{v.AutocompleteElement=v.AutoCompleteElement=auto_complete_element_AutoCompleteElement.define()}catch(t){if(!(v.DOMException&&t instanceof DOMException&&"NotSupportedError"===t.name)&&!(t instanceof ReferenceError))throw t}let E=auto_complete_element_AutoCompleteElement},39595:(t,e,i)=>{let n;i.d(e,{CF:()=>b,p_:()=>R,FB:()=>d,Se:()=>C,aC:()=>O,zV:()=>D});let o=new WeakSet,s=new WeakMap;function r(t=document){if(s.has(t))return s.get(t);let e=!1,i=new MutationObserver(t=>{for(let e of t)if("attributes"===e.type&&e.target instanceof Element)c(e.target);else if("childList"===e.type&&e.addedNodes.length)for(let t of e.addedNodes)t instanceof Element&&a(t)});i.observe(t,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let n={get closed(){return e},unsubscribe(){e=!0,s.delete(t),i.disconnect()}};return s.set(t,n),n}function a(t){for(let e of t.querySelectorAll("[data-action]"))c(e);t instanceof Element&&t.hasAttribute("data-action")&&c(t)}function l(t){let e=t.currentTarget;for(let i of u(e))if(t.type===i.type){let n=e.closest(i.tag);o.has(n)&&"function"==typeof n[i.method]&&n[i.method](t);let s=e.getRootNode();if(s instanceof ShadowRoot&&o.has(s.host)&&s.host.matches(i.tag)){let e=s.host;"function"==typeof e[i.method]&&e[i.method](t)}}}function*u(t){for(let e of(t.getAttribute("data-action")||"").trim().split(/\s+/)){let t=e.lastIndexOf(":"),i=Math.max(0,e.lastIndexOf("#"))||e.length;yield{type:e.slice(0,t),tag:e.slice(t+1,i),method:e.slice(i+1)||"handleEvent"}}}function c(t){for(let e of u(t))t.addEventListener(e.type,l)}function d(t,e){let i=t.tagName.toLowerCase();if(t.shadowRoot){for(let n of t.shadowRoot.querySelectorAll(`[data-target~="${i}.${e}"]`))if(!n.closest(i))return n}for(let n of t.querySelectorAll(`[data-target~="${i}.${e}"]`))if(n.closest(i)===t)return n}let h=t=>String("symbol"==typeof t?t.description:t).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),p=(t,e="property")=>{let i=h(t);if(!i.includes("-"))throw new DOMException(`${e}: ${String(t)} is not a valid ${e} name`,"SyntaxError");return i},f="attr";function b(t,e){x(t,f).add(e)}let m=new WeakSet;function g(t,e){if(m.has(t))return;m.add(t);let i=Object.getPrototypeOf(t),n=i?.constructor?.attrPrefix??"data-";for(let o of(e||(e=x(i,f)),e)){let e=t[o],i=p(`${n}${o}`),s={configurable:!0,get(){return this.getAttribute(i)||""},set(t){this.setAttribute(i,t||"")}};"number"==typeof e?s={configurable:!0,get(){return Number(this.getAttribute(i)||0)},set(t){this.setAttribute(i,t)}}:"boolean"==typeof e&&(s={configurable:!0,get(){return this.hasAttribute(i)},set(t){this.toggleAttribute(i,t)}}),Object.defineProperty(t,o,s),o in t&&!t.hasAttribute(i)&&s.set.call(t,e)}}let v=new Map,E=new Promise(t=>{"loading"!==document.readyState?t():document.addEventListener("readystatechange",()=>t(),{once:!0})}),y=new Promise(t=>{let e=new AbortController;e.signal.addEventListener("abort",()=>t());let i={once:!0,passive:!0,signal:e.signal},n=()=>e.abort();document.addEventListener("mousedown",n,i),document.addEventListener("touchstart",n,i),document.addEventListener("keydown",n,i),document.addEventListener("pointerdown",n,i)}),w={ready:()=>E,firstInteraction:()=>y,visible:t=>new Promise(e=>{let i=new IntersectionObserver(t=>{for(let n of t)if(n.isIntersecting){e(),i.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let e of document.querySelectorAll(t))i.observe(e)})},A=new WeakMap;function k(t){cancelAnimationFrame(A.get(t)||0),A.set(t,requestAnimationFrame(()=>{for(let e of v.keys()){let i=t instanceof Element&&t.matches(e)?t:t.querySelector(e);if(customElements.get(e)||i){let n=i?.getAttribute("data-load-on")||"ready",o=n in w?w[n]:w.ready;for(let t of v.get(e)||[])o(e).then(t);v.delete(e),A.delete(t)}}}))}function C(t,e){for(let[i,n]of("string"==typeof t&&e&&(t={[t]:e}),Object.entries(t)))v.has(i)||v.set(i,new Set),v.get(i).add(n);S(document)}function S(t){n||(n=new MutationObserver(t=>{if(v.size)for(let e of t)for(let t of e.addedNodes)t instanceof Element&&k(t)})),k(t),n.observe(t,{subtree:!0,childList:!0})}let L=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(t){let e=this,i=t.prototype.connectedCallback;t.prototype.connectedCallback=function(){e.connectedCallback(this,i)};let n=t.prototype.disconnectedCallback;t.prototype.disconnectedCallback=function(){e.disconnectedCallback(this,n)};let o=t.prototype.attributeChangedCallback;t.prototype.attributeChangedCallback=function(t,i,n){e.attributeChangedCallback(this,t,i,n,o)};let s=t.observedAttributes||[];Object.defineProperty(t,"observedAttributes",{configurable:!0,get(){return e.observedAttributes(this,s)},set(t){s=t}}),function(t){let e=t.observedAttributes||[],i=t.attrPrefix??"data-",n=t=>p(`${i}${t}`);Object.defineProperty(t,"observedAttributes",{configurable:!0,get:()=>[...x(t.prototype,f)].map(n).concat(e),set(t){e=t}})}(t),function(t){let e=h(t.name).replace(/-element$/,"");try{window.customElements.define(e,t),window[t.name]=customElements.get(e)}catch(t){if(!(t instanceof DOMException&&"NotSupportedError"===t.name))throw t}}(t)}observedAttributes(t,e){return e}connectedCallback(t,e){var i,n;for(let e of(t.toggleAttribute("data-catalyst",!0),customElements.upgrade(t),t.querySelectorAll("template[data-shadowroot]")))e.parentElement===t&&t.attachShadow({mode:"closed"===e.getAttribute("data-shadowroot")?"closed":"open"}).append(e.content.cloneNode(!0));(g(t),o.add(t),t.shadowRoot&&(a(n=t.shadowRoot),r(n)),a(t),r(t.ownerDocument),e?.call(t),t.shadowRoot)&&(a(i=t.shadowRoot),r(i),S(t.shadowRoot))}disconnectedCallback(t,e){e?.call(t)}attributeChangedCallback(t,e,i,n,o){g(t),"data-catalyst"!==e&&o&&o.call(t,e,i,n)}};function x(t,e){if(!Object.prototype.hasOwnProperty.call(t,L)){let e=t[L],i=t[L]=new Map;if(e)for(let[t,n]of e)i.set(t,new Set(n))}let i=t[L];return i.has(e)||i.set(e,new Set),i.get(e)}function O(t,e){x(t,"target").add(e),Object.defineProperty(t,e,{configurable:!0,get(){return d(this,e)}})}function D(t,e){x(t,"targets").add(e),Object.defineProperty(t,e,{configurable:!0,get(){let t=this.tagName.toLowerCase(),i=[];if(this.shadowRoot)for(let n of this.shadowRoot.querySelectorAll(`[data-targets~="${t}.${e}"]`))n.closest(t)||i.push(n);for(let n of this.querySelectorAll(`[data-targets~="${t}.${e}"]`))n.closest(t)===this&&i.push(n);return i}})}function R(t){new CatalystDelegate(t)}},74057:(t,e,i)=>{i.d(e,{A:()=>m});let n="data-close-dialog",o=`[${n}]`;function s(t){let e=Array.from(t.querySelectorAll("[autofocus]")).filter(a)[0];e||(e=t,t.setAttribute("tabindex","-1")),e.focus()}function r(t){let e=t.currentTarget;e instanceof Element&&("Escape"===t.key||"Esc"===t.key?(d(e,!1),t.stopPropagation()):"Tab"===t.key&&function(t){if(!(t.currentTarget instanceof Element))return;let e=t.currentTarget.querySelector("details-dialog");if(!e)return;t.preventDefault();let i=Array.from(e.querySelectorAll("*")).filter(a);if(0===i.length)return;let n=t.shiftKey?-1:1,o=e.getRootNode(),s=e.contains(o.activeElement)?o.activeElement:null,r=-1===n?-1:0;if(s instanceof HTMLElement){let t=i.indexOf(s);-1!==t&&(r=t+n)}r<0?r=i.length-1:r%=i.length,i[r].focus()}(t))}function a(t){var e;return t.tabIndex>=0&&!t.disabled&&!(e=t).hidden&&(!e.type||"hidden"!==e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}function l(t){let e=t.querySelector("details-dialog");return!(e instanceof DetailsDialogElement)||e.dispatchEvent(new CustomEvent("details-dialog-close",{bubbles:!0,cancelable:!0}))}function u(t){if(!(t.currentTarget instanceof Element))return;let e=t.currentTarget.closest("details");e&&e.hasAttribute("open")&&(l(e)||(t.preventDefault(),t.stopPropagation()))}function c(t){let e=t.currentTarget;if(!(e instanceof Element))return;let i=e.querySelector("details-dialog");if(i instanceof DetailsDialogElement)if(e.hasAttribute("open")){let t="getRootNode"in i?i.getRootNode():document;t.activeElement instanceof HTMLElement&&b.set(i,{details:e,activeElement:t.activeElement}),s(i),e.addEventListener("keydown",r)}else{for(let t of i.querySelectorAll("form"))t.reset();let t=function(t,e){let i=b.get(e);return i&&i.activeElement instanceof HTMLElement?i.activeElement:t.querySelector("summary")}(e,i);t&&t.focus(),e.removeEventListener("keydown",r)}}function d(t,e){e!==t.hasAttribute("open")&&(e?t.setAttribute("open",""):l(t)&&t.removeAttribute("open"))}function h(t){let e=t.currentTarget;if(!(e instanceof Element))return;let i=e.querySelector("details-dialog");if(!(i instanceof DetailsDialogElement))return;let n=i.querySelector("include-fragment:not([src])");if(!n)return;let o=i.src;null!==o&&(n.addEventListener("loadend",()=>{e.hasAttribute("open")&&s(i)}),n.setAttribute("src",o),f(e))}function p(t,e,i){f(t),e&&t.addEventListener("toggle",h,{once:!0}),e&&i&&t.addEventListener("mouseover",h,{once:!0})}function f(t){t.removeEventListener("toggle",h),t.removeEventListener("mouseover",h)}let b=new WeakMap;let DetailsDialogElement=class DetailsDialogElement extends HTMLElement{static get CLOSE_ATTR(){return n}static get CLOSE_SELECTOR(){return o}constructor(){super(),b.set(this,{details:null,activeElement:null}),this.addEventListener("click",function({target:t}){if(!(t instanceof Element))return;let e=t.closest("details");e&&t.closest(o)&&d(e,!1)})}get src(){return this.getAttribute("src")}set src(t){this.setAttribute("src",t||"")}get preload(){return this.hasAttribute("preload")}set preload(t){t?this.setAttribute("preload",""):this.removeAttribute("preload")}connectedCallback(){this.setAttribute("role","dialog"),this.setAttribute("aria-modal","true");let t=b.get(this);if(!t)return;let e=this.parentElement;if(!e)return;let i=e.querySelector("summary");i&&(i.hasAttribute("role")||i.setAttribute("role","button"),i.addEventListener("click",u,{capture:!0})),e.addEventListener("toggle",c),t.details=e,p(e,this.src,this.preload)}disconnectedCallback(){let t=b.get(this);if(!t)return;let{details:e}=t;if(!e)return;e.removeEventListener("toggle",c),f(e);let i=e.querySelector("summary");i&&i.removeEventListener("click",u,{capture:!0}),t.details=null}toggle(t){let e=b.get(this);if(!e)return;let{details:i}=e;i&&d(i,t)}static get observedAttributes(){return["src","preload"]}attributeChangedCallback(){let t=b.get(this);if(!t)return;let{details:e}=t;e&&p(e,this.src,this.preload)}};let m=DetailsDialogElement;window.customElements.get("details-dialog")||(window.DetailsDialogElement=DetailsDialogElement,window.customElements.define("details-dialog",DetailsDialogElement))}}]);
//# sourceMappingURL=vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78-5b14deb5c63c.js.map