"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_github_sticky-scroll-into-view_ts"],{77176:(t,e,n)=>{n.d(e,{Sz:()=>f,Z:()=>d,kn:()=>u});var i=n(73238),o=n(97797);let l="ontransitionend"in window;function s(t){return"height"===getComputedStyle(t).transitionProperty}function r(t,e){t.style.transition="none",e(),t.offsetHeight,t.style.transition=""}var a=n(95493);function c(t,e){if(t.classList.toggle("open",e),t.classList.toggle("Details--on",e),e){let e=t.querySelector(".js-details-initial-focus");e&&setTimeout(()=>{e.focus()},0)}for(let n of[...t.querySelectorAll(".js-details-target")].filter(e=>e.closest(".js-details-container")===t))n.setAttribute("aria-expanded",e.toString()),n.hasAttribute("data-aria-label-open")&&n.hasAttribute("data-aria-label-closed")&&n.setAttribute("aria-label",e?n.getAttribute("data-aria-label-open"):n.getAttribute("data-aria-label-closed"))}function u(t,e){let n=t.getAttribute("data-details-container")||".js-details-container",i=t.closest(n),o=e?.force??!i.classList.contains("open"),u=e?.withGroup??!1;!function(t,e){if(!l)return e();let n=Array.from(t.querySelectorAll(".js-transitionable"));for(let e of(t.classList.contains("js-transitionable")&&n.push(t),n)){let t=s(e);e instanceof HTMLElement&&(e.addEventListener("transitionend",()=>{e.style.display="",e.style.visibility="",t&&r(e,function(){e.style.height=""})},{once:!0}),e.style.boxSizing="content-box",e.style.display="block",e.style.visibility="visible",t&&r(e,function(){e.style.height=getComputedStyle(e).height}),e.offsetHeight)}for(let t of(e(),n))if(t instanceof HTMLElement&&s(t)){let e=getComputedStyle(t).height;t.style.boxSizing="","0px"===e?t.style.height=`${t.scrollHeight}px`:t.style.height="0px"}}(i,()=>{c(i,o);let e=u?function(t,e){let n=t.getAttribute("data-details-container-group");return n?((0,a._H)(t,()=>{for(let i of[...document.querySelectorAll(".js-details-container")].filter(t=>t.getAttribute("data-details-container-group")===n))i!==t&&c(i,e)}),n):null}(i,o):null;Promise.resolve().then(()=>{[".js-focus-on-dismiss","input[autofocus], textarea[autofocus]"].find(t=>{let e=Array.from(i.querySelectorAll(t)).findLast(t=>"none"!==window.getComputedStyle(t).display);if(e&&document.activeElement!==e)return e.focus(),!0}),t.classList.contains("tooltipped")&&(t.classList.remove("tooltipped"),t.addEventListener("mouseleave",()=>{t.classList.add("tooltipped"),t.blur()},{once:!0})),i.dispatchEvent(new CustomEvent("details:toggled",{bubbles:!0,cancelable:!1,detail:{open:o}})),e&&i.dispatchEvent(new CustomEvent("details:toggled-group",{bubbles:!0,cancelable:!1,detail:{open:o,group:e}}))})})}function d(t){let e=t.getAttribute("data-details-container")||".js-details-container",n=t.closest(e).classList;return n.contains("Details--on")||n.contains("open")}function f(t){let e=!1,n=t.parentElement;for(;n;)n.classList.contains("Details-content--shown")&&(e=!0),n.classList.contains("js-details-container")&&(n.classList.toggle("open",!e),n.classList.toggle("Details--on",!e),e=!1),n=n.parentElement}(0,o.on)("click",".js-details-target",function(t){let e=t.altKey;u(t.currentTarget,{withGroup:e}),t.preventDefault()}),(0,i.A)(function({target:t}){t&&f(t)})},73238:(t,e,n)=>{n.d(e,{A:()=>r});var i=n(32475),o=n(21715);let l=[],s=0;function r(t){!async function(){l.push(t),await i.G,function(){let t=s;s=l.length,a(l.slice(t),null,window.location.href)}()}()}function a(t,e,n){let i=window.location.hash.slice(1),o={oldURL:e,newURL:n,target:i?document.getElementById(i):null};for(let e of t)e.call(null,o)}r.clear=()=>{l.length=s=0};let c=window.location.href;window.addEventListener("popstate",function(){c=window.location.href}),window.addEventListener("hashchange",function(t){let e=window.location.href;try{a(l,t.oldURL||c,e)}finally{c=e}});let u=null;document.addEventListener(o.z.START,function(){u=window.location.href}),document.addEventListener(o.z.SUCCESS,function(){a(l,u,window.location.href)})},52232:(t,e,n)=>{n.d(e,{L:()=>l});var i=n(21403),o=n(77176);function l(){let t=document.querySelector("[data-skipped-to-content]");return!!t&&(t.removeAttribute("data-skipped-to-content"),!0)}(0,i.lB)(".js-skip-to-content",t=>{t.addEventListener("focus",t=>{let e=t.currentTarget;if("true"===e.getAttribute("data-skip-target-assigned"))return;let n=document.querySelector("main");if(n||(n=document.querySelector("#skip-to-content")?.nextElementSibling),!n)return;let i=n.getAttribute("id");i||(i="main-content",n.setAttribute("id",i)),e.setAttribute("href",`#${i}`),e.setAttribute("data-skip-target-assigned","true")}),t.addEventListener("click",t=>{let e=t.currentTarget.getAttribute("href");if(!e)return;let n=document.querySelector(e);n&&(n.setAttribute("tabindex","-1"),n.setAttribute("data-skipped-to-content","1"),n.focus())})});let s="ontouchstart"in document,r=document.querySelectorAll(".js-header-menu-item");for(let t of r)t.addEventListener("details:toggled",t=>{let e=t.target;if(t instanceof CustomEvent&&t.detail.open)for(let t of r)t!==e&&(0,o.kn)(t,{force:!1})}),s||t.addEventListener("mouseleave",t=>{let e=t.target;e.classList.contains("open")&&(0,o.kn)(e,{force:!1})});document.addEventListener("context-region-label:update",t=>{if(t instanceof CustomEvent&&t.detail.label)for(let e of document.querySelectorAll(".js-context-region-label"))e.textContent=t.detail.label}),document.addEventListener("turbo:before-cache",t=>{for(let e of t.target.querySelectorAll("dialog[open], modal-dialog[open]"))e.close()}),(0,i.lB)("qbsearch-input",()=>{document.addEventListener("qbsearch-input:expand",()=>{document.body.setAttribute("blackbird-search-active","true")}),document.addEventListener("qbsearch-input:close",()=>{document.body.setAttribute("blackbird-search-active","false"),document.body.style.overflow=""})})},23129:(t,e,n)=>{n.d(e,{K:()=>p});var i=n(52232),o=n(32475),l=n(21403);let s=0,r=new Set;function a(t){for(let e of(s=t,t?document.body.style.setProperty("--base-sticky-header-height",`${t}px`):document.body.style.removeProperty("--base-sticky-header-height"),r))e(t)}let c=!1,u=!1,d=[];function f(){d.length?c||(window.addEventListener("resize",g),document.addEventListener("scroll",g),c=!0):(window.removeEventListener("resize",g),document.removeEventListener("scroll",g),c=!1)}function p(){y(!0)}function g(){y()}function y(t=!1){for(let e of d)if(e.element.offsetHeight>0){let{element:n,placeholder:i,top:o}=e,l=n.getBoundingClientRect();if(i){let s=i.getBoundingClientRect();n.classList.contains("is-stuck")?s.top>k(n,o)?h(e):b(e):l.top<=k(n,o)?m(e):t&&b(e)}else l.top-k(n,o)<.1?m(e):h(e)}}function m({element:t,placeholder:e,top:n}){if(e){let i=t.getBoundingClientRect();S(t,k(t,n)),t.style.left=`${i.left}px`,t.style.width=`${i.width}px`,t.style.marginTop="0",t.style.position="fixed",e.style.display="block"}t.classList.add("is-stuck")}function h({element:t,placeholder:e}){e&&(t.style.position="static",t.style.marginTop=e.style.marginTop,e.style.display="none"),t.classList.remove("is-stuck")}function b({element:t,placeholder:e,offsetParent:n,top:o}){if(e&&!(0,i.L)()){let i=t.getBoundingClientRect(),l=e.getBoundingClientRect();if(S(t,k(t,o)),t.style.left=`${l.left}px`,0!==l.width&&(t.style.width=`${l.width}px`),n){let e=n.getBoundingClientRect();e.bottom<i.height+parseInt(String(o))&&(t.style.top=`${e.bottom-i.height}px`)}}}async function w(t){await o.K,requestAnimationFrame(()=>{t.isConnected&&(!function(t){let e=function(t){if(function(t){let{position:e}=window.getComputedStyle(t);return/sticky/.test(e)}(t))return null;let e=t.previousElementSibling;if(e&&e.classList.contains("is-placeholder"))return e;let n=document.createElement("div");return n.style.visibility="hidden",n.style.display="none",n.style.height=window.getComputedStyle(t).height,n.className=t.className,n.classList.remove("js-sticky"),n.classList.add("is-placeholder"),t.parentNode.insertBefore(n,t)}(t),n=window.getComputedStyle(t).position;t.style.position="static";let i=t.offsetParent;t.style.position="fixed";let o=E(t),l={element:t,placeholder:e,offsetParent:i,top:"auto"===o?0:parseInt(o||"0")};t.style.position=n,d.push(l)}(t),y(),f())}),u||(window.dispatchEvent(new CustomEvent("sticky-header-rendered")),u=!0)}async function v(t){if(null===t.offsetParent)return;await o.K;let e=Math.floor(t.getBoundingClientRect().height);e>0&&(a(e),L(),p())}function L(){for(let t of document.querySelectorAll(".js-position-sticky, .js-notification-shelf-offset-top"))A(t)}function A(t){if(t.classList.contains("js-notification-top-shelf"))return;let e=parseInt(E(t))||0,n=t.classList.contains("js-second-sticky-header")?0:s;S(t,e+n)}function E(t){let e=t.getAttribute("data-original-top");if(null!=e)return e;let n=window.getComputedStyle(t).top;return t.setAttribute("data-original-top",n),n}function k(t,e){return t.classList.contains("js-notification-top-shelf")?e:e+s}function S(t,e){t.style.setProperty("top",`${e}px`,"important")}(0,l.lB)(".js-sticky",{constructor:HTMLElement,add(t){w(t)},remove(t){let e=d.map(t=>t.element).indexOf(t);d.splice(e,1),f()}}),(0,l.lB)(".js-notification-top-shelf",{constructor:HTMLElement,add(t){v(t)},remove(){s>0&&(a(0),L(),p())}}),(0,l.lB)(".js-notification-shelf-offset-top, .js-position-sticky",{constructor:HTMLElement,add:A})},12747:(t,e,n)=>{function i(t,e=location.hash){return o(t,l(e))}function o(t,e){return""===e?null:t.getElementById(e)||t.getElementsByName(e)[0]}function l(t){try{return decodeURIComponent(t.slice(1))}catch{return""}}n.d(e,{gX:()=>l,rG:()=>i,w$:()=>o})},63159:(t,e,n)=>{n.d(e,{GO:()=>s,Oc:()=>r,Rt:()=>l});var i=n(12747),o=n(23129);function l(t){if(t.hasAttribute("data-ignore-sticky-scroll"))return;let e=t.ownerDocument;setTimeout(()=>{e&&e.defaultView&&(t.scrollIntoView(),e.defaultView.scrollBy(0,-r(e)))},0)}function s(t){let e=(0,i.rG)(t);e&&l(e)}function r(t){(0,o.K)();let e=t.querySelectorAll(".js-sticky-offset-scroll"),n=t.querySelectorAll(".js-position-sticky");return Math.max(0,...Array.from(e).map(t=>{let{top:e,height:n}=t.getBoundingClientRect();return 0===e?n:0}))+Math.max(0,...Array.from(n).map(t=>{let{top:e,height:n}=t.getBoundingClientRect(),i=parseInt(getComputedStyle(t).top);if(!t.parentElement)return 0;let o=t.parentElement.getBoundingClientRect().top;return e===i&&o<0?n:0}))+Array.from(t.querySelectorAll(".js-position-sticky-stacked")).reduce((t,e)=>{let{height:n,top:i}=e.getBoundingClientRect(),o=e.classList.contains("is-stuck");return t+(!(i<0)&&o?n:0)},0)}}}]);
//# sourceMappingURL=app_assets_modules_github_sticky-scroll-into-view_ts-4481ba1aaae4.js.map