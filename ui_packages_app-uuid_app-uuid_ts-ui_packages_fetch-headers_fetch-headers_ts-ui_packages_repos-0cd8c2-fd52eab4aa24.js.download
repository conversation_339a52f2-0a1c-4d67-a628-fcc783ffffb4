"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_app-uuid_app-uuid_ts-ui_packages_fetch-headers_fetch-headers_ts-ui_packages_repos-0cd8c2"],{13255:(e,t,o)=>{o.d(t,{_S:()=>n,cB:()=>l,cW:()=>a});var r=o(66871),i=o(96679);let n=()=>{let e=(0,r.JV)().appId;return e&&"rails"!==e?e:crypto.randomUUID()},a=e=>{(0,r.C3)({appId:e})},l=()=>{let e=document.querySelector("react-app")||document.querySelector("projects-v2");return e?.uuid||"rails"};i.cg?.addEventListener("hashchange",()=>{(0,r.C3)({appId:l()})},!0)},24167:(e,t,o)=>{o.d(t,{Z:()=>r});function r({appendQuery:e,retainScrollPosition:t,returnTarget:o}){window.dispatchEvent(new CustomEvent("blackbird_monolith_append_and_focus_input",{detail:{appendQuery:e,retainScrollPosition:t,returnTarget:o}}))}},53005:(e,t,o)=>{o.d(t,{O:()=>a,S:()=>n});var r=o(96679);let i=r.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",n="X-GitHub-Client-Version";function a(){return i}},50104:(e,t,o)=>{o.d(t,{V3:()=>c,_y:()=>s,wk:()=>l});var r=o(80663),i=o(33299);let n={cursorNavigationHopWordLeft:{hotkey:"Alt+ArrowLeft,Ctrl+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"],modifierRequired:!0},cursorNavigationHopWordRight:{hotkey:"Alt+ArrowRight,Ctrl+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"],modifierRequired:!0},cursorNavigationTopOfPage:{hotkey:"Meta+ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"],modifierRequired:!0},cursorNavigationBottomOfPage:{hotkey:"Meta+ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"],modifierRequired:!0},cursorNavigationEnd:{hotkey:"End,Meta+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["End"]},cursorNavigationHome:{hotkey:"Home,Meta+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["Home"]},cursorNavigationPageUp:{hotkey:"PageUp",useWhileBlobFocused:!0,noModifierHotkey:["PageUp"]},cursorNavigationPageDown:{hotkey:"PageDown",useWhileBlobFocused:!0,noModifierHotkey:["PageDown"]},cursorNavigationArrowDown:{hotkey:"ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"]},cursorNavigationArrowUp:{hotkey:"ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"]},cursorNavigationArrowLeft:{hotkey:"ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"]},cursorNavigationArrowRight:{hotkey:"ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"]},cursorNavigationShiftHopWordLeft:{hotkey:"Alt+Shift+ArrowLeft,Ctrl+Shift+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"],modifierRequired:!0},cursorNavigationShiftHopWordRight:{hotkey:"Alt+Shift+ArrowRight,Ctrl+Shift+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"],modifierRequired:!0},cursorNavigationShiftTopOfPage:{hotkey:"Meta+Shift+ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"],modifierRequired:!0},cursorNavigationShiftBottomOfPage:{hotkey:"Meta+Shift+ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"],modifierRequired:!0},cursorNavigationShiftEnd:{hotkey:"Shift+End,Meta+Shift+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["End"],modifierRequired:!0},cursorNavigationShiftHome:{hotkey:"Shift+Home,Meta+Shift+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["Home"],modifierRequired:!0},cursorNavigationShiftPageUp:{hotkey:"Shift+PageUp",useWhileBlobFocused:!0,noModifierHotkey:["PageUp"],modifierRequired:!0},cursorNavigationShiftPageDown:{hotkey:"Shift+PageDown",useWhileBlobFocused:!0,noModifierHotkey:["PageDown"],modifierRequired:!0},cursorNavigationShiftArrowDown:{hotkey:"Shift+ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"],modifierRequired:!0},cursorNavigationShiftArrowUp:{hotkey:"Shift+ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"],modifierRequired:!0},cursorNavigationShiftArrowLeft:{hotkey:"Shift+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"],modifierRequired:!0},cursorNavigationShiftArrowRight:{hotkey:"Shift+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"],modifierRequired:!0},cursorNavigationHighlightLine:{text:"J",hotkey:"Shift+J",useWhileBlobFocused:!0,noModifierHotkey:["J"],modifierRequired:!0},cursorNavigationGoLineUp:{hotkey:"Ctrl+p",useWhileBlobFocused:!0,noModifierHotkey:["p"],modifierRequired:!0},cursorNavigationOpenHelpDialog:{hotkey:"Alt+F1,Control+Alt+\u02D9,Control+Alt+h",useWhileBlobFocused:!0,noModifierHotkey:["F1","h","\u02D9"],modifierRequired:!0},cursorNavigationGoLineDown:{hotkey:"Ctrl+n",useWhileBlobFocused:!0,noModifierHotkey:["n"],modifierRequired:!0},cursorNavigationEnter:{text:"\u2318 Enter",hotkey:"Meta+Enter",useWhileBlobFocused:!0,noModifierHotkey:["Enter"],modifierRequired:!0},cursorNavigationSpace:{hotkey:" ",useWhileBlobFocused:!0,noModifierHotkey:[" "],modifierRequired:!1},cursorNavigationShiftSpace:{hotkey:"Shift+ ",useWhileBlobFocused:!0,noModifierHotkey:[" "],modifierRequired:!0},expandAndFocusLineContextMenu:{text:"Shift Alt C",hotkey:"Alt+Shift+C,Alt+Shift+\xc7",useWhileBlobFocused:!0,noModifierHotkey:["C"],modifierRequired:!0},copyFilePathShortcut:{text:"\u2318 shift .",hotkey:"Meta+Shift+>",useWhileBlobFocused:!0,noModifierHotkey:["."],modifierRequired:!0},copyPermalinkShortcut:{text:"\u2318 shift ,",hotkey:"Meta+Shift+<",useWhileBlobFocused:!0,noModifierHotkey:[","],modifierRequired:!0},copyRawContentShortcut:{text:"\u2318 shift c",hotkey:"Meta+Shift+C",useWhileBlobFocused:!0,noModifierHotkey:["c"],modifierRequired:!0},downloadRawContentShortcut:{text:"\u2318 shift s",hotkey:"Meta+Shift+S",useWhileBlobFocused:!0,noModifierHotkey:["s"],modifierRequired:!0},editFileShortcut:{hotkey:"e,Shift+E",useWhileBlobFocused:!0,noModifierHotkey:["e","E"]},goToLineShortcut:{text:"l",hotkey:"l,Shift+L",ariaKeyShortcuts:"l",useWhileBlobFocused:!0,noModifierHotkey:["l","L"]},alternativeGoToLineShortcut:{hotkey:"Mod+Alt+g",ariaKeyShortcuts:"Mod+Alt+g",useWhileBlobFocused:!0,noModifierHotkey:["g","G"]},findInFileShortcut:{hotkey:"Meta+f, F3",text:"\u2318 f",ariaKeyShortcuts:"Meta+F",useWhileBlobFocused:!0,noModifierHotkey:["f","F3"],modifierRequired:!0},findFilesShortcut:{hotkey:"t,Shift+T",useWhileBlobFocused:!0,noModifierHotkey:["t","T"]},findSelectionShortcut:{hotkey:"Meta+e",useWhileBlobFocused:!0,noModifierHotkey:["e"],modifierRequired:!0},findNextShortcut:{hotkey:"Mod+g"},findPrevShortcut:{hotkey:"Mod+Shift+G"},openWithGitHubDevShortcut:{hotkey:"., Meta+Shift+/",useWhileBlobFocused:!0,noModifierHotkey:["."]},openWithGitHubDevInNewWindowShortcut:{hotkey:"Shift+.,Shift+>,>",useWhileBlobFocused:!0,noModifierHotkey:[">"]},permalinkShortcut:{hotkey:"y,Shift+Y",useWhileBlobFocused:!0,noModifierHotkey:["y","Y"]},searchShortcut:{hotkey:"/",useWhileBlobFocused:!0,noModifierHotkey:["/"]},selectAllShortcut:{hotkey:"Meta+a",useWhileBlobFocused:!0,noModifierHotkey:["a"],modifierRequired:!0},selectEditTabShortcut:{hotkey:"Mod+Shift+P"},submitCommitDialogShortcut:{hotkey:"Mod+Enter"},refSelectorShortcut:{hotkey:"w",text:"w",useWhileBlobFocused:!0,noModifierHotkey:["w"]},escapeRightClickMenu:{hotkey:"Escape",useWhileBlobFocused:!0,noModifierHotkey:["Escape"]},toggleFocusedPaneShortcut:{hotkey:"Meta+F6,Meta+Shift+F6",useWhileBlobFocused:!0,noModifierHotkey:["F6"],modifierRequired:!0},toggleSymbolsShortcut:{hotkey:"Meta+i",useWhileBlobFocused:!0,noModifierHotkey:["i"],modifierRequired:!0},toggleTreeShortcut:{hotkey:"Meta+b",useWhileBlobFocused:!0,noModifierHotkey:["b"],modifierRequired:!0},viewBlameShortcut:{hotkey:"b,Shift+B,Meta+/ Meta+b",useWhileBlobFocused:!0,noModifierHotkey:["b"]},viewCodeShortcut:{hotkey:"Meta+/ Meta+c",useWhileBlobFocused:!0,modifierRequired:!0},viewPreviewShortcut:{hotkey:"Meta+/ Meta+p"},viewRawContentShortcut:{text:"\u2318 / \u2318 r",hotkey:"Meta+/ Meta+r",useWhileBlobFocused:!0,noModifierHotkey:["r"],modifierRequired:!0},findSymbolShortcut:{hotkey:"r,Shift+R",useWhileBlobFocused:!0,noModifierHotkey:["r","R"],modifierRequired:!1}},a=new Map;function l(){let e=(0,i.X)(["mac"]),[t]=(0,r.I)(()=>!1,!0,[]),o=0;if(e?o=1:t||(o=2),!a.has(o)){var l,s;let r=n;e||t||(r=Object.keys(l=r).reduce((e,t)=>{let o=l[t];return e[t]={hotkey:o.hotkey?.replace(/Meta/g,"Control"),text:o.text?.replace(/⌘/g,"Ctrl").replace(/⇧/g,"Shift"),ariaKeyShortcuts:o.ariaKeyShortcuts?.replace(/Meta/g,"Control"),useWhileBlobFocused:o.useWhileBlobFocused,modifierRequired:o.modifierRequired,noModifierHotkey:o.noModifierHotkey},e},{})),t&&(r=Object.keys(s=r).reduce((e,t)=>{let o=s[t];return e[t]={hotkey:void 0,text:o.text?.replace(/⌘/g,"Ctrl").replace(/⇧/g,"Shift"),ariaKeyShortcuts:o.ariaKeyShortcuts?.replace(/Meta/g,"Control"),useWhileBlobFocused:o.useWhileBlobFocused,modifierRequired:o.modifierRequired,noModifierHotkey:o.noModifierHotkey},e},{})),a.set(o,r)}return a.get(o)}function s(){let e=l();return Object.keys(e).reduce((t,o)=>{let r=e[o];if(r.useWhileBlobFocused&&r.noModifierHotkey&&r.modifierRequired)for(let e of r.noModifierHotkey)t.includes(e)||t.push(e);return t},[])}function c(){let e=l();return Object.keys(e).reduce((t,o)=>{let r=e[o];if(r.useWhileBlobFocused&&r.noModifierHotkey&&!r.modifierRequired)for(let e of r.noModifierHotkey)t.includes(e)||t.push(e);return t},[])}},98637:(e,t,o)=>{o.d(t,{T:()=>d});var r=o(88795),i=o(96235),n=o(60183),a=o(75367),l=o(60039),s=o(96540),c=o(93955);function d(){let{sendAnalyticsEvent:e}=(0,a.s)(),t=function(){let e=function(){let e=(0,r.t)(),t=(0,c.i)();return(0,s.useMemo)(()=>({react_app:"code-view",repository_id:e.id,repository_nwo:`${e.ownerLogin}/${e.name}`,repository_public:e.public,repository_is_fork:e.isFork,actor_id:t?.id,actor_login:t?.login}),[e,t])}(),t=(0,r.t)(),o=(0,i.yH8)(i.tTz,{owner:t.ownerLogin,repo:t.name});return(0,s.useCallback)((t,r,i)=>{let n={target:t,interaction:r,context:i,...e,...{url:window.location.href,user_agent:window.navigator.userAgent,browser_width:window.innerWidth,browser_languages:window.navigator.languages.join(",")}};(0,l.lS)(o,{method:"POST",body:n})},[e,o])}(),o=(0,n.u)("code_nav_ui_events");return{sendRepoClickEvent:(0,s.useCallback)((r,i={})=>{e("repository.click",r,i),o&&t(r,"click",i)},[e,t,o]),sendRepoKeyDownEvent:(0,s.useCallback)((r,i={})=>{e("repository.keydown",r,i),o&&t(r,"keydown",i)},[e,t,o]),sendStats:(0,s.useCallback)((r,i={})=>{e(r,"",i),o&&t(r,"stats",i)},[e,t,o]),sendMarketplaceActionEvent:(0,s.useCallback)((t,o={})=>{e("marketplace.action.click",t,o)},[e])}}},62834:(e,t,o)=>{o.d(t,{o:()=>u});var r=o(5225),i=o(88795),n=o(96235),a=o(60039),l=o(96540);async function s(e){let t=await (0,a.lS)(e);return t.ok?await t.json():void 0}let c=new Map,d=(0,r.A)(s,{cache:c});function u(e,t,o){let r=(0,i.t)(),[a,s]=(0,l.useState)({list:[],directories:[],loading:!0}),c=(0,n.ClY)({repo:r,commitOid:e,includeDirectories:!o});return(0,l.useEffect)(()=>{let e=!1,o=async()=>{s({list:[],directories:[],loading:!0});let t=await d(c);if(e)return;let o=t?.paths||[],r=t?.directories||[];s({list:o.concat(r).sort(),directories:r,error:!t})};return t&&o(),function(){e=!0}},[c,t,o]),a}},21113:(e,t,o)=>{o.d(t,{Z:()=>s});var r=o(141),i=o(88795),n=o(96235),a=o(80663),l=o(96540);function s(){let e=(0,i.t)(),{path:t,action:o,refInfo:s}=(0,r.eu)(),[c]=(0,a.I)(()=>!1,!0,[]);function d(e){return e?`?${e}`:""}function u(e){return c?"":void 0===e?window.location.hash:e?`#${e}`:""}return{getItemUrl:l.useCallback(t=>(0,n.IO9)({repo:e,commitish:s.name,action:"directory"===t.contentType?"tree":"blob",path:t.path}),[e.ownerLogin,e.name,s.name]),getUrl(r={}){let i=(0,n.IO9)({repo:e,commitish:r.commitish||s.name,action:r.action||o,path:r.path||t})+function({params:e,hash:t}){return d(e)+u(t)}(r);return r.absolute?new URL(i,window.location.origin).href:i},createPermalink(r={}){let i=(0,n.IO9)({repo:e,commitish:s.currentOid,action:r.action||o,path:r.path||t})+function({params:e,hash:t}){return d(e)+u(t)}(r);return r.absolute?new URL(i,window.location.origin).href:i},isCurrentPagePermalink:()=>!c&&s.name===s.currentOid&&window.location.pathname.includes(s.currentOid)}}},89276:(e,t,o)=>{function r(e,t,o){if(!t.has(e))throw TypeError("attempted to "+o+" private field on non-instance");return t.get(e)}function i(e,t){var o=r(e,t,"get");return o.get?o.get.call(e):o.value}function n(e,t,o){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,o)}function a(e,t,o){var i=r(e,t,"set");if(i.set)i.set.call(e,o);else{if(!i.writable)throw TypeError("attempted to set read only private field");i.value=o}return o}function l(e,t,o){return t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o,e}o.d(t,{z:()=>WebWorker});let WebWorker=class WebWorker{set onmessage(e){this.worker.onmessage=e}postMessage(e){this.worker.postMessage(e)}terminate(){this.worker.terminate()}constructor(e,t){l(this,"worker",void 0);try{this.worker=new Worker(`${e}?module=true`,{type:"module"})}catch{console.warn("Web workers are not available. Please enable web workers to benefit from the improved performance."),this.worker=new MainThreadWorker(t)}}};var s=new WeakMap,c=new WeakMap;let MainThreadWorker=class MainThreadWorker{async postMessage(e){if(i(this,s))return;let t={data:i(this,c).call(this,{data:e})};this.onmessage?.(t)}terminate(){a(this,s,!0)}constructor(e){n(this,s,{writable:!0,value:!1}),l(this,"onmessage",void 0),n(this,c,{writable:!0,value:void 0}),a(this,c,e)}}},27406:(e,t,o)=>{o.d(t,{j:()=>a});var r=o(5225),i=o(91385);let n=(0,r.A)(i.fN);function a({data:e}){let{query:t,baseList:o,startTime:r}=e,a=t.replaceAll("\\","");return{query:t,list:o.filter(e=>{var t,o;return t=e,""===(o=a)||(0,i.qA)(o,t)&&n(o,t)>0}).sort((e,t)=>n(a,t)-n(a,e)),baseCount:o.length,startTime:r}}},18138:(e,t,o)=>{function r(e){return"blob"in e}function i(e){return r(e)&&"blame"in e}function n(e){return"deleteInfo"in e&&"webCommitInfo"in e}function a(e){return"editInfo"in e&&"webCommitInfo"in e}o.d(t,{fP:()=>l,KR:()=>i,mM:()=>r,iS:()=>n,di:()=>a,Hf:()=>s});let l={README:"readme",CODE_OF_CONDUCT:"code_of_conduct",LICENSE:"license",SECURITY:"security"};function s(e){return"tree"in e}},8367:(e,t,o)=>{function r(e){return i(e)[0]}function i(e){let t=[];for(let o of function(){try{return document.cookie.split(";")}catch{return[]}}()){let[r,i]=o.trim().split("=");e===r&&void 0!==i&&t.push({key:r,value:i})}return t}function n(e,t,o=null,r=!1,i="lax"){let a=document.domain;if(null==a)throw Error("Unable to get document domain");a.endsWith(".github.com")&&(a="github.com");let l="https:"===location.protocol?"; secure":"",s=o?`; expires=${o}`:"";!1===r&&(a=`.${a}`);try{document.cookie=`${e}=${t}; path=/; domain=${a}${s}${l}; samesite=${i}`}catch{}}function a(e,t=!1){let o=document.domain;if(null==o)throw Error("Unable to get document domain");o.endsWith(".github.com")&&(o="github.com");let r=new Date(Date.now()-1).toUTCString(),i="https:"===location.protocol?"; secure":"",n=`; expires=${r}`;!1===t&&(o=`.${o}`);try{document.cookie=`${e}=''; path=/; domain=${o}${n}${i}`}catch{}}o.d(t,{OR:()=>i,Ri:()=>r,TV:()=>n,Yj:()=>a})},640:(e,t,o)=>{o.d(t,{D:()=>r});function r(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e);let t=document.body;if(!t)return Promise.reject(Error());let o=function(e){let t=document.createElement("pre");return t.style.width="1px",t.style.height="1px",t.style.position="fixed",t.style.top="5px",t.textContent=e,t}(e);return t.appendChild(o),!function(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e.textContent||"");let t=getSelection();if(null==t)return Promise.reject(Error());t.removeAllRanges();let o=document.createRange();o.selectNodeContents(e),t.addRange(o),document.execCommand("copy"),t.removeAllRanges(),Promise.resolve()}(o),t.removeChild(o),Promise.resolve()}},26559:(e,t,o)=>{o.d(t,{jC:()=>s,kt:()=>a,tV:()=>l});var r=o(53005),i=o(27851),n=o(88191);function a(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,n.wE)(e)};return(0,i.G7)("client_version_header")&&(t={...t,[r.S]:(0,r.O)()}),t}function l(e,t){for(let[o,r]of Object.entries(a(t)))e.set(o,r)}function s(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,o)=>{o.d(t,{$r:()=>a,M1:()=>l,li:()=>i,pS:()=>c,wE:()=>s});var r=o(96679);let i="X-Fetch-Nonce",n=new Set;function a(e){n.add(e)}function l(){return n.values().next().value||""}function s(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[i]=l():n.has(e)?t[i]=e:t[i]=Array.from(n).join(","),t}function c(){let e=r.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&a(e)}},81367:(e,t,o)=>{o.d(t,{U:()=>n});let r=e=>{let t=getComputedStyle(e,null);return["overflow","overflow-y","overflow-x"].some(e=>{let o=t.getPropertyValue(e);return"auto"===o||"scroll"===o})},i=(e,t)=>e&&null!==e.parentNode?i(e.parentNode,t.concat([e])):t;function n(e){if(e instanceof HTMLElement||e instanceof SVGElement){for(let t of i(e.parentNode,[]))if((t instanceof HTMLElement||t instanceof SVGElement)&&r(t))return t;return document.scrollingElement||document.documentElement}}},72841:(e,t,o)=>{o.d(t,{A:()=>r});let r=(e,t)=>{let o=new URL(e,window.location.origin),r=new URL(t,window.location.origin);return r.href.includes("#")&&o.host===r.host&&o.pathname===r.pathname&&o.search===r.search}},42658:(e,t,o)=>{o.d(t,{Nq:()=>$,H:()=>p,c2:()=>ec,Uy:()=>U,qn:()=>ew});var r=o(74848),i=o(21609),n=o(50104),a=o(63869),l=o(80663),s=o(38621),c=o(55847),d=o(34164),u=o(96540);let f={expandButton:"ExpandFileTreeButton-module__expandButton--gL4is",filesButtonBreakpoint:"ExpandFileTreeButton-module__filesButtonBreakpoint--WfX9t",Button_1:"ExpandFileTreeButton-module__Button_1--M2dXF"},h=u.forwardRef(({expanded:e,onToggleExpanded:t,className:o,ariaControls:u,textAreaId:h,useFilesButtonBreakpoint:m=!0,variant:p,getTooltipDirection:y},g)=>{let{toggleTreeShortcut:x}=(0,n.wk)(),[w]=(0,l.I)(()=>!1,!0,[]),v=y?.(e)??"se";return(0,r.jsxs)(r.Fragment,{children:[m&&(!e||w)&&(0,r.jsx)(c.Q,{"aria-label":"Expand file tree",leadingVisual:s.ArrowLeftIcon,"data-hotkey":x.hotkey,"data-testid":"expand-file-tree-button-mobile",ref:g,onClick:t,variant:p??"invisible",className:f.Button_1,children:"Files"}),(0,r.jsx)(a.L,{dataHotkey:x.hotkey,className:(0,d.$)(o,"position-relative",f.expandButton,{[f.filesButtonBreakpoint]:m&&(!e||w)}),expanded:e,alignment:"left",ariaLabel:e?"Collapse file tree":"Expand file tree",tooltipDirection:v,testid:"file-tree-button",ariaControls:u,ref:g,variant:p,onToggleExpanded:t}),(0,r.jsx)(i._,{buttonFocusId:h,buttonHotkey:x.hotkey,onButtonClick:t,onlyAddHotkeyScopeButton:!0})]})});h.displayName="ExpandFileTreeButton";var m=o(98637);function p({inputRef:e,onFindFilesShortcut:t,textAreaId:o}){let{sendRepoKeyDownEvent:a}=(0,m.T)(),{findFilesShortcut:l}=(0,n.wk)();return(0,r.jsx)(i._,{buttonFocusId:o,buttonHotkey:l.hotkey,onButtonClick:()=>{t?.(),e?.current?.focus(),a("GO_TO_FILE")}})}try{p.displayName||(p.displayName="FindFilesShortcut")}catch{}var y=o(2680),g=o(25925),x=o(85579),w=o(61763),v=o(21113),k=o(38007),b=o(96235),R=o(28391),F=o(68415),S=o(21325),C=o(60039),N=o(69676),_=o(75177),B=o(87330),T=o(93653),E=o(22870),M=o(53110),A=o(27104);let I=(0,u.createContext)({knownFolders:new Map,dispatchKnownFolders:()=>{}});try{I.displayName||(I.displayName="FileTreeContext")}catch{}let j={Pane:"ReposFileTreePane-module__Pane--wS7IV",HidePane:"ReposFileTreePane-module__HidePane--Gj4XZ",HidePaneWithTreeOverlay:"ReposFileTreePane-module__HidePaneWithTreeOverlay--fHI8k",Overlay:"ReposFileTreePane-module__Overlay--jlqn5",HideTree:"ReposFileTreePane-module__HideTree--zU_Nd",Box_1:"ReposFileTreePane-module__Box_1--Bz4Aw",Box_2:"ReposFileTreePane-module__Box_2--uC_pl",Box_3:"ReposFileTreePane-module__Box_3--Tgoja",Box_4:"ReposFileTreePane-module__Box_4--brBpx",IconButton:"ReposFileTreePane-module__IconButton--tNSTv",FileResultsList:"ReposFileTreePane-module__FileResultsList--t38MI",Box_5:"ReposFileTreePane-module__Box_5--tQNH_",Box_6:"ReposFileTreePane-module__Box_6--Z7NP1",Box_7:"ReposFileTreePane-module__Box_7--tn_qm",Box_8:"ReposFileTreePane-module__Box_8--kZEZY"};var H=o(141),P=o(99987),W=o(11251),L=o(26807),O=o(55864),D=o(63867);let q=(0,u.createContext)({});function $({children:e}){let t=(0,u.useRef)(!1),o=(0,u.useRef)(!1),i=(0,u.useRef)(!0),n=(0,u.useCallback)(e=>{t.current=e},[]),a=(0,u.useCallback)(e=>{o.current=e},[]),l=(0,u.useCallback)(e=>{i.current=e},[]),s=(0,u.useMemo)(()=>({expandAllFolders:t,refreshTree:o,shouldFetchFolders:i,setExpandAllFolders:n,setRefreshTree:a,setShouldFetchFolders:l}),[n,a,l]);return(0,r.jsx)(q.Provider,{value:s,children:e})}function U(){return(0,u.useContext)(q)}try{q.displayName||(q.displayName="FileTreeControlContext")}catch{}try{$.displayName||($.displayName="FileTreeControlProvider")}catch{}var V=o(65556);function G(e,t,o){let r=[];for(let i of t)if(i.startsWith(e)){let t=i.slice(e.length+1),n=t.indexOf("/"),a=n>0?t.slice(0,n):t,l=`${e}/${a}`;if(o&&o.some(e=>e.path===l))continue;let s={items:[],data:{contentType:l===i?"file":"directory",name:a,path:l,isClientOnly:!0}};r.push(s)}return r}function z(e,t,o){e.data.name=e.data.name.slice(e.data.name.lastIndexOf("/")+1,e.data.name.length);let r=t.name.slice(0,t.name.lastIndexOf("/")),i=r.indexOf("/")>-1,n={path:t.path.slice(0,t.path.lastIndexOf("/")),contentType:t.contentType,name:r,hasSimplifiedPath:i},a={items:[e],data:n};return(o.set(n.path,a),i)?z(a,{...n},o):a}let K={Box:"ReposFileTreeView-module__Box--V2jWA",Box_1:"ReposFileTreeView-module__Box_1--ln7mT",Octicon:"ReposFileTreeView-module__Octicon--iZ0YO",Box_2:"ReposFileTreeView-module__Box_2--uFuxA"};function Z(e,t,o,r){if(!e)return{newRootItems:o,rootItemsUpdated:!1};let i=!!e[""]&&J("",t,o,e[""].items);for(let i of(r?.(o),Object.keys(e).sort()))if(i){let o=t.get(i);o&&(J(i,t,o.items,e[i].items,r),o.data.totalCount=e[i].totalCount)}return{newRootItems:o,rootItemsUpdated:i}}function J(e,t,o,r,i){let n=!1;for(let a of r){let r=e?`${e}/${a.name}`:a.name;if(!t.get(r)){let e={items:[],data:{...a}};if(t.set(r,e),a.hasSimplifiedPath){let r=z(e,a,t),l=o.findIndex(e=>e.data.path===r.data.path);-1!==l?r.items.length>o[l].items.length&&(o[l]=r,n=!0):(o.push(r),i?.(o))}else o.push(e),i?.(o)}}return n}function X({isActive:e,file:t,onItemSelected:o,getItemUrl:i,selectedItemRef:n,navigate:a,onRenderRow:l,getFileTrailingVisual:c,getFileIcon:d}){let{sendRepoClickEvent:f}=(0,m.T)(),h=u.useRef(null),p=(0,W.Z)({focusRowRef:h,mouseRowRef:h}),y="submodule"===t.data.contentType,g=c?.(t.data),x=u.useCallback(r=>{y?(r.preventDefault(),t.data.submoduleUrl&&(window.location.href=t.data.submoduleUrl)):r.metaKey||r.ctrlKey||1===r.button?(window.open(i(t.data),"_blank"),r.preventDefault()):e?r.preventDefault():(o?.(),f("FILES_TREE.ITEM",{item_path:t.data.path}),a(i(t.data)),r.stopPropagation())},[t.data,i,e,y,a,o,f]);return l?.(),(0,r.jsxs)(O.G.Item,{ref:h,onSelect:x,current:e,id:`${t.data.path}-item`,containIntrinsicSize:e?void 0:"auto 2rem",children:[(0,r.jsx)(O.G.LeadingVisual,{children:d?d(t.data):y?(0,r.jsx)(s.FileSubmoduleIcon,{}):(0,r.jsx)(s.FileIcon,{})}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{ref:n,style:{color:y?"var(--fgColor-accent, var(--color-accent-fg))":void 0},children:t.data.name}),p&&(0,r.jsx)(P.m,{"data-testid":`${t.data.name}-item-tooltip`,id:`${t.data.name}-item-tooltip`,contentRef:h,"aria-label":t.data.name,open:!0,direction:"ne"})]}),!!g?.screenReaderText&&(0,r.jsx)("span",{className:"sr-only",children:g.screenReaderText}),!!g?.trailingVisual&&(0,r.jsx)(O.G.TrailingVisual,{children:g.trailingVisual})]})}let Y=u.memo(X);function Q({clientOnlyFilePaths:e,directory:t,isActive:o,isAncestorOfActive:i,leadingPath:n="",onItemSelected:a,dispatchKnownFolders:l,getItemUrl:s,getFetchUrl:c,selectedItemRef:d,navigate:f,onRenderRow:h,getFileTrailingVisual:p,getFileIcon:y,navigateOnClick:g=!0}){let{expandAllFolders:x,shouldFetchFolders:w}=U(),[v,k]=(0,u.useState)(x?.current||i),{sendRepoClickEvent:b}=(0,m.T)(),R=u.useRef(null),F=u.useRef(null),S=(0,W.Z)({focusRowRef:F,mouseRowRef:R}),[N,_,B,T,E,M,A]=function(e,t,o){let[r,i]=u.useState(e.items),[n,a]=u.useState(e.data.totalCount||0),[l,s]=u.useState(!1),[c,d]=u.useState(!1),{safeSetTimeout:f}=(0,V.A)();u.useEffect(()=>{i(e.items)},[e.items]),u.useEffect(()=>{void 0!==e.data.totalCount&&a(e.data.totalCount)},[e.data.totalCount]);let h=u.useCallback(()=>{d(!1)},[]),m=u.useCallback((e,t)=>{let o=e||[...r];i(o.slice(0,100)),f(()=>{i(o),void 0!==t&&a(t)},1)},[r,f]);return[u.useCallback(async r=>{let n=new Map;if(e.data.isClientOnly){if(r){let o=G(e.data.path,r);for(let e of o)n.set(e.data.path,e);t({type:"add",folders:n,processingTime:0}),o.length>100?m(o,o.length):(i(o),a(o.length))}}else{let l=o(e.data);d(!1),s(!0);let c=Date.now(),u=await (0,C.lS)(`${l}?noancestors=1`);try{if(u.ok){let o=await u.json(),l=o.payload.tree.items.map(e=>{let t={items:[],data:{...e},autoExpand:"directory"===e.contentType&&1===o.payload.tree.items.length};return(n.set(e.path,t),e.hasSimplifiedPath)?z(t,e,n):t});if(r){let t=e.data.path,i=G(t,r,o.payload.tree.items);for(let e of(l.push(...i),i))n.set(e.data.path,e)}t({type:"add",folders:n,processingTime:Date.now()-c}),e.items=l,e.data.totalCount=o.payload.tree.totalCount,l.length>100?m(l,o.payload.tree.totalCount):(i(l),a(o.payload.tree.totalCount))}else d(!0)}catch{d(!0)}}s(!1)},[o,e,t,m]),m,r,l,c,h,n]}(t,l,c),I=A-B.length,j=n?`${n}/`:"";(0,u.useEffect)(()=>{x?.current&&!v&&k(!0)},[t,x,v]);let H=u.useCallback(r=>{x?.current&&(x.current=!1),r&&!v&&!T&&!E&&(t.items.length>100?_():0!==t.items.length||o||i||w?.current===!1||N(e)),r!==v&&k(r)},[x,v,T,E,t.items.length,o,i,w,_,N,e]),L=u.useCallback(e=>{e.metaKey||e.ctrlKey||1===e.button&&g?(window.open(s(t.data),"_blank"),e.preventDefault()):o?e.preventDefault():(a?.(),b("FILES_TREE.ITEM",{item_path:t.data.path}),g?f(s(t.data)):H?.(!v),e.stopPropagation())},[t.data,s,o,v,f,g,a,H,b]);u.useEffect(()=>{i&&!v&&H?.(!0)},[i]),u.useEffect(()=>{0===t.items.length&&v?H?.(!1):!v&&t.autoExpand&&H?.(!0)},[t.items.length]);let D=u.useCallback(e=>{d&&o&&d(e),R.current=e},[d,o]);return 1===t.items.length&&"directory"===t.items[0].data.contentType?(0,r.jsx)(eo,{clientOnlyFilePaths:e,directoryItems:t.items,leadingPath:j+t.data.name,inheritsActive:o,dispatchKnownFolders:l,onItemSelected:a,selectedItemRef:d,getItemUrl:s,directoryNavigateOnClick:g,getFileTrailingVisual:p,getFileIcon:y}):(h?.(),(0,r.jsxs)(O.G.Item,{ref:F,expanded:v,onExpandedChange:H,current:o,onSelect:L,id:`${t.data.path}-item`,containIntrinsicSize:o?void 0:"auto 2rem",children:[(0,r.jsx)(O.G.LeadingVisual,{children:(0,r.jsx)(O.G.DirectoryIcon,{})}),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("span",{ref:D,children:[j,t.data.name]}),S&&(0,r.jsx)(P.m,{"data-testid":`${t.data.name}-directory-item-tooltip`,id:`${t.data.name}-directory-item-tooltip`,contentRef:F,"aria-label":`${j}${t.data.name}`,open:!0,direction:"ne"})]}),(0,r.jsx)(O.G.SubTree,{state:T?"loading":E?"error":"done",children:E?(0,r.jsx)(O.G.ErrorDialog,{onRetry:N,onDismiss:M,children:"There was an error loading the folder contents."}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(eo,{clientOnlyFilePaths:e,directoryItems:B,dispatchKnownFolders:l,onItemSelected:a,selectedItemRef:d,getItemUrl:s,directoryNavigateOnClick:g,getFileTrailingVisual:p,getFileIcon:y}),I>0&&(0,r.jsx)(ei,{message:`${I} entries not shown`})]})})]}))}let ee=u.memo(Q);function et({clientOnlyFilePaths:e,directoryItems:t,leadingPath:o,inheritsActive:i,onItemSelected:n,dispatchKnownFolders:a,selectedItemRef:l,onRenderRow:s,getItemUrl:c,getFileTrailingVisual:d,getFileIcon:f,directoryNavigateOnClick:h=!0}){let{path:m}=(0,H.eu)(),p=(0,v.Z)(),y=(0,L.Z)(),g=u.useRef(y);return(0,r.jsx)(r.Fragment,{children:t.map(t=>{let u=m===t.data.path,y=u||m.startsWith(`${t.data.path}/`);return"directory"===t.data.contentType?(0,r.jsx)(ee,{clientOnlyFilePaths:e,isActive:i||u,isAncestorOfActive:y,onItemSelected:n,leadingPath:o,directory:t,dispatchKnownFolders:a,getItemUrl:c,getFetchUrl:p.getItemUrl,selectedItemRef:y?l:void 0,navigate:g.current,onRenderRow:s,navigateOnClick:h,getFileTrailingVisual:d,getFileIcon:f,itemCount:t.items.length},t.data.name):(0,r.jsx)(Y,{onItemSelected:n,file:t,isActive:u,getItemUrl:c,selectedItemRef:u?l:void 0,navigate:g.current,onRenderRow:s,getFileTrailingVisual:d,getFileIcon:f},t.data.name)})})}let eo=u.memo(et);function er(e){let{clientOnlyFilePaths:t,data:o,rootItems:i,setRootItems:n,fetchError:a,incompleteFileTree:l,processingTime:s,loading:c,onRenderRow:f,getItemUrl:h,getFileTrailingVisual:m,getFileIcon:p,sortDirectoryItems:y}=e,{knownFolders:g,dispatchKnownFolders:x}=(0,u.useContext)(I),{refreshTree:w}=U();u.useEffect(()=>{if(c)return;let e=new Map,t=[];w?.current||(e=new Map(g),t=i.slice());let{newRootItems:r,rootItemsUpdated:a}=Z(o,e,t,y);(r.length>i.length||a||w?.current)&&n(r),(e.size>g.size||w?.current)&&x({type:"set",folders:e,processingTime:s}),w?.current&&(w.current=!1)},[o,c]);let v=u.useCallback(e=>{1===e.button&&e.preventDefault()},[]);return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)("div",{onMouseDown:v,className:(0,d.$)(e.className,K.Box),"data-testid":"repos-file-tree-container",children:c?(0,r.jsx)("div",{className:K.Box_1,children:(0,r.jsx)(D.A,{"aria-label":"Loading file tree"})}):(0,r.jsx)("nav",{"aria-label":"File Tree Navigation",children:(0,r.jsxs)(O.G,{"aria-label":"Files",children:[a&&(0,r.jsx)(ei,{message:"Some files could not be loaded."}),l&&(0,r.jsx)(ei,{message:"Sign in to see the full file tree."}),(0,r.jsx)(eo,{clientOnlyFilePaths:t,directoryItems:i,onItemSelected:e.onItemSelected,dispatchKnownFolders:x,selectedItemRef:e.selectedItemRef,onRenderRow:f,directoryNavigateOnClick:e.directoryNavigateOnClick,getItemUrl:h,getFileTrailingVisual:m,getFileIcon:p})]})})})})}function ei({message:e}){return(0,r.jsxs)(O.G.Item,{id:"error-tree-row",children:[(0,r.jsx)(O.G.LeadingVisual,{children:(0,r.jsx)(M.A,{icon:s.AlertFillIcon,className:K.Octicon})}),(0,r.jsx)("div",{className:K.Box_2,children:e||"Couldn't load."})]})}try{X.displayName||(X.displayName="WrappedFileTreeRow")}catch{}try{Y.displayName||(Y.displayName="FileTreeRow")}catch{}try{Q.displayName||(Q.displayName="WrappedDirectoryTreeRow")}catch{}try{ee.displayName||(ee.displayName="DirectoryTreeRow")}catch{}try{et.displayName||(et.displayName="WrappedDirectoryContents")}catch{}try{eo.displayName||(eo.displayName="DirectoryContents")}catch{}try{er.displayName||(er.displayName="ReposFileTreeView")}catch{}try{ei.displayName||(ei.displayName="ErrorTreeRow")}catch{}var en=o(24167);let ea={IconButton:"SearchButton-module__IconButton--LGy8b"};function el({sx:e,onClick:t,textAreaId:o}){let{searchShortcut:a}=(0,n.wk)();return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(B.K,{"aria-label":"Search this repository",tooltipDirection:"nw",icon:s.SearchIcon,"data-hotkey":a.hotkey,sx:e,size:"medium",onClick:e=>{t?.(),(0,en.Z)({retainScrollPosition:!0,returnTarget:e.target.closest("button")})},className:ea.IconButton}),(0,r.jsx)(i._,{buttonFocusId:o,buttonHotkey:a.hotkey,onButtonClick:()=>{let e=document.getElementById(o);t?.(),(0,en.Z)({retainScrollPosition:!0,returnTarget:e??void 0})},onlyAddHotkeyScopeButton:!0})]})}try{el.displayName||(el.displayName="SearchButton")}catch{}let es=F.Gy.xxxlarge;function ec({clientOnlyFilePaths:e,collapseTree:t,showTree:o,fileTree:i,treeToggleElement:n,treeToggleRef:a,onItemSelected:c,processingTime:f,searchBoxRef:h,repo:m,path:k,refInfo:H,isFilePath:P,foldersToFetch:W,incompleteFileTree:L,id:O,onFindFilesShortcut:D,textAreaId:q,getItemUrlOverride:$,headerClassName:U,paneClassName:V,paneContentsClassName:G,treeContainerClassName:z,findFileWorkerPath:K,headerContent:J,getFileTrailingVisual:X,getFileIcon:Y,sortDirectoryItems:Q,showFindFile:ee=!0,directoryNavigateOnClick:et=!0,showRefSelectorRow:eo=!0,paneResizable:ei=!0}){if(ee&&void 0===K)throw Error("findFileWorkerPath must be provided when showFindFile is true");let{openPanel:en}=(0,w.Ak)(),[ea,ec]=u.useState(W.length>0),[ef,eh]=u.useState(!1),em=u.useRef([]),ep=u.useRef(null),ey=u.useRef(!1),eg=u.useRef(!1),ex=u.useRef(null),{query:ew}=(0,x.JS)(),ev=u.useRef(ew);ev.current=ew;let{codeCenterOption:ek}=(0,S.ud)(),eb=u.useRef(en),[eR]=(0,l.I)(()=>!1,!0,[]),{getItemUrl:eF}=(0,v.Z)(),eS=$||eF,eC=[],eN=new Map,e_=u.useRef(ea);!e_.current&&i&&(eC=Z(i,eN,[],Q).newRootItems),e_.current=!0;let[eB,eT]=u.useReducer(ed,eN),[eE,eM]=u.useState(eC);u.useEffect(()=>{o&&(!ew||window.innerWidth>=F.Gy.large)||(ex.current=null)},[o,ew]),u.useEffect(()=>{en&&eb.current!==en&&window.innerWidth<es&&t({setPreference:!1}),eb.current=en},[t,en]);let eA=u.useCallback(async e=>{let t=eF({contentType:"directory",path:e,name:e});try{let o=await (0,C.lS)(`${t}?noancestors=1`);if(o.ok){let t=await o.json(),r={items:t.payload.tree.items,totalCount:t.payload.tree.totalCount};i[e]=r}else eh(!0)}catch{eh(!0)}em.current.push(e),em.current.length===W.length&&ec(!1)},[i,W.length,eF]);u.useEffect(()=>{if(L)ec(!1);else if(W&&!ey.current)for(let e of W)eA(e);ey.current=!0},[eA,W,L,eB.size]);let eI=u.useCallback(e=>{o&&(!ev.current||window.innerWidth>=F.Gy.large)&&ep.current&&e&&(0,N.Rt)(e,ep.current,{endMargin:window.innerHeight/2,startMargin:window.innerHeight/2,behavior:"auto"})},[o]),ej=u.useCallback(e=>{e&&eg.current?eg.current=!1:ex.current!==e&&eI(e),ex.current=e},[eI]),eH=u.useCallback(e=>{ep.current=e,window.innerWidth>=es&&eI(ex.current)},[eI]),eP=u.useCallback(e=>{e&&eI(ex.current)},[eI]),{screenSize:eW}=(0,F.lm)(),eL=!eR&&(en&&eW<es||eW<F.Gy.xlarge)&&eW>=F.Gy.large,eO=u.useCallback(()=>{eL||c(),eg.current=!0},[c,eL]),eD=P?k.substring(0,k.lastIndexOf("/")):k,eq=(0,u.useMemo)(()=>({knownFolders:eB,dispatchKnownFolders:eT}),[eB]),e$=u.useMemo(()=>eR?null:(0,A.KF)(),[eR]),eU=u.useCallback(()=>{window.innerWidth>F.Gy.large&&window.innerWidth<F.Gy.xxxxlarge&&t({setPreference:!1})},[t]),eV=(0,r.jsxs)(_.A,{id:O,sx:{maxHeight:"100%",height:"100%",display:"flex",flexDirection:"column","@media screen and (max-width: 768px)":eR?{display:"none"}:void 0,"@media screen and (min-width: 768px)":{maxHeight:"100vh",height:"100vh"}},className:G,children:[(0,r.jsxs)("div",{className:(0,d.$)(U,j.Box_1),children:[J,eo&&(0,r.jsxs)("div",{className:j.Box_2,children:[(0,r.jsx)("div",{className:j.Box_3,children:(0,r.jsx)(g.R,{buttonClassName:"react-repos-tree-pane-ref-selector width-full ref-selector-class",allowResizing:!0})}),(0,r.jsxs)("div",{className:j.Box_4,children:[H.canEdit&&(0,r.jsx)(B.K,{"aria-label":"Add file",tooltipDirection:"n",as:R.N,icon:s.PlusIcon,to:(0,b.IO9)({repo:m,path:eD,commitish:H.name,action:"new"}),onClick:eO,className:j.IconButton}),(0,r.jsx)(el,{sx:H.canEdit?{borderTopLeftRadius:0,borderBottomLeftRadius:0}:void 0,onClick:eU,textAreaId:q})]})]})]}),H.currentOid&&ee&&K&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(y.default,{commitOid:H.currentOid,findFileWorkerPath:K,onItemSelected:c,searchBoxRef:h,sx:{"@media screen and (max-width: 768px)":eR?{display:"none"}:void 0},className:j.FileResultsList}),(0,r.jsx)(p,{inputRef:h,onFindFilesShortcut:D,textAreaId:q})]}),(0,r.jsx)(eu,{scrollingRef:ep}),(0,r.jsxs)(_.A,{ref:eH,sx:{"@media screen and (max-width: 768px)":eR?{display:"none"}:void 0},className:j.Box_5,children:[eR?H.currentOid&&(0,r.jsx)("div",{className:ew?"react-tree-show-tree-items-on-large-screen":"react-tree-show-tree-items",children:(0,r.jsx)(er,{clientOnlyFilePaths:e,data:i,rootItems:eE,selectedItemRef:ej,setRootItems:eM,onItemSelected:eO,processingTime:f,loading:ea,fetchError:ef,incompleteFileTree:L,directoryNavigateOnClick:et,getItemUrl:eS,className:z,getFileTrailingVisual:X,getFileIcon:Y,sortDirectoryItems:Q})}):e$&&(0,r.jsx)(A.bL,{node:e$}),!H.currentOid&&!m.isEmpty&&(0,r.jsxs)("div",{className:j.Box_6,children:[(0,r.jsx)(M.A,{icon:s.AlertFillIcon}),"\xa0Ref is invalid"]})]})]});return(0,r.jsxs)(I.Provider,{value:eq,children:[e$&&(0,r.jsx)(A.oj,{node:e$,children:H.currentOid&&(0,r.jsx)("div",{className:ew?"react-tree-show-tree-items-on-large-screen":"react-tree-show-tree-items",children:(0,r.jsx)(er,{clientOnlyFilePaths:e,data:i,directoryNavigateOnClick:et,rootItems:eE,selectedItemRef:ej,setRootItems:eM,onItemSelected:eO,processingTime:f,loading:ea,fetchError:ef,incompleteFileTree:L,getItemUrl:eS,className:z,getFileTrailingVisual:X,getFileIcon:Y,sortDirectoryItems:Q})})}),!o&&ek.enabled&&n&&(0,r.jsx)(_.A,{sx:{"@media screen and (min-width: 1360px)":{display:"block"}},className:j.Box_7,children:n}),(0,r.jsx)(T.O7.Pane,{position:"start",sticky:!0,padding:"none",width:"large",resizable:!!ei,widthStorageKey:"codeView.tree-pane-width",divider:{regular:"none",narrow:"none"},className:(0,d.$)(j.Pane,!o&&j.HideTree,eL||en?j.HidePaneWithTreeOverlay:j.HidePane,V),children:o&&!eL&&(0,r.jsx)("div",{className:eR?en?"react-tree-pane-contents-3-panel":"react-tree-pane-contents":void 0,children:eV})}),o&&eL&&eb.current===en&&(0,r.jsx)(E.Ay,{className:(0,d.$)(eR?en?"react-tree-pane-overlay-3-panel":"react-tree-pane-overlay":void 0,j.Overlay,!o&&j.HideTree),ref:eP,returnFocusRef:a,onClickOutside:eU,onEscape:eU,top:0,position:"fixed",children:(0,r.jsx)(r.Fragment,{children:eV})})]})}function ed(e,t){switch(t.type){case"set":{let o=e?.size>0;return(0,k.BI)("file-tree",{"fetch-count":o?t.folders.size-e.size:t.folders.size,"file-count":t.folders.size,"nav-type":o?"soft":"hard","processing-time":t.processingTime}),t.folders}case"add":{let o=new Map([...e,...t.folders]);return(0,k.BI)("file-tree",{"fetch-count":t.folders.size,"file-count":o.size,"nav-type":"fetch","processing-time":t.processingTime}),o}default:throw Error(`Unknown action type: ${t.type}`)}}function eu({scrollingRef:e}){let[t,o]=u.useState(e.current&&e.current.scrollTop>0);return u.useEffect(()=>{if(e.current){let t=e.current,r=()=>{t&&t.scrollTop>0?o(!0):o(!1)};return t.addEventListener("scroll",r),()=>{t.removeEventListener("scroll",r)}}},[e]),t?(0,r.jsx)("div",{className:j.Box_8}):null}try{ec.displayName||(ec.displayName="ReposFileTreePane")}catch{}try{eu.displayName||(eu.displayName="TreeBorder")}catch{}var ef=o(38111),eh=o(8367),em=o(93955),ep=o(96679),ey=o(17515),eg=o(84217);let ex={Heading:"use-tree-pane-module__Heading--DlnQ2"};function ew(e,t,o,i,n,a="fileTreeExpanded",l){let{sendRepoClickEvent:s}=(0,m.T)(),c=(0,em.i)(),d=a?(0,eh.Ri)(a):void 0,f=void 0===ep.XC,p=!c&&d&&"false"!==d.value||c&&o;void 0===p&&(p=!1);let y=(0,u.useRef)(null),g=(0,u.useRef)(null),{screenSize:x}=(0,F.lm)(),[w,v]=(0,u.useState)(p),k=(0,u.useRef)(p),b=(0,u.useRef)(!1),R=(0,u.useRef)(!1),S=(0,u.useCallback)(()=>!(t.current&&window.innerWidth>=es||!t.current&&window.innerWidth>=F.Gy.xlarge),[t]);(0,ey.N)(()=>{let e=S();e||(b.current=!1),v((0,ef.q)()&&x<F.Gy.large&&!R.current||(!e||b.current)&&(c&&w||!c&&d?.value!=="false"))},[b,d?.value,x,t,S,c]),(0,ey.N)(()=>{let e=!t.current&&window.innerWidth<F.Gy.xlarge,o=!t.current&&window.innerWidth>=F.Gy.xlarge;e&&k.current&&!(0,ef.q)()&&w&&v(!1),o&&k.current&&!w&&v(!0)},[t,x]),(0,ey.N)(()=>{let e=t.current&&window.innerWidth<es,o=t.current&&window.innerWidth>=es;e&&k.current&&!(0,ef.q)()&&w&&v(!1),o&&k.current&&!w&&v(!0)},[t]);let C=(0,u.useCallback)(e=>{if(v(!0),S()&&(b.current=!0),e?.setPreference&&(n?.(!0),k.current=!0,a)){let e=new Date(Date.now()+2592e6).toUTCString();(0,eh.TV)(a,"true",e)}e?.focus==="toggleButton"?requestAnimationFrame(()=>y.current?.focus()):e?.focus==="search"&&requestAnimationFrame(()=>g.current?.focus())},[a,2592e6,S,n]),N=(0,u.useCallback)(e=>{if(v(!1),b.current=!1,R.current=!0,e?.setPreference&&(n?.(!1),k.current=!1,a)){let e=new Date(Date.now()+2592e6).toUTCString();(0,eh.TV)(a,"false",e)}e?.focus==="toggleButton"&&requestAnimationFrame(()=>y.current?.focus())},[a,2592e6,n]),_=(0,u.useCallback)(e=>e&&window.innerWidth>=es||!t.current&&window.innerWidth>=F.Gy.xlarge,[t]),B=(0,u.useMemo)(()=>(0,r.jsx)(eg.A,{as:"h2",className:ex.Heading,children:(0,r.jsx)(h,{expanded:w,ariaControls:e,onToggleExpanded:()=>{s(w?"FILES_TREE.HIDE":"FILES_TREE.SHOW"),w?N({focus:"toggleButton",setPreference:_(t.current)}):C({focus:"toggleButton",setPreference:_(t.current)})},className:void 0!==d||w||f?void 0:"react-tree-toggle-button-with-indicator",ref:y,textAreaId:i,...l})}),[w,e,d,f,i,l,s,N,_,t,C]);return{isTreeExpanded:w,expandTree:C,collapseTree:N,treeToggleElement:B,treeToggleRef:y,searchBoxRef:g}}},75367:(e,t,o)=>{o.d(t,{S:()=>s,s:()=>l});var r=o(21728),i=o(96540),n=o(38007),a=o(78924);function l(){let e,t,o=(0,r.c)(6),l=(0,i.useContext)(a.I);if(!l)throw Error("useAnalytics must be used within an AnalyticsContext");let{appName:s,category:c,metadata:d}=l;o[0]!==s||o[1]!==c||o[2]!==d?(e=(e,t,o)=>{let r={react:!0,app_name:s,category:c,...d};(0,n.BI)(e,{...r,...void 0===o?{}:o,target:t})},o[0]=s,o[1]=c,o[2]=d,o[3]=e):e=o[3];let u=e;return o[4]!==u?(t={sendAnalyticsEvent:u},o[4]=u,o[5]=t):t=o[5],t}function s(){let e,t,o=(0,r.c)(4),{sendAnalyticsEvent:i}=l();o[0]!==i?(e=e=>{i("analytics.click",void 0,void 0===e?{}:e)},o[0]=i,o[1]=e):e=o[1];let n=e;return o[2]!==n?(t={sendClickAnalyticsEvent:n},o[2]=n,o[3]=t):t=o[3],t}},11251:(e,t,o)=>{o.d(t,{Z:()=>i});var r=o(96540);function i({focusRowRef:e,mouseRowRef:t}){let[o,i]=r.useState(!1);return r.useEffect(()=>{if(e.current&&t.current){let o=()=>{let t=e.current?.querySelector(".PRIVATE_TreeView-item-content-text");t?.scrollWidth!==t?.offsetWidth&&i(!0)};e.current.onfocus=()=>{o()},e.current.onblur=()=>{i(!1)},t.current.onmouseenter=()=>{o()},t.current.onmouseleave=()=>{i(!1)}}},[e,t]),o}},33299:(e,t,o)=>{o.d(t,{X:()=>i});var r=o(80663);function i(e){let t=e.join(","),[o]=(0,r.I)(()=>{let e=/Windows/.test(navigator.userAgent)?"windows":/Macintosh/.test(navigator.userAgent)?"mac":null;return!!e&&t.includes(e)},!1,[t]);return o}},26807:(e,t,o)=>{o.d(t,{Z:()=>c,o:()=>d});var r=o(96540),i=o(85647),n=o(72841),a=o(97396),l=o(32494),s=o(23818);let c=()=>{let{routes:e}=r.useContext(s.k),t=(0,i.Zp)();return r.useCallback((s,c={})=>{let d=(0,i.o1)(s).pathname;if(!(0,i.ue)(e,d)||c.reloadDocument){let e="string"==typeof s?s:(0,i.AO)(s);(async()=>{let{softNavigate:t}=await Promise.all([o.e("vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js"),o.e("ui_packages_soft-navigate_soft-navigate_ts")]).then(o.bind(o,59519));t(e)})()}else{(0,n.A)(location.href,s.toString())||(0,a.SC)("react");let{preventAutofocus:e,...o}=c;(0,r.startTransition)(()=>{t(s,e?{...o,state:{[l.V]:!0,...o.state}}:o)})}},[t,e])},d=()=>{let[e]=(0,i.ok)(),t=c(),{pathname:o}=(0,i.zy)(),n=r.useCallback((r,n={})=>{t({pathname:o,search:(0,i.PI)("function"==typeof r?r(e):r).toString()},n)},[e,t,o]);return[e,n]}},54763:(e,t,o)=>{o.d(t,{E:()=>n});var r=o(74848),i=o(69098);function n({children:e}){return(0,i.P)()?(0,r.jsx)(r.Fragment,{children:e}):null}try{n.displayName||(n.displayName="AllShortcutsEnabled")}catch{}},21609:(e,t,o)=>{o.d(t,{_:()=>i});var r=o(74848);function i({buttonFocusId:e,buttonHotkey:t,onButtonClick:o,buttonTestLabel:i,onlyAddHotkeyScopeButton:n}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("button",{hidden:!0,"data-testid":i||"","data-hotkey":t,onClick:o,"data-hotkey-scope":e}),!n&&(0,r.jsx)("button",{hidden:!0,"data-hotkey":t,onClick:o})]})}try{i.displayName||(i.displayName="DuplicateOnKeydownButton")}catch{}},25925:(e,t,o)=>{o.d(t,{R:()=>f});var r=o(74848),i=o(88795),n=o(96235),a=o(33613),l=o(99543),s=o(96540),c=o(141),d=o(50104),u=o(98637);function f({size:e,buttonClassName:t,allowResizing:o,idEnding:f}){let h=(0,i.t)(),{refInfo:m,path:p,action:y}=(0,c.eu)(),g=function(){let{addToast:e}=(0,l.Y6)();return(0,s.useCallback)(t=>e({type:"error",message:t}),[e])}(),{sendRepoClickEvent:x}=(0,u.T)(),{refSelectorShortcut:w}=(0,d.wk)();return(0,r.jsx)(a.PI,{currentCommitish:m.name,defaultBranch:h.defaultBranch,owner:h.ownerLogin,repo:h.name,canCreate:h.currentUserCanPush,cacheKey:m.listCacheKey,selectedRefType:m.refType,getHref:e=>`${(0,n.IO9)({repo:h,commitish:e,action:y,path:p})}${window.location.search}`,hotKey:w.hotkey,onBeforeCreate:e=>x("REF_SELECTOR_MENU.CREATE_BRANCH",{ref_name:e}),onCreateError:g,onOpenChange:e=>e&&x("REF_SELECTOR_MENU"),size:e,buttonClassName:t,allowResizing:o,idEnding:f||"repos-header-ref-selector",useFocusZone:!0})}try{f.displayName||(f.displayName="ReposHeaderRefSelector")}catch{}},2680:(e,t,o)=>{o.r(t),o.d(t,{FileResultRow:()=>j,default:()=>A});var r=o(74848),i=o(85579),n=o(141),a=o(98637),l=o(21113),s=o(88795),c=o(81367),d=o(96235),u=o(28391),f=o(68415),h=o(26807),m=o(69676),p=o(38621),y=o(82678),g=o(6869),x=o(15385),w=o(34614),v=o(75177),k=o(3971),b=o(63867),R=o(53110),F=o(91385),S=o(96540),C=o(89276),N=o(27406),_=o(38111),B=o(62834);let T={Box:"FileResultsList-module__Box--CSFTs",OverlayEnabled:"FileResultsList-module__OverlayEnabled--x2UoJ",ActionList:"FileResultsList-module__ActionList--E5FBw",ActionList_Overlay:"FileResultsList-module__ActionList_Overlay--EOr6b",Box_1:"FileResultsList-module__Box_1--ZnWjQ",FilesSearchBox:"FileResultsList-module__FilesSearchBox--J5FtW",ActionList_Item:"FileResultsList-module__ActionList_Item--Jzwuy",HighlightMatch:"FileResultsList-module__HighlightMatch--jIibS",Box_2:"FileResultsList-module__Box_2--oguP6",Octicon:"FileResultsList-module__Octicon--Qr0yq",PrimerLink:"FileResultsList-module__PrimerLink--yrBav"};var E=o(34164);let M={excludeDirectories:!1,excludeSeeAllResults:!1};function A({actionListClassName:e,additionalResults:t,commitOid:o,config:u=M,findFileWorkerPath:p,getItemUrl:b,onRenderRow:R,onItemSelected:F,searchBoxRef:A,className:j,sx:H}){let{excludeDirectories:P,excludeSeeAllResults:L}=u,{query:O,setQuery:D}=(0,i.JS)(),q=(0,s.t)(),$=S.useRef(null),U=A??$,[V,G]=S.useState(O.length>0),[z,K]=S.useState(!!O),{list:Z,directories:J,loading:X,error:Y}=(0,B.o)(o,V,!!P),{path:Q}=(0,n.eu)(),{getUrl:ee}=(0,l.Z)(),{queryText:et,queryLine:eo}=function(e){let t=(e=e.replaceAll(" ","")).indexOf(":");return t>=0?{queryText:e.substring(0,t),queryLine:parseInt(e.substring(t+1),10)}:{queryText:e,queryLine:void 0}}(O),{matches:er,clearMatches:ei}=function(e,t,o,r){let[i,n]=S.useState(),l=S.useRef(""),s=S.useRef(),{sendStats:c}=(0,a.T)(),d=S.useRef(!1),u=S.useCallback(()=>{let e=new C.z(o,N.j);e.onmessage=({data:e})=>{d.current=!1,n(e.list),l.current=e.query,e.startTime&&c("repository.find-file",{"find-file-base-count":e.baseCount,"find-file-results-count":e.list.length,"find-file-duration-ms":performance.now()-e.startTime})},s.current=e},[c,o]);return S.useEffect(()=>{if(r)return u(),function(){s.current?.terminate()}},[u,r]),S.useEffect(()=>{if(e.length&&t){d.current&&(s.current?.terminate(),u());let o=l.current&&t.startsWith(l.current);d.current=!0,s.current?.postMessage({baseList:o&&i||e,query:t,startTime:performance.now()})}},[e,t,u]),{matches:i,clearMatches:()=>n(void 0)}}((0,S.useMemo)(()=>[...Z,...t??[]].sort(),[t,Z]),et,p,V),{sendRepoClickEvent:en}=(0,a.T)(),ea=(0,h.Z)(),[el,es]=S.useState(0),[ec,ed]=S.useState(()=>(0,_.q)()),eu=S.useRef(null),ef=S.useRef(null),eh="file-results-list",{sendRepoKeyDownEvent:em}=(0,a.T)(),{screenSize:ep}=(0,f.lm)(),ey=u.enableOverlay??ep>=f.Gy.large,eg=S.useCallback(e=>{en("FILE_TREE.SEARCH_RESULT_CLICK"),F?.(e),K(!1)},[en,F]),ex=(e,t,o)=>b?b(e,t,o):ee({path:e,action:t?"tree":"blob",hash:o}),{containerRef:ew}=(0,y.G)({bindKeys:m.z0.ArrowVertical|m.z0.HomeAndEnd,focusInStrategy:"previous"},[X,Y]);S.useEffect(()=>{O||K(!1)},[O]),S.useEffect(()=>{document.activeElement!==U.current&&ey&&K(!1)},[Q,U,ey]);let ev=er?.slice(0,40)||[],ek=er&&er.length>ev.length,eb=(0,r.jsx)("div",{className:(0,E.$)(T.Box,{[T.OverlayEnabled]:ey}),children:Y?(0,r.jsx)(g.A,{variant:"danger",className:"m-3",children:"Failed to search"}):(0,r.jsxs)(x.l,{ref:ew,className:(0,E.$)(e,T.ActionList,{[T.ActionList_Overlay]:ey}),role:"listbox",children:[!X&&ev.map((e,t)=>{let o=J.includes(e),i=ex(e,o,eo?`L${eo}`:"");return(0,r.jsx)(W,{active:e===Q,index:t,focused:ec&&el===t,match:e,onRender:R,query:et,onClick:eg,isDirectory:o,to:i,useOverlay:ey,listRef:ew},e)}),(0,r.jsxs)("div",{className:"m-3 text-center",children:[(0,r.jsx)(I,{loading:X||!er,visibleResultCount:ev.length,truncated:!!ek}),ek&&!L&&(0,r.jsxs)(r.Fragment,{children:["\xa0",(0,r.jsx)(w.A,{id:"see-all-results-link",className:(0,E.$)("focus-visible",{[T.PrimerLink]:ec&&el===ev.length}),ref:eu,href:(0,d.Y8Y)({owner:q.ownerLogin,repo:q.name,searchTerm:`path:${et}`}),children:"See all results"})]})]})]})});return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(v.A,{ref:ef,sx:H,className:(0,E.$)(j,T.Box_1),children:(0,r.jsx)(_.Z,{ariaActiveDescendant:(!ey&&O||ey&&z)&&ec&&el>-1?ek&&el===ev.length?"see-all-results-link":`file-result-${el}`:void 0,ariaExpanded:ey?z:void 0,ariaHasPopup:ey,ariaControls:ey?eh:void 0,ref:U,query:O,onKeyDown:e=>{let{key:t,shiftKey:o,metaKey:r,altKey:i,ctrlKey:n}=e;if(!o&&!r&&!i){if("Escape"===t)O?(em("FILE_TREE.CANCEL_SEARCH"),D(""),ei()):document.activeElement&&document.activeElement.blur();else if(!O)return;else if("Enter"===t){if(!L&&ek&&el===ev.length)ea((0,d.Y8Y)({owner:q.ownerLogin,repo:q.name,searchTerm:`path:${et}`})),F?.();else if(ev[el]){let e=ev[el];ea(ex(ev[el],!1,eo?`L${eo}`:"")),K(!1),F?.(e)}}else if("ArrowDown"===t||n&&"n"===t){if(!L&&ek&&el>=ev.length-1){if(es(ev.length),eu.current&&ew.current){let e=(0,c.U)(ew.current);(0,m.Rt)(eu.current,e,{behavior:"instant"})}}else es(Math.min(el+1,ev.length-1));e.preventDefault();return}else if("ArrowUp"===t||n&&"p"===t){es(Math.max(el-1,0)),e.preventDefault();return}}},onPreload:()=>G(!0),onSearch:e=>{D(e),e?K(!0):(ei(),K(!1)),es(0)},onBlur:e=>{ew.current?.contains(e.relatedTarget)||(K(!1),ed(!1))},onFocus:()=>{O&&K(!0),ed(!0)},className:T.FilesSearchBox})}),ey&&(0,r.jsx)(k.T,{anchorRef:ef,open:ey&&z,renderAnchor:null,onClose:()=>{K(!1)},focusZoneSettings:{disabled:!0},focusTrapSettings:{disabled:!0},width:"xlarge",align:"end",overlayProps:{id:eh,role:"dialog"},children:eb}),!ey&&O&&eb]})}function I({visibleResultCount:e,truncated:t,loading:o}){return(0,r.jsx)("span",{role:"status",className:o||t||0===e?void 0:"sr-only","aria-label":o?"Loading":void 0,children:o?(0,r.jsx)(b.A,{size:"large"}):0===e?"No matches found":t?`First ${e} files shown.`:`Showing ${e} files.`},"results-count-status")}let j=({active:e,focused:t,index:o,match:i,query:n,to:a,isDirectory:l,onClick:s,onRender:d,useOverlay:f,listRef:h})=>{let p=(0,F.Xq)(n,i);d?.();let y=S.useRef(null);S.useEffect(()=>{if(t&&y.current&&h?.current){let e=(0,c.U)(h.current);(0,m.Rt)(y.current,e,{behavior:"instant"})}},[t,h]);let g={};t&&(g={outline:"none",border:"2 solid",boxShadow:"0 0 0 2px #0969da"});let w=(0,S.useCallback)(()=>{s?.(i)},[i,s]);return(0,r.jsx)(x.l.LinkItem,{id:`file-result-${o}`,ref:y,as:u.N,onClick:w,to:a,active:e,className:(0,E.$)("d-flex",T.ActionList_Item),sx:g,role:"option","data-focus-visible-added":t||void 0,tabIndex:f?-1:0,children:(0,r.jsxs)("div",{className:"d-flex",children:[(0,r.jsx)("div",{className:"d-flex flex-1 flex-column overflow-hidden",children:(0,r.jsx)(L,{text:i,positionsList:p,LeadingIcon:l?H:P,className:T.HighlightMatch})}),t&&(0,r.jsx)("div",{className:T.Box_2,children:`Go to ${l?"folder":"file"}`})]})},i)},H=()=>(0,r.jsx)(R.A,{"aria-label":"Directory",icon:p.FileDirectoryFillIcon,size:"small",className:T.Octicon}),P=()=>(0,r.jsx)(R.A,{"aria-label":"File",icon:p.FileIcon,className:"fgColor-muted mr-2",size:"small"}),W=S.memo(j);function L({text:e,positionsList:t,className:o,LeadingIcon:i}){let n=[],a=0;for(let o of t){if(Number(o)!==o||o<a||o>e.length)continue;let t=e.slice(a,o);t&&n.push(O(t)),a=o+1,n.push((0,r.jsx)("mark",{className:"text-bold bgColor-transparent fgColor-default",children:e[o]},o))}return n.push(O(e.slice(a))),(0,r.jsx)("div",{className:o,children:(0,r.jsxs)(r.Fragment,{children:[i&&(0,r.jsx)(i,{}),n]})})}function O(e){return e.replaceAll("/","/\u200B")}try{A.displayName||(A.displayName="FileResultsList")}catch{}try{I.displayName||(I.displayName="FileResultsStatus")}catch{}try{j.displayName||(j.displayName="FileResultRow")}catch{}try{H.displayName||(H.displayName="DirectoryIcon")}catch{}try{P.displayName||(P.displayName="FileResultIcon")}catch{}try{W.displayName||(W.displayName="MemoizedFileResultRow")}catch{}try{L.displayName||(L.displayName="HighlightMatch")}catch{}},38111:(e,t,o)=>{o.d(t,{Z:()=>h,q:()=>m});var r=o(74848),i=o(70170),n=o(54763),a=o(98637),l=o(96679),s=o(38621),c=o(9591),d=o(96540);let u={TextInput:"FilesSearchBox-module__TextInput--LKpMn",Box:"FilesSearchBox-module__Box--Ye6rL"};var f=o(34164);let h=d.forwardRef(({ariaActiveDescendant:e,ariaControls:t,ariaExpanded:o,ariaHasPopup:l,onBlur:h,onFocus:p,onKeyDown:y,onPreload:g,onSearch:x,query:w,sx:v,className:k},b)=>{let{sendRepoClickEvent:R}=(0,a.T)(),[F,S]=d.useState(w),C=d.useRef((0,i.s)(e=>x(e),250));d.useEffect(()=>{S(w)},[w]);let N=w?(0,r.jsx)(c.A.Action,{onClick:()=>{R("FILE_TREE.CANCEL_SEARCH"),x("")},icon:s.XCircleFillIcon,"aria-label":"Clear",className:"fgColor-muted"}):void 0;return(0,r.jsx)(c.A,{autoFocus:m(),ref:b,value:F,onKeyDown:y,onChange:e=>{S(e.target.value),g(),C.current(e.target.value)},sx:v,className:(0,f.$)(k,u.TextInput),"aria-label":"Go to file","aria-activedescendant":e,role:l?"combobox":void 0,"aria-controls":t,"aria-expanded":o,"aria-haspopup":l?"dialog":void 0,autoCorrect:"off",spellCheck:"false",placeholder:"Go to file",leadingVisual:s.SearchIcon,trailingAction:N,trailingVisual:N?void 0:()=>(0,r.jsx)(n.E,{children:(0,r.jsx)("div",{className:u.Box,children:(0,r.jsx)("kbd",{children:"t"})})}),onFocus:e=>{g(),e.target.select(),p?.(e)},onBlur:h,onClick:()=>R("FILE_TREE.SEARCH_BOX")})});function m(){return"1"===new URLSearchParams(l.fV.search).get("search")}h.displayName="FilesSearchBox"},69098:(e,t,o)=>{o.d(t,{$:()=>a,P:()=>l});var r=o(74848),i=o(96540);let n=i.createContext(!0);function a({allShortcutsEnabled:e,children:t}){return(0,r.jsxs)(n.Provider,{value:e,children:[" ",t," "]})}function l(){return i.useContext(n)}try{n.displayName||(n.displayName="AllShortcutsEnabledContext")}catch{}try{a.displayName||(a.displayName="AllShortcutsEnabledProvider")}catch{}},85579:(e,t,o)=>{o.d(t,{Ck:()=>l,JS:()=>a});var r=o(74848),i=o(96540);let n=(0,i.createContext)({query:"",setQuery:()=>void 0});function a(){return(0,i.useContext)(n)}function l({children:e}){let[t,o]=(0,i.useState)(""),a=(0,i.useMemo)(()=>({query:t,setQuery:o}),[o,t]);return(0,r.jsx)(n.Provider,{value:a,children:e})}try{n.displayName||(n.displayName="FileQueryContext")}catch{}try{l.displayName||(l.displayName="FileQueryProvider")}catch{}},141:(e,t,o)=>{o.d(t,{eu:()=>s,qV:()=>l,sq:()=>c});var r=o(74848),i=o(64262),n=o(96540);let a=n.createContext({});function l({children:e,...t}){return(0,r.jsx)(a.Provider,{value:t,children:e})}function s(){return n.useContext(a)}function c(){return(0,i.X)()}try{a.displayName||(a.displayName="FilesPageInfoContext")}catch{}try{l.displayName||(l.displayName="FilesPageInfoProvider")}catch{}},61763:(e,t,o)=>{o.d(t,{Ak:()=>f,cD:()=>u,iK:()=>d});var r=o(74848),i=o(18138),n=o(93955),a=o(85351),l=o(68415),s=o(96540);let c=(0,a.A)("localStorage"),d=(0,s.createContext)({openPanel:void 0,setOpenPanel:()=>void 0});function u({children:e,payload:t,openPanelRef:o}){let a="blame"in t,u=(0,i.Hf)(t),f=(0,n.i)(),h=(0,s.useRef)(void 0),[m,p]=(0,s.useState)(()=>{let e=c.getItem("codeNavOpen");if(!f&&""!==e&&null!==e||f&&t.symbolsExpanded)return"codeNav"}),y=(a||u)&&"codeNav"===m?void 0:m;s.useEffect(()=>{o.current=y},[y,o]);let g=(0,s.useCallback)((e,t)=>{p(o=>(o&&h.current&&h.current.focus(),h.current=t,a||u&&"codeNav"===e?void 0:e))},[a,u]);var x=g;let{screenSize:w}=(0,l.lm)(),v=(0,s.useRef)(w);(0,s.useEffect)(()=>{let e=v.current>=l.Gy.large,t=v.current===w;w<l.Gy.large&&(e||t)&&x(void 0),v.current=w},[w,x]);let k=(0,s.useMemo)(()=>({openPanel:y,setOpenPanel:g}),[y,g]);return(0,r.jsx)(d.Provider,{value:k,children:e})}function f(){return(0,s.useContext)(d)}try{d.displayName||(d.displayName="OpenPanelContext")}catch{}try{u.displayName||(u.displayName="OpenPanelProvider")}catch{}},8579:(e,t,o)=>{o.d(t,{T:()=>h});var r=o(74848),i=o(64899),n=o(38621),a=o(87330),l=o(96540),s=o(34164),c=o(89985),d=o(52811),u=o(640);let f={tooltip:"CopyToClipboardButton-module__tooltip--Dq1IB"};function h({icon:e=n.CopyIcon,size:t="medium",onCopy:o,textToCopy:h,tooltipProps:m,variant:p="invisible",ariaLabel:y,className:g,disabled:x,...w}){let[v,k]=l.useState(!1),b=(0,i.A)(),R=y??`Copy "${h}" to clipboard`,F=v?"Copied!":R,S={size:t,variant:p,onClick:()=>{k(!0),(0,d.i)("Copied!"),(0,u.D)(h),o?.(),setTimeout(()=>b()&&k(!1),2e3)},icon:v?n.CheckIcon:e,className:(0,s.$)(v?"color-fg-success":void 0,g),...w},C=(0,l.useId)();return x?(0,r.jsx)(a.K,{...S,"aria-label":R,disabled:!0}):(0,r.jsx)(c.m,{text:F,"aria-label":R,type:"label",id:C,"aria-hidden":!0,...m,className:(0,s.$)(f.tooltip,m?.className),children:(0,r.jsx)(a.K,{...S,"aria-labelledby":C})})}try{h.displayName||(h.displayName="CopyToClipboardButton")}catch{}},63869:(e,t,o)=>{o.d(t,{L:()=>s});var r=o(74848),i=o(21728),n=o(38621),a=o(87330),l=o(34164);let s=o(96540).forwardRef((e,t)=>{let o,s,c,d=(0,i.c)(17),{expanded:u,testid:f,ariaLabel:h,ariaControls:m,onToggleExpanded:p,alignment:y,dataHotkey:g,className:x,size:w,tooltipDirection:v,variant:k}=e,b=u?`collapse-${f}`:`expand-${f}`,R=u?"left"===y?n.SidebarExpandIcon:n.SidebarCollapseIcon:"left"===y?n.SidebarCollapseIcon:n.SidebarExpandIcon;d[0]!==p?(o=e=>{p(e)},d[0]=p,d[1]=o):o=d[1];let F=k??"invisible";return d[2]!==x?(s=(0,l.$)(x,"fgColor-muted"),d[2]=x,d[3]=s):s=d[3],d[4]!==m||d[5]!==h||d[6]!==g||d[7]!==u||d[8]!==t||d[9]!==w||d[10]!==b||d[11]!==R||d[12]!==o||d[13]!==F||d[14]!==s||d[15]!==v?(c=(0,r.jsx)(a.K,{"aria-label":h,tooltipDirection:v,ref:t,"data-testid":b,"aria-expanded":u,"aria-controls":m,icon:R,"data-hotkey":g,onClick:o,variant:F,size:w,className:s}),d[4]=m,d[5]=h,d[6]=g,d[7]=u,d[8]=t,d[9]=w,d[10]=b,d[11]=R,d[12]=o,d[13]=F,d[14]=s,d[15]=v,d[16]=c):c=d[16],c});s.displayName="ExpandButton"},41059:(e,t,o)=>{o.d(t,{Y:()=>h});var r=o(74848),i=o(59299),n=o(44999),a=o(16255),l=o(34164),s=o(96540);let c=o(38267).Ay.span.withConfig({displayName:"ControlledTooltip__TooltipBase",componentId:"sc-b39269ff-0"})(["&::after{position:absolute;z-index:1000000;display:none;padding:0.5em 0.75em;font:normal normal 11px/1.5 ",";-webkit-font-smoothing:subpixel-antialiased;color:",";text-align:center;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-wrap:break-word;white-space:pre;pointer-events:none;content:attr(data-visible-text);background:",";border-radius:",";opacity:0;}@keyframes tooltip-appear{from{opacity:0;}to{opacity:1;}}&.tooltipped-open,&:hover,&:active,&:focus{&::after{display:inline-block;text-decoration:none;animation-name:tooltip-appear;animation-duration:0.1s;animation-fill-mode:forwards;animation-timing-function:ease-in;animation-delay:0s;}}&.tooltipped-no-delay.tooltipped-open,&.tooltipped-no-delay:hover,&.tooltipped-no-delay:active,&.tooltipped-no-delay:focus{&::after{animation-delay:0s;}}&.tooltipped-s,&.tooltipped-se,&.tooltipped-sw{&::after{top:100%;right:50%;margin-top:6px;}}&.tooltipped-se{&::after{right:auto;left:50%;margin-left:-",";}}&.tooltipped-sw::after{margin-right:-",";}&.tooltipped-n,&.tooltipped-ne,&.tooltipped-nw{&::after{right:50%;bottom:100%;margin-bottom:6px;}}&.tooltipped-ne{&::after{right:auto;left:50%;margin-left:-",";}}&.tooltipped-nw::after{margin-right:-",";}&.tooltipped-s::after,&.tooltipped-n::after{transform:translateX(50%);}&.tooltipped-w{&::after{right:100%;bottom:50%;margin-right:6px;transform:translateY(50%);}}&.tooltipped-e{&::after{bottom:50%;left:100%;margin-left:6px;transform:translateY(50%);}}&.tooltipped-align-right-2::after{right:0;margin-right:0;}&.tooltipped-align-left-2::after{left:0;margin-left:0;}",";"],(0,i.Jt)("fonts.normal"),(0,i.Jt)("colors.fg.onEmphasis"),(0,i.Jt)("colors.neutral.emphasisPlus"),(0,i.Jt)("radii.2"),(0,i.Jt)("space.3"),(0,i.Jt)("space.3"),(0,i.Jt)("space.3"),(0,i.Jt)("space.3"),n.A),d=()=>()=>void 0,u=()=>!1,f=()=>!0,h=(0,s.forwardRef)(function({direction:e="n",className:t,text:o,noDelay:i,align:n,wrap:h,open:m=!1,portalProps:p={},...y},g){let x=(0,s.useSyncExternalStore)(d,f,u),w=(0,l.$)(t,`tooltipped-${e}`,n&&`tooltipped-align-${n}-2`,i&&"tooltipped-no-delay",h&&"tooltipped-multiline",m&&"tooltipped-open");return x?(0,r.jsx)(a.Z,{...p,children:(0,r.jsx)(c,{ref:g,role:"tooltip","aria-label":o,"data-visible-text":o||y["aria-label"],...y,sx:{position:"fixed",zIndex:1,...y.sx},className:w})}):null});try{h.displayName||(h.displayName="ControlledTooltip")}catch{}},99987:(e,t,o)=>{o.d(t,{m:()=>s});var r=o(74848),i=o(69676),n=o(96540),a=o(41059),l=o(81367);let s=(0,n.forwardRef)(function({contentRef:e,open:t,anchoredPositionAlignment:o,anchorSide:s,anchorOffset:c,alignmentOffset:d,allowOutOfBounds:u,...f},h){let m=(0,n.useRef)(null);(0,n.useImperativeHandle)(h,()=>m.current);let p=(0,n.useRef)({left:0,top:0}),y=(0,n.useSyncExternalStore)((0,n.useCallback)(o=>{if(!m.current||!e.current||!t)return()=>void 0;let r=(0,l.U)(e.current);return r?.addEventListener("scroll",o),()=>{r?.removeEventListener("scroll",o)}},[e,t]),(0,n.useCallback)(()=>{if(!m.current||!e.current)return p.current;let t=(0,i.uG)(m.current,e.current,{align:o??"center",side:s??"outside-top",alignmentOffset:d??0,anchorOffset:c??0,allowOutOfBounds:u});return(t.left!==p.current.left||t.top!==p.current.top)&&(p.current=t),p.current},[e,d,c,o,s,u]),(0,n.useCallback)(()=>p.current,[]));return(0,r.jsx)(a.Y,{...f,ref:m,open:t,style:{position:"absolute",...y,...f.style}})});try{s.displayName||(s.displayName="PortalTooltip")}catch{}},30422:(e,t,o)=>{o.d(t,{BC:()=>c,JU:()=>u,Pk:()=>s});var r=o(74848),i=o(75177),n=o(52464),a=o(99418),l=o(96540);let s=f(i.A),c=f(n.A),d=(0,l.forwardRef)((e,t)=>(0,r.jsx)("div",{ref:t,...e}));d.displayName="Div";let u=f(d);function f(e){let t=(0,l.forwardRef)((t,o)=>{let{sanitizedHTML:i,props:n}=function(e){let{html:t,domPurifyConfig:o,...r}=e,i={...o,RETURN_DOM:!1,RETURN_DOM_FRAGMENT:!1};return{sanitizedHTML:a.default.sanitize(t,i),props:r}}(t);return(0,r.jsx)(e,{ref:o,...n,dangerouslySetInnerHTML:{__html:i}})});return t.displayName=`UnsafeHTML${e.displayName||e.name}`,t}try{u.displayName||(u.displayName="UnsafeHTMLDiv")}catch{}},21325:(e,t,o)=>{o.d(t,{T2:()=>c,cp:()=>m,ud:()=>y});var r=o(74848),i=o(21728),n=o(85351),a=o(96540);let l=(0,n.A)("localStorage"),s="codeView.codeFolding",c="codeView.codeWrapping",d="codeView.centerView",u="codeView.openSymbolsOnClick",f=new Map([[s,"Show code folding buttons"],[c,"Wrap lines"],[d,"Center content"],[u,"Open symbols on click"]]),h=(0,a.createContext)({codeFoldingOption:{},codeWrappingOption:{},codeCenterOption:{},openSymbolsOption:{}}),m=e=>{let t,o,n,a=(0,i.c)(8),{children:l}=e,f=p(s,!0),m=p(c,!1),y=p(d,!1),g=p(u,!0);return a[0]!==y||a[1]!==f||a[2]!==m||a[3]!==g?(o={codeFoldingOption:f,codeWrappingOption:m,codeCenterOption:y,openSymbolsOption:g},a[0]=y,a[1]=f,a[2]=m,a[3]=g,a[4]=o):o=a[4],t=o,a[5]!==l||a[6]!==t?(n=(0,r.jsx)(h.Provider,{value:t,children:l}),a[5]=l,a[6]=t,a[7]=n):n=a[7],n};function p(e,t){let o,r,n,s,c=(0,i.c)(11);c[0]!==e?(o=l.getItem(e),c[0]=e,c[1]=o):o=c[1];let d=o;c[2]!==t||c[3]!==d?(r=()=>d?"true"===d:t,c[2]=t,c[3]=d,c[4]=r):r=c[4];let[u,h]=(0,a.useState)(r);c[5]!==e?(n=f.get(e)||"",c[5]=e,c[6]=n):n=c[6];let m=n;return c[7]!==u||c[8]!==m||c[9]!==e?(s={name:e,enabled:u,setEnabled:h,label:m},c[7]=u,c[8]=m,c[9]=e,c[10]=s):s=c[10],s}function y(){return(0,a.useContext)(h)}try{h.displayName||(h.displayName="CodeViewOptionsContext")}catch{}try{m.displayName||(m.displayName="CodeViewOptionsProvider")}catch{}}}]);
//# sourceMappingURL=ui_packages_app-uuid_app-uuid_ts-ui_packages_fetch-headers_fetch-headers_ts-ui_packages_repos-0cd8c2-f3f5556496ef.js.map