"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52"],{70170:(e,t,o)=>{function n(e,t=0,{start:o=!0,middle:r=!0,once:i=!1}={}){let a,l=o,c=0,s=!1;function u(...n){if(s)return;let m=Date.now()-c;c=Date.now(),o&&r&&m>=t&&(l=!0),l?(l=!1,e.apply(this,n),i&&u.cancel()):(r&&m<t||!r)&&(clearTimeout(a),a=setTimeout(()=>{c=Date.now(),e.apply(this,n),i&&u.cancel()},r?t-m:t))}return u.cancel=()=>{clearTimeout(a),s=!0},u}function r(e,t=0,{start:o=!1,middle:i=!1,once:a=!1}={}){return n(e,t,{start:o,middle:i,once:a})}o.d(t,{n:()=>n,s:()=>r})},24212:(e,t,o)=>{o.d(t,{q:()=>r});var n="<unknown>";function r(e){return e.split(`
`).reduce(function(e,t){var o,r,d,p=function(e){var t=i.exec(e);if(!t)return null;var o=t[2]&&0===t[2].indexOf("native"),r=t[2]&&0===t[2].indexOf("eval"),l=a.exec(t[2]);return r&&null!=l&&(t[2]=l[1],t[3]=l[2],t[4]=l[3]),{file:o?null:t[2],methodName:t[1]||n,arguments:o?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}(t)||((o=l.exec(t))?{file:o[2],methodName:o[1]||n,arguments:[],lineNumber:+o[3],column:o[4]?+o[4]:null}:null)||function(e){var t=c.exec(e);if(!t)return null;var o=t[3]&&t[3].indexOf(" > eval")>-1,r=s.exec(t[3]);return o&&null!=r&&(t[3]=r[1],t[4]=r[2],t[5]=null),{file:t[3],methodName:t[1]||n,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}(t)||((r=m.exec(t))?{file:r[2],methodName:r[1]||n,arguments:[],lineNumber:+r[3],column:r[4]?+r[4]:null}:null)||((d=u.exec(t))?{file:d[3],methodName:d[1]||n,arguments:[],lineNumber:+d[4],column:d[5]?+d[5]:null}:null);return p&&e.push(p),e},[])}var i=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,a=/\((\S*)(?::(\d+))(?::(\d+))\)/,l=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,c=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,s=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,u=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i,m=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i},77065:(e,t,o)=>{o.d(t,{Bb:()=>q,TT:()=>$});var n={};o.r(n),o.d(n,{ClipboardItem:()=>clipboarditem_ClipboardItem,apply:()=>p,isPolyfilled:()=>d,isSupported:()=>m});var r={};o.r(r),o.d(r,{apply:()=>v,checkVisibility:()=>f,isPolyfilled:()=>b,isSupported:()=>h});var i={};o.r(i),o.d(i,{apply:()=>k,clipboardRead:()=>y,clipboardWrite:()=>g,isPolyfilled:()=>E,isSupported:()=>w});var a={};o.r(a),o.d(a,{apply:()=>I,isPolyfilled:()=>S,isSupported:()=>C,withResolvers:()=>T});var l={};o.r(l),o.d(l,{apply:()=>M,cancelIdleCallback:()=>A,isPolyfilled:()=>j,isSupported:()=>x,requestIdleCallback:()=>P});var c={};o.r(c),o.d(c,{apply:()=>R,isPolyfilled:()=>D,isSupported:()=>N});let s=new WeakMap,u=new WeakMap;let clipboarditem_ClipboardItem=class clipboarditem_ClipboardItem{constructor(e,t={}){if(0===Object.keys(e).length)throw TypeError("Empty dictionary argument");s.set(this,e),u.set(this,t.presentationStyle||"unspecified")}get presentationStyle(){return u.get(this)||"unspecified"}get types(){return Object.freeze(Object.keys(s.get(this)||{}))}async getType(e){let t=s.get(this);if(t&&e in t){let o=await t[e];return"string"==typeof o?new Blob([o],{type:e}):o}throw new DOMException("Failed to execute 'getType' on 'ClipboardItem': The type was not found","NotFoundError")}};function m(){try{return new globalThis.ClipboardItem({"text/plain":Promise.resolve("")}),!0}catch{return!1}}function d(){return globalThis.ClipboardItem===clipboarditem_ClipboardItem}function p(){m()||(globalThis.ClipboardItem=clipboarditem_ClipboardItem)}function f({checkOpacity:e=!1,checkVisibilityCSS:t=!1}={}){if(!this.isConnected)return!1;let o=getComputedStyle(this);if("contents"===o.getPropertyValue("display")||t&&"visible"!==o.getPropertyValue("visibility"))return!1;let n=this;for(;n;){let t=n===this?o:getComputedStyle(n);if("none"===t.getPropertyValue("display")||e&&"0"===t.getPropertyValue("opacity")||n!==this&&"hidden"===t.getPropertyValue("content-visibility"))return!1;n=!n.parentElement&&n.getRootNode()instanceof ShadowRoot?n.getRootNode().host:n.parentElement}return!0}function h(){return"checkVisibility"in Element.prototype&&"function"==typeof Element.prototype.checkVisibility}function b(){return Element.prototype.checkVisibility===f}function v(){h()||(Element.prototype.checkVisibility=f)}async function g(e){if(0===e.length)return;let t=e[0],o=await t.getType(t.types.includes("text/plain")?"text/plain":t.types[0]);return navigator.clipboard.writeText("string"==typeof o?o:await o.text())}async function y(){return[new ClipboardItem({"text/plain":navigator.clipboard.readText()})]}function w(){return"clipboard"in navigator&&"function"==typeof navigator.clipboard.read&&"function"==typeof navigator.clipboard.write}function E(){return"clipboard"in navigator&&(navigator.clipboard.write===g||navigator.clipboard.read===y)}function k(){"clipboard"in navigator&&!w()&&(navigator.clipboard.write=g,navigator.clipboard.read=y)}function T(){let e={};return e.promise=new Promise((t,o)=>{e.resolve=t,e.reject=o}),e}function C(){return"withResolvers"in Promise&&"function"==typeof Promise.withResolvers}function S(){return"withResolvers"in Promise&&Promise.withResolvers===T}function I(){C()||Object.assign(Promise,{withResolvers:T})}function P(e,t={}){let o=Date.now(),n=t.timeout||0,r=Object.defineProperty({didTimeout:!1,timeRemaining:()=>Math.max(0,50-(Date.now()-o))},"didTimeout",{get:()=>Date.now()-o>n});return window.setTimeout(()=>{e(r)})}function A(e){clearTimeout(e)}function x(){return"function"==typeof globalThis.requestIdleCallback}function j(){return globalThis.requestIdleCallback===P&&globalThis.cancelIdleCallback===A}function M(){x()||(globalThis.requestIdleCallback=P,globalThis.cancelIdleCallback=A)}var O=o(913);function N(){return"undefined"!=typeof HTMLButtonElement&&"command"in HTMLButtonElement.prototype&&"source"in((globalThis.CommandEvent||{}).prototype||{})}function D(){return!/native code/i.test((globalThis.CommandEvent||{}).toString())}function R(){function e(t,o,n=!0){Object.defineProperty(t,o,{...Object.getOwnPropertyDescriptor(t,o),enumerable:n})}function t(e){return e&&"function"==typeof e.getRootNode?e.getRootNode():e&&e.parentNode?t(e.parentNode):e}document.addEventListener("invoke",e=>{"invoke"==e.type&&e.isTrusted&&(e.stopImmediatePropagation(),e.preventDefault())},!0),document.addEventListener("command",e=>{"command"==e.type&&e.isTrusted&&(e.stopImmediatePropagation(),e.preventDefault())},!0);let o=globalThis.ShadowRoot||function(){},n=new WeakMap,r=new WeakMap;let CommandEvent=class CommandEvent extends Event{constructor(e,t={}){super(e,t);let{source:o,command:i}=t;if(null!=o&&!(o instanceof Element))throw TypeError("source must be an element");n.set(this,o||null),r.set(this,void 0!==i?String(i):"")}get[Symbol.toStringTag](){return"CommandEvent"}get source(){if(!n.has(this))throw TypeError("illegal invocation");let e=n.get(this);if(!(e instanceof Element))return null;let o=t(e);return o!==t(this.target||document)?o.host:e}get command(){if(!r.has(this))throw TypeError("illegal invocation");return r.get(this)}get action(){throw Error("CommandEvent#action was renamed to CommandEvent#command")}get invoker(){throw Error("CommandEvent#invoker was renamed to CommandEvent#source")}};e(CommandEvent.prototype,"source"),e(CommandEvent.prototype,"command");let InvokeEvent=class InvokeEvent extends Event{constructor(){throw Error("InvokeEvent has been deprecated, it has been renamed to `CommandEvent`")}};let i=new WeakMap,a=new WeakMap;function l(e){for(let t of e)t.oncommand=Function("event",t.getAttribute("oncommand"))}Object.defineProperties(HTMLElement.prototype,{oncommand:{enumerable:!0,configurable:!0,get(){return c.takeRecords(),a.get(this)||null},set(e){let t=a.get(this)||null;t&&this.removeEventListener("command",t),a.set(this,"object"==typeof e||"function"==typeof e?e:null),"function"==typeof e&&this.addEventListener("command",e)}}});let c=new MutationObserver(e=>{for(let t of e){let{target:e}=t;"childList"===t.type?l(e.querySelectorAll("[oncommand]")):l([e])}});function s(e){if(e.defaultPrevented||"click"!==e.type)return;let t=e.target.closest("button[invoketarget], button[invokeaction], input[invoketarget], input[invokeaction]");if(t&&(console.warn("Elements with `invoketarget` or `invokeaction` are deprecated and should be renamed to use `commandfor` and `command` respectively"),t.matches("input")))throw Error("Input elements no longer support `commandfor`");let o=e.target.closest("button[commandfor], button[command]");if(!o)return;if(this.form&&"button"!==this.getAttribute("type"))throw e.preventDefault(),Error("Element with `commandFor` is a form participant. It should explicitly set `type=button` in order for `commandFor` to work. In order for it to act as a Submit button, it must not have command or commandfor attributes");if(o.hasAttribute("command")!==o.hasAttribute("commandfor")){let e=o.hasAttribute("command")?"command":"commandfor",t=o.hasAttribute("command")?"commandfor":"command";throw Error(`Element with ${e} attribute must also have a ${t} attribute to function.`)}if("show-popover"!==o.command&&"hide-popover"!==o.command&&"toggle-popover"!==o.command&&"show-modal"!==o.command&&"close"!==o.command&&!o.command.startsWith("--"))return void console.warn(`"${o.command}" is not a valid command value. Custom commands must begin with --`);let n=o.commandForElement;if(!n)return;let r=new CommandEvent("command",{command:o.command,source:o,cancelable:!0});if(n.dispatchEvent(r),r.defaultPrevented)return;let i=r.command.toLowerCase();if(n.popover){let e=!n.matches(":popover-open");e&&("toggle-popover"===i||"show-popover"===i)?n.showPopover({source:o}):e||"hide-popover"!==i||n.hidePopover()}else if("dialog"===n.localName){let e=!n.hasAttribute("open");e&&"show-modal"===i?n.showModal():e||"close"!==i||n.close()}}function u(e){e.addEventListener("click",s,!0)}c.observe(document,{subtree:!0,childList:!0,attributeFilter:["oncommand"]}),l(document.querySelectorAll("[oncommand]")),Object.defineProperties((globalThis.HTMLButtonElement||function(){}).prototype,{commandForElement:{enumerable:!0,configurable:!0,set(e){if(this.hasAttribute("invokeaction"))throw TypeError("Element has deprecated `invokeaction` attribute, replace with `command`");if(this.hasAttribute("invoketarget"))throw TypeError("Element has deprecated `invoketarget` attribute, replace with `commandfor`");if(null===e)this.removeAttribute("commandfor"),i.delete(this);else if(e instanceof Element){this.setAttribute("commandfor","");let o=t(e);t(this)===o||o===this.ownerDocument?i.set(this,e):i.delete(this)}else throw TypeError("commandForElement must be an element or null")},get(){if("button"!==this.localName)return null;if(this.hasAttribute("invokeaction")||this.hasAttribute("invoketarget"))return console.warn("Element has deprecated `invoketarget` or `invokeaction` attribute, use `commandfor` and `command` instead"),null;if(this.disabled)return null;if(this.form&&"button"!==this.getAttribute("type"))return console.warn("Element with `commandFor` is a form participant. It should explicitly set `type=button` in order for `commandFor` to work"),null;let e=i.get(this);if(e)if(e.isConnected)return e;else return i.delete(this),null;let n=t(this),r=this.getAttribute("commandfor");return(n instanceof Document||n instanceof o)&&r&&n.getElementById(r)||null}},command:{enumerable:!0,configurable:!0,get(){let e=this.getAttribute("command")||"";if(e.startsWith("--"))return e;let t=e.toLowerCase();switch(t){case"show-modal":case"close":case"toggle-popover":case"hide-popover":case"show-popover":return t}return""},set(e){this.setAttribute("command",e)}},invokeAction:{enumerable:!1,configurable:!0,get(){throw Error("invokeAction is deprecated. It has been renamed to command")},set(e){throw Error("invokeAction is deprecated. It has been renamed to command")}},invokeTargetElement:{enumerable:!1,configurable:!0,get(){throw Error("invokeTargetElement is deprecated. It has been renamed to command")},set(e){throw Error("invokeTargetElement is deprecated. It has been renamed to command")}}}),function(e,t){let o=e.prototype.attachShadow;e.prototype.attachShadow=function(e){let n=o.call(this,e);return t(n),n};let n=e.prototype.attachInternals;e.prototype.attachInternals=function(){let e=n.call(this);return e.shadowRoot&&t(e.shadowRoot),e}}(globalThis.HTMLElement||function(){},e=>{u(e),c.observe(e,{attributeFilter:["oncommand"]}),l(e.querySelectorAll("[oncommand]"))}),u(document),Object.defineProperty(window,"CommandEvent",{value:CommandEvent,configurable:!0,writable:!0}),Object.defineProperty(window,"InvokeEvent",{value:InvokeEvent,configurable:!0,writable:!0})}let L=!1;try{L=!1===document.body.matches(":modal")}catch{L=!1}let _="object"==typeof globalThis&&"fromEntries"in Object&&"flatMap"in Array.prototype&&"trimEnd"in String.prototype&&"allSettled"in Promise&&"matchAll"in String.prototype&&"replaceAll"in String.prototype&&"any"in Promise&&"at"in String.prototype&&"at"in Array.prototype&&"hasOwn"in Object&&"abort"in AbortSignal&&"timeout"in AbortSignal&&"function"==typeof queueMicrotask&&"function"==typeof HTMLDialogElement&&L&&"function"==typeof AggregateError&&"function"==typeof BroadcastChannel&&"randomUUID"in crypto&&"replaceChildren"in Element.prototype&&"requestSubmit"in HTMLFormElement.prototype&&!0,F={clipboardItem:n,elementCheckVisibility:r,navigatorClipboard:i,requestIdleCallback:l,withResolvers:a,popover:O,commandAndCommandFor:c};function $(){return _&&Object.values(F).every(e=>e.isSupported())}function q(){for(let e of Object.values(F))e.isSupported()||e.apply()}},36301:(e,t,o)=>{let n;function r(){return`${Math.round(0x7fffffff*Math.random())}.${Math.round(Date.now()/1e3)}`}function i(){try{let e=function(){let e,t=document.cookie.match(/_octo=([^;]+)/g);if(!t)return;let o=[0,0];for(let n of t){let[,t]=n.split("="),[,r,...i]=t.split("."),a=r.split("-").map(Number);a>o&&(o=a,e=i.join("."))}return e}();if(e)return e;let t=r();return!function(e){let t=`GH1.1.${e}`,o=new Date(Date.now()+31536e6).toUTCString(),{domain:n}=document;n.endsWith(".github.com")&&(n="github.com"),document.cookie=`_octo=${t}; expires=${o}; path=/; domain=${n}; secure; samesite=lax`}(t),t}catch(e){return n||(n=r()),n}}o.d(t,{y:()=>i})},5225:(e,t,o)=>{function n(...e){return JSON.stringify(e,(e,t)=>"object"==typeof t?t:String(t))}function r(e,t={}){let{hash:o=n,cache:i=new Map}=t;return function(...t){let n=o.apply(this,t);if(i.has(n))return i.get(n);let r=e.apply(this,t);return r instanceof Promise&&(r=r.catch(e=>{throw i.delete(n),e})),i.set(n,r),r}}o.d(t,{A:()=>r})}}]);
//# sourceMappingURL=vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52-0d1bad63d2be.js.map