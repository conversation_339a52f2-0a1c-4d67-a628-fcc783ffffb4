"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_fzy_js_index_js-node_modules_github_paste-markdown_dist_index_js"],{91385:(e,t,r)=>{r.d(t,{Xq:()=>l,ai:()=>a,fN:()=>o,qA:()=>u});var n=-1/0,a=1/0;function i(e,t,r,a){for(var i=e.length,o=t.length,l=e.toLowerCase(),u=t.toLowerCase(),s=function(e){for(var t=e.length,r=Array(t),n="/",a=0;a<t;a++){var i,o=e[a];"/"===n?r[a]=.9:"-"===n||"_"===n||" "===n?r[a]=.8:"."===n?r[a]=.6:(i=n).toLowerCase()===i&&o.toUpperCase()===o?r[a]=.7:r[a]=0,n=o}return r}(t,s),f=0;f<i;f++){r[f]=Array(o),a[f]=Array(o);for(var c=n,d=f===i-1?-.005:-.01,p=0;p<o;p++)if(l[f]===u[p]){var v=n;f?p&&(v=Math.max(a[f-1][p-1]+s[p],r[f-1][p-1]+1)):v=-.005*p+s[p],r[f][p]=v,a[f][p]=c=Math.max(v,c+d)}else r[f][p]=n,a[f][p]=c+=d}}function o(e,t){var r=e.length,o=t.length;if(!r||!o)return n;if(r===o)return a;if(o>1024)return n;var l=Array(r),u=Array(r);return i(e,t,l,u),u[r-1][o-1]}function l(e,t){var r=e.length,a=t.length,o=Array(r);if(!r||!a)return o;if(r===a){for(var l=0;l<r;l++)o[l]=l;return o}if(a>1024)return o;var u=Array(r),s=Array(r);i(e,t,u,s);for(var f=!1,l=r-1,c=a-1;l>=0;l--)for(;c>=0;c--)if(u[l][c]!==n&&(f||u[l][c]===s[l][c])){f=l&&c&&s[l][c]===u[l-1][c-1]+1,o[l]=c--;break}return o}function u(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var r=e.length,n=0,a=0;n<r;n+=1)if(0===(a=t.indexOf(e[n],a)+1))return!1;return!0}},44358:(e,t,r)=>{function n(e,t){var r,n,a;let i=e.value.slice(0,null!=(r=e.selectionStart)?r:void 0),o=e.value.slice(null!=(n=e.selectionEnd)?n:void 0),l=!0;e.contentEditable="true";try{l=document.execCommand("insertText",!1,t)}catch(e){l=!1}if(e.contentEditable="false",l&&!e.value.slice(0,null!=(a=e.selectionStart)?a:void 0).endsWith(t)&&(l=!1),!l){try{document.execCommand("ms-beginUndoUnit")}catch(e){}e.value=i+t+o;try{document.execCommand("ms-endUndoUnit")}catch(e){}e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!0}))}}r.d(t,{B1:()=>P});let a=new WeakMap;function i(e){let{currentTarget:t}=e,r="KeyV"===e.code&&(e.ctrlKey||e.metaKey)&&e.shiftKey;(r||r&&e.altKey)&&a.set(t,!0)}function o(e){let{currentTarget:t}=e;a.delete(t)}function l(e){var t;return null!=(t=a.get(e))&&t}function u(e){e.addEventListener("paste",s)}function s(e){let t=e.clipboardData,{currentTarget:r}=e;if(l(r)||!t||!t.types.includes("text/html"))return;let a=e.currentTarget;if(!(a instanceof HTMLTextAreaElement)||function(e){let t=e.selectionStart||0;return 0!==t&&"@"===e.value.substring(t-1,t)}(a))return;let i=t.getData("text/plain"),o=t.getData("text/html"),u=o.replace(/\u00A0/g," ").replace(/\uC2A0/g," ");if(!o||!(i=i.trim()))return;let s=new DOMParser().parseFromString(u,"text/html"),c=function(e,t){let r=t.firstChild(),n=e,a=0,i=0;for(;r&&i<1e4;){var o;i++;let e=f(r)?(r.textContent||"").replace(/[\t\n\r ]+/g," "):(null==r?void 0:r.wholeText)||"";if(!(o=e)||(null==o?void 0:o.trim().length)===0){r=t.nextNode();continue}if(!f(r)){a+=e.replace(/[\t\n\r ]+/g," ").trimStart().length,r=t.nextNode();continue}let l=n.indexOf(e,a);if(l>=0){let t=function(e,t){var r,n,a;let i=e.href||"",o="";return function(e){var t;return(null==(t=e.textContent)?void 0:t.slice(0,1))==="@"&&"user"===e.getAttribute("data-hovercard-type")}(e)||function(e){var t;return(null==(t=e.textContent)?void 0:t.slice(0,1))==="@"&&"team"===e.getAttribute("data-hovercard-type")}(e)?t:(r=e).className.indexOf("commit-link")>=0||r.getAttribute("data-hovercard-type")&&"user"!==r.getAttribute("data-hovercard-type")||(n=i,a=t,n="/"===n.slice(-1)?n.slice(0,-1):n,a="/"===a.slice(-1)?a.slice(0,-1):a,n.toLowerCase()===a.toLowerCase())?i:`[${t}](${i})`}(r,e);n=n.slice(0,l)+t+n.slice(l+e.length),a=l+t.length}r=t.nextNode()}return 1e4===i?e:n}(i,s.createTreeWalker(s.body,NodeFilter.SHOW_ALL,e=>e.parentNode&&f(e.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT));c!==i&&(e.stopPropagation(),e.preventDefault(),n(a,c))}function f(e){var t;return(null==(t=e.tagName)?void 0:t.toLowerCase())==="a"&&e.hasAttribute("href")}function c(e){e.addEventListener("dragover",p),e.addEventListener("drop",d),e.addEventListener("paste",v)}function d(e){let t=e.dataTransfer;if(!t||Array.from(t.types).indexOf("Files")>=0||!g(t))return;let r=h(t);if(!r.some(y))return;e.stopPropagation(),e.preventDefault();let a=e.currentTarget;a instanceof HTMLTextAreaElement&&n(a,r.map(m).join(""))}function p(e){let t=e.dataTransfer;t&&(t.dropEffect="link")}function v(e){let{currentTarget:t}=e;if(l(t))return;let r=e.clipboardData;if(!r||!g(r))return;let a=h(r);if(!a.some(y))return;e.stopPropagation(),e.preventDefault();let i=e.currentTarget;i instanceof HTMLTextAreaElement&&n(i,a.map(m).join(""))}function m(e){return y(e)?`
![](${e})
`:e}function g(e){return Array.from(e.types).indexOf("text/uri-list")>=0}function h(e){return(e.getData("text/uri-list")||"").split(`\r
`)}let x=/\.(gif|png|jpe?g)$/i;function y(e){return x.test(e)}let L=new WeakMap;function E(e,t){var r;L.set(e,(null==(r=null==t?void 0:t.defaultPlainTextPaste)?void 0:r.urlLinks)===!0),e.addEventListener("paste",T)}function T(e){var t,r,a;let{currentTarget:i}=e,o=null!=(t=L.get(i))&&t,u=l(i);if(!o&&u||o&&!u)return;let s=e.clipboardData;if(!s||!Array.from(s.types).includes("text/plain"))return;let f=e.currentTarget;if(!(f instanceof HTMLTextAreaElement))return;let c=s.getData("text/plain");if(!c||!A(c)||function(e){let t=e.selectionStart||0;return t>1&&"]("===e.value.substring(t-2,t)}(f))return;let d=f.value.substring(f.selectionStart,f.selectionEnd);if(d.length){A(d.trim())||(e.stopPropagation(),e.preventDefault(),n(f,(r=d,a=c.trim(),`[${r}](${a})`)))}}function A(e){try{let t=new URL(e);return b(t.href).trim()===b(e).trim()}catch(e){return!1}}function b(e){return e.endsWith("/")?e.slice(0,e.length-1):e}function C(e){e.addEventListener("dragover",D),e.addEventListener("drop",w),e.addEventListener("paste",k)}function w(e){let t=e.dataTransfer;if(!t||Array.from(t.types).indexOf("Files")>=0)return;let r=M(t);if(!r)return;e.stopPropagation(),e.preventDefault();let a=e.currentTarget;a instanceof HTMLTextAreaElement&&n(a,r)}function D(e){let t=e.dataTransfer;t&&(t.dropEffect="copy")}function k(e){let{currentTarget:t}=e;if(l(t)||!e.clipboardData)return;let r=M(e.clipboardData);if(!r)return;e.stopPropagation(),e.preventDefault();let a=e.currentTarget;a instanceof HTMLTextAreaElement&&n(a,r)}function _(e){return(e.textContent||"").trim().replace(/\|/g,"\\|").replace(/\n/g," ")||"\xa0"}function M(e){if(-1===Array.from(e.types).indexOf("text/html"))return;let t=e.getData("text/html");if(!/<table/i.test(t))return;let r=t.substring(0,t.indexOf("<table")),n=t.lastIndexOf("</table>");if(!r||!n)return;let a=t.substring(n+8),i=new DOMParser().parseFromString(t,"text/html").querySelector("table");if(!(i=!i||i.closest("[data-paste-markdown-skip]")?null:i))return;let o=function(e){let t=Array.from(e.querySelectorAll("tr")),r=t.shift();if(!r)return"";let n=Array.from(r.querySelectorAll("td, th")).map(_),a=n.map(()=>"--"),i=`${n.join(" | ")}
${a.join(" | ")}
`,o=t.map(e=>Array.from(e.querySelectorAll("td")).map(_).join(" | ")).join(`
`);return`
${i}${o}

`}(i);if(o)return[r,o,a].join("").replace(/<meta.*?>/,"")}function O(e){e.addEventListener("paste",S)}function S(e){let{currentTarget:t}=e;if(l(t))return;let r=e.clipboardData;if(!r||!(Array.from(r.types).indexOf("text/x-gfm")>=0))return;let a=e.currentTarget;if(!(a instanceof HTMLTextAreaElement))return;let i=r.getData("text/x-gfm");i&&(e.stopPropagation(),e.preventDefault(),n(a,i))}function P(e,t){for(let r of(e.addEventListener("keydown",i),[C,c,E,O,u]))r(e,t);return e.addEventListener("paste",o),{unsubscribe:()=>{e.removeEventListener("keydown",i),e.removeEventListener("paste",o),e.removeEventListener("dragover",D),e.removeEventListener("drop",w),e.removeEventListener("paste",k),e.removeEventListener("paste",s),e.removeEventListener("dragover",p),e.removeEventListener("drop",d),e.removeEventListener("paste",v),e.removeEventListener("paste",T),e.removeEventListener("paste",S)}}}}}]);
//# sourceMappingURL=vendors-node_modules_fzy_js_index_js-node_modules_github_paste-markdown_dist_index_js-36853939da9c.js.map