"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_ui-commands_ui-commands_ts"],{55463:(e,t,i)=>{i.d(t,{Fr:()=>a,U0:()=>d,xl:()=>r});var n=i(96679);let s={Android:"Android",iOS:"iOS",macOS:"macOS",Windows:"Windows",Linux:"Linux",Unknown:"Unknown"};function o(){let e=s.Unknown,t=!1;if(n.cg){let i=n.cg.navigator,o="";try{o=i.userAgent}catch{}let a="";try{a=i?.userAgentData?.platform||i.platform}catch{}-1!==["Macintosh","MacIntel","MacPPC","Mac68K","macOS"].indexOf(a)?e=s.macOS:-1!==["iPhone","iPad","iPod"].indexOf(a)?e=s.iOS:-1!==["Win32","Win64","Windows","WinCE"].indexOf(a)?e=s.Windows:/Android/.test(o)?e=s.Android:/Linux/.test(a)&&(e=s.Linux),t=i?.userAgentData?.mobile??(e===s.Android||e===s.iOS)}return{os:e,isAndroid:e===s.Android,isIOS:e===s.iOS,isMacOS:e===s.macOS,isWindows:e===s.Windows,isLinux:e===s.Linux,isDesktop:e===s.macOS||e===s.Windows||e===s.Linux,isMobile:t}}function a(){return o().isMobile}function r(){return o().isDesktop}function d(){return o().isMacOS}},65461:(e,t,i)=>{i.d(t,{JC:()=>n.JC,KK:()=>n.KK,SK:()=>o,Vy:()=>n.Vy,ai:()=>n.ai,oc:()=>n.oc,rd:()=>n.rd});var n=i(50515);let s=/(?:^|,)((?:[^,]|,(?=\+| |$))*(?:,(?=,))?)/g;function o(e){return Array.from(e.matchAll(s)).map(([,e])=>e)}},57909:(e,t,i)=>{i.d(t,{$$:()=>d,GI:()=>a,zw:()=>o});var n=i(55463),s=i(65461);let o=()=>{if("undefined"==typeof document)return!1;let e=document.querySelector("meta[name=keyboard-shortcuts-preference]");return!e||"all"===e.content},a=e=>/Enter|Arrow|Escape|Meta|Control|Mod|Esc|Tab/.test(e)||!(0,n.U0)()&&e.includes("Alt")&&e.includes("Shift"),r=new Set(["button","checkbox","color","file","hidden","image","radio","range","reset","submit"]),d=e=>{let t=(0,s.Vy)(e),i=o()&&!function(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),i=e.getAttribute("type")?.toLowerCase()??"text",n="true"===e.ariaReadOnly||"true"===e.getAttribute("aria-readonly")||null!==e.getAttribute("readonly");return("select"===t||"textarea"===t||"input"===t&&!r.has(i)||e.isContentEditable)&&!n}(e.target);return a(t)||i}},52200:(e,t,i)=>{i.d(t,{J:()=>s,k:()=>CommandEvent});var n=i(45356);let CommandEvent=class CommandEvent{constructor(e){!function(e,t,i){t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i}(this,"commandId",void 0),this.commandId=e}};let s={entries:e=>Object.entries(e).filter(e=>n.dx.is(e[0])&&void 0!==e[1]),keys:e=>Object.keys(e).filter(n.dx.is)}},73566:(e,t,i)=>{i.d(t,{J:()=>d,c:()=>r});var n=i(96540),s=i(52200),o=i(45356);let a=new Map;function r(){let e=new Map;for(let t of new Set(Array.from(a.values()).flat())){let i=o.dx.getServiceId(t);if(!e.has(i)){let t=(0,o.tp)(i);e.set(i,{service:{id:t.id,name:t.name},commands:[]})}let n=(0,o.fL)(t);n&&n.defaultBinding&&e.get(i)?.commands.push({id:t,name:n.name,description:n.description,keybinding:n.defaultBinding})}return Array.from(e.values())}let d=e=>{let t=(0,n.useId)();(0,n.useEffect)(()=>(a.set(t,s.J.keys(e)),()=>{a.delete(t)}),[e,t])}},45356:(e,t,i)=>{i.d(t,{dx:()=>d,fL:()=>l,xJ:()=>u,eY:()=>m,tp:()=>c});var n=i(27851),s=i(65461);let{P:o,$:a}=JSON.parse('{"$":{"commit-diff-view":{"id":"commit-diff-view","name":"Commits diff view","commandIds":["commit-diff-view:open-find","commit-diff-view:create-permalink","commit-diff-view:go-up-commit","commit-diff-view:collapse-expand-comments","commit-diff-view:go-up-commit-arrow","commit-diff-view:go-down-commit","commit-diff-view:go-down-commit-arrow","commit-diff-view:tab-forward","commit-diff-view:tab-backward"]},"copilot-chat":{"id":"copilot-chat","name":"Copilot Chat","commandIds":["copilot-chat:open-assistive","copilot-chat:open-immersive","copilot-chat:continue-in-immersive","copilot-chat:send-message","copilot-chat:send-message-new-conversation","copilot-chat:add-attachment","copilot-chat:task-oriented-select-previous","copilot-chat:task-oriented-select-next","copilot-chat:close-assistive","copilot-chat:deep-codesearch","copilot-chat:edit-last-message"]},"github":{"id":"github","name":"GitHub (site-wide)","commandIds":["github:submit-form","github:select","github:select-multiple","github:cancel","github:go-to-start","github:go-to-end","github:up","github:down","github:focus-next"]},"issue-create":{"id":"issue-create","name":"Issue Create","commandIds":["issue-create:new","issue-create:submit-and-create-more","issue-create:open-fullscreen"]},"issue-viewer":{"id":"issue-viewer","name":"Issue viewer","commandIds":["issue-viewer:edit-parent","issue-viewer:edit-title-submit","issue-viewer:close-edit-title","issue-viewer:mark-blocked-by","issue-viewer:mark-blocking"]},"issues-react":{"id":"issues-react","name":"Issues","commandIds":["issues-react:focus-next-issue","issues-react:focus-previous-issue"]},"item-pickers":{"id":"item-pickers","name":"Item Pickers","commandIds":["item-pickers:open-assignees","item-pickers:open-development","item-pickers:open-labels","item-pickers:open-milestone","item-pickers:open-projects","item-pickers:open-issue-type","item-pickers:open-author","item-pickers:open-fields"]},"keyboard-shortcuts-dialog":{"id":"keyboard-shortcuts-dialog","name":"Keyboard Shortcuts Dialog","commandIds":["keyboard-shortcuts-dialog:show-dialog"]},"list-view-items-issues-prs":{"id":"list-view-items-issues-prs","name":"List View Items: Issues & Pull Requests","commandIds":["list-view-items-issues-prs:open-focused-item","list-view-items-issues-prs:toggle-focused-item-selection"]},"list-views":{"id":"list-views","name":"List views including lists of issues, pull requests, discussions, and notifications.","commandIds":["list-views:open-manage-item-dialog"]},"projects":{"id":"projects","name":"Projects","commandIds":["projects:save-view","projects:save-form"]},"pull-request-files-changed":{"id":"pull-request-files-changed","name":"Pull Requests files changed","commandIds":["pull-request-files-changed:collapse-expand-comments","pull-request-files-changed:submit-review"]},"pull-requests-conversations":{"id":"pull-requests-conversations","name":"Conversations","commandIds":["pull-requests-conversations:submit-comment-and-review"]},"pull-requests-diff-file-tree":{"id":"pull-requests-diff-file-tree","name":"Pull requests - diff file tree","commandIds":["pull-requests-diff-file-tree:focus-file-tree"]},"pull-requests-diff-view":{"id":"pull-requests-diff-view","name":"Pull requests - \'Files changed\' view","commandIds":["pull-requests-diff-view:copy-code","pull-requests-diff-view:copy-anchor-link","pull-requests-diff-view:start-conversation-current","pull-requests-diff-view:jump-to-next-result","pull-requests-diff-view:jump-to-next-result-alternate","pull-requests-diff-view:jump-to-previous-result","pull-requests-diff-view:jump-to-previous-result-alternate","pull-requests-diff-view:open-find","pull-requests-diff-view:close-find"]},"react-sandbox":{"id":"react-sandbox","name":"React Sandbox","commandIds":["react-sandbox:example-command"]},"repository-label":{"id":"repository-label","name":"Repository label","commandIds":["repository-label:save-label-submit","repository-label:cancel-save-label"]},"sub-issues":{"id":"sub-issues","name":"sub-issues","commandIds":["sub-issues:create-sub-issue","sub-issues:add-existing-issue"]},"workspace-editor":{"id":"workspace-editor","name":"Workspace Editor","commandIds":["workspace-editor:escape-editor--mac","workspace-editor:escape-editor","workspace-editor:toggle-file-tree-pane"]}},"P":{"commit-diff-view:collapse-expand-comments":{"name":"Collapse/Expand comments","description":"swaps between having all comments collapsed or expanded","defaultBinding":"i"},"commit-diff-view:create-permalink":{"name":"Create permalink","description":"Hotkey to expand the current url to a full permalink.","defaultBinding":"y"},"commit-diff-view:go-down-commit":{"name":"Go up commit","description":"navigates up one commit","defaultBinding":"j"},"commit-diff-view:go-down-commit-arrow":{"name":"Go up commit","description":"navigates down one commit","defaultBinding":"ArrowDown"},"commit-diff-view:go-up-commit":{"name":"Go up commit","description":"navigates up one commit","defaultBinding":"k"},"commit-diff-view:go-up-commit-arrow":{"name":"Go up commit","description":"navigates up one commit","defaultBinding":"ArrowUp"},"commit-diff-view:open-find":{"name":"Open up find and search on selection","description":"Hotkey to open up the custom find and search on selection.","defaultBinding":"Mod+e"},"commit-diff-view:tab-backward":{"name":"Track when a user tabs backwards through the commit list","description":"navigates through the tab-index items in the commit list backwards","defaultBinding":"Shift+Tab"},"commit-diff-view:tab-forward":{"name":"Track when a user tabs through the commit list","description":"navigates through the tab-index items in the commit list","defaultBinding":"Tab"},"copilot-chat:add-attachment":{"name":"Add attachment","description":"Add an attachment to the current message","defaultBinding":"Mod+Shift+@"},"copilot-chat:close-assistive":{"name":"Close chat","description":"Close the assistive chat","defaultBinding":"Shift+Z"},"copilot-chat:continue-in-immersive":{"name":"Continue conversation in immersive","description":"Continue the current assistive chat conversation in immersive mode","defaultBinding":"Mod+Shift+C"},"copilot-chat:deep-codesearch":{"name":"Deep code search","description":"Perform a deep code search for the current message if applicable"},"copilot-chat:edit-last-message":{"name":"Edit last message","description":"Edit the last-sent message in the conversation","defaultBinding":"ArrowUp"},"copilot-chat:open-assistive":{"name":"Open Copilot Chat (assistive mode)","description":"Open Copilot Chat in the assistive overlay mode","defaultBinding":"Shift+C"},"copilot-chat:open-immersive":{"name":"Open Copilot Chat (immersive mode)","description":"Open Copilot Chat in the full-page immersive mode"},"copilot-chat:send-message":{"name":"Send message","description":"Send the current message","defaultBinding":"Enter"},"copilot-chat:send-message-new-conversation":{"name":"Send message to new conversation","description":"Send the current message to a newly created conversation","defaultBinding":"Mod+Shift+S"},"copilot-chat:task-oriented-select-next":{"name":"Select next task suggestion","description":"Move the task selection to the next suggestion","defaultBinding":"ArrowDown"},"copilot-chat:task-oriented-select-previous":{"name":"Select previous task suggestion","description":"Move the task selection to the previous suggestion","defaultBinding":"ArrowUp"},"github:cancel":{"name":"Cancel","description":"Cancel the current operation","defaultBinding":"Escape"},"github:down":{"name":"Down","description":"Navigate down","defaultBinding":"ArrowDown"},"github:focus-next":{"name":"Focus next item","description":"Navigate to the next focusable item","defaultBinding":"Tab"},"github:go-to-end":{"name":"Go to end","description":"Go to the last item in a list","defaultBinding":"End"},"github:go-to-start":{"name":"Go to start","description":"Go to the first item in a list","defaultBinding":"Home"},"github:select":{"name":"Select item","description":"Select the current item","defaultBinding":"Enter"},"github:select-multiple":{"name":"Select multiple items","description":"Add the current item to a multi-selection (or remove it if already added)","defaultBinding":"Mod+Enter"},"github:submit-form":{"name":"Submit form","description":"Submit the current form","defaultBinding":"Mod+Enter"},"github:up":{"name":"Up","description":"Navigate up","defaultBinding":"ArrowUp"},"issue-create:new":{"name":"Create a new issue","description":"Initiate new issue creation","defaultBinding":"c"},"issue-create:open-fullscreen":{"name":"Open issue creation in fullscreen","description":"Open the issue creation dialog in fullscreen mode","defaultBinding":"Mod+Shift+Enter"},"issue-create:submit-and-create-more":{"name":"Submit and create more","description":"Submit the current issue and create a new one","defaultBinding":"Mod+Alt+Enter"},"issue-viewer:close-edit-title":{"name":"Cancel","description":"Cancel out of editing an issue\'s title","defaultBinding":"Escape"},"issue-viewer:edit-parent":{"name":"Edit parent","description":"Edit parent for current issue","defaultBinding":"Alt+Shift+P"},"issue-viewer:edit-title-submit":{"name":"Save","description":"Submit changes made to an issue\'s title","defaultBinding":"Enter"},"issue-viewer:mark-blocked-by":{"name":"Change blocked by","description":"Change the current issue as blocked by another issue","defaultBinding":"b b"},"issue-viewer:mark-blocking":{"name":"Mark or remove as blocking","description":"Mark or remove the current issue as blocking another issue","defaultBinding":"b x"},"issues-react:focus-next-issue":{"name":"Focus on Next Issue","description":"Focus on the next issue in the list, or the first one if none are focused.","defaultBinding":"j"},"issues-react:focus-previous-issue":{"name":"Focus on Previous Issue","description":"Focus on the previous issue in the list","defaultBinding":"k"},"item-pickers:open-assignees":{"name":"Open assignees panel","description":"Open panel to select assignees","defaultBinding":"a"},"item-pickers:open-author":{"name":"Open author panel","description":"Open panel to select author","defaultBinding":"u"},"item-pickers:open-development":{"name":"Open development panel","description":"Open panel to create or link a pull request","defaultBinding":"d"},"item-pickers:open-fields":{"name":"Open fields panel","description":"Open panel to select custom fields","defaultBinding":"f"},"item-pickers:open-issue-type":{"name":"Open issue type panel","description":"Open panel to select issue type","defaultBinding":"t"},"item-pickers:open-labels":{"name":"Open labels panel","description":"Open panel to select labels","defaultBinding":"l"},"item-pickers:open-milestone":{"name":"Open milestone panel","description":"Open panel to select milestone","defaultBinding":"m"},"item-pickers:open-projects":{"name":"Open projects panel","description":"Open panel to select projects","defaultBinding":"p"},"keyboard-shortcuts-dialog:show-dialog":{"name":"Show Keyboard Shortcuts Dialog","description":"Display the keyboard shortcuts help dialog","defaultBinding":"Shift+?"},"list-view-items-issues-prs:open-focused-item":{"name":"Open Focused Item","description":"Open the currently focused item","defaultBinding":"o"},"list-view-items-issues-prs:toggle-focused-item-selection":{"name":"Toggle Focused Item Selection","description":"Toggle the selection state of the currently focused item for bulk actions","defaultBinding":"x"},"list-views:open-manage-item-dialog":{"name":"Open \'manage item\' dialog","defaultBinding":"Mod+Shift+U","description":"Open a dialog to manage the currently focused item."},"projects:save-form":{"name":"Save","description":"Submits the currently focused form.","defaultBinding":"Mod+Enter"},"projects:save-view":{"name":"Save view","description":"Save any unsaved changes to the current project view.","defaultBinding":"Mod+s"},"pull-request-files-changed:collapse-expand-comments":{"name":"Collapse/Expand comments","description":"swaps between having all comments collapsed or expanded","defaultBinding":"i"},"pull-request-files-changed:submit-review":{"name":"Submit review","description":"submits the current review from the review menu","defaultBinding":"Mod+Enter"},"pull-requests-conversations:submit-comment-and-review":{"name":"Submit comment and review","description":"Submit the new comment and any pending review comments.","defaultBinding":"Mod+Shift+Enter"},"pull-requests-diff-file-tree:focus-file-tree":{"name":"Focus file tree","description":"Move focus to the file tree","defaultBinding":"Mod+F6"},"pull-requests-diff-view:close-find":{"name":"Close Find","description":"Close the find window","defaultBinding":"Escape"},"pull-requests-diff-view:copy-anchor-link":{"name":"Copy link","description":"Copy link to the current line","defaultBinding":"Mod+Alt+y"},"pull-requests-diff-view:copy-code":{"name":"Copy","description":"Copy the code for the current line(s)","defaultBinding":"Mod+c"},"pull-requests-diff-view:jump-to-next-result":{"name":"Jump to the next search result","description":"Jump to the next search result","defaultBinding":"Enter"},"pull-requests-diff-view:jump-to-next-result-alternate":{"name":"Jump to the next search result","description":"Jump to the next search result","defaultBinding":"Mod+g"},"pull-requests-diff-view:jump-to-previous-result":{"name":"Jump to the previous search result","description":"Jump to the previous search result","defaultBinding":"Shift+Enter"},"pull-requests-diff-view:jump-to-previous-result-alternate":{"name":"Jump to the previous search result","description":"Jump to the previous search result","defaultBinding":"Mod+Shift+G"},"pull-requests-diff-view:open-find":{"name":"Open up find","description":"Hotkey to open up the custom find.","defaultBinding":"Mod+f"},"pull-requests-diff-view:start-conversation-current":{"name":"Add comment on line","description":"Add a comment on the current line"},"react-sandbox:example-command":{"name":"React Sandbox Example Command","description":"Do something.","defaultBinding":"Mod+Shift+Enter"},"repository-label:cancel-save-label":{"name":"Cancel","description":"Cancel out of editing a label","defaultBinding":"Escape"},"repository-label:save-label-submit":{"name":"Create label","description":"Submit changes made to a label","defaultBinding":"Mod+Enter"},"sub-issues:add-existing-issue":{"name":"Add existing issue","description":"Add an existing issue as a sub-issue","defaultBinding":"Alt+Shift+A"},"sub-issues:create-sub-issue":{"name":"Create sub-issue","description":"Create a new sub-issue","defaultBinding":"Alt+Shift+C"},"workspace-editor:escape-editor":{"name":"Move focus out of editor","description":"When in editor, move focus to other elements.","defaultBinding":"Mod+m"},"workspace-editor:escape-editor--mac":{"name":"Move focus out of editor","description":"When in editor, move focus to other elements.","defaultBinding":"Alt+Tab"},"workspace-editor:toggle-file-tree-pane":{"name":"Toggle file tree pane","description":"Show or hide the file tree pane.","defaultBinding":"Mod+b"}}}'),r=new Set(Object.keys(o)),d={is:e=>r.has(e),getServiceId:e=>e.split(":")[0]},l=e=>{let t=o[e];return!t.featureFlag||(0,n.G7)(t.featureFlag)?t:void 0},c=e=>a[e],u=e=>{let t=l(e);return t?.defaultBinding?(0,s.rd)(t.defaultBinding):void 0},m=e=>new Map(e.map(e=>[e,u(e)]).filter(e=>void 0!==e[1]))},42080:(e,t,i)=>{i.d(t,{Vr:()=>B,cQ:()=>M,ky:()=>s.k,N5:()=>O,hh:()=>S,ak:()=>b,tL:()=>K});var n,s=i(52200),o=i(45356),a=i(74848),r=i(15385),d=i(96540),l=i(73566),c=i(38007);let u={TYPE:"command.trigger",send(e){(0,c.BI)(u.TYPE,e)}};function m(e,t){u.send({app_name:"ui-commands",command_id:e.commandId,trigger_type:t instanceof KeyboardEvent?"keybinding":"click",target_element_html:t.target instanceof HTMLElement?function(e){let t=e.tagName.toLowerCase(),i=Array.from(e.attributes).map(e=>`${e.name}="${e.value.replaceAll('"','\\"')}"`).join(" ");return`<${t}${i?` ${i}`:""}>`}(t.target):void 0,keybinding:(0,o.xJ)(e.commandId)})}let p=new Map;function f(e,t){let i=(0,d.useMemo)(()=>new Map,[]),n="global"===e?p:i;(0,d.useEffect)(()=>{for(let[e,i]of(0,o.eY)(s.J.keys(t))){let t=n.get(i)?.filter(t=>t!==e)??[];t.length&&console.warn(`The keybinding (${i}) for the "${e}" command conflicts with the keybinding for the already-registered command(s) "${t.join(", ")}". This may result in unpredictable behavior.`),n.set(i,t.concat(e))}return()=>{for(let[e,i]of(0,o.eY)(s.J.keys(t))){let t=function(e,t){let i=!1;return e.filter(e=>e!==t||!!i||(i=!0,!1))}(n.get(i)??[],e);t?.length?n.set(i,t):n.delete(i)}}},[t,n])}var g=i(65461),h=i(57909);function v(e,t,{triggerOnDefaultPrevented:i=!1}={}){let n=(0,d.useMemo)(()=>new g.KK,[]),s=(0,d.useMemo)(()=>{let t=new Map;for(let i of e){let e=(0,o.xJ)(i);e&&t.set(e,i)}return t},[e]),a=(0,d.useRef)(null);return(0,d.useCallback)(e=>{let o="nativeEvent"in e?e.nativeEvent:e;if(!i&&o.defaultPrevented||a.current===o)return;if(a.current=o,!(0,h.$$)(o))return void n.reset();n.registerKeypress(o);let r=s.get(n.sequence)??s.get((0,g.Vy)(o));r&&(t(r,o)??!0)&&(n.reset(),e.preventDefault(),e.stopPropagation(),o.stopImmediatePropagation())},[s,n,t,i])}let w="ui-command-trigger",b=({commands:e})=>{let t=(0,d.useRef)(null),i=(0,d.useCallback)((i,n)=>{if(n instanceof KeyboardEvent){let e=function(){let e=[...document.querySelectorAll('dialog:modal, [role="dialog"][aria-modal="true"]')].filter(e=>e.childNodes.length>0&&function e(t){if(t.clientHeight>0)return!0;for(let i of t.children)if(e(i))return!0;return!1}(e));return e.length?e[e.length-1]:null}();if(e&&!function(e,t){return!!t&&(e.contains(t)??!1)}(e,t.current))return!1}let o=e[i];if(o){let e=new s.k(i);try{o(e)}finally{m(e,n)}}},[e]),n=v(s.J.keys(e),i);return f("global",e),(0,l.J)(e),(0,d.useEffect)(()=>{let e=e=>{let t="detail"in e&&"object"==typeof e.detail?e.detail:void 0;if(!t)return;let n="commandId"in t&&"string"==typeof t.commandId&&o.dx.is(t.commandId)?t.commandId:void 0,s="domEvent"in t&&(t.domEvent instanceof KeyboardEvent||t.domEvent instanceof MouseEvent)?t.domEvent:void 0;n&&s&&i(n,s)};return document.addEventListener("keydown",n),document.addEventListener(w,e),()=>{document.removeEventListener("keydown",n),document.removeEventListener(w,e)}},[n,i,t]),(0,a.jsx)("div",{ref:t,className:"d-none"})};try{b.displayName||(b.displayName="GlobalCommands")}catch{}let y=(0,d.createContext)({triggerCommand(e,t,i=!1){if(i)return!1;document.dispatchEvent(new CustomEvent(w,{detail:{commandId:e,domEvent:t}}))},registerLimitedKeybindingScope:()=>{}}),k=y.Provider,x=()=>(0,d.useContext)(y);var C=i(96339);let S=({commandId:e,...t})=>{let i=(0,o.xJ)(e);return i?(0,a.jsx)(C.U,{keys:i,...t}):null};try{S.displayName||(S.displayName="CommandKeybindingHint")}catch{}let B=(0,d.forwardRef)(({commandId:e,children:t,description:i,leadingVisual:n,trailingVisual:s,...d},l)=>{let c=(0,o.fL)(e),{triggerCommand:u}=x();return c?(0,a.jsxs)(r.l.Item,{...d,onSelect:t=>u(e,t.nativeEvent),ref:l,children:[t??(0,a.jsx)("span",{children:c.name}),i?(0,a.jsx)(r.l.Description,{truncate:!0,children:i}):null,n?(0,a.jsx)(r.l.LeadingVisual,{children:n}):null,null!==s&&(0,a.jsxs)(r.l.TrailingVisual,{children:[(0,a.jsx)("span",{className:"sr-only",children:"("}),s??(0,a.jsx)(S,{commandId:e,format:"condensed"}),(0,a.jsx)("span",{className:"sr-only",children:")"})]})]}):null});B.displayName="ActionList.CommandItem";var E=i(55847);let M=(0,d.forwardRef)(({commandId:e,children:t,trailingVisual:i,showKeybindingHint:n=!1,keybindingHintVariant:s,onClick:r,...d},l)=>{let c=(0,o.fL)(e),{triggerCommand:u}=x();if(!c)return null;let m=s??("primary"===d.variant?"onPrimary":"normal");return(0,a.jsx)(E.Q,{...d,onClick:t=>{r?.(t),t.defaultPrevented||u(e,t.nativeEvent)},trailingVisual:i??n?()=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"sr-only",children:"("}),(0,a.jsx)(S,{commandId:e,format:"condensed",variant:m}),(0,a.jsx)("span",{className:"sr-only",children:")"})]}):void 0,ref:l,children:t??c.name})});M.displayName="CommandButton";try{(n=HintVisual).displayName||(n.displayName="HintVisual")}catch{}var I=i(87330);let O=(0,d.forwardRef)(({commandId:e,"aria-label":t,onClick:i,...n},s)=>{let r=(0,o.fL)(e),{triggerCommand:d}=x();return r?(0,a.jsx)(I.K,{"aria-label":t??r.name,onClick:t=>{i?.(t),t.defaultPrevented||d(e,t.nativeEvent)},ref:s,keybindingHint:(0,o.xJ)(e),...n}):null});O.displayName="CommandIconButton";var j=i(40031),A=i(90851),q=i(64515);let L=new Map,N=(0,d.forwardRef)(({commands:e,...t},i)=>{let n=(0,A.M)(e),o=x(),r=(0,d.useCallback)((e,t,i=!1)=>{let a=n.current[e];if(!a)return o.triggerCommand(e,t,i);{let i=new s.k(e);try{a(i)}finally{m(i,t)}}},[n,o]);f("scoped",e),(0,l.J)(e);let[c,u]=(0,d.useState)(L),p=(0,d.useCallback)((e,t)=>u(i=>{let n=i.get(e);return t.length===n?.length&&t.every((e,t)=>n[t]===e)?i:new Map([...i,[e,t]])}),[]),g=(0,d.useMemo)(()=>{let t=new Set(Array.from(c.values()).flat());return s.J.keys(e).filter(i=>void 0!==e[i]&&!t.has(i))},[e,c]),h=(0,d.useMemo)(()=>({triggerCommand:r,registerLimitedKeybindingScope:p}),[r,p]);return(0,a.jsx)(k,{value:h,children:(0,a.jsx)(P,{ref:i,commandIds:g,...t})})});N.displayName="ScopedCommands";let P=(0,d.forwardRef)(({commandIds:e,as:t,limited:i=!1,triggerOnDefaultPrevented:n,...s},o)=>{let r=x(),l=v(e,(0,d.useCallback)((e,t)=>r.triggerCommand(e,t,i),[r,i]),{triggerOnDefaultPrevented:n}),c=(0,j._)(l),u=(0,d.useRef)(null);(0,q.T)(o,u),(0,d.useEffect)(()=>{let e=u.current,t=c.onKeyDown;if(e)return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)});let m=void 0!==t||void 0!==s.className?void 0:{display:"contents"};return(0,a.jsx)(t??"div",{style:m,...s,...c,ref:u})});P.displayName="KeyboardScope";let J=(0,d.forwardRef)(({commandIds:e,...t},i)=>{let n=x(),s=(0,d.useId)();return(0,d.useEffect)(()=>n.registerLimitedKeybindingScope(s,e),[n,e,s]),(0,d.useEffect)(()=>()=>n.registerLimitedKeybindingScope(s,[]),[n,s]),(0,a.jsx)(P,{limited:!0,ref:i,commandIds:e,...t})});J.displayName="LimitKeybindingScope";let K=Object.assign(N,{LimitKeybindingScope:J});try{K.displayName||(K.displayName="ScopedCommands")}catch{}},40031:(e,t,i)=>{i.d(t,{_:()=>a});var n=i(55463),s=i(96540);let o=new Set(["enter","tab"]),a=e=>{let t=(0,s.useRef)(!1),i=(0,s.useRef)(!1),a=(0,s.useCallback)(e=>{"compositionstart"===e.type&&(t.current=!0,i.current=!1),"compositionend"===e.type&&(t.current=!1,i.current=!0)},[]),r=(0,s.useCallback)(s=>{if(!o.has(s.key.toLowerCase())||!t.current){if((0,n.U0)()&&229===s.keyCode&&i.current){i.current=!1;return}e(s)}},[e]);return(0,s.useMemo)(()=>({onCompositionStart:a,onCompositionEnd:a,onKeyDown:r}),[a,r])}},90851:(e,t,i)=>{i.d(t,{M:()=>o});var n=i(17515),s=i(96540);function o(e){let t=(0,s.useRef)(e);return(0,n.N)(()=>{t.current=e},[e]),t}}}]);
//# sourceMappingURL=ui_packages_ui-commands_ui-commands_ts-dfc71e681f62.js.map