(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_focus-visible_dist_focus-visible_js-node_modules_fzy_js_index_js-node_mo-296806"],{43842:function(){(function(){"use strict";function e(e){var t=!0,n=!1,r=null,o={text:!0,search:!0,url:!0,tel:!0,email:!0,password:!0,number:!0,date:!0,month:!0,week:!0,time:!0,datetime:!0,"datetime-local":!0};function i(e){return!!e&&e!==document&&"HTML"!==e.nodeName&&"BODY"!==e.nodeName&&"classList"in e&&"contains"in e.classList}function u(e){e.classList.contains("focus-visible")||(e.classList.add("focus-visible"),e.setAttribute("data-focus-visible-added",""))}function a(e){t=!1}function s(){document.addEventListener("mousemove",c),document.addEventListener("mousedown",c),document.addEventListener("mouseup",c),document.addEventListener("pointermove",c),document.addEventListener("pointerdown",c),document.addEventListener("pointerup",c),document.addEventListener("touchmove",c),document.addEventListener("touchstart",c),document.addEventListener("touchend",c)}function c(e){e.target.nodeName&&"html"===e.target.nodeName.toLowerCase()||(t=!1,document.removeEventListener("mousemove",c),document.removeEventListener("mousedown",c),document.removeEventListener("mouseup",c),document.removeEventListener("pointermove",c),document.removeEventListener("pointerdown",c),document.removeEventListener("pointerup",c),document.removeEventListener("touchmove",c),document.removeEventListener("touchstart",c),document.removeEventListener("touchend",c))}document.addEventListener("keydown",function(n){n.metaKey||n.altKey||n.ctrlKey||(i(e.activeElement)&&u(e.activeElement),t=!0)},!0),document.addEventListener("mousedown",a,!0),document.addEventListener("pointerdown",a,!0),document.addEventListener("touchstart",a,!0),document.addEventListener("visibilitychange",function(e){"hidden"===document.visibilityState&&(n&&(t=!0),s())},!0),s(),e.addEventListener("focus",function(e){if(i(e.target)){var n,r,a;(t||(r=(n=e.target).type,"INPUT"===(a=n.tagName)&&o[r]&&!n.readOnly||"TEXTAREA"===a&&!n.readOnly||n.isContentEditable||0))&&u(e.target)}},!0),e.addEventListener("blur",function(e){if(i(e.target)&&(e.target.classList.contains("focus-visible")||e.target.hasAttribute("data-focus-visible-added"))){var t;n=!0,window.clearTimeout(r),r=window.setTimeout(function(){n=!1},100),(t=e.target).hasAttribute("data-focus-visible-added")&&(t.classList.remove("focus-visible"),t.removeAttribute("data-focus-visible-added"))}},!0),e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&e.host?e.host.setAttribute("data-js-focus-visible",""):e.nodeType===Node.DOCUMENT_NODE&&(document.documentElement.classList.add("js-focus-visible"),document.documentElement.setAttribute("data-js-focus-visible",""))}if("undefined"!=typeof window&&"undefined"!=typeof document){var t;window.applyFocusVisiblePolyfill=e;try{t=new CustomEvent("focus-visible-polyfill-ready")}catch(e){(t=document.createEvent("CustomEvent")).initCustomEvent("focus-visible-polyfill-ready",!1,!1,{})}window.dispatchEvent(t)}"undefined"!=typeof document&&e(document)})()},91385:(e,t,n)=>{"use strict";n.d(t,{Xq:()=>a,ai:()=>o,fN:()=>u,qA:()=>s});var r=-1/0,o=1/0;function i(e,t,n,o){for(var i=e.length,u=t.length,a=e.toLowerCase(),s=t.toLowerCase(),c=function(e){for(var t=e.length,n=Array(t),r="/",o=0;o<t;o++){var i,u=e[o];"/"===r?n[o]=.9:"-"===r||"_"===r||" "===r?n[o]=.8:"."===r?n[o]=.6:(i=r).toLowerCase()===i&&u.toUpperCase()===u?n[o]=.7:n[o]=0,r=u}return n}(t,c),l=0;l<i;l++){n[l]=Array(u),o[l]=Array(u);for(var d=r,f=l===i-1?-.005:-.01,v=0;v<u;v++)if(a[l]===s[v]){var m=r;l?v&&(m=Math.max(o[l-1][v-1]+c[v],n[l-1][v-1]+1)):m=-.005*v+c[v],n[l][v]=m,o[l][v]=d=Math.max(m,d+f)}else n[l][v]=r,o[l][v]=d+=f}}function u(e,t){var n=e.length,u=t.length;if(!n||!u)return r;if(n===u)return o;if(u>1024)return r;var a=Array(n),s=Array(n);return i(e,t,a,s),s[n-1][u-1]}function a(e,t){var n=e.length,o=t.length,u=Array(n);if(!n||!o)return u;if(n===o){for(var a=0;a<n;a++)u[a]=a;return u}if(o>1024)return u;var s=Array(n),c=Array(n);i(e,t,s,c);for(var l=!1,a=n-1,d=o-1;a>=0;a--)for(;d>=0;d--)if(s[a][d]!==r&&(l||s[a][d]===c[a][d])){l=a&&d&&c[a][d]===s[a-1][d-1]+1,u[a]=d--;break}return u}function s(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var n=e.length,r=0,o=0;r<n;r+=1)if(0===(o=t.indexOf(e[r],o)+1))return!1;return!0}},27104:(e,t,n)=>{"use strict";n.d(t,{KF:()=>d,bL:()=>l,oj:()=>c});var r=n(96540),o=n(40961),i=function(){var e=function(t,n){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(t,n)};return function(t,n){if("function"!=typeof n&&null!==n)throw TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),u="html",a=function(e,t){var n,r,o,i=null!=(n=e.ownerDocument)?n:document,a=null!=(o=null!=(r=i.defaultView)?r:i.parentWindow)?o:window;switch(t){case u:return e instanceof a.HTMLElement;case"svg":return e instanceof a.SVGElement;default:throw Error('Unrecognized element type "'.concat(t,'" for validateElementType.'))}},s=function(e,t){var n,r,o,i,s,c={};switch(e){case u:s=document.createElement(null!=(n=null==t?void 0:t.containerElement)?n:"div");break;case"svg":s=document.createElementNS("http://www.w3.org/2000/svg",null!=(r=null==t?void 0:t.containerElement)?r:"g");break;default:throw Error('Invalid element type "'.concat(e,'" for createPortalNode: must be "html" or "svg".'))}if(t&&"object"==typeof t&&t.attributes)for(var l=0,d=Object.entries(t.attributes);l<d.length;l++){var f=d[l],v=f[0],m=f[1];s.setAttribute(v,m)}var p={element:s,elementType:e,setPortalProps:function(e){c=e},getInitialPortalProps:function(){return c},mount:function(t,n){if(n!==i){if(p.unmount(),t!==o&&!a(t,e))throw Error('Invalid element type for portal: "'.concat(e,'" portalNodes must be used with ').concat(e," elements, but OutPortal is within <").concat(t.tagName,">."));t.replaceChild(p.element,n),o=t,i=n}},unmount:function(e){(!e||e===i)&&o&&i&&(o.replaceChild(i,p.element),o=void 0,i=void 0)}};return p},c=function(e){function t(t){var n=e.call(this,t)||this;return n.addPropsChannel=function(){Object.assign(n.props.node,{setPortalProps:function(e){n.setState({nodeProps:e})}})},n.state={nodeProps:n.props.node.getInitialPortalProps()},n}return i(t,e),t.prototype.componentDidMount=function(){this.addPropsChannel()},t.prototype.componentDidUpdate=function(){this.addPropsChannel()},t.prototype.render=function(){var e=this,t=this.props,n=t.children,i=t.node;return o.createPortal(r.Children.map(n,function(t){return r.isValidElement(t)?r.cloneElement(t,e.state.nodeProps):t}),i.element)},t}(r.PureComponent),l=function(e){function t(t){var n=e.call(this,t)||this;return n.placeholderNode=r.createRef(),n.passPropsThroughPortal(),n}return i(t,e),t.prototype.passPropsThroughPortal=function(){var e=Object.assign({},this.props,{node:void 0});this.props.node.setPortalProps(e)},t.prototype.componentDidMount=function(){var e=this.props.node;this.currentPortalNode=e;var t=this.placeholderNode.current,n=t.parentNode;e.mount(n,t),this.passPropsThroughPortal()},t.prototype.componentDidUpdate=function(){var e=this.props.node;this.currentPortalNode&&e!==this.currentPortalNode&&(this.currentPortalNode.unmount(this.placeholderNode.current),this.currentPortalNode.setPortalProps({}),this.currentPortalNode=e);var t=this.placeholderNode.current,n=t.parentNode;e.mount(n,t),this.passPropsThroughPortal()},t.prototype.componentWillUnmount=function(){var e=this.props.node;e.unmount(this.placeholderNode.current),e.setPortalProps({})},t.prototype.render=function(){var e=this.props.node.element.tagName,t=this.props.node.elementType===u?e.toLowerCase():e;return r.createElement(t,{ref:this.placeholderNode})},t}(r.PureComponent),d=s.bind(null,u);s.bind(null,"svg")},31993:(e,t,n)=>{"use strict";function r(e){return e}n.d(t,{j:()=>r})},241:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=n(41917).A.Symbol},87385:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(241),o=Object.prototype,i=o.hasOwnProperty,u=o.toString,a=r.A?r.A.toStringTag:void 0;let s=function(e){var t=i.call(e,a),n=e[a];try{e[a]=void 0;var r=!0}catch(e){}var o=u.call(e);return r&&(t?e[a]=n:delete e[a]),o};var c=Object.prototype.toString,l=r.A?r.A.toStringTag:void 0;let d=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":l&&l in Object(e)?s(e):c.call(e)}},72136:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r="object"==typeof global&&global&&global.Object===Object&&global},25353:(e,t,n)=>{"use strict";n.d(t,{A:()=>o});var r=/^(?:0|[1-9]\d*)$/;let o=function(e,t){var n=typeof e;return!!(t=null==t?0x1fffffffffffff:t)&&("number"==n||"symbol"!=n&&r.test(e))&&e>-1&&e%1==0&&e<t}},41917:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(72136),o="object"==typeof self&&self&&self.Object===Object&&self;let i=r.A||o||Function("return this")()},66984:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=function(e,t){return e===t||e!=e&&t!=t}},38446:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(89610),o=n(5254);let i=function(e){return null!=e&&(0,o.A)(e.length)&&!(0,r.A)(e)}},89610:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});var r=n(87385),o=n(23149);let i=function(e){if(!(0,o.A)(e))return!1;var t=(0,r.A)(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t}},5254:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=0x1fffffffffffff}},23149:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}},53098:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=function(e){return null!=e&&"object"==typeof e}},35822:(e,t,n)=>{"use strict";n.d(t,{v:()=>m,z:()=>p});var r,o=n(96540);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var u=["bottom","height","left","right","top","width"],a=new Map,s=function e(){var t=[];a.forEach(function(e,n){var r,o,i=n.getBoundingClientRect();r=i,o=e.rect,void 0===r&&(r={}),void 0===o&&(o={}),u.some(function(e){return r[e]!==o[e]})&&(e.rect=i,t.push(e))}),t.forEach(function(e){e.callbacks.forEach(function(t){return t(e.rect)})}),r=window.requestAnimationFrame(e)},c="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;function l(e,t){var n=t.rect;return e.height!==n.height||e.width!==n.width?n:e}var d=function(){return 50},f=function(e){return e},v=function(e,t){return e[t?"offsetWidth":"offsetHeight"]},m=function(e){for(var t=Math.max(e.start-e.overscan,0),n=Math.min(e.end+e.overscan,e.size-1),r=[],o=t;o<=n;o++)r.push(o);return r};function p(e){var t,n=e.size,u=void 0===n?0:n,p=e.estimateSize,b=void 0===p?d:p,g=e.overscan,y=void 0===g?1:g,E=e.paddingStart,w=void 0===E?0:E,A=e.paddingEnd,L=e.parentRef,P=e.horizontal,O=e.scrollToFn,j=e.useObserver,C=e.initialRect,N=e.onScrollElement,T=e.scrollOffsetFn,k=e.keyExtractor,S=void 0===k?f:k,_=e.measureSize,z=void 0===_?v:_,M=e.rangeExtractor,x=void 0===M?m:M,R=P?"width":"height",F=P?"scrollLeft":"scrollTop",D=o.useRef({scrollOffset:0,measurements:[]}),U=o.useState(0),I=U[0],q=U[1];D.current.scrollOffset=I;var K=(j||function(e,t){void 0===t&&(t={width:0,height:0});var n=o.useState(e.current),i=n[0],u=n[1],d=o.useReducer(l,t),f=d[0],v=d[1],m=o.useRef(!1);return c(function(){e.current!==i&&u(e.current)}),c(function(){i&&!m.current&&(m.current=!0,v({rect:i.getBoundingClientRect()}))},[i]),o.useEffect(function(){if(i){var e,t=(e=function(e){v({rect:e})},{observe:function(){var t=0===a.size;a.has(i)?a.get(i).callbacks.push(e):a.set(i,{rect:void 0,hasRectChanged:!1,callbacks:[e]}),t&&s()},unobserve:function(){var t=a.get(i);if(t){var n=t.callbacks.indexOf(e);n>=0&&t.callbacks.splice(n,1),t.callbacks.length||a.delete(i),a.size||cancelAnimationFrame(r)}}});return t.observe(),function(){t.unobserve()}}},[i]),f})(L,C)[R];D.current.outerSize=K;var V=o.useCallback(function(e){L.current&&(L.current[F]=e)},[L,F]),B=O||V;O=o.useCallback(function(e){B(e,V)},[V,B]);var G=o.useState({}),H=G[0],W=G[1],X=o.useCallback(function(){return W({})},[]),Y=o.useRef([]),$=o.useMemo(function(){var e=Y.current.length>0?Math.min.apply(Math,Y.current):0;Y.current=[];for(var t=D.current.measurements.slice(0,e),n=e;n<u;n++){var r=S(n),o=H[r],i=t[n-1]?t[n-1].end:w,a="number"==typeof o?o:b(n),s=i+a;t[n]={index:n,start:i,size:a,end:s,key:r}}return t},[b,H,w,u,S]),J=((null==(t=$[u-1])?void 0:t.end)||w)+(void 0===A?0:A);D.current.measurements=$,D.current.totalSize=J;var Q=N?N.current:L.current,Z=o.useRef(T);Z.current=T,c(function(){if(!Q)return void q(0);var e=function(e){q(Z.current?Z.current(e):Q[F])};return e(),Q.addEventListener("scroll",e,{capture:!1,passive:!0}),function(){Q.removeEventListener("scroll",e)}},[Q,F]);var ee=function(e){for(var t=e.measurements,n=e.outerSize,r=e.scrollOffset,o=t.length-1,i=h(0,o,function(e){return t[e].start},r),u=i;u<o&&t[u].end<r+n;)u++;return{start:i,end:u}}(D.current),et=ee.start,en=ee.end,er=o.useMemo(function(){return x({start:et,end:en,overscan:y,size:$.length})},[et,en,y,$.length,x]),eo=o.useRef(z);eo.current=z;var ei=o.useMemo(function(){for(var e=[],t=0,n=er.length;t<n;t++)!function(t,n){var r=er[t],o=$[r],u=i(i({},o),{},{measureRef:function(e){if(e){var t=eo.current(e,P);if(t!==u.size){var n=D.current.scrollOffset;u.start<n&&V(n+(t-u.size)),Y.current.push(r),W(function(e){var n;return i(i({},e),{},((n={})[u.key]=t,n))})}}}});e.push(u)}(t);return e},[er,V,P,$]),eu=o.useRef(!1);c(function(){eu.current&&W({}),eu.current=!0},[b]);var ea=o.useCallback(function(e,t){var n=(void 0===t?{}:t).align,r=void 0===n?"start":n,o=D.current,i=o.scrollOffset,u=o.outerSize;"auto"===r&&(r=e<=i?"start":e>=i+u?"end":"start"),"start"===r?O(e):"end"===r?O(e-u):"center"===r&&O(e-u/2)},[O]),es=o.useCallback(function(e,t){var n=void 0===t?{}:t,r=n.align,o=void 0===r?"auto":r,a=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)t.indexOf(n=i[r])>=0||(o[n]=e[n]);return o}(n,["align"]),s=D.current,c=s.measurements,l=s.scrollOffset,d=s.outerSize,f=c[Math.max(0,Math.min(e,u-1))];if(f){if("auto"===o)if(f.end>=l+d)o="end";else{if(!(f.start<=l))return;o="start"}ea("center"===o?f.start+f.size/2:"end"===o?f.end:f.start,i({align:o},a))}},[ea,u]);return{virtualItems:ei,totalSize:J,scrollToOffset:ea,scrollToIndex:o.useCallback(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];es.apply(void 0,t),requestAnimationFrame(function(){es.apply(void 0,t)})},[es]),measure:X}}var h=function(e,t,n,r){for(;e<=t;){var o=(e+t)/2|0,i=n(o);if(i<r)e=o+1;else{if(!(i>r))return o;t=o-1}}return e>0?e-1:0}}}]);
//# sourceMappingURL=vendors-node_modules_focus-visible_dist_focus-visible_js-node_modules_fzy_js_index_js-node_mo-296806-b04549288836.js.map