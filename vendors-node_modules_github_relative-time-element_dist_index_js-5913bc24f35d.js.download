"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_relative-time-element_dist_index_js"],{4712:(t,e,i)=>{i.d(e,{ak:()=>_});var s,a,r,n,o,h,l,u,m,d,c,f,g,y=function(t,e,i,s,a){if("m"===s)throw TypeError("Private method is not writable");if("a"===s&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!a:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?a.call(t,i):a?a.value=i:e.set(t,i),i},w=function(t,e,i,s){if("a"===i&&!s)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!s:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(t):s?s.value:e.get(t)};let ListFormatPonyFill=class ListFormatPonyFill{formatToParts(t){let e=[];for(let i of t)e.push({type:"element",value:i}),e.push({type:"literal",value:", "});return e.slice(0,-1)}};let p="undefined"!=typeof Intl&&Intl.ListFormat||ListFormatPonyFill,b=[["years","year"],["months","month"],["weeks","week"],["days","day"],["hours","hour"],["minutes","minute"],["seconds","second"],["milliseconds","millisecond"]],T={minimumIntegerDigits:2};let DurationFormat=class DurationFormat{constructor(t,e={}){s.set(this,void 0);let i=String(e.style||"short");"long"!==i&&"short"!==i&&"narrow"!==i&&"digital"!==i&&(i="short");let a="digital"===i?"numeric":i,r=e.hours||a;a="2-digit"===r?"numeric":r;let n=e.minutes||a;a="2-digit"===n?"numeric":n;let o=e.seconds||a;a="2-digit"===o?"numeric":o;let h=e.milliseconds||a;y(this,s,{locale:t,style:i,years:e.years||"digital"===i?"short":i,yearsDisplay:"always"===e.yearsDisplay?"always":"auto",months:e.months||"digital"===i?"short":i,monthsDisplay:"always"===e.monthsDisplay?"always":"auto",weeks:e.weeks||"digital"===i?"short":i,weeksDisplay:"always"===e.weeksDisplay?"always":"auto",days:e.days||"digital"===i?"short":i,daysDisplay:"always"===e.daysDisplay?"always":"auto",hours:r,hoursDisplay:"always"===e.hoursDisplay||"digital"===i?"always":"auto",minutes:n,minutesDisplay:"always"===e.minutesDisplay||"digital"===i?"always":"auto",seconds:o,secondsDisplay:"always"===e.secondsDisplay||"digital"===i?"always":"auto",milliseconds:h,millisecondsDisplay:"always"===e.millisecondsDisplay?"always":"auto"},"f")}resolvedOptions(){return w(this,s,"f")}formatToParts(t){let e=[],i=w(this,s,"f"),a=i.style,r=i.locale;for(let[s,a]of b){let n=t[s];if("auto"===i[`${s}Display`]&&!n)continue;let o=i[s],h="2-digit"===o?T:"numeric"===o?{}:{style:"unit",unit:a,unitDisplay:o};e.push(new Intl.NumberFormat(r,h).format(n))}return new p(r,{type:"unit",style:"digital"===a?"short":a}).formatToParts(e)}format(t){return this.formatToParts(t).map(t=>t.value).join("")}};s=new WeakMap;let v=/^[-+]?P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)W)?(?:(\d+)D)?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?)?$/,M=["year","month","week","day","hour","minute","second","millisecond"],D=t=>v.test(t);let Duration=class Duration{constructor(t=0,e=0,i=0,s=0,a=0,r=0,n=0,o=0){this.years=t,this.months=e,this.weeks=i,this.days=s,this.hours=a,this.minutes=r,this.seconds=n,this.milliseconds=o,this.years||(this.years=0),this.sign||(this.sign=Math.sign(this.years)),this.months||(this.months=0),this.sign||(this.sign=Math.sign(this.months)),this.weeks||(this.weeks=0),this.sign||(this.sign=Math.sign(this.weeks)),this.days||(this.days=0),this.sign||(this.sign=Math.sign(this.days)),this.hours||(this.hours=0),this.sign||(this.sign=Math.sign(this.hours)),this.minutes||(this.minutes=0),this.sign||(this.sign=Math.sign(this.minutes)),this.seconds||(this.seconds=0),this.sign||(this.sign=Math.sign(this.seconds)),this.milliseconds||(this.milliseconds=0),this.sign||(this.sign=Math.sign(this.milliseconds)),this.blank=0===this.sign}abs(){return new Duration(Math.abs(this.years),Math.abs(this.months),Math.abs(this.weeks),Math.abs(this.days),Math.abs(this.hours),Math.abs(this.minutes),Math.abs(this.seconds),Math.abs(this.milliseconds))}static from(t){var e;if("string"==typeof t){let i=String(t).trim(),s=i.startsWith("-")?-1:1,a=null==(e=i.match(v))?void 0:e.slice(1).map(t=>(Number(t)||0)*s);return a?new Duration(...a):new Duration}if("object"==typeof t){let{years:e,months:i,weeks:s,days:a,hours:r,minutes:n,seconds:o,milliseconds:h}=t;return new Duration(e,i,s,a,r,n,o,h)}throw RangeError("invalid duration")}static compare(t,e){let i=Date.now(),s=Math.abs(C(i,Duration.from(t)).getTime()-i),a=Math.abs(C(i,Duration.from(e)).getTime()-i);return s>a?-1:+(s<a)}toLocaleString(t,e){return new DurationFormat(t,e).format(this)}};function C(t,e){let i=new Date(t);return e.sign<0?(i.setUTCSeconds(i.getUTCSeconds()+e.seconds),i.setUTCMinutes(i.getUTCMinutes()+e.minutes),i.setUTCHours(i.getUTCHours()+e.hours),i.setUTCDate(i.getUTCDate()+7*e.weeks+e.days),i.setUTCMonth(i.getUTCMonth()+e.months),i.setUTCFullYear(i.getUTCFullYear()+e.years)):(i.setUTCFullYear(i.getUTCFullYear()+e.years),i.setUTCMonth(i.getUTCMonth()+e.months),i.setUTCDate(i.getUTCDate()+7*e.weeks+e.days),i.setUTCHours(i.getUTCHours()+e.hours),i.setUTCMinutes(i.getUTCMinutes()+e.minutes),i.setUTCSeconds(i.getUTCSeconds()+e.seconds)),i}function A(t,{relativeTo:e=Date.now()}={}){if(e=new Date(e),t.blank)return t;let i=t.sign,s=Math.abs(t.years),a=Math.abs(t.months),r=Math.abs(t.weeks),n=Math.abs(t.days),o=Math.abs(t.hours),h=Math.abs(t.minutes),l=Math.abs(t.seconds),u=Math.abs(t.milliseconds);u>=900&&(l+=Math.round(u/1e3)),(l||h||o||n||r||a||s)&&(u=0),l>=55&&(h+=Math.round(l/60)),(h||o||n||r||a||s)&&(l=0),h>=55&&(o+=Math.round(h/60)),(o||n||r||a||s)&&(h=0),n&&o>=12&&(n+=Math.round(o/24)),!n&&o>=21&&(n+=Math.round(o/24)),(n||r||a||s)&&(o=0);let m=e.getFullYear(),d=e.getMonth(),c=e.getDate();if(n>=27||s+a+n){let t=new Date(e);t.setDate(1),t.setMonth(d+a*i+1),t.setDate(0);let o=Math.max(0,c-t.getDate()),h=new Date(e);h.setFullYear(m+s*i),h.setDate(c-o),h.setMonth(d+a*i),h.setDate(c-o+n*i);let l=h.getFullYear()-e.getFullYear(),u=h.getMonth()-e.getMonth(),f=Math.abs(Math.round((Number(h)-Number(e))/864e5))+o,g=Math.abs(12*l+u);f<27?(n>=6?(r+=Math.round(n/7),n=0):n=f,a=s=0):g<=11?(a=g,s=0):(a=0,s=l*i),(a||s)&&(n=0)}return s&&(a=0),r>=4&&(a+=Math.round(r/4)),(a||s)&&(r=0),n&&r&&!a&&!s&&(r+=Math.round(n/7),n=0),new Duration(s*i,a*i,r*i,n*i,o*i,h*i,l*i,u*i)}var k=function(t,e,i,s){if("a"===i&&!s)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!s:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?s:"a"===i?s.call(t):s?s.value:e.get(t)},U=function(t,e,i,s,a){if("m"===s)throw TypeError("Private method is not writable");if("a"===s&&!a)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!a:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===s?a.call(t,i):a?a.value=i:e.set(t,i),i};let E=globalThis.HTMLElement||null,S=new Duration,F=new Duration(0,0,0,0,0,1);let RelativeTimeUpdatedEvent=class RelativeTimeUpdatedEvent extends Event{constructor(t,e,i,s){super("relative-time-updated",{bubbles:!0,composed:!0}),this.oldText=t,this.newText=e,this.oldTitle=i,this.newTitle=s}};function x(t){if(!t.date)return 1/0;if("duration"===t.format||"elapsed"===t.format){let e=t.precision;if("second"===e)return 1e3;if("minute"===e)return 6e4}let e=Math.abs(Date.now()-t.date.getTime());return e<6e4?1e3:e<36e5?6e4:36e5}let P=new class{constructor(){this.elements=new Set,this.time=1/0,this.timer=-1}observe(t){if(this.elements.has(t))return;this.elements.add(t);let e=t.date;if(e&&e.getTime()){let e=x(t),i=Date.now()+e;i<this.time&&(clearTimeout(this.timer),this.timer=setTimeout(()=>this.update(),e),this.time=i)}}unobserve(t){this.elements.has(t)&&this.elements.delete(t)}update(){if(clearTimeout(this.timer),!this.elements.size)return;let t=1/0;for(let e of this.elements)t=Math.min(t,x(e)),e.update();this.time=Math.min(36e5,t),this.timer=setTimeout(()=>this.update(),this.time),this.time+=Date.now()}};let relative_time_element_RelativeTimeElement=class relative_time_element_RelativeTimeElement extends E{constructor(){super(...arguments),a.add(this),r.set(this,!1),n.set(this,!1),h.set(this,this.shadowRoot?this.shadowRoot:this.attachShadow?this.attachShadow({mode:"open"}):this),g.set(this,null)}static define(t="relative-time",e=customElements){return e.define(t,this),this}static get observedAttributes(){return["second","minute","hour","weekday","day","month","year","time-zone-name","prefix","threshold","tense","precision","format","format-style","no-title","datetime","lang","title","aria-hidden"]}get onRelativeTimeUpdated(){return k(this,g,"f")}set onRelativeTimeUpdated(t){k(this,g,"f")&&this.removeEventListener("relative-time-updated",k(this,g,"f")),U(this,g,"object"==typeof t||"function"==typeof t?t:null,"f"),"function"==typeof t&&this.addEventListener("relative-time-updated",t)}get second(){let t=this.getAttribute("second");if("numeric"===t||"2-digit"===t)return t}set second(t){this.setAttribute("second",t||"")}get minute(){let t=this.getAttribute("minute");if("numeric"===t||"2-digit"===t)return t}set minute(t){this.setAttribute("minute",t||"")}get hour(){let t=this.getAttribute("hour");if("numeric"===t||"2-digit"===t)return t}set hour(t){this.setAttribute("hour",t||"")}get weekday(){let t=this.getAttribute("weekday");return"long"===t||"short"===t||"narrow"===t?t:"datetime"===this.format&&""!==t?this.formatStyle:void 0}set weekday(t){this.setAttribute("weekday",t||"")}get day(){var t;let e=null!=(t=this.getAttribute("day"))?t:"numeric";if("numeric"===e||"2-digit"===e)return e}set day(t){this.setAttribute("day",t||"")}get month(){let t=this.format,e=this.getAttribute("month");if(""!==e&&(null!=e||(e="datetime"===t?this.formatStyle:"short"),"numeric"===e||"2-digit"===e||"short"===e||"long"===e||"narrow"===e))return e}set month(t){this.setAttribute("month",t||"")}get year(){var t;let e=this.getAttribute("year");return"numeric"===e||"2-digit"===e?e:this.hasAttribute("year")||new Date().getUTCFullYear()===(null==(t=this.date)?void 0:t.getUTCFullYear())?void 0:"numeric"}set year(t){this.setAttribute("year",t||"")}get timeZoneName(){let t=this.getAttribute("time-zone-name");if("long"===t||"short"===t||"shortOffset"===t||"longOffset"===t||"shortGeneric"===t||"longGeneric"===t)return t}set timeZoneName(t){this.setAttribute("time-zone-name",t||"")}get prefix(){var t;return null!=(t=this.getAttribute("prefix"))?t:"datetime"===this.format?"":"on"}set prefix(t){this.setAttribute("prefix",t)}get threshold(){let t=this.getAttribute("threshold");return t&&D(t)?t:"P30D"}set threshold(t){this.setAttribute("threshold",t)}get tense(){let t=this.getAttribute("tense");return"past"===t?"past":"future"===t?"future":"auto"}set tense(t){this.setAttribute("tense",t)}get precision(){let t=this.getAttribute("precision");return M.includes(t)?t:"micro"===this.format?"minute":"second"}set precision(t){this.setAttribute("precision",t)}get format(){let t=this.getAttribute("format");return"datetime"===t?"datetime":"relative"===t?"relative":"duration"===t?"duration":"micro"===t?"micro":"elapsed"===t?"elapsed":"auto"}set format(t){this.setAttribute("format",t)}get formatStyle(){let t=this.getAttribute("format-style");if("long"===t)return"long";if("short"===t)return"short";if("narrow"===t)return"narrow";let e=this.format;return"elapsed"===e||"micro"===e?"narrow":"datetime"===e?"short":"long"}set formatStyle(t){this.setAttribute("format-style",t)}get noTitle(){return this.hasAttribute("no-title")}set noTitle(t){this.toggleAttribute("no-title",t)}get datetime(){return this.getAttribute("datetime")||""}set datetime(t){this.setAttribute("datetime",t)}get date(){let t=Date.parse(this.datetime);return Number.isNaN(t)?null:new Date(t)}set date(t){this.datetime=(null==t?void 0:t.toISOString())||""}connectedCallback(){this.update()}disconnectedCallback(){P.unobserve(this)}attributeChangedCallback(t,e,i){e!==i&&("title"===t&&U(this,r,null!==i&&(this.date&&k(this,a,"m",l).call(this,this.date))!==i,"f"),k(this,n,"f")||"title"===t&&k(this,r,"f")||U(this,n,(async()=>{await Promise.resolve(),this.update(),U(this,n,!1,"f")})(),"f"))}update(){let t=k(this,h,"f").textContent||this.textContent||"",e=this.getAttribute("title")||"",i=e,s=this.date;if("undefined"==typeof Intl||!Intl.DateTimeFormat||!s){k(this,h,"f").textContent=t;return}let n=Date.now();!k(this,r,"f")&&(i=k(this,a,"m",l).call(this,s)||"")&&!this.noTitle&&this.setAttribute("title",i);let o=function(t,e="second",i=Date.now()){let s=t.getTime()-i;if(0===s)return new Duration;let a=Math.sign(s),r=Math.abs(s),n=Math.floor(r/1e3),o=Math.floor(n/60),h=Math.floor(o/60),l=Math.floor(h/24),u=Math.floor(l/30),m=Math.floor(u/12),d=M.indexOf(e)||M.length;return new Duration(d>=0?m*a:0,d>=1?(u-12*m)*a:0,0,d>=3?(l-30*u)*a:0,d>=4?(h-24*l)*a:0,d>=5?(o-60*h)*a:0,d>=6?(n-60*o)*a:0,d>=7?(r-1e3*n)*a:0)}(s,this.precision,n),g=k(this,a,"m",u).call(this,o),y=t;(y="duration"===g?k(this,a,"m",m).call(this,o):"relative"===g?k(this,a,"m",d).call(this,o):k(this,a,"m",c).call(this,s))?k(this,a,"m",f).call(this,y):this.shadowRoot===k(this,h,"f")&&this.textContent&&k(this,a,"m",f).call(this,this.textContent),(y!==t||i!==e)&&this.dispatchEvent(new RelativeTimeUpdatedEvent(t,y,e,i)),"relative"===g||"duration"===g?P.observe(this):P.unobserve(this)}};r=new WeakMap,n=new WeakMap,h=new WeakMap,g=new WeakMap,a=new WeakSet,o=function(){var t;let e=(null==(t=this.closest("[lang]"))?void 0:t.getAttribute("lang"))||this.ownerDocument.documentElement.getAttribute("lang");try{return new Intl.Locale(null!=e?e:"").toString()}catch(t){return"default"}},l=function(t){return new Intl.DateTimeFormat(k(this,a,"a",o),{day:"numeric",month:"short",year:"numeric",hour:"numeric",minute:"2-digit",timeZoneName:"short"}).format(t)},u=function(t){let e=this.format;if("datetime"===e)return"datetime";if("duration"===e||"elapsed"===e||"micro"===e)return"duration";if(("auto"===e||"relative"===e)&&"undefined"!=typeof Intl&&Intl.RelativeTimeFormat){let e=this.tense;if("past"===e||"future"===e||1===Duration.compare(t,this.threshold))return"relative"}return"datetime"},m=function(t){let e=k(this,a,"a",o),i=this.format,s=this.formatStyle,r=this.tense,n=S;"micro"===i?(t=A(t),n=F,("past"===this.tense&&-1!==t.sign||"future"===this.tense&&1!==t.sign)&&(t=F)):("past"===r&&-1!==t.sign||"future"===r&&1!==t.sign)&&(t=n);let h=`${this.precision}sDisplay`;return t.blank?n.toLocaleString(e,{style:s,[h]:"always"}):t.abs().toLocaleString(e,{style:s})},d=function(t){let e=new Intl.RelativeTimeFormat(k(this,a,"a",o),{numeric:"auto",style:this.formatStyle}),i=this.tense;"future"===i&&1!==t.sign&&(t=S),"past"===i&&-1!==t.sign&&(t=S);let[s,r]=function(t,e){let i=A(t,void 0);if(i.blank)return[0,"second"];for(let t of M){if("millisecond"===t)continue;let e=i[`${t}s`];if(e)return[e,t]}return[0,"second"]}(t);return"second"===r&&s<10?e.format(0,"millisecond"===this.precision?"second":this.precision):e.format(s,r)},c=function(t){let e=new Intl.DateTimeFormat(k(this,a,"a",o),{second:this.second,minute:this.minute,hour:this.hour,weekday:this.weekday,day:this.day,month:this.month,year:this.year,timeZoneName:this.timeZoneName});return`${this.prefix} ${e.format(t)}`.trim()},f=function(t){if(this.hasAttribute("aria-hidden")&&"true"===this.getAttribute("aria-hidden")){let e=document.createElement("span");e.setAttribute("aria-hidden","true"),e.textContent=t,k(this,h,"f").replaceChildren(e)}else k(this,h,"f").textContent=t};let R="undefined"!=typeof globalThis?globalThis:window;try{R.RelativeTimeElement=relative_time_element_RelativeTimeElement.define()}catch(t){if(!(R.DOMException&&t instanceof DOMException&&"NotSupportedError"===t.name)&&!(t instanceof ReferenceError))throw t}let _=relative_time_element_RelativeTimeElement}}]);
//# sourceMappingURL=vendors-node_modules_github_relative-time-element_dist_index_js-4032c4e10b05.js.map