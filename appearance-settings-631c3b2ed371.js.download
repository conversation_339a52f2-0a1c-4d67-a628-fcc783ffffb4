"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["appearance-settings"],{65291:(e,t,o)=>{var a,n=o(52497),i=o(74848),r=o(21728),s=o(8367),d=o(37573),c=o(2724),l=o(96540),_=o(59760),u=o(96679);function m(e,t){let o=u.XC?.documentElement.getAttribute(e);o&&("enabled"!==t||o.endsWith("_high_contrast")?"disabled"===t&&o.endsWith("_high_contrast")&&u.XC?.documentElement.setAttribute(e,o.replace(/_high_contrast$/,"")):u.XC?.documentElement.setAttribute(e,`${o}_high_contrast`))}function h(e){let t=(0,r.c)(2);if(e.reactPartialAnchor){let o;return t[0]!==e?(o=(0,i.jsx)(p,{...e,reactPartialAnchor:e.reactPartialAnchor}),t[0]=e,t[1]=o):o=t[1],o}return null}function p(e){let t,o,a,n=(0,r.c)(9),{ref:u,open:h,setOpen:p}=(0,d.Mm)(e.reactPartialAnchor),g=(0,s.Ri)("increase_contrast_light")?.value??"disabled",[b,f]=(0,l.useState)(g),k=(0,s.Ri)("increase_contrast_dark")?.value??"disabled",[y,v]=(0,l.useState)(k);n[0]===Symbol.for("react.memo_cache_sentinel")?(t=e=>{void 0!==e.light&&(f(e.light),(0,s.TV)("increase_contrast_light",e.light),m("data-light-theme",e.light)),void 0!==e.dark&&(v(e.dark),(0,s.TV)("increase_contrast_dark",e.dark),m("data-dark-theme",e.dark))},n[0]=t):t=n[0];let $=t;n[1]!==y||n[2]!==b?(o=function(){return(0,i.jsx)(c.l.Body,{className:"px-0 py-1",children:(0,i.jsx)(_.P,{onChange:$,lightModeValue:b,darkModeValue:y,border:!1})})},n[1]=y,n[2]=b,n[3]=o):o=n[3];let x=o;return n[4]!==x||n[5]!==u||n[6]!==h||n[7]!==p?(a=h?(0,i.jsx)(c.l,{title:"Appearance settings",onClose:()=>p(!1),returnFocusRef:u,width:"large",renderBody:x}):null,n[4]=x,n[5]=u,n[6]=h,n[7]=p,n[8]=a):a=n[8],a}try{h.displayName||(h.displayName="AppearanceSettings")}catch{}try{p.displayName||(p.displayName="ExternallyAnchoredAppearanceSettings")}catch{}try{(a=CustomBody).displayName||(a.displayName="CustomBody")}catch{}(0,n.k)("appearance-settings",{Component:h})},8367:(e,t,o)=>{function a(e){return n(e)[0]}function n(e){let t=[];for(let o of function(){try{return document.cookie.split(";")}catch{return[]}}()){let[a,n]=o.trim().split("=");e===a&&void 0!==n&&t.push({key:a,value:n})}return t}function i(e,t,o=null,a=!1,n="lax"){let r=document.domain;if(null==r)throw Error("Unable to get document domain");r.endsWith(".github.com")&&(r="github.com");let s="https:"===location.protocol?"; secure":"",d=o?`; expires=${o}`:"";!1===a&&(r=`.${r}`);try{document.cookie=`${e}=${t}; path=/; domain=${r}${d}${s}; samesite=${n}`}catch{}}function r(e,t=!1){let o=document.domain;if(null==o)throw Error("Unable to get document domain");o.endsWith(".github.com")&&(o="github.com");let a=new Date(Date.now()-1).toUTCString(),n="https:"===location.protocol?"; secure":"",i=`; expires=${a}`;!1===t&&(o=`.${o}`);try{document.cookie=`${e}=''; path=/; domain=${o}${i}${n}`}catch{}}o.d(t,{OR:()=>n,Ri:()=>a,TV:()=>i,Yj:()=>r})}},e=>{var t=t=>e(e.s=t);e.O(0,["primer-react","react-core","react-lib","octicons-react","vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52","vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483","vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6","ui_packages_failbot_failbot_ts","ui_packages_document-metadata_document-metadata_ts-ui_packages_promise-with-resolvers-polyfil-1e7a2a"],()=>t(65291)),e.O()}]);
//# sourceMappingURL=appearance-settings-e8c81d52829e.js.map