"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6"],{57427:(e,t)=>{Object.prototype.toString,Object.prototype.hasOwnProperty},46493:(e,t,i)=>{i.d(t,{Cj:()=>Q,iP:()=>B});let n={Less:"less",Equal:"equal",Greater:"greater"};var s,r,l,o,a,h,c,d,u,p,m,f,v,w,g,y,E,b=(e,t,i)=>{if(!t.has(e))throw TypeError("Cannot "+i)},C=(e,t,i)=>(b(e,t,"read from private field"),i?i.call(e):t.get(e)),k=(e,t,i)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,i)},T=(e,t,i,n)=>(b(e,t,"write to private field"),n?n.call(e,i):t.set(e,i),i),x=(e,t,i)=>(b(e,t,"access private method"),i);let MinHeap=class MinHeap{constructor({compareFn:e}){k(this,l),k(this,a),k(this,s,void 0),k(this,r,void 0),T(this,s,e),T(this,r,[])}insert(e){C(this,r).push(e),x(this,a,h).call(this)}pop(){let e=C(this,r)[0];return C(this,r)[C(this,r).length-1]&&(C(this,r)[0]=C(this,r)[C(this,r).length-1],C(this,r).pop()),x(this,l,o).call(this),e}peek(){return C(this,r)[0]}delete(e){let t=C(this,r).indexOf(e);-1!==t&&(M(C(this,r),t,C(this,r).length-1),C(this,r).pop(),x(this,l,o).call(this))}clear(){T(this,r,[])}get size(){return C(this,r).length}};function W(e){return Math.floor((e-1)/2)}function M(e,t,i){let n=e[t];e[t]=e[i],e[i]=n}s=new WeakMap,r=new WeakMap,l=new WeakSet,o=function(){let e=0;for(;2*e+1<C(this,r).length;){var t,i;let l=2*e+1;if(2*e+2<C(this,r).length&&C(this,s).call(this,(t=C(this,r),t[2*e+2]),(i=C(this,r),i[2*e+1]))===n.Less&&(l=2*e+2),C(this,s).call(this,C(this,r)[e],C(this,r)[l])===n.Less)break;M(C(this,r),e,l),e=l}},a=new WeakSet,h=function(){var e;let t=C(this,r).length-1;for(;t>0&&C(this,s).call(this,C(this,r)[t],(e=C(this,r),e[W(t)]))===n.Less;)M(C(this,r),t,W(t)),t=W(t)};var P=Object.defineProperty,S=(e,t,i)=>t in e?P(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,L=(e,t,i)=>(S(e,"symbol"!=typeof t?t+"":t,i),i),_=(e,t,i)=>{if(!t.has(e))throw TypeError("Cannot "+i)},j=(e,t,i)=>(_(e,t,"read from private field"),i?i.call(e):t.get(e)),R=(e,t,i)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,i)},q=(e,t,i,n)=>(_(e,t,"write to private field"),n?n.call(e,i):t.set(e,i),i);let Deferred=class Deferred{constructor(){L(this,c,"Deferred"),R(this,d,void 0),R(this,u,void 0),R(this,p,void 0),q(this,d,new Promise((e,t)=>{q(this,u,e),q(this,p,t)}))}then(e,t){return Promise.prototype.then.apply(j(this,d),[e,t])}catch(e){return Promise.prototype.catch.apply(j(this,d),[e])}finally(e){return Promise.prototype.finally.apply(j(this,d),[e])}resolve(e){j(this,u).call(this,e)}reject(e){j(this,p).call(this,e)}getPromise(){return j(this,d)}};c=Symbol.toStringTag,d=new WeakMap,u=new WeakMap,p=new WeakMap;var D=(e,t,i)=>{if(!t.has(e))throw TypeError("Cannot "+i)},H=(e,t,i)=>(D(e,t,"read from private field"),i?i.call(e):t.get(e)),O=(e,t,i)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,i)},A=(e,t,i,n)=>(D(e,t,"write to private field"),n?n.call(e,i):t.set(e,i),i),F=(e,t,i)=>(D(e,t,"access private method"),i);let LiveRegionElement=class LiveRegionElement extends HTMLElement{constructor(){if(super(),O(this,w),O(this,y),O(this,m,void 0),O(this,f,void 0),O(this,v,void 0),!this.shadowRoot){let e=(G||((G=document.createElement("template")).innerHTML=I),G);this.attachShadow({mode:"open"}).appendChild(e.content.cloneNode(!0))}A(this,m,!1),A(this,v,null),A(this,f,new MinHeap({compareFn:N}))}get delay(){let e=this.getAttribute("delay");return e?parseInt(e,10):150}set delay(e){this.setAttribute("delay",`${e}`)}announce(e,t={}){let{delayMs:i,politeness:n="polite"}=t,s=Date.now(),r=new Deferred,l={deferred:r,politeness:n,contents:e,scheduled:void 0!==i?s+i:s};return H(this,f).insert(l),F(this,w,g).call(this),{...r.getPromise(),cancel:()=>{H(this,f).delete(l),r.resolve()}}}announceFromElement(e,t){var i;let n,s=(n="",(i=e).hasAttribute("aria-label")?n=i.getAttribute("aria-label"):i.innerText?n=i.innerText:i.textContent&&(n=i.textContent),n?n.trim():"");return""!==s?this.announce(s,t):{...Promise.resolve(),cancel:$}}getMessage(e="polite"){let t=this.shadowRoot?.getElementById(e);if(!t)throw Error("Unable to find container for message");return t.textContent}clear(){null!==H(this,v)&&(clearTimeout(H(this,v)),A(this,v,null)),H(this,f).clear()}};m=new WeakMap,f=new WeakMap,v=new WeakMap,w=new WeakSet,g=function(){if(H(this,m))return;let e=H(this,f).peek();if(!e)return;null!==H(this,v)&&(clearTimeout(H(this,v)),A(this,v,null));let t=Date.now();if(e.scheduled<=t){(e=H(this,f).pop())&&F(this,y,E).call(this,e),F(this,w,g).call(this);return}let i=e.scheduled-t;A(this,v,window.setTimeout(()=>{A(this,v,null),F(this,w,g).call(this)},i))},y=new WeakSet,E=function(e){A(this,m,!0);let{contents:t,deferred:i,politeness:n}=e,s=this.shadowRoot?.getElementById(n);if(!s)throw A(this,m,!1),Error(`Unable to find container for message. Expected a container with id="${n}"`);s.textContent===t?s.textContent=`${t}\xa0`:s.textContent=t,null!==H(this,v)&&clearTimeout(H(this,v)),i.resolve(),this.delay>0?A(this,v,window.setTimeout(()=>{A(this,v,null),A(this,m,!1),F(this,w,g).call(this)},this.delay)):(A(this,v,null),A(this,m,!1),F(this,w,g).call(this))};let G=null,I=`
<style>
:host {
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
</style>
<div id="polite" aria-live="polite" aria-atomic="true"></div>
<div id="assertive" aria-live="assertive" aria-atomic="true"></div>
`;function N(e,t){return e.politeness===t.politeness?e.scheduled===t.scheduled?n.Equal:e.scheduled<t.scheduled?n.Less:n.Greater:"assertive"===e.politeness&&"assertive"!==t.politeness?n.Less:"assertive"!==e.politeness&&"assertive"===t.politeness?n.Greater:n.Equal}function $(){}function B(e,t={}){let i=U(t.from);if(!i){i=document.createElement("live-region"),t.appendTo?t.appendTo.appendChild(i):z(t.from).appendChild(i);let n=!1,s=()=>{n=!0};return{...K(J).then(()=>{if(!n){let n=i.announce(e,t);return s=n.cancel,n}}),cancel:()=>{s()}}}return i.announce(e,t)}function Q(e,t={}){let i=U(t.from);if(!i){i=document.createElement("live-region"),t.appendTo?t.appendTo.appendChild(i):z(t.from).appendChild(i);let n=!1,s=()=>{n=!0};return{...K(J).then(()=>{if(!n){let n=i.announceFromElement(e,t);return s=n.cancel,n}}),cancel:()=>{s()}}}return i.announceFromElement(e,t)}function U(e){let t=null;return null!==(t=e?function(e){let t=e.closest("dialog"),i=e;for(;(i=i.parentElement)&&(!t||t.contains(i));)for(let e of i.childNodes)if(e instanceof LiveRegionElement)return e;return null}(e):null)||null!==(t=z(e).querySelector("live-region"))?t:null}function z(e){let t=document.body;if(e){let i=e.closest("dialog");i&&(t=i)}return t}customElements.get("live-region")||customElements.define("live-region",LiveRegionElement);let J=150;function K(e){return new Promise(t=>{setTimeout(t,e)})}},97665:(e,t,i)=>{i.d(t,{Ht:()=>o,jE:()=>l,v4:()=>r});var n=i(96540),s=i(74848),r=n.createContext(void 0),l=e=>{let t=n.useContext(r);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},o=({client:e,children:t})=>(n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,s.jsx)(r.Provider,{value:e,children:t}))}}]);
//# sourceMappingURL=vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6-144960f74bcb.js.map