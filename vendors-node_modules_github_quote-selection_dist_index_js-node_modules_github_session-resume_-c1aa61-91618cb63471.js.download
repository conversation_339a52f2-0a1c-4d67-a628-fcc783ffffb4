"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_quote-selection_dist_index_js-node_modules_github_session-resume_-c1aa61"],{66661:(e,t,n)=>{function i(e){let t=e.parentNode;if(null===t||!(t instanceof HTMLElement))throw Error();let n=0;t instanceof HTMLOListElement&&1!==t.start&&(n=t.start-1);let i=t.children;for(let t=0;t<i.length;++t)if(i[t]===e)return n+t;return n}n.d(t,{P:()=>Quote,g:()=>MarkdownQuote});let r=0;function a(e){return e.replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}let o={INPUT:e=>e instanceof HTMLInputElement&&e.checked?"[x] ":"[ ] ",CODE(e){let t=e.textContent||"";return e.parentNode&&"PRE"===e.parentNode.nodeName?(e.textContent=`\`\`\`
${t.replace(/\n+$/,"")}
\`\`\`

`,e):t.indexOf("`")>=0?`\`\` ${t} \`\``:`\`${t}\``},P(e){let t=document.createElement("p");return t.textContent=(e.textContent||"").replace(/<(\/?)(pre|strong|weak|em)>/g,"\\<$1$2\\>"),t},STRONG:e=>`**${e.textContent||""}**`,EM:e=>`_${e.textContent||""}_`,DEL:e=>`~${e.textContent||""}~`,BLOCKQUOTE(e){let t=(e.textContent||"").trim().replace(/^/gm,"> "),n=document.createElement("pre");return n.textContent=`${t}

`,n},A(e){let t=e.textContent||"",n=e.getAttribute("href");return/^https?:/.test(t)&&t===n?t:n?`[${t}](${n})`:t},IMG(e){let t=e.getAttribute("alt")||"",n=e.getAttribute("src");if(!n)throw Error();let i=e.hasAttribute("width")?` width="${a(e.getAttribute("width")||"")}"`:"",r=e.hasAttribute("height")?` height="${a(e.getAttribute("height")||"")}"`:"";return i||r?`<img alt="${a(t)}"${i}${r} src="${a(n)}">`:`![${t}](${n})`},LI(e){let t=e.parentNode;if(!t)throw Error();let n="";if(!function(e){let t=e.childNodes[0],n=e.childNodes[1];return!!t&&e.childNodes.length<3&&("OL"===t.nodeName||"UL"===t.nodeName)&&(!n||n.nodeType===Node.TEXT_NODE&&!(n.textContent||"").trim())}(e))if("OL"===t.nodeName)if(r>0&&!t.previousSibling){let t=i(e)+r+1;n=`${t}\\. `}else n=`${i(e)+1}. `;else n="* ";let a=n.replace(/\S/g," "),o=(e.textContent||"").trim().replace(/^/gm,a),l=document.createElement("pre");return l.textContent=o.replace(a,n),l},OL(e){let t=document.createElement("li");return t.appendChild(document.createElement("br")),e.append(t),e},H1(e){let t=parseInt(e.nodeName.slice(1));return e.prepend(`${Array(t+1).join("#")} `),e},UL:e=>e};o.UL=o.OL;for(let e=2;e<=6;++e)o[`H${e}`]=o.H1;let Quote=class Quote{constructor(){this.selection=window.getSelection(),this.processSelectionText=e=>e}closest(e){let t=this.range.startContainer,n=t instanceof Element?t:t.parentElement;return n?n.closest(e):null}get active(){var e;return((null==(e=this.selection)?void 0:e.rangeCount)||0)>0}get range(){var e;return(null==(e=this.selection)?void 0:e.rangeCount)?this.selection.getRangeAt(0):new Range}set range(e){var t,n;null==(t=this.selection)||t.removeAllRanges(),null==(n=this.selection)||n.addRange(e)}set processSelectionTextFn(e){this.processSelectionText=e}get selectionText(){var e;return this.processSelectionText((null==(e=this.selection)?void 0:e.toString().trim())||"")}get quotedText(){return`> ${this.selectionText.replace(/\n/g,`
> `)}

`}select(e){this.selection&&(this.selection.removeAllRanges(),this.selection.selectAllChildren(e))}insert(e){e.value?e.value=`${e.value}

${this.quotedText}`:e.value=this.quotedText,e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1})),e.focus(),e.selectionStart=e.value.length,e.scrollTop=e.scrollHeight}};let MarkdownQuote=class MarkdownQuote extends Quote{constructor(e="",t){super(),this.scopeSelector=e,this.callback=t}get selectionText(){var e,t;if(!this.selection)return"";let n=function(e,t){let n=e.startContainer;if(!n||!n.parentNode||!(n.parentNode instanceof HTMLElement))throw Error("the range must start within an HTMLElement");let a=n.parentNode,o=e.cloneContents();if(t){let e=o.querySelector(t);e&&(o=document.createDocumentFragment()).appendChild(e)}r=0;let l=a.closest("li");if(a.closest("pre")){let e=document.createElement("pre");e.appendChild(o),(o=document.createDocumentFragment()).appendChild(e)}else if(l&&l.parentNode&&("OL"===l.parentNode.nodeName&&(r=i(l)),!o.querySelector("li"))){let e=document.createElement("li");if(!l.parentNode)throw Error();let t=document.createElement(l.parentNode.nodeName);e.appendChild(o),t.appendChild(e),(o=document.createDocumentFragment()).appendChild(t)}return o}(this.range,null!=(e=this.scopeSelector)?e:"");null==(t=this.callback)||t.call(this,n);let a=document.createNodeIterator(n,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.nodeName in o&&!function(e){if(e instanceof HTMLAnchorElement&&1===e.childNodes.length){let t=e.childNodes[0];if(t instanceof HTMLImageElement)return t.src===e.href}return!1}(e)&&("IMG"===e.nodeName||null!=e.firstChild||"INPUT"===e.nodeName&&e instanceof HTMLInputElement&&"checkbox"===e.type)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),l=[],s=a.nextNode();for(;s;)s instanceof HTMLElement&&l.push(s),s=a.nextNode();for(let e of(l.reverse(),l))e.replaceWith(o[e.nodeName](e));let c=document.body;if(!c)return"";let u=document.createElement("div");u.appendChild(n),u.style.cssText="position:absolute;left:-9999px;",c.appendChild(u);let d="";try{let e=document.createRange();e.selectNodeContents(u),this.selection.removeAllRanges(),this.selection.addRange(e),d=this.selection.toString(),this.selection.removeAllRanges(),e.detach()}finally{c.removeChild(u)}return this.processSelectionText(d.trim())}}},55150:(e,t,n)=>{n.d(t,{Bu:()=>c,YV:()=>l,o:()=>s});let i=null;function r(e){return e instanceof HTMLSelectElement||(o(e)?e.checked!==e.defaultChecked:e.value!==e.defaultValue)}function a(e){return e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement}function o(e){return e instanceof HTMLInputElement&&/checkbox|radio/.test(e.type)}function l(e,t){var n,o,l,s,c,u;let d,h=null!=(n=null==t?void 0:t.scope)?n:document,f=null!=(o=null==t?void 0:t.selector)?o:".js-session-resumable",p=null!=(l=null==t?void 0:t.fields)?l:h.querySelectorAll(f),m=null!=(s=null==t?void 0:t.keyPrefix)?s:"session-resume:",g=null!=(c=null==t?void 0:t.storageFilter)?c:r;try{d=null!=(u=null==t?void 0:t.storage)?u:sessionStorage}catch(e){return}let v=`${m}${e}`,T=[];for(let e of p)a(e)&&T.push(e);let b=T.filter(e=>!!e.id&&g(e)&&e.form!==i).map(e=>e instanceof HTMLSelectElement?[e.id,Array.from(e.selectedOptions).map(e=>e.value)]:[e.id,e.value]);if(b.length)try{let e=d.getItem(v);if(null!==e){let t=JSON.parse(e).filter(function(e){return!b.some(t=>t[0]===e[0])});b=b.concat(t)}d.setItem(v,JSON.stringify(b))}catch(e){}}function s(e,t){var n,i;let r,l,s=null!=(n=null==t?void 0:t.keyPrefix)?n:"session-resume:";try{r=null!=(i=null==t?void 0:t.storage)?i:sessionStorage}catch(e){return}let c=`${s}${e}`;try{l=r.getItem(c)}catch(e){}if(!l)return;let u=[],d=[];for(let[e,t]of JSON.parse(l)){let n=new CustomEvent("session:resume",{bubbles:!0,cancelable:!0,detail:{targetId:e,targetValue:t}});if(document.dispatchEvent(n)){let n=document.getElementById(e);if(a(n))if(n instanceof HTMLSelectElement){for(let e of n.options)e.selected=t.includes(e.value);u.push(n)}else o(n)?(n.checked=!n.defaultChecked,u.push(n)):n.value===n.defaultValue&&(n.value=t,u.push(n));else d.push([e,t])}}if(0===d.length)try{r.removeItem(c)}catch(e){}else r.setItem(c,JSON.stringify(d));setTimeout(function(){for(let e of u)e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!0}))},0)}function c(e){i=e.target,setTimeout(function(){e.defaultPrevented&&(i=null)},0)}},5497:(e,t,n)=>{n.d(t,{A:()=>i});function i(e,t){var n,i,r,a=(void 0===t?{}:t).viewportMarginBottom,o=void 0===a?100:a,l=null,s=!1;function c(t){if(n!==t.clientX||i!==t.clientY){var a=e.style.height;r&&r!==a&&(s=!0,e.style.maxHeight="",e.removeEventListener("mousemove",c)),r=a}n=t.clientX,i=t.clientY}var u=e.ownerDocument,d=u.documentElement;function h(){if(!s&&e.value!==l&&(!(e.offsetWidth<=0)||!(e.offsetHeight<=0))){var t=function(){for(var t=0,n=e;n!==u.body&&null!==n;)t+=n.offsetTop||0,n=n.offsetParent;var i=t-u.defaultView.pageYOffset,r=d.clientHeight-(i+e.offsetHeight);return{top:i,bottom:r}}(),n=t.top,i=t.bottom;if(!(n<0)&&!(i<0)){var a=getComputedStyle(e),c=Math.ceil(parseFloat(a.borderTopWidth)),h=Math.ceil(parseFloat(a.borderBottomWidth)),f="border-box"===a.boxSizing,p=parseFloat(a.height)+i;e.style.maxHeight=p-(i<o?i:o)+"px";var m=e.parentElement;if(m instanceof HTMLElement){var g=m.style.height;m.style.height=getComputedStyle(m).height,e.style.height="auto",e.style.height=e.scrollHeight+(f?c+h:0)+"px",m.style.height=g,r=e.style.height}l=e.value}}}function f(){s=!1,e.style.height="",e.style.maxHeight=""}e.addEventListener("mousemove",c),e.addEventListener("input",h),e.addEventListener("change",h),e.addEventListener("paste",h);var p=e.form;return p&&p.addEventListener("reset",f),e.value&&h(),{unsubscribe:function(){e.removeEventListener("mousemove",c),e.removeEventListener("input",h),e.removeEventListener("change",h),e.removeEventListener("paste",h),p&&p.removeEventListener("reset",f)}}}},95493:(e,t,n)=>{function i(e,t){return r(function(e){if(e.activeElement!==e.body)return e.activeElement;var t=e.querySelectorAll(":hover"),n=t.length;if(n)return t[n-1]}(e),t)}function r(e,t){var n=e;if(!n)return Promise.resolve(t());var i=n.ownerDocument.documentElement,r=function(e){for(var t=[];e;){var n=e.getBoundingClientRect(),i=n.top,r=n.left;t.push({element:e,top:i,left:r}),e=e.parentElement}return t}(n);return Promise.resolve(t()).then(function(e){var t=function(e){for(var t=0;t<e.length;t++){var n=e[t];if(i.contains(n.element))return n}}(r);if(t){n=t.element;var a=t.top,o=t.left,l=n.getBoundingClientRect(),s=l.top,c=l.left;!function(e,t,n){var i=e.ownerDocument,r=i.defaultView;function a(e){return e.offsetParent?{top:e.scrollTop,left:e.scrollLeft}:{top:r.pageYOffset,left:r.pageXOffset}}function o(e){var t=e;if(t.offsetParent&&t!==i.body){for(;t!==i.body;){if(!t.parentElement)return;t=t.parentElement;var n=r.getComputedStyle(t),a=n.position,o=n.overflowY,l=n.overflowX;if("fixed"===a||"auto"===o||"auto"===l||"scroll"===o||"scroll"===l)break}return t}}for(var l=o(e),s=0,c=0;l;){var u=function(e,t,n){if(0===t&&0===n)return[0,0];var o=a(e),l=o.top+n,s=o.left+t;e===i||e===r||e===i.documentElement||e===i.body?i.defaultView.scrollTo(s,l):(e.scrollTop=l,e.scrollLeft=s);var c=a(e);return[c.left-o.left,c.top-o.top]}(l,t-s,n-c);if(s+=u[0],c+=u[1],s===t&&c===n)break;l=o(l)}}(n,c-o,s-a)}return e})}n.d(t,{JR:()=>i,_H:()=>r})},15955:(e,t,n)=>{n.d(t,{A:()=>i});function i(e){let t=!1,n=null;function i(e,t,n,r=!1){t instanceof HTMLInputElement&&(t.indeterminate=r,t.checked!==n&&(t.checked=n,setTimeout(()=>{let n=new CustomEvent("change",{bubbles:!0,cancelable:!1,detail:{relatedTarget:e}});t.dispatchEvent(n)})))}function r(r){let a=r.target;a instanceof Element&&(a.hasAttribute("data-check-all")?function(t){if(t instanceof CustomEvent&&t.detail){let{relatedTarget:e}=t.detail;if(e&&e.hasAttribute("data-check-all-item"))return}let r=t.target;if(r instanceof HTMLInputElement){for(let t of(n=null,e.querySelectorAll("[data-check-all-item]")))i(r,t,r.checked);r.indeterminate=!1,o()}}(r):a.hasAttribute("data-check-all-item")&&function(r){if(r instanceof CustomEvent&&r.detail){let{relatedTarget:e}=r.detail;if(e&&(e.hasAttribute("data-check-all")||e.hasAttribute("data-check-all-item")))return}let a=r.target;if(!(a instanceof HTMLInputElement))return;let l=Array.from(e.querySelectorAll("[data-check-all-item]"));if(t&&n){let[e,t]=[l.indexOf(n),l.indexOf(a)].sort();for(let n of l.slice(e,+t+1||9e9))i(a,n,a.checked)}t=!1,n=a;let s=e.querySelector("[data-check-all]");if(s){let e=l.length,t=l.filter(e=>e instanceof HTMLInputElement&&e.checked).length;i(a,s,t===e,e>t&&t>0)}o()}(r))}function a(e){e.target instanceof Element&&(e.target instanceof HTMLLabelElement&&e.target.control||e.target).hasAttribute("data-check-all-item")&&(t=e.shiftKey)}function o(){let t=e.querySelector("[data-check-all-count]");t&&(t.textContent=e.querySelectorAll("[data-check-all-item]:checked").length.toString())}return e.addEventListener("mousedown",a),e.addEventListener("change",r),{unsubscribe:()=>{e.removeEventListener("mousedown",a),e.removeEventListener("change",r)}}}},50515:(e,t,n)=>{n.d(t,{JC:()=>T,KK:()=>SequenceTracker,Vy:()=>o,ai:()=>v,oc:()=>s,rd:()=>u});let Leaf=class Leaf{constructor(e){this.children=[],this.parent=e}delete(e){let t=this.children.indexOf(e);return -1!==t&&(this.children=this.children.slice(0,t).concat(this.children.slice(t+1)),0===this.children.length&&this.parent.delete(this),!0)}add(e){return this.children.push(e),this}};let RadixTrie=class RadixTrie{constructor(e){this.parent=null,this.children={},this.parent=e||null}get(e){return this.children[e]}insert(e){let t=this;for(let n=0;n<e.length;n+=1){let i=e[n],r=t.get(i);if(n===e.length-1)return r instanceof RadixTrie&&(t.delete(r),r=null),r||(r=new Leaf(t),t.children[i]=r),r;r instanceof Leaf&&(r=null),r||(r=new RadixTrie(t),t.children[i]=r),t=r}return t}delete(e){for(let t in this.children)if(this.children[t]===e){let e=delete this.children[t];return 0===Object.keys(this.children).length&&this.parent&&this.parent.delete(this),e}return!1}};let i={"\xa1":"1","\u2122":"2","\xa3":"3","\xa2":"4","\u221E":"5","\xa7":"6","\xb6":"7","\u2022":"8","\xaa":"9","\xba":"0","\u2013":"-","\u2260":"=","\u2044":"!","\u20AC":"@","\u2039":"#","\u203A":"$",\uFB01:"%",\uFB02:"^","\u2021":"&","\xb0":"*","\xb7":"(","\u201A":")","\u2014":"_","\xb1":"+",\u0153:"q","\u2211":"w","\xae":"r","\u2020":"t","\xa5":"y","\xf8":"o",\u03C0:"p","\u201C":"[","\u2018":"]","\xab":"\\",\u0152:"Q","\u201E":"W","\xb4":"E","\u2030":"R",\u02C7:"T","\xc1":"Y","\xa8":"U",\u02C6:"I","\xd8":"O","\u220F":"P","\u201D":"{","\u2019":"}","\xbb":"|","\xe5":"a","\xdf":"s","\u2202":"d",\u0192:"f","\xa9":"g","\u02D9":"h","\u2206":"j","\u02DA":"k","\xac":"l","\u2026":";","\xe6":"'","\xc5":"A","\xcd":"S","\xce":"D","\xcf":"F","\u02DD":"G","\xd3":"H","\xd4":"J","\uF8FF":"K","\xd2":"L","\xda":":","\xc6":'"',\u03A9:"z","\u2248":"x","\xe7":"c","\u221A":"v","\u222B":"b","\xb5":"m","\u2264":",","\u2265":".","\xf7":"/","\xb8":"Z","\u02DB":"X","\xc7":"C","\u25CA":"V",\u0131:"B","\u02DC":"N","\xc2":"M","\xaf":"<","\u02D8":">","\xbf":"?"},r={"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+","[":"{","]":"}","\\":"|",";":":","'":'"',",":"<",".":">","/":"?",q:"Q",w:"W",e:"E",r:"R",t:"T",y:"Y",u:"U",i:"I",o:"O",p:"P",a:"A",s:"S",d:"D",f:"F",g:"G",h:"H",j:"J",k:"K",l:"L",z:"Z",x:"X",c:"C",v:"V",b:"B",n:"N",m:"M"},a={" ":"Space","+":"Plus"};function o(e,t=navigator.platform){var n,s,u;let{ctrlKey:d,altKey:h,metaKey:f,shiftKey:p,key:m}=e,g=[];for(let[e,t]of[d,h,f,p].entries())t&&g.push(l[e]);if(!l.includes(m)){let e=g.includes("Alt")&&c.test(t)&&null!=(n=i[m])?n:m,o=g.includes("Shift")&&c.test(t)&&null!=(s=r[e])?s:e,l=null!=(u=a[o])?u:o;g.push(l)}return g.join("+")}let l=["Control","Alt","Meta","Shift"];function s(e,t){let n;var i=function(e,t){var n;let i="undefined"==typeof window?void 0:window,r=c.test(null!=(n=null!=t?t:null==i?void 0:i.navigator.platform)?n:"")?"Meta":"Control";return e.replace("Mod",r)}(e,t);let r=i.split("+").pop(),a=[];for(let e of["Control","Alt","Meta","Shift"])i.includes(e)&&a.push(e);return r&&a.push(r),a.join("+")}let c=/Mac|iPod|iPhone|iPad/i;let SequenceTracker=class SequenceTracker{constructor({onReset:e}={}){this._path=[],this.timer=null,this.onReset=e}get path(){return this._path}get sequence(){return this._path.join(" ")}registerKeypress(e){this._path=[...this._path,o(e)],this.startTimer()}reset(){var e;this.killTimer(),this._path=[],null==(e=this.onReset)||e.call(this)}killTimer(){null!=this.timer&&window.clearTimeout(this.timer),this.timer=null}startTimer(){this.killTimer(),this.timer=window.setTimeout(()=>this.reset(),SequenceTracker.CHORD_TIMEOUT)}};function u(e){return e.split(" ").map(e=>s(e)).join(" ")}function d(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==n&&"reset"!==n&&"checkbox"!==n&&"radio"!==n&&"file"!==n||e.isContentEditable}SequenceTracker.CHORD_TIMEOUT=1500;let h=new RadixTrie,f=new WeakMap,p=h,m=new SequenceTracker({onReset(){p=h}});function g(e){if(e.defaultPrevented||!(e.target instanceof Node))return;if(d(e.target)){let t=e.target;if(!t.id||!t.ownerDocument.querySelector(`[data-hotkey-scope="${t.id}"]`))return}let t=p.get(o(e));if(!t)return void m.reset();if(m.registerKeypress(e),p=t,t instanceof Leaf){let i,r=e.target,a=!1,o=d(r);for(let e=t.children.length-1;e>=0;e-=1){let n=(i=t.children[e]).getAttribute("data-hotkey-scope");if(!o&&!n||o&&r.id===n){a=!0;break}}if(i&&a){var n=i;let t=new CustomEvent("hotkey-fire",{cancelable:!0,detail:{path:m.path}});n.dispatchEvent(t)&&(d(n)?n.focus():n.click()),e.preventDefault()}m.reset()}}function v(e,t){0===Object.keys(h.children).length&&document.addEventListener("keydown",g);let n=(function(e){let t=[],n=[""],i=!1;for(let r=0;r<e.length;r++){if(i&&","===e[r]){t.push(n),n=[""],i=!1;continue}if(" "===e[r]){n.push(""),i=!1;continue}i="+"!==e[r],n[n.length-1]+=e[r]}return t.push(n),t.map(e=>e.map(e=>s(e)).filter(e=>""!==e)).filter(e=>e.length>0)})(t||e.getAttribute("data-hotkey")||"").map(t=>h.insert(t).add(e));f.set(e,n)}function T(e){let t=f.get(e);if(t&&t.length)for(let n of t)n&&n.delete(e);0===Object.keys(h.children).length&&document.removeEventListener("keydown",g)}},18679:(e,t,n)=>{n.d(t,{s:()=>AnalyticsClient});let i=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","scid"];var r=n(36301);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,r.y)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...function(){let e={};try{for(let[t,n]of new URLSearchParams(window.location.search)){let r=t.toLowerCase();i.includes(r)&&(e[r]=n)}return e}catch(e){return{}}}(),...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n=JSON.stringify({client_id:this.clientId,page_views:e,events:t,request_context:{referrer:function(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}(),user_agent:navigator.userAgent,screen_resolution:function(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}(),browser_resolution:function(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}(),browser_languages:navigator.languages?navigator.languages.join(","):navigator.language||"",pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}});try{if(navigator.sendBeacon)return void navigator.sendBeacon(this.collectorUrl,n)}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:n,keepalive:!1})}}},70837:(e,t,n)=>{n.d(t,{O:()=>i});function i(e="ha"){let t,n={};for(let i of Array.from(document.head.querySelectorAll(`meta[name^="${e}-"]`))){let{name:r,content:a}=i,o=r.replace(`${e}-`,"").replace(/-/g,"_");"url"===o?t=a:n[o]=a}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}},31143:(e,t,n)=>{n.d(t,{qy:()=>S,XX:()=>v,_3:()=>x});let i=new Map;function r(e){if(i.has(e))return i.get(e);let t=e.length,n=0,r=0,a=0,o=[];for(let i=0;i<t;i+=1){let t=e[i],l=e[i+1],s=e[i-1];"{"===t&&"{"===l&&"\\"!==s?(1===(a+=1)&&(r=i),i+=1):"}"===t&&"}"===l&&"\\"!==s&&a&&0==(a-=1)&&(r>n&&(o.push(Object.freeze({type:"string",start:n,end:r,value:e.slice(n,r)})),n=r),o.push(Object.freeze({type:"part",start:r,end:i+2,value:e.slice(n+2,i).trim()})),i+=1,n=i+1)}return n<t&&o.push(Object.freeze({type:"string",start:n,end:t,value:e.slice(n,t)})),i.set(e,Object.freeze(o)),i.get(e)}let a=new WeakMap,o=new WeakMap;let AttributeTemplatePart=class AttributeTemplatePart{constructor(e,t){this.expression=t,a.set(this,e),e.updateParent("")}get attributeName(){return a.get(this).attr.name}get attributeNamespace(){return a.get(this).attr.namespaceURI}get value(){return o.get(this)}set value(e){o.set(this,e||""),a.get(this).updateParent(e)}get element(){return a.get(this).element}get booleanValue(){return a.get(this).booleanValue}set booleanValue(e){a.get(this).booleanValue=e}};let AttributeValueSetter=class AttributeValueSetter{constructor(e,t){this.element=e,this.attr=t,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(e){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=e?"":null}append(e){this.partList.push(e)}updateParent(e){if(1===this.partList.length&&null===e)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let e=this.partList.map(e=>"string"==typeof e?e:e.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,e)}}};let l=new WeakMap;let NodeTemplatePart=class NodeTemplatePart{constructor(e,t){this.expression=t,l.set(this,[e]),e.textContent=""}get value(){return l.get(this).map(e=>e.textContent).join("")}set value(e){this.replace(e)}get previousSibling(){return l.get(this)[0].previousSibling}get nextSibling(){return l.get(this)[l.get(this).length-1].nextSibling}replace(...e){let t=e.map(e=>"string"==typeof e?new Text(e):e);for(let e of(t.length||t.push(new Text("")),l.get(this)[0].before(...t),l.get(this)))e.remove();l.set(this,t)}};function s(e){return{processCallback(t,n,i){var r;if("object"==typeof i&&i){for(let t of n)if(t.expression in i){let n=null!=(r=i[t.expression])?r:"";e(t,n)}}}}}function c(e,t){e.value=String(t)}function u(e,t){return"boolean"==typeof t&&e instanceof AttributeTemplatePart&&"boolean"==typeof e.element[e.attributeName]&&(e.booleanValue=t,!0)}let d=s(c);s((e,t)=>{u(e,t)||c(e,t)});let h=new WeakMap,f=new WeakMap;let TemplateInstance=class TemplateInstance extends DocumentFragment{constructor(e,t,n=d){var i,a;super(),Object.getPrototypeOf(this)!==TemplateInstance.prototype&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(e.content.cloneNode(!0)),f.set(this,Array.from(function*(e){let t,n=e.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null,!1);for(;t=n.nextNode();)if(t instanceof Element&&t.hasAttributes())for(let e=0;e<t.attributes.length;e+=1){let n=t.attributes.item(e);if(n&&n.value.includes("{{")){let e=new AttributeValueSetter(t,n);for(let t of r(n.value))if("string"===t.type)e.append(t.value);else{let n=new AttributeTemplatePart(e,t.value);e.append(n),yield n}}}else if(t instanceof Text&&t.textContent&&t.textContent.includes("{{")){let e=r(t.textContent);for(let n=0;n<e.length;n+=1){let i=e[n];i.end<t.textContent.length&&t.splitText(i.end),"part"===i.type&&(yield new NodeTemplatePart(t,i.value));break}}}(this))),h.set(this,n),null==(a=(i=h.get(this)).createCallback)||a.call(i,this,f.get(this),t),h.get(this).processCallback(this,f.get(this),t)}update(e){h.get(this).processCallback(this,f.get(this),e)}};let p=new WeakMap,m=new WeakMap,g=new WeakMap;let TemplateResult=class TemplateResult{constructor(e,t,n){this.strings=e,this.values=t,this.processor=n}static setCSPTrustedTypesPolicy(e){TemplateResult.cspTrustedTypesPolicy=e}get template(){var e,t;if(p.has(this.strings))return p.get(this.strings);{let n=document.createElement("template"),i=this.strings.length-1,r=this.strings.reduce((e,t,n)=>e+t+(n<i?`{{ ${n} }}`:""),"");return n.innerHTML=null!=(t=null==(e=TemplateResult.cspTrustedTypesPolicy)?void 0:e.createHTML(r))?t:r,p.set(this.strings,n),n}}renderInto(e){let t=this.template;if(m.get(e)!==t){m.set(e,t);let n=new TemplateInstance(t,this.values,this.processor);g.set(e,n),e instanceof NodeTemplatePart?e.replace(...n.children):e.appendChild(n);return}g.get(e).update(this.values)}};function v(e,t){e.renderInto(t)}TemplateResult.cspTrustedTypesPolicy=null;let T=new WeakSet;function b(e){return(...t)=>{let n=e(...t);return T.add(n),n}}let y=new WeakMap;let EventHandler=class EventHandler{constructor(e,t){this.element=e,this.type=t,this.element.addEventListener(this.type,this),y.get(this.element).set(this.type,this)}set(e){"function"==typeof e?this.handleEvent=e.bind(this.element):"object"==typeof e&&"function"==typeof e.handleEvent?this.handleEvent=e.handleEvent.bind(e):(this.element.removeEventListener(this.type,this),y.get(this.element).delete(this.type))}static for(e){y.has(e.element)||y.set(e.element,new Map);let t=e.attributeName.slice(2),n=y.get(e.element);return n.has(t)?n.get(t):new EventHandler(e.element,t)}};function w(e,t){T.has(t)&&(t(e),1)||u(e,t)||e instanceof AttributeTemplatePart&&e.attributeName.startsWith("on")&&(EventHandler.for(e).set(t),e.element.removeAttributeNS(e.attributeNamespace,e.attributeName),1)||t instanceof TemplateResult&&e instanceof NodeTemplatePart&&(t.renderInto(e),1)||t instanceof DocumentFragment&&e instanceof NodeTemplatePart&&(t.childNodes.length&&e.replace(...t.childNodes),1)||function(e,t){if(!("object"==typeof t&&Symbol.iterator in t))return!1;if(!(e instanceof NodeTemplatePart))return e.value=Array.from(t).join(" "),!0;{let n=[];for(let e of t)if(e instanceof TemplateResult){let t=document.createDocumentFragment();e.renderInto(t),n.push(...t.childNodes)}else e instanceof DocumentFragment?n.push(...e.childNodes):n.push(String(e));return n.length&&e.replace(...n),!0}}(e,t)||c(e,t)}let E=s(w);function S(e,...t){return new TemplateResult(e,t,E)}let C=new WeakMap;b((...e)=>t=>{C.has(t)||C.set(t,{i:e.length});let n=C.get(t);for(let i=0;i<e.length;i+=1)e[i]instanceof Promise?Promise.resolve(e[i]).then(e=>{i<n.i&&(n.i=i,w(t,e))}):i<=n.i&&(n.i=i,w(t,e[i]))});let x=b(e=>t=>{var n,i;if(!(t instanceof NodeTemplatePart))return;let r=document.createElement("template");r.innerHTML=null!=(i=null==(n=TemplateResult.cspTrustedTypesPolicy)?void 0:n.createHTML(e))?i:e;let a=document.importNode(r.content,!0);t.replace(...a.childNodes)})},78134:(e,t,n)=>{n.d(t,{i4:()=>TemplateInstance,xr:()=>d});let i=new Map;function r(e){if(i.has(e))return i.get(e);let t=e.length,n=0,r=0,a=0,o=[];for(let i=0;i<t;i+=1){let t=e[i],l=e[i+1],s=e[i-1];"{"===t&&"{"===l&&"\\"!==s?(1===(a+=1)&&(r=i),i+=1):"}"===t&&"}"===l&&"\\"!==s&&a&&0==(a-=1)&&(r>n&&(o.push(Object.freeze({type:"string",start:n,end:r,value:e.slice(n,r)})),n=r),o.push(Object.freeze({type:"part",start:r,end:i+2,value:e.slice(n+2,i).trim()})),i+=1,n=i+1)}return n<t&&o.push(Object.freeze({type:"string",start:n,end:t,value:e.slice(n,t)})),i.set(e,Object.freeze(o)),i.get(e)}let a=new WeakMap,o=new WeakMap;let AttributeTemplatePart=class AttributeTemplatePart{constructor(e,t){this.expression=t,a.set(this,e),e.updateParent("")}get attributeName(){return a.get(this).attr.name}get attributeNamespace(){return a.get(this).attr.namespaceURI}get value(){return o.get(this)}set value(e){o.set(this,e||""),a.get(this).updateParent(e)}get element(){return a.get(this).element}get booleanValue(){return a.get(this).booleanValue}set booleanValue(e){a.get(this).booleanValue=e}};let AttributeValueSetter=class AttributeValueSetter{constructor(e,t){this.element=e,this.attr=t,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(e){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=e?"":null}append(e){this.partList.push(e)}updateParent(e){if(1===this.partList.length&&null===e)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let e=this.partList.map(e=>"string"==typeof e?e:e.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,e)}}};let l=new WeakMap;let NodeTemplatePart=class NodeTemplatePart{constructor(e,t){this.expression=t,l.set(this,[e]),e.textContent=""}get value(){return l.get(this).map(e=>e.textContent).join("")}set value(e){this.replace(e)}get previousSibling(){return l.get(this)[0].previousSibling}get nextSibling(){return l.get(this)[l.get(this).length-1].nextSibling}replace(...e){var t,n;let i=e.map(e=>"string"==typeof e?new Text(e):e);i.length||i.push(new Text(""));let r=l.get(this)[0];for(let e of i)null==(t=r.parentNode)||t.insertBefore(e,r);for(let e of l.get(this))null==(n=e.parentNode)||n.removeChild(e);l.set(this,i)}};let InnerTemplatePart=class InnerTemplatePart extends NodeTemplatePart{constructor(e){var t;super(e,null!=(t=e.getAttribute("expression"))?t:""),this.template=e}get directive(){var e;return null!=(e=this.template.getAttribute("directive"))?e:""}};function s(e){return{processCallback(t,n,i){var r;if("object"==typeof i&&i){for(let t of n)if(t.expression in i){let n=null!=(r=i[t.expression])?r:"";e(t,n,i)}}}}}function c(e,t){e.value=t instanceof Node?t:String(t)}let u=s(c),d=s((e,t)=>{!function(e,t){return"boolean"==typeof t&&e instanceof AttributeTemplatePart&&"boolean"==typeof e.element[e.attributeName]&&(e.booleanValue=t,!0)}(e,t)&&c(e,t)}),h=new WeakMap,f=new WeakMap;let TemplateInstance=class TemplateInstance extends(globalThis.DocumentFragment||EventTarget){constructor(e,t,n=u){var i,a;super(),Object.getPrototypeOf(this)!==TemplateInstance.prototype&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(e.content.cloneNode(!0)),f.set(this,Array.from(function* e(t){let n,i=t.ownerDocument.createTreeWalker(t,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null);for(;n=i.nextNode();)if(n instanceof HTMLTemplateElement)if(n.hasAttribute("directive"))yield new InnerTemplatePart(n);else for(let t of e(n.content))yield t;else if(n instanceof Element&&n.hasAttributes())for(let e=0;e<n.attributes.length;e+=1){let t=n.attributes.item(e);if(t&&t.value.includes("{{")){let e=new AttributeValueSetter(n,t);for(let n of r(t.value))if("string"===n.type)e.append(n.value);else{let t=new AttributeTemplatePart(e,n.value);e.append(t),yield t}}}else if(n instanceof Text&&n.textContent&&n.textContent.includes("{{")){let e=r(n.textContent);for(let t=0;t<e.length;t+=1){let i=e[t];i.end<n.textContent.length&&n.splitText(i.end),"part"===i.type&&(yield new NodeTemplatePart(n,i.value));break}}}(this))),h.set(this,n),null==(a=(i=h.get(this)).createCallback)||a.call(i,this,f.get(this),t),h.get(this).processCallback(this,f.get(this),t)}update(e){h.get(this).processCallback(this,f.get(this),e)}}},95754:(e,t,n)=>{n.d(t,{A:()=>a});let i=["direction","boxSizing","width","height","overflowX","overflowY","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderStyle","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing","tabSize","MozTabSize"],r="undefined"!=typeof window&&null!=window.mozInnerScreenX;function a(e,t,n){let a=n&&n.debug||!1;if(a){let e=document.querySelector("#input-textarea-caret-position-mirror-div");e&&e.parentNode.removeChild(e)}let o=document.createElement("div");o.id="input-textarea-caret-position-mirror-div",document.body.appendChild(o);let l=o.style,s=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,c="INPUT"===e.nodeName;for(let t of(l.whiteSpace="pre-wrap",c||(l.wordWrap="break-word"),l.position="absolute",a||(l.visibility="hidden"),i))if(c&&"lineHeight"===t)if("border-box"===s.boxSizing){let e=parseInt(s.height),t=parseInt(s.paddingTop)+parseInt(s.paddingBottom)+parseInt(s.borderTopWidth)+parseInt(s.borderBottomWidth),n=t+parseInt(s.lineHeight);e>n?l.lineHeight=`${e-t}px`:e===n?l.lineHeight=s.lineHeight:l.lineHeight=0}else l.lineHeight=s.height;else if(c||"width"!==t||"border-box"!==s.boxSizing)l[t]=s[t];else{let n=parseFloat(s.borderLeftWidth)+parseFloat(s.borderRightWidth),i=r?parseFloat(s[t])-n:e.clientWidth+n;l[t]=`${i}px`}r?e.scrollHeight>parseInt(s.height)&&(l.overflowY="scroll"):l.overflow="hidden",o.textContent=e.value.substring(0,t),c&&(o.textContent=o.textContent.replace(/\s/g,"\xa0"));let u=document.createElement("span");u.textContent=e.value.substring(t)||".",o.appendChild(u);let d={top:u.offsetTop+parseInt(s.borderTopWidth),left:u.offsetLeft+parseInt(s.borderLeftWidth),height:parseInt(s.lineHeight)};return a?u.style.backgroundColor="#aaa":document.body.removeChild(o),d}},46493:(e,t,n)=>{n.d(t,{Cj:()=>U,iP:()=>V});let i={Less:"less",Equal:"equal",Greater:"greater"};var r,a,o,l,s,c,u,d,h,f,p,m,g,v,T,b,y,w=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},E=(e,t,n)=>(w(e,t,"read from private field"),n?n.call(e):t.get(e)),S=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},C=(e,t,n,i)=>(w(e,t,"write to private field"),i?i.call(e,n):t.set(e,n),n),x=(e,t,n)=>(w(e,t,"access private method"),n);let MinHeap=class MinHeap{constructor({compareFn:e}){S(this,o),S(this,s),S(this,r,void 0),S(this,a,void 0),C(this,r,e),C(this,a,[])}insert(e){E(this,a).push(e),x(this,s,c).call(this)}pop(){let e=E(this,a)[0];return E(this,a)[E(this,a).length-1]&&(E(this,a)[0]=E(this,a)[E(this,a).length-1],E(this,a).pop()),x(this,o,l).call(this),e}peek(){return E(this,a)[0]}delete(e){let t=E(this,a).indexOf(e);-1!==t&&(L(E(this,a),t,E(this,a).length-1),E(this,a).pop(),x(this,o,l).call(this))}clear(){C(this,a,[])}get size(){return E(this,a).length}};function I(e){return Math.floor((e-1)/2)}function L(e,t,n){let i=e[t];e[t]=e[n],e[n]=i}r=new WeakMap,a=new WeakMap,o=new WeakSet,l=function(){let e=0;for(;2*e+1<E(this,a).length;){var t,n;let o=2*e+1;if(2*e+2<E(this,a).length&&E(this,r).call(this,(t=E(this,a),t[2*e+2]),(n=E(this,a),n[2*e+1]))===i.Less&&(o=2*e+2),E(this,r).call(this,E(this,a)[e],E(this,a)[o])===i.Less)break;L(E(this,a),e,o),e=o}},s=new WeakSet,c=function(){var e;let t=E(this,a).length-1;for(;t>0&&E(this,r).call(this,E(this,a)[t],(e=E(this,a),e[I(t)]))===i.Less;)L(E(this,a),t,I(t)),t=I(t)};var k=Object.defineProperty,N=(e,t,n)=>t in e?k(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,M=(e,t,n)=>(N(e,"symbol"!=typeof t?t+"":t,n),n),A=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},P=(e,t,n)=>(A(e,t,"read from private field"),n?n.call(e):t.get(e)),H=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},O=(e,t,n,i)=>(A(e,t,"write to private field"),i?i.call(e,n):t.set(e,n),n);let Deferred=class Deferred{constructor(){M(this,u,"Deferred"),H(this,d,void 0),H(this,h,void 0),H(this,f,void 0),O(this,d,new Promise((e,t)=>{O(this,h,e),O(this,f,t)}))}then(e,t){return Promise.prototype.then.apply(P(this,d),[e,t])}catch(e){return Promise.prototype.catch.apply(P(this,d),[e])}finally(e){return Promise.prototype.finally.apply(P(this,d),[e])}resolve(e){P(this,h).call(this,e)}reject(e){P(this,f).call(this,e)}getPromise(){return P(this,d)}};u=Symbol.toStringTag,d=new WeakMap,h=new WeakMap,f=new WeakMap;var W=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},R=(e,t,n)=>(W(e,t,"read from private field"),n?n.call(e):t.get(e)),F=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},_=(e,t,n,i)=>(W(e,t,"write to private field"),i?i.call(e,n):t.set(e,n),n),$=(e,t,n)=>(W(e,t,"access private method"),n);let LiveRegionElement=class LiveRegionElement extends HTMLElement{constructor(){if(super(),F(this,v),F(this,b),F(this,p,void 0),F(this,m,void 0),F(this,g,void 0),!this.shadowRoot){let e=(D||((D=document.createElement("template")).innerHTML=j),D);this.attachShadow({mode:"open"}).appendChild(e.content.cloneNode(!0))}_(this,p,!1),_(this,g,null),_(this,m,new MinHeap({compareFn:q}))}get delay(){let e=this.getAttribute("delay");return e?parseInt(e,10):150}set delay(e){this.setAttribute("delay",`${e}`)}announce(e,t={}){let{delayMs:n,politeness:i="polite"}=t,r=Date.now(),a=new Deferred,o={deferred:a,politeness:i,contents:e,scheduled:void 0!==n?r+n:r};return R(this,m).insert(o),$(this,v,T).call(this),{...a.getPromise(),cancel:()=>{R(this,m).delete(o),a.resolve()}}}announceFromElement(e,t){var n;let i,r=(i="",(n=e).hasAttribute("aria-label")?i=n.getAttribute("aria-label"):n.innerText?i=n.innerText:n.textContent&&(i=n.textContent),i?i.trim():"");return""!==r?this.announce(r,t):{...Promise.resolve(),cancel:B}}getMessage(e="polite"){let t=this.shadowRoot?.getElementById(e);if(!t)throw Error("Unable to find container for message");return t.textContent}clear(){null!==R(this,g)&&(clearTimeout(R(this,g)),_(this,g,null)),R(this,m).clear()}};p=new WeakMap,m=new WeakMap,g=new WeakMap,v=new WeakSet,T=function(){if(R(this,p))return;let e=R(this,m).peek();if(!e)return;null!==R(this,g)&&(clearTimeout(R(this,g)),_(this,g,null));let t=Date.now();if(e.scheduled<=t){(e=R(this,m).pop())&&$(this,b,y).call(this,e),$(this,v,T).call(this);return}let n=e.scheduled-t;_(this,g,window.setTimeout(()=>{_(this,g,null),$(this,v,T).call(this)},n))},b=new WeakSet,y=function(e){_(this,p,!0);let{contents:t,deferred:n,politeness:i}=e,r=this.shadowRoot?.getElementById(i);if(!r)throw _(this,p,!1),Error(`Unable to find container for message. Expected a container with id="${i}"`);r.textContent===t?r.textContent=`${t}\xa0`:r.textContent=t,null!==R(this,g)&&clearTimeout(R(this,g)),n.resolve(),this.delay>0?_(this,g,window.setTimeout(()=>{_(this,g,null),_(this,p,!1),$(this,v,T).call(this)},this.delay)):(_(this,g,null),_(this,p,!1),$(this,v,T).call(this))};let D=null,j=`
<style>
:host {
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
</style>
<div id="polite" aria-live="polite" aria-atomic="true"></div>
<div id="assertive" aria-live="assertive" aria-atomic="true"></div>
`;function q(e,t){return e.politeness===t.politeness?e.scheduled===t.scheduled?i.Equal:e.scheduled<t.scheduled?i.Less:i.Greater:"assertive"===e.politeness&&"assertive"!==t.politeness?i.Less:"assertive"!==e.politeness&&"assertive"===t.politeness?i.Greater:i.Equal}function B(){}function V(e,t={}){let n=z(t.from);if(!n){n=document.createElement("live-region"),t.appendTo?t.appendTo.appendChild(n):X(t.from).appendChild(n);let i=!1,r=()=>{i=!0};return{...Y(K).then(()=>{if(!i){let i=n.announce(e,t);return r=i.cancel,i}}),cancel:()=>{r()}}}return n.announce(e,t)}function U(e,t={}){let n=z(t.from);if(!n){n=document.createElement("live-region"),t.appendTo?t.appendTo.appendChild(n):X(t.from).appendChild(n);let i=!1,r=()=>{i=!0};return{...Y(K).then(()=>{if(!i){let i=n.announceFromElement(e,t);return r=i.cancel,i}}),cancel:()=>{r()}}}return n.announceFromElement(e,t)}function z(e){let t=null;return null!==(t=e?function(e){let t=e.closest("dialog"),n=e;for(;(n=n.parentElement)&&(!t||t.contains(n));)for(let e of n.childNodes)if(e instanceof LiveRegionElement)return e;return null}(e):null)||null!==(t=X(e).querySelector("live-region"))?t:null}function X(e){let t=document.body;if(e){let n=e.closest("dialog");n&&(t=n)}return t}customElements.get("live-region")||customElements.define("live-region",LiveRegionElement);let K=150;function Y(e){return new Promise(t=>{setTimeout(t,e)})}},57226:(e,t,n)=>{n.d(t,{Ck:()=>ed,IN:()=>W,fK:()=>eo,lt:()=>U,zB:()=>R});var i,r,a,o,l,s,c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},u=(null==(i=c())?void 0:i.navigationId)||"1",d=function(e){if("loading"===document.readyState)return"loading";var t=c();if(t){if(e<t.domInteractive)return"loading";if(0===t.domContentLoadedEventStart||e<t.domContentLoadedEventStart)return"dom-interactive";if(0===t.domComplete||e<t.domComplete)return"dom-content-loaded"}return"complete"},h=function(e){var t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},f=function(e,t){var n="";try{for(;e&&9!==e.nodeType;){var i=e,r=i.id?"#"+i.id:h(i)+(i.classList&&i.classList.value&&i.classList.value.trim()&&i.classList.value.trim().length?"."+i.classList.value.trim().replace(/\s+/g,"."):"");if(n.length+r.length>(t||100)-1)return n||r;if(n=n?r+">"+n:r,i.id)break;e=i.parentNode}}catch(e){}return n},p=function(e,t,n,i){var r,a;return function(o){var l;t.value>=0&&(o||i)&&((a=t.value-(r||0))||void 0===r)&&(r=t.value,t.delta=a,l=t.value,t.rating=l>n[1]?"poor":l>n[0]?"needs-improvement":"good",e(t))}},m=-1,g=function(){return m},v=function(e){addEventListener("pageshow",function(t){t.persisted&&(m=t.timeStamp,e(t))},!0)},T=function(e){requestAnimationFrame(function(){return requestAnimationFrame(function(){return e()})})},b=function(){var e=c();return e&&e.activationStart||0},y=function(e,t,n,i){var r=c(),a="navigate";return n?a=n:g()>=0?a="back-forward-cache":r&&(document.prerendering||b()>0?a="prerender":document.wasDiscarded?a="restore":r.type&&(a=r.type.replace(/_/g,"-"))),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(0x82f79cd8fff*Math.random())+1e12),navigationType:a,navigationId:i||u}},w=function(e){return PerformanceObserver.supportedEntryTypes.includes("soft-navigation")&&e&&e.reportSoftNavs},E=function(e){if(e){var t=window.performance.getEntriesByType("soft-navigation").filter(function(t){return t.navigationId===e});return t?t[0]:void 0}},S=function(e,t,n){var i=w(n);try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver(function(e){Promise.resolve().then(function(){t(e.getEntries())})});return r.observe(Object.assign({type:e,buffered:!0,includeSoftNavigationObservations:i},n||{})),r}}catch(e){}},C=function(e){var t=function(t){"pagehide"!==t.type&&"hidden"!==document.visibilityState||e(t)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},x=-1,I=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},L=function(e){"hidden"===document.visibilityState&&x>-1&&(x="visibilitychange"===e.type?e.timeStamp:0,N())},k=function(){addEventListener("visibilitychange",L,!0),addEventListener("prerenderingchange",L,!0)},N=function(){removeEventListener("visibilitychange",L,!0),removeEventListener("prerenderingchange",L,!0)},M=function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&(x=-1),x<0&&(x=I(),k(),v(function(){setTimeout(function(){x=I(),k()},0)})),{get firstHiddenTime(){return x}}},A=function(e){document.prerendering?addEventListener("prerenderingchange",function(){return e()},!0):e()},P=[1800,3e3],H=function(e,t){var n=w(t=t||{}),i=0;A(function(){var r,a=M(),o=y("FCP"),l=S("paint",function(s){s.forEach(function(s){if("first-contentful-paint"===s.name){n?s.navigationId&&s.navigationId!==o.navigationId&&function(n,l){if(r=p(e,o=y("FCP",0,n,l),P,t.reportAllChanges),"soft-navigation"===n){a=M(!0);var s=l?E(l):null;i=s&&s.startTime||0}}("soft-navigation",s.navigationId):l.disconnect();var c=0;if(s.navigationId&&s.navigationId!==u){var d=E(s.navigationId),h=d&&d.startTime?d.startTime:0;c=Math.max(s.startTime-h,0)}else c=Math.max(s.startTime-b(),0);var f=n&&s.navigationId?E(s.navigationId):null,m=f&&f.startTime?f.startTime:0;(s.startTime<a.firstHiddenTime||n&&s.navigationId&&s.navigationId!==o.navigationId&&s.navigationId!==u&&m>i)&&(o.value=c,o.entries.push(s),o.navigationId=s.navigationId||"1",r(!0))}})},t);l&&(r=p(e,o,P,t.reportAllChanges),v(function(n){r=p(e,o=y("FCP",0,"back-forward-cache",o.navigationId),P,t.reportAllChanges),T(function(){o.value=performance.now()-n.timeStamp,r(!0)})}))})},O=[.1,.25],W=function(e,t){var n,i,r,a,o,l,s;n=function(t){!function(e){if(e.entries.length){var t,n=e.entries.reduce(function(e,t){return e&&e.value>t.value?e:t});if(n&&n.sources&&n.sources.length){var i=(t=n.sources).find(function(e){return e.node&&1===e.node.nodeType})||t[0];if(i)return e.attribution={largestShiftTarget:f(i.node),largestShiftTime:n.startTime,largestShiftValue:n.value,largestShiftSource:i,largestShiftEntry:n,loadState:d(n.startTime)}}}e.attribution={}}(t),e(t)},o=w(i=(i=t)||{}),l=!1,s=0,H((r=function(){var e,t=y("CLS",0),r=0,a=[],c=function(a,o){if(e=p(n,t=y("CLS",0,a,o),O,i.reportAllChanges),r=0,l=!1,"soft-navigation"===a){var c=E(o);s=c&&c.startTime||0}},u=function(n){n.forEach(function(n){if(o&&n.navigationId&&n.navigationId!==t.navigationId&&(r>t.value&&(t.value=r,t.entries=a),e(!0),c("soft-navigation",n.navigationId)),!n.hadRecentInput){var i=a[0],l=a[a.length-1];r&&n.startTime-l.startTime<1e3&&n.startTime-i.startTime<5e3?(r+=n.value,a.push(n)):(r=n.value,a=[n])}}),r>t.value&&(t.value=r,t.entries=a,e())},d=S("layout-shift",u,i);d&&(e=p(n,t,O,i.reportAllChanges),C(function(){u(d.takeRecords()),e(!0),l=!0}),v(function(){c("back-forward-cache",t.navigationId),T(function(){return e()})}),o&&S("soft-navigation",function(r){r.forEach(function(r){var a=r.navigationId,o=a?E(a):null;a&&a!==t.navigationId&&o&&(o.startTime||0)>s&&(l||e(!0),c("soft-navigation",r.navigationId),e=p(n,t,O,i.reportAllChanges))})},i),setTimeout(e,0))},a=!1,function(e){a||(r(e),a=!0)}))},R=function(e,t){H(function(t){!function(e){if(e.entries.length){var t,n=e.entries[e.entries.length-1],i=0;if(e.navigationId&&e.navigationId!==u)i=(t=E(e.navigationId))?t.startTime:0;else if(t=c()){var r=t.activationStart||0;i=Math.max(0,t.responseStart-r)}if(t)return e.attribution={timeToFirstByte:i,firstByteToFCP:e.value-i,loadState:d(e.entries[0].startTime),navigationEntry:t,fcpEntry:n}}e.attribution={timeToFirstByte:0,firstByteToFCP:e.value,loadState:d(g())}}(t),e(t)},t)},F={passive:!0,capture:!0},_=new Date,$=function(e,t){r||(r=t,a=e,o=new Date,q(removeEventListener),D())},D=function(){if(a>=0&&a<o-_){var e={entryType:"first-input",name:r.type,target:r.target,cancelable:r.cancelable,startTime:r.timeStamp,processingStart:r.timeStamp+a};l.forEach(function(t){t([e])}),l=[]}},j=function(e){if(e.cancelable){var t,n,i,r=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){$(r,e),i()},n=function(){i()},i=function(){removeEventListener("pointerup",t,F),removeEventListener("pointercancel",n,F)},addEventListener("pointerup",t,F),addEventListener("pointercancel",n,F)):$(r,e)}},q=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,j,F)})},B=[100,300],V=function(e,t){var n=w(t=t||{});A(function(){var i,o=M(),s=y("FID"),c=function(r){r.forEach(function(r){var a;n?r.navigationId&&r.navigationId!==s.navigationId&&(a=r.navigationId,o=M(!0),i=p(e,s=y("FID",0,"soft-navigation",a),B,t.reportAllChanges)):d.disconnect(),r.startTime<o.firstHiddenTime&&(s.value=r.processingStart-r.startTime,s.entries.push(r),s.navigationId=r.navigationId||u,i(!0))})},d=S("first-input",c,t);i=p(e,s,B,t.reportAllChanges),d&&C(function(){c(d.takeRecords()),n||d.disconnect()}),d&&v(function(){i=p(e,s=y("FID",0,"back-forward-cache",s.navigationId),B,t.reportAllChanges),l=[],a=-1,r=null,q(addEventListener),l.push(c),D()})})},U=function(e,t){V(function(t){var n;n=t.entries[0],t.attribution={eventTarget:f(n.target),eventType:n.name,eventTime:n.startTime,eventEntry:n,loadState:d(n.startTime)},e(t)},t)},z=0,X=null,K=0,Y=null,J=null,G=function(e){e.forEach(function(e){e.interactionId&&(J&&e.navigationId&&e.navigationId!==Y&&(Y=e.navigationId,z=0,X=1/0,K=0),X=Math.min(X,e.interactionId),z=(K=Math.max(K,e.interactionId))?(K-X)/7+1:0)})},Q=function(){return s?z:performance.interactionCount||0},Z=function(e){"interactionCount"in performance||s||(s=S("event",G,{type:"event",buffered:!0,durationThreshold:0,includeSoftNavigationObservations:J=e||!1}))},ee=0,et=function(){return Q()-ee},en=null,ei={},er=function(e){var t=en[en.length-1],n=ei[e.interactionId];if(n||en.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{var i={id:e.interactionId,latency:e.duration,entries:[e]};ei[i.id]=i,en.push(i)}en.sort(function(e,t){return t.latency-e.latency}),en.splice(10).forEach(function(e){delete ei[e.id]})}},ea=[2500,4e3],eo=function(e,t){var n,i,r,a,o;n=function(t){!function(e){if(e.entries.length){var t,n=0,i=0,r=0;if(e.navigationId&&e.navigationId!==u?n=r=(t=E(e.navigationId))?t.startTime:0:(n=(t=c())&&t.activationStart?t.activationStart:0,i=t&&t.responseStart?t.responseStart:0),t){var a=e.entries[e.entries.length-1],o=a.url&&performance.getEntriesByType("resource").filter(function(e){return e.name===a.url})[0],l=Math.max(0,i-n),s=Math.max(l,o?(o.requestStart||o.startTime)-n:0),d=Math.max(s-r,o?o.responseEnd-n:0,0),h=Math.max(d-r,a?a.startTime-n:0,0),p={element:f(a.element),timeToFirstByte:l,resourceLoadDelay:s-l,resourceLoadTime:d-s,elementRenderDelay:h-d,navigationEntry:t,lcpEntry:a};return a.url&&(p.url=a.url),o&&(p.lcpResourceEntry=o),e.attribution=p}}e.attribution={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadTime:0,elementRenderDelay:e.value}}(t),e(t)},r=!1,a=w(i=(i=t)||{}),o=0,A(function(){var e,t=M(),l=y("LCP"),s=function(a,s){if(e=p(n,l=y("LCP",0,a,s),ea,i.reportAllChanges),r=!1,"soft-navigation"===a){t=M(!0);var c=E(s);o=c&&c.startTime?c.startTime:0}},c=function(n){n.forEach(function(n){if(n){a&&n.navigationId&&n.navigationId!==l.navigationId&&(r||e(!0),s("soft-navigation",n.navigationId));var i=0;if(n.navigationId&&n.navigationId!==u){var o=E(n.navigationId),c=o&&o.startTime?o.startTime:0;i=Math.max(n.startTime-c,0)}else i=Math.max(n.startTime-b(),0);n.startTime<t.firstHiddenTime&&(l.value=i,l.entries=[n],l.navigationId=n.navigationId||u,e())}})},d=function(){r||(c(h.takeRecords()),a||h.disconnect(),r=!0,e(!0))},h=S("largest-contentful-paint",c,i);h&&(e=p(n,l,ea,i.reportAllChanges),["keydown","click"].forEach(function(e){addEventListener(e,function(){return setTimeout(d,0)},!0)}),C(d),v(function(t){s("back-forward-cache",l.navigationId),T(function(){l.value=performance.now()-t.timeStamp,r=!0,e(!0)})}),a&&S("soft-navigation",function(t){t.forEach(function(t){var n=t.navigationId?E(t.navigationId):null;t.navigationId&&t.navigationId!==l.navigationId&&n&&(n.startTime||0)>o&&(r||e(!0),s("soft-navigation",t.navigationId))})},i))})},el=[800,1800],es=c(),ec=function e(t){document.prerendering?A(function(){return e(t)}):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},eu=function(e,t){var n=w(t=t||{}),i=y("TTFB"),r=p(e,i,el,t.reportAllChanges);ec(function(){if(es){var a=es.responseStart;!(a<=0||a>performance.now())&&(i.value=Math.max(a-b(),0),i.entries=[es],r(!0),v(function(){(r=p(e,i=y("TTFB",0,"back-forward-cache",i.navigationId),el,t.reportAllChanges))(!0)}),n&&S("soft-navigation",function(n){n.forEach(function(n){n.navigationId&&((i=y("TTFB",0,"soft-navigation",n.navigationId)).entries=[n],(r=p(e,i,el,t.reportAllChanges))(!0))})},t))}})},ed=function(e,t){eu(function(t){!function(e){if(e.entries.length){var t=e.entries[0],n=t.activationStart||0,i=Math.max(t.domainLookupStart-n||0,0),r=Math.max(t.connectStart-n||0,0),a=Math.max(t.requestStart-n||0,0);e.attribution={waitingTime:i,dnsTime:r-i,connectionTime:a-r,requestTime:e.value-a,navigationEntry:t}}else e.attribution={waitingTime:0,dnsTime:0,connectionTime:0,requestTime:0}}(t),e(t)},t)}}}]);
//# sourceMappingURL=vendors-node_modules_github_quote-selection_dist_index_js-node_modules_github_session-resume_-c1aa61-c95072e2e2d3.js.map