[data-color-mode="dark"][data-dark-theme="dark"],
[data-color-mode="dark"][data-dark-theme="dark"] ::backdrop,
[data-color-mode="auto"][data-light-theme="dark"],
[data-color-mode="auto"][data-light-theme="dark"] ::backdrop {
  --button-danger-fgColor-rest: #fa5e55;
  --button-primary-bgColor-active: #2e9a40;
  --button-primary-bgColor-disabled: #105823;
  --button-primary-bgColor-hover: #29903b;
  --button-primary-borderColor-disabled: #105823;
  --color-ansi-cyan: #39c5cf;
  --color-ansi-cyan-bright: #56d4dd;
  --control-checked-bgColor-active: #3685f3;
  --control-checked-bgColor-hover: #2a7aef;
  --fgColor-accent: #4493f8;
  --reactionButton-selected-bgColor-hover: #3a8cfd5c;
  --avatar-shadow: 0px 0px 0px 2px #0d1117;
  --avatarStack-fade-bgColor-default: #3d444d;
  --avatarStack-fade-bgColor-muted: #2a313c;
  --bgColor-accent-emphasis: #1f6feb;
  --bgColor-accent-muted: #388bfd1a;
  --bgColor-attention-emphasis: #9e6a03;
  --bgColor-attention-muted: #bb800926;
  --bgColor-danger-emphasis: #da3633;
  --bgColor-danger-muted: #f851491a;
  --bgColor-default: #0d1117;
  --bgColor-disabled: #212830;
  --bgColor-done-emphasis: #8957e5;
  --bgColor-done-muted: #ab7df826;
  --bgColor-emphasis: #3d444d;
  --bgColor-muted: #151b23;
  --bgColor-neutral-emphasis: #656c76;
  --bgColor-neutral-muted: #656c7633;
  --bgColor-severe-emphasis: #bd561d;
  --bgColor-severe-muted: #db6d281a;
  --bgColor-sponsors-emphasis: #bf4b8a;
  --bgColor-sponsors-muted: #db61a21a;
  --bgColor-success-emphasis: #238636;
  --bgColor-success-muted: #2ea04326;
  --bgColor-transparent: #00000000;
  --borderColor-accent-emphasis: #1f6feb;
  --borderColor-accent-muted: #388bfd66;
  --borderColor-attention-emphasis: #9e6a03;
  --borderColor-attention-muted: #bb800966;
  --borderColor-danger-emphasis: #da3633;
  --borderColor-danger-muted: #f8514966;
  --borderColor-default: #3d444d;
  --borderColor-disabled: #656c761a;
  --borderColor-done-emphasis: #8957e5;
  --borderColor-done-muted: #ab7df866;
  --borderColor-emphasis: #656c76;
  --borderColor-severe-emphasis: #bd561d;
  --borderColor-severe-muted: #db6d2866;
  --borderColor-sponsors-emphasis: #bf4b8a;
  --borderColor-sponsors-muted: #db61a266;
  --borderColor-success-emphasis: #238636;
  --borderColor-success-muted: #2ea04366;
  --borderColor-transparent: #00000000;
  --button-danger-bgColor-hover: #b62324;
  --button-danger-iconColor-rest: var(--button-danger-fgColor-rest);
  --button-danger-shadow-selected: 0px 0px 0px 0px #000000;
  --button-default-shadow-resting: 0px 0px 0px 0px #000000;
  --button-inactive-bgColor: #262c36;
  --button-inactive-fgColor: #9198a1;
  --button-invisible-bgColor-disabled: #00000000;
  --button-invisible-borderColor-disabled: #00000000;
  --button-outline-bgColor-active: #0d419d;
  --button-outline-bgColor-rest: #f0f6fc;
  --button-outline-fgColor-disabled: #4493f880;
  --button-outline-fgColor-hover: #58a6ff;
  --button-outline-fgColor-rest: #388bfd;
  --button-outline-shadow-selected: 0px 0px 0px 0px #000000;
  --button-primary-shadow-selected: 0px 0px 0px 0px #000000;
  --button-star-iconColor: #e3b341;
  --buttonCounter-danger-bgColor-rest: #49020233;
  --buttonCounter-default-bgColor-rest: #2f3742;
  --buttonCounter-outline-bgColor-hover: #051d4d33;
  --buttonCounter-outline-bgColor-rest: #051d4d33;
  --buttonCounter-outline-fgColor-disabled: #4493f880;
  --buttonCounter-outline-fgColor-hover: #58a6ff;
  --buttonCounter-outline-fgColor-rest: #388bfd;
  --buttonCounter-primary-bgColor-rest: #04260f33;
  --codeMirror-syntax-fgColor-comment: #656c76;
  --codeMirror-syntax-fgColor-constant: #79c0ff;
  --codeMirror-syntax-fgColor-entity: #d2a8ff;
  --codeMirror-syntax-fgColor-keyword: #ff7b72;
  --codeMirror-syntax-fgColor-storage: #ff7b72;
  --codeMirror-syntax-fgColor-string: #a5d6ff;
  --codeMirror-syntax-fgColor-support: #79c0ff;
  --codeMirror-syntax-fgColor-variable: #ffa657;
  --color-ansi-black: #2f3742;
  --color-ansi-black-bright: #656c76;
  --color-ansi-blue: #58a6ff;
  --color-ansi-blue-bright: #79c0ff;
  --color-ansi-gray: #656c76;
  --color-ansi-green: #3fb950;
  --color-ansi-green-bright: #56d364;
  --color-ansi-magenta: #be8fff;
  --color-ansi-magenta-bright: #d2a8ff;
  --color-ansi-red: #ff7b72;
  --color-ansi-red-bright: #ffa198;
  --color-ansi-white: #f0f6fc;
  --color-ansi-yellow: #d29922;
  --color-ansi-yellow-bright: #e3b341;
  --color-prettylights-syntax-brackethighlighter-angle: #9198a1;
  --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
  --color-prettylights-syntax-carriage-return-bg: #b62324;
  --color-prettylights-syntax-carriage-return-text: #f0f6fc;
  --color-prettylights-syntax-comment: #9198a1;
  --color-prettylights-syntax-constant: #79c0ff;
  --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
  --color-prettylights-syntax-entity: #d2a8ff;
  --color-prettylights-syntax-entity-tag: #7ee787;
  --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
  --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
  --color-prettylights-syntax-keyword: #ff7b72;
  --color-prettylights-syntax-markup-bold: #f0f6fc;
  --color-prettylights-syntax-markup-changed-bg: #5a1e02;
  --color-prettylights-syntax-markup-changed-text: #ffdfb6;
  --color-prettylights-syntax-markup-deleted-bg: #67060c;
  --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
  --color-prettylights-syntax-markup-heading: #1f6feb;
  --color-prettylights-syntax-markup-ignored-bg: #1158c7;
  --color-prettylights-syntax-markup-ignored-text: #f0f6fc;
  --color-prettylights-syntax-markup-inserted-bg: #033a16;
  --color-prettylights-syntax-markup-inserted-text: #aff5b4;
  --color-prettylights-syntax-markup-italic: #f0f6fc;
  --color-prettylights-syntax-markup-list: #f2cc60;
  --color-prettylights-syntax-meta-diff-range: #d2a8ff;
  --color-prettylights-syntax-storage-modifier-import: #f0f6fc;
  --color-prettylights-syntax-string: #a5d6ff;
  --color-prettylights-syntax-string-regexp: #7ee787;
  --color-prettylights-syntax-sublimelinter-gutter-mark: #3d444d;
  --color-prettylights-syntax-variable: #ffa657;
  --contribution-default-bgColor-0: #151b23;
  --contribution-default-bgColor-1: #033a16;
  --contribution-default-bgColor-2: #196c2e;
  --contribution-default-bgColor-3: #2ea043;
  --contribution-default-bgColor-4: #56d364;
  --contribution-default-borderColor-0: #0104090d;
  --contribution-halloween-bgColor-1: #fac68f;
  --contribution-halloween-bgColor-2: #c46212;
  --contribution-halloween-bgColor-3: #984b10;
  --contribution-halloween-bgColor-4: #e3d04f;
  --contribution-winter-bgColor-1: #0c2d6b;
  --contribution-winter-bgColor-2: #1158c7;
  --contribution-winter-bgColor-3: #58a6ff;
  --contribution-winter-bgColor-4: #cae8ff;
  --control-bgColor-active: #2a313c;
  --control-bgColor-hover: #262c36;
  --control-bgColor-rest: #212830;
  --control-borderColor-selected: #f0f6fc;
  --control-checked-borderColor-active: var(--control-checked-bgColor-active);
  --control-checked-borderColor-hover: var(--control-checked-bgColor-hover);
  --control-danger-fgColor-hover: #ff7b72;
  --control-transparent-bgColor-active: #656c7640;
  --control-transparent-bgColor-hover: #656c7633;
  --control-transparent-bgColor-rest: #00000000;
  --control-transparent-bgColor-selected: #656c7633;
  --control-transparent-borderColor-active: #00000000;
  --control-transparent-borderColor-hover: #00000000;
  --control-transparent-borderColor-rest: #00000000;
  --controlTrack-bgColor-active: #2f3742;
  --controlTrack-bgColor-hover: #2a313c;
  --controlTrack-bgColor-rest: #262c36;
  --counter-borderColor: #00000000;
  --data-auburn-color-emphasis: #a86f6b;
  --data-auburn-color-muted: #271817;
  --data-blue-color-emphasis: #0576ff;
  --data-blue-color-muted: #001a47;
  --data-brown-color-emphasis: #94774c;
  --data-brown-color-muted: #241c14;
  --data-coral-color-emphasis: #e1430e;
  --data-coral-color-muted: #351008;
  --data-gray-color-emphasis: #576270;
  --data-gray-color-muted: #1c1c1c;
  --data-green-color-emphasis: #2f6f37;
  --data-green-color-muted: #122117;
  --data-lemon-color-emphasis: #977b0c;
  --data-lemon-color-muted: #291d00;
  --data-lime-color-emphasis: #5f892f;
  --data-lime-color-muted: #141f0f;
  --data-olive-color-emphasis: #7a8321;
  --data-olive-color-muted: #171e0b;
  --data-orange-color-emphasis: #984b10;
  --data-orange-color-muted: #311708;
  --data-pine-color-emphasis: #18915e;
  --data-pine-color-muted: #082119;
  --data-pink-color-emphasis: #d34591;
  --data-pink-color-muted: #2d1524;
  --data-plum-color-emphasis: #b643ef;
  --data-plum-color-muted: #2a0e3f;
  --data-purple-color-emphasis: #975bf1;
  --data-purple-color-muted: #211047;
  --data-red-color-emphasis: #eb3342;
  --data-red-color-muted: #3c0614;
  --data-teal-color-emphasis: #106c70;
  --data-teal-color-muted: #041f25;
  --data-yellow-color-emphasis: #895906;
  --data-yellow-color-muted: #2e1a00;
  --diffBlob-additionNum-bgColor: #3fb9504d;
  --diffBlob-additionWord-bgColor: #2ea04366;
  --diffBlob-deletionNum-bgColor: #f851494d;
  --diffBlob-deletionWord-bgColor: #f8514966;
  --diffBlob-hunkNum-bgColor-rest: #0c2d6b;
  --display-auburn-bgColor-emphasis: #87534f;
  --display-auburn-bgColor-muted: #271817;
  --display-auburn-borderColor-emphasis: #a86f6b;
  --display-auburn-borderColor-muted: #3a2422;
  --display-auburn-fgColor: #bf9592;
  --display-auburn-scale-0: #271817;
  --display-auburn-scale-1: #3a2422;
  --display-auburn-scale-2: #543331;
  --display-auburn-scale-3: #6d4340;
  --display-auburn-scale-4: #87534f;
  --display-auburn-scale-5: #a86f6b;
  --display-auburn-scale-6: #bf9592;
  --display-auburn-scale-7: #c6a19f;
  --display-auburn-scale-8: #d4b7b5;
  --display-auburn-scale-9: #dfcac8;
  --display-blue-bgColor-emphasis: #005bd1;
  --display-blue-bgColor-muted: #001a47;
  --display-blue-borderColor-emphasis: #0576ff;
  --display-blue-borderColor-muted: #002766;
  --display-blue-fgColor: #4da0ff;
  --display-blue-scale-0: #001a47;
  --display-blue-scale-1: #002766;
  --display-blue-scale-2: #00378a;
  --display-blue-scale-3: #0046a8;
  --display-blue-scale-4: #005bd1;
  --display-blue-scale-5: #0576ff;
  --display-blue-scale-6: #4da0ff;
  --display-blue-scale-7: #61adff;
  --display-blue-scale-8: #85c2ff;
  --display-blue-scale-9: #a3d3ff;
  --display-brown-bgColor-emphasis: #755e3e;
  --display-brown-bgColor-muted: #241c14;
  --display-brown-borderColor-emphasis: #94774c;
  --display-brown-borderColor-muted: #342a1d;
  --display-brown-fgColor: #b69a6d;
  --display-brown-scale-0: #241c14;
  --display-brown-scale-1: #342a1d;
  --display-brown-scale-2: #483a28;
  --display-brown-scale-3: #5d4a32;
  --display-brown-scale-4: #755e3e;
  --display-brown-scale-5: #94774c;
  --display-brown-scale-6: #b69a6d;
  --display-brown-scale-7: #bfa77d;
  --display-brown-scale-8: #cdbb98;
  --display-brown-scale-9: #dbceb3;
  --display-coral-bgColor-emphasis: #c31328;
  --display-coral-bgColor-muted: #3c0614;
  --display-coral-borderColor-emphasis: #eb3342;
  --display-coral-borderColor-muted: #58091a;
  --display-coral-fgColor: #f27d83;
  --display-coral-scale-0: #351008;
  --display-coral-scale-1: #51180b;
  --display-coral-scale-2: #72220d;
  --display-coral-scale-3: #902a0e;
  --display-coral-scale-4: #b3350f;
  --display-coral-scale-5: #e1430e;
  --display-coral-scale-6: #f7794b;
  --display-coral-scale-7: #fa8c61;
  --display-coral-scale-8: #fdaa86;
  --display-coral-scale-9: #ffc0a3;
  --display-cyan-bgColor-emphasis: #036a8c;
  --display-cyan-bgColor-muted: #001f29;
  --display-cyan-borderColor-emphasis: #0587b3;
  --display-cyan-borderColor-muted: #002e3d;
  --display-cyan-fgColor: #07ace4;
  --display-cyan-scale-0: #001f29;
  --display-cyan-scale-1: #002e3d;
  --display-cyan-scale-2: #014156;
  --display-cyan-scale-3: #02536f;
  --display-cyan-scale-4: #036a8c;
  --display-cyan-scale-5: #0587b3;
  --display-cyan-scale-6: #07ace4;
  --display-cyan-scale-7: #09b7f1;
  --display-cyan-scale-8: #45cbf7;
  --display-cyan-scale-9: #80dbf9;
  --display-gray-bgColor-emphasis: #576270;
  --display-gray-bgColor-muted: #1c1c1c;
  --display-gray-borderColor-emphasis: #6e7f96;
  --display-gray-borderColor-muted: #2a2b2d;
  --display-gray-fgColor: #92a1b5;
  --display-gray-scale-0: #1c1c1c;
  --display-gray-scale-1: #2a2b2d;
  --display-gray-scale-2: #393d41;
  --display-gray-scale-3: #474e57;
  --display-gray-scale-4: #576270;
  --display-gray-scale-5: #6e7f96;
  --display-gray-scale-6: #92a1b5;
  --display-gray-scale-7: #9babbf;
  --display-gray-scale-8: #b3c0d1;
  --display-gray-scale-9: #c4cfde;
  --display-green-bgColor-emphasis: #2f6f37;
  --display-green-bgColor-muted: #122117;
  --display-green-borderColor-emphasis: #388f3f;
  --display-green-borderColor-muted: #182f1f;
  --display-green-fgColor: #41b445;
  --display-green-scale-0: #122117;
  --display-green-scale-1: #182f1f;
  --display-green-scale-2: #214529;
  --display-green-scale-3: #285830;
  --display-green-scale-4: #2f6f37;
  --display-green-scale-5: #388f3f;
  --display-green-scale-6: #41b445;
  --display-green-scale-7: #46c144;
  --display-green-scale-8: #75d36f;
  --display-green-scale-9: #99e090;
  --display-indigo-bgColor-emphasis: #514ed4;
  --display-indigo-bgColor-muted: #1b183f;
  --display-indigo-borderColor-emphasis: #7070e1;
  --display-indigo-borderColor-muted: #25215f;
  --display-indigo-fgColor: #9899ec;
  --display-indigo-scale-0: #1b183f;
  --display-indigo-scale-1: #25215f;
  --display-indigo-scale-2: #312c90;
  --display-indigo-scale-3: #3935c0;
  --display-indigo-scale-4: #514ed4;
  --display-indigo-scale-5: #7070e1;
  --display-indigo-scale-6: #9899ec;
  --display-indigo-scale-7: #a2a5f1;
  --display-indigo-scale-8: #b7baf6;
  --display-indigo-scale-9: #c8cbf9;
  --display-lemon-bgColor-emphasis: #786008;
  --display-lemon-bgColor-muted: #291d00;
  --display-lemon-borderColor-emphasis: #977b0c;
  --display-lemon-borderColor-muted: #372901;
  --display-lemon-fgColor: #ba9b12;
  --display-lemon-scale-0: #291d00;
  --display-lemon-scale-1: #372901;
  --display-lemon-scale-2: #4f3c02;
  --display-lemon-scale-3: #614c05;
  --display-lemon-scale-4: #786008;
  --display-lemon-scale-5: #977b0c;
  --display-lemon-scale-6: #ba9b12;
  --display-lemon-scale-7: #c4a717;
  --display-lemon-scale-8: #d7bc1d;
  --display-lemon-scale-9: #e3d04f;
  --display-lime-bgColor-emphasis: #496c28;
  --display-lime-bgColor-muted: #141f0f;
  --display-lime-borderColor-emphasis: #5f892f;
  --display-lime-borderColor-muted: #1f3116;
  --display-lime-fgColor: #7dae37;
  --display-lime-scale-0: #141f0f;
  --display-lime-scale-1: #1f3116;
  --display-lime-scale-2: #2c441d;
  --display-lime-scale-3: #375421;
  --display-lime-scale-4: #496c28;
  --display-lime-scale-5: #5f892f;
  --display-lime-scale-6: #7dae37;
  --display-lime-scale-7: #89ba36;
  --display-lime-scale-8: #9fcc3e;
  --display-lime-scale-9: #bcda67;
  --display-olive-bgColor-emphasis: #5e681d;
  --display-olive-bgColor-muted: #171e0b;
  --display-olive-borderColor-emphasis: #7a8321;
  --display-olive-borderColor-muted: #252d10;
  --display-olive-fgColor: #a2a626;
  --display-olive-scale-0: #171e0b;
  --display-olive-scale-1: #252d10;
  --display-olive-scale-2: #374115;
  --display-olive-scale-3: #485219;
  --display-olive-scale-4: #5e681d;
  --display-olive-scale-5: #7a8321;
  --display-olive-scale-6: #a2a626;
  --display-olive-scale-7: #b2af24;
  --display-olive-scale-8: #cbc025;
  --display-olive-scale-9: #e2d04b;
  --display-orange-bgColor-emphasis: #984b10;
  --display-orange-bgColor-muted: #311708;
  --display-orange-borderColor-emphasis: #c46212;
  --display-orange-borderColor-muted: #43200a;
  --display-orange-fgColor: #ed8326;
  --display-orange-scale-0: #311708;
  --display-orange-scale-1: #43200a;
  --display-orange-scale-2: #632f0d;
  --display-orange-scale-3: #7b3c0e;
  --display-orange-scale-4: #984b10;
  --display-orange-scale-5: #c46212;
  --display-orange-scale-6: #ed8326;
  --display-orange-scale-7: #f1933b;
  --display-orange-scale-8: #f6b06a;
  --display-orange-scale-9: #fac68f;
  --display-pine-bgColor-emphasis: #14714c;
  --display-pine-bgColor-muted: #082119;
  --display-pine-borderColor-emphasis: #18915e;
  --display-pine-borderColor-muted: #0b3224;
  --display-pine-fgColor: #1bb673;
  --display-pine-scale-0: #082119;
  --display-pine-scale-1: #0b3224;
  --display-pine-scale-2: #0e4430;
  --display-pine-scale-3: #115a3e;
  --display-pine-scale-4: #14714c;
  --display-pine-scale-5: #18915e;
  --display-pine-scale-6: #1bb673;
  --display-pine-scale-7: #1ac176;
  --display-pine-scale-8: #1bda81;
  --display-pine-scale-9: #3eea97;
  --display-pink-bgColor-emphasis: #ac2f74;
  --display-pink-bgColor-muted: #2d1524;
  --display-pink-borderColor-emphasis: #d34591;
  --display-pink-borderColor-muted: #451c35;
  --display-pink-fgColor: #e57bb2;
  --display-pink-scale-0: #2d1524;
  --display-pink-scale-1: #451c35;
  --display-pink-scale-2: #65244a;
  --display-pink-scale-3: #842a5d;
  --display-pink-scale-4: #ac2f74;
  --display-pink-scale-5: #d34591;
  --display-pink-scale-6: #e57bb2;
  --display-pink-scale-7: #ec8dbd;
  --display-pink-scale-8: #f4a9cd;
  --display-pink-scale-9: #f9bed9;
  --display-plum-bgColor-emphasis: #9518d8;
  --display-plum-bgColor-muted: #2a0e3f;
  --display-plum-borderColor-emphasis: #b643ef;
  --display-plum-borderColor-muted: #40125e;
  --display-plum-fgColor: #d07ef7;
  --display-plum-scale-0: #2a0e3f;
  --display-plum-scale-1: #40125e;
  --display-plum-scale-2: #5c1688;
  --display-plum-scale-3: #7517ab;
  --display-plum-scale-4: #9518d8;
  --display-plum-scale-5: #b643ef;
  --display-plum-scale-6: #d07ef7;
  --display-plum-scale-7: #d889fa;
  --display-plum-scale-8: #e4a5fd;
  --display-plum-scale-9: #edbdff;
  --display-purple-bgColor-emphasis: #7730e8;
  --display-purple-bgColor-muted: #211047;
  --display-purple-borderColor-emphasis: #975bf1;
  --display-purple-borderColor-muted: #31146b;
  --display-purple-fgColor: #b687f7;
  --display-purple-scale-0: #211047;
  --display-purple-scale-1: #31146b;
  --display-purple-scale-2: #481a9e;
  --display-purple-scale-3: #5b1cca;
  --display-purple-scale-4: #7730e8;
  --display-purple-scale-5: #975bf1;
  --display-purple-scale-6: #b687f7;
  --display-purple-scale-7: #c398fb;
  --display-purple-scale-8: #d2affd;
  --display-purple-scale-9: #e1c7ff;
  --display-red-bgColor-emphasis: #c31328;
  --display-red-bgColor-muted: #3c0614;
  --display-red-borderColor-emphasis: #eb3342;
  --display-red-borderColor-muted: #58091a;
  --display-red-fgColor: #f27d83;
  --display-red-scale-0: #3c0614;
  --display-red-scale-1: #58091a;
  --display-red-scale-2: #790c20;
  --display-red-scale-3: #990f24;
  --display-red-scale-4: #c31328;
  --display-red-scale-5: #eb3342;
  --display-red-scale-6: #f27d83;
  --display-red-scale-7: #f48b8d;
  --display-red-scale-8: #f7adab;
  --display-red-scale-9: #f9c1be;
  --display-teal-bgColor-emphasis: #106c70;
  --display-teal-bgColor-muted: #041f25;
  --display-teal-borderColor-emphasis: #158a8a;
  --display-teal-borderColor-muted: #073036;
  --display-teal-fgColor: #1cb0ab;
  --display-teal-scale-0: #041f25;
  --display-teal-scale-1: #073036;
  --display-teal-scale-2: #0a464d;
  --display-teal-scale-3: #0c555a;
  --display-teal-scale-4: #106c70;
  --display-teal-scale-5: #158a8a;
  --display-teal-scale-6: #1cb0ab;
  --display-teal-scale-7: #1fbdb2;
  --display-teal-scale-8: #24d6c4;
  --display-teal-scale-9: #5fe3d1;
  --display-yellow-bgColor-emphasis: #895906;
  --display-yellow-bgColor-muted: #2e1a00;
  --display-yellow-borderColor-emphasis: #aa7109;
  --display-yellow-borderColor-muted: #3d2401;
  --display-yellow-fgColor: #d3910d;
  --display-yellow-scale-0: #2e1a00;
  --display-yellow-scale-1: #3d2401;
  --display-yellow-scale-2: #5a3702;
  --display-yellow-scale-3: #6d4403;
  --display-yellow-scale-4: #895906;
  --display-yellow-scale-5: #aa7109;
  --display-yellow-scale-6: #d3910d;
  --display-yellow-scale-7: #df9e11;
  --display-yellow-scale-8: #edb431;
  --display-yellow-scale-9: #f0ca6a;
  --fgColor-attention: #d29922;
  --fgColor-danger: #f85149;
  --fgColor-default: #f0f6fc;
  --fgColor-disabled: #656c76;
  --fgColor-done: #ab7df8;
  --fgColor-link: var(--fgColor-accent);
  --fgColor-muted: #9198a1;
  --fgColor-neutral: #9198a1;
  --fgColor-severe: #db6d28;
  --fgColor-sponsors: #db61a2;
  --fgColor-success: #3fb950;
  --header-bgColor: #151b23f2;
  --header-borderColor-divider: #656c76;
  --header-fgColor-logo: #f0f6fc;
  --headerSearch-bgColor: #0d1117;
  --headerSearch-borderColor: #2a313c;
  --highlight-neutral-bgColor: #d2992266;
  --label-auburn-bgColor-active: #543331;
  --label-auburn-bgColor-hover: #3a2422;
  --label-auburn-bgColor-rest: #271817;
  --label-auburn-fgColor-active: #d4b7b5;
  --label-auburn-fgColor-hover: #c6a19f;
  --label-auburn-fgColor-rest: #bf9592;
  --label-blue-bgColor-active: #00378a;
  --label-blue-bgColor-hover: #002766;
  --label-blue-bgColor-rest: #001a47;
  --label-blue-fgColor-active: #85c2ff;
  --label-blue-fgColor-hover: #61adff;
  --label-blue-fgColor-rest: #4da0ff;
  --label-brown-bgColor-active: #483a28;
  --label-brown-bgColor-hover: #342a1d;
  --label-brown-bgColor-rest: #241c14;
  --label-brown-fgColor-active: #cdbb98;
  --label-brown-fgColor-hover: #bfa77d;
  --label-brown-fgColor-rest: #b69a6d;
  --label-coral-bgColor-active: #72220d;
  --label-coral-bgColor-hover: #51180b;
  --label-coral-bgColor-rest: #351008;
  --label-coral-fgColor-active: #fdaa86;
  --label-coral-fgColor-hover: #fa8c61;
  --label-coral-fgColor-rest: #f7794b;
  --label-cyan-bgColor-active: #014156;
  --label-cyan-bgColor-hover: #002e3d;
  --label-cyan-bgColor-rest: #001f29;
  --label-cyan-fgColor-active: #45cbf7;
  --label-cyan-fgColor-hover: #09b7f1;
  --label-cyan-fgColor-rest: #07ace4;
  --label-gray-bgColor-active: #393d41;
  --label-gray-bgColor-hover: #2a2b2d;
  --label-gray-bgColor-rest: #1c1c1c;
  --label-gray-fgColor-active: #b3c0d1;
  --label-gray-fgColor-hover: #9babbf;
  --label-gray-fgColor-rest: #92a1b5;
  --label-green-bgColor-active: #214529;
  --label-green-bgColor-hover: #182f1f;
  --label-green-bgColor-rest: #122117;
  --label-green-fgColor-active: #75d36f;
  --label-green-fgColor-hover: #46c144;
  --label-green-fgColor-rest: #41b445;
  --label-indigo-bgColor-active: #312c90;
  --label-indigo-bgColor-hover: #25215f;
  --label-indigo-bgColor-rest: #1b183f;
  --label-indigo-fgColor-active: #b7baf6;
  --label-indigo-fgColor-hover: #a2a5f1;
  --label-indigo-fgColor-rest: #9899ec;
  --label-lemon-bgColor-active: #4f3c02;
  --label-lemon-bgColor-hover: #372901;
  --label-lemon-bgColor-rest: #291d00;
  --label-lemon-fgColor-active: #d7bc1d;
  --label-lemon-fgColor-hover: #c4a717;
  --label-lemon-fgColor-rest: #ba9b12;
  --label-lime-bgColor-active: #2c441d;
  --label-lime-bgColor-hover: #1f3116;
  --label-lime-bgColor-rest: #141f0f;
  --label-lime-fgColor-active: #9fcc3e;
  --label-lime-fgColor-hover: #89ba36;
  --label-lime-fgColor-rest: #7dae37;
  --label-olive-bgColor-active: #374115;
  --label-olive-bgColor-hover: #252d10;
  --label-olive-bgColor-rest: #171e0b;
  --label-olive-fgColor-active: #cbc025;
  --label-olive-fgColor-hover: #b2af24;
  --label-olive-fgColor-rest: #a2a626;
  --label-orange-bgColor-active: #632f0d;
  --label-orange-bgColor-hover: #43200a;
  --label-orange-bgColor-rest: #311708;
  --label-orange-fgColor-active: #f6b06a;
  --label-orange-fgColor-hover: #f1933b;
  --label-orange-fgColor-rest: #ed8326;
  --label-pine-bgColor-active: #0e4430;
  --label-pine-bgColor-hover: #0b3224;
  --label-pine-bgColor-rest: #082119;
  --label-pine-fgColor-active: #1bda81;
  --label-pine-fgColor-hover: #1ac176;
  --label-pine-fgColor-rest: #1bb673;
  --label-pink-bgColor-active: #65244a;
  --label-pink-bgColor-hover: #451c35;
  --label-pink-bgColor-rest: #2d1524;
  --label-pink-fgColor-active: #f4a9cd;
  --label-pink-fgColor-hover: #ec8dbd;
  --label-pink-fgColor-rest: #e57bb2;
  --label-plum-bgColor-active: #5c1688;
  --label-plum-bgColor-hover: #40125e;
  --label-plum-bgColor-rest: #2a0e3f;
  --label-plum-fgColor-active: #e4a5fd;
  --label-plum-fgColor-hover: #d889fa;
  --label-plum-fgColor-rest: #d07ef7;
  --label-purple-bgColor-active: #481a9e;
  --label-purple-bgColor-hover: #31146b;
  --label-purple-bgColor-rest: #211047;
  --label-purple-fgColor-active: #d2affd;
  --label-purple-fgColor-hover: #c398fb;
  --label-purple-fgColor-rest: #b687f7;
  --label-red-bgColor-active: #790c20;
  --label-red-bgColor-hover: #58091a;
  --label-red-bgColor-rest: #3c0614;
  --label-red-fgColor-active: #f7adab;
  --label-red-fgColor-hover: #f48b8d;
  --label-red-fgColor-rest: #f27d83;
  --label-teal-bgColor-active: #0a464d;
  --label-teal-bgColor-hover: #073036;
  --label-teal-bgColor-rest: #041f25;
  --label-teal-fgColor-active: #24d6c4;
  --label-teal-fgColor-hover: #1fbdb2;
  --label-teal-fgColor-rest: #1cb0ab;
  --label-yellow-bgColor-active: #5a3702;
  --label-yellow-bgColor-hover: #3d2401;
  --label-yellow-bgColor-rest: #2e1a00;
  --label-yellow-fgColor-active: #edb431;
  --label-yellow-fgColor-hover: #df9e11;
  --label-yellow-fgColor-rest: #d3910d;
  --menu-bgColor-active: #151b23;
  --overlay-backdrop-bgColor: #21283066;
  --reactionButton-selected-bgColor-rest: #388bfd33;
  --reactionButton-selected-fgColor-hover: #79c0ff;
  --selectMenu-bgColor-active: #0c2d6b;
  --sideNav-bgColor-selected: #212830;
  --skeletonLoader-bgColor: #656c7633;
  --timelineBadge-bgColor: #212830;
  --topicTag-borderColor: #00000000;
  --underlineNav-borderColor-active: #f78166;
  --avatar-bgColor: #ffffff1a;
  --bgColor-black: #010409;
  --bgColor-closed-emphasis: var(--bgColor-danger-emphasis);
  --bgColor-closed-muted: var(--bgColor-danger-muted);
  --bgColor-inset: #010409;
  --bgColor-inverse: #ffffff;
  --bgColor-open-emphasis: var(--bgColor-success-emphasis);
  --bgColor-open-muted: var(--bgColor-success-muted);
  --bgColor-upsell-emphasis: var(--bgColor-done-emphasis);
  --bgColor-upsell-muted: var(--bgColor-done-muted);
  --bgColor-white: #ffffff;
  --border-accent-emphasis: 0.0625rem solid #1f6feb;
  --border-accent-muted: 0.0625rem solid #388bfd66;
  --border-attention-emphasis: 0.0625rem solid #9e6a03;
  --border-attention-muted: 0.0625rem solid #bb800966;
  --border-danger-emphasis: 0.0625rem solid #da3633;
  --border-danger-muted: 0.0625rem solid #f8514966;
  --border-default: 0.0625rem solid #3d444d;
  --border-disabled: 0.0625rem solid #656c761a;
  --border-done-emphasis: 0.0625rem solid #8957e5;
  --border-done-muted: 0.0625rem solid #ab7df866;
  --border-emphasis: 0.0625rem solid #656c76;
  --border-severe-emphasis: 0.0625rem solid #bd561d;
  --border-severe-muted: 0.0625rem solid #db6d2866;
  --border-sponsors-emphasis: 0.0625rem solid #bf4b8a;
  --border-sponsors-muted: 0.0625rem solid #db61a266;
  --border-success-emphasis: 0.0625rem solid #238636;
  --border-success-muted: 0.0625rem solid #2ea04366;
  --border-transparent: 0.0625rem solid #00000000;
  --borderColor-closed-emphasis: var(--borderColor-danger-emphasis);
  --borderColor-closed-muted: var(--borderColor-danger-muted);
  --borderColor-muted: #3d444db3;
  --borderColor-neutral-emphasis: var(--borderColor-emphasis);
  --borderColor-open-emphasis: var(--borderColor-success-emphasis);
  --borderColor-open-muted: var(--borderColor-success-muted);
  --borderColor-translucent: #ffffff26;
  --borderColor-upsell-emphasis: var(--borderColor-done-emphasis);
  --borderColor-upsell-muted: var(--borderColor-done-muted);
  --button-danger-bgColor-active: var(--bgColor-danger-emphasis);
  --button-danger-bgColor-rest: var(--control-bgColor-rest);
  --button-danger-fgColor-active: #ffffff;
  --button-danger-fgColor-disabled: #f8514980;
  --button-danger-fgColor-hover: #ffffff;
  --button-danger-iconColor-hover: #ffffff;
  --button-default-bgColor-active: var(--control-bgColor-active);
  --button-default-bgColor-hover: var(--control-bgColor-hover);
  --button-default-bgColor-rest: var(--control-bgColor-rest);
  --button-default-bgColor-selected: var(--control-bgColor-active);
  --button-invisible-bgColor-active: var(--control-transparent-bgColor-active);
  --button-invisible-bgColor-hover: var(--control-transparent-bgColor-hover);
  --button-invisible-bgColor-rest: var(--control-transparent-bgColor-rest);
  --button-invisible-borderColor-hover: var(--control-transparent-borderColor-hover);
  --button-invisible-borderColor-rest: var(--control-transparent-borderColor-rest);
  --button-invisible-iconColor-hover: var(--fgColor-muted);
  --button-invisible-iconColor-rest: var(--fgColor-muted);
  --button-outline-bgColor-hover: var(--control-bgColor-hover);
  --button-outline-fgColor-active: #ffffff;
  --button-primary-bgColor-rest: var(--bgColor-success-emphasis);
  --button-primary-fgColor-disabled: #ffffff66;
  --buttonCounter-danger-bgColor-disabled: #da36330d;
  --buttonCounter-danger-bgColor-hover: #ffffff33;
  --buttonCounter-danger-fgColor-disabled: #f8514980;
  --buttonCounter-danger-fgColor-hover: #ffffff;
  --buttonCounter-danger-fgColor-rest: var(--fgColor-danger);
  --buttonCounter-invisible-bgColor-rest: var(--bgColor-neutral-muted);
  --buttonCounter-outline-bgColor-disabled: #1f6feb0d;
  --card-bgColor: var(--bgColor-muted);
  --codeMirror-activeline-bgColor: var(--bgColor-neutral-muted);
  --codeMirror-bgColor: var(--bgColor-default);
  --codeMirror-cursor-fgColor: var(--fgColor-default);
  --codeMirror-fgColor: var(--fgColor-default);
  --codeMirror-gutterMarker-fgColor-default: var(--bgColor-default);
  --codeMirror-gutterMarker-fgColor-muted: var(--fgColor-muted);
  --codeMirror-gutters-bgColor: var(--bgColor-default);
  --codeMirror-lineNumber-fgColor: var(--fgColor-muted);
  --codeMirror-lines-bgColor: var(--bgColor-default);
  --codeMirror-matchingBracket-fgColor: var(--fgColor-default);
  --codeMirror-selection-bgColor: var(--borderColor-accent-muted);
  --color-ansi-white-bright: #ffffff;
  --contribution-default-borderColor-1: var(--contribution-default-borderColor-0);
  --contribution-default-borderColor-2: var(--contribution-default-borderColor-0);
  --contribution-default-borderColor-3: var(--contribution-default-borderColor-0);
  --contribution-default-borderColor-4: var(--contribution-default-borderColor-0);
  --control-bgColor-disabled: var(--bgColor-disabled);
  --control-bgColor-selected: var(--control-bgColor-rest);
  --control-borderColor-danger: var(--borderColor-danger-emphasis);
  --control-borderColor-disabled: var(--borderColor-disabled);
  --control-borderColor-emphasis: var(--borderColor-emphasis);
  --control-borderColor-rest: var(--borderColor-default);
  --control-borderColor-success: var(--borderColor-success-emphasis);
  --control-borderColor-warning: var(--borderColor-attention-emphasis);
  --control-checked-bgColor-disabled: var(--fgColor-disabled);
  --control-checked-bgColor-rest: var(--bgColor-accent-emphasis);
  --control-checked-fgColor-disabled: #010409;
  --control-danger-bgColor-active: #f8514966;
  --control-danger-bgColor-hover: var(--bgColor-danger-muted);
  --control-danger-fgColor-rest: var(--fgColor-danger);
  --control-fgColor-disabled: var(--fgColor-disabled);
  --control-fgColor-placeholder: var(--fgColor-muted);
  --control-fgColor-rest: var(--fgColor-default);
  --control-iconColor-rest: var(--fgColor-muted);
  --control-transparent-bgColor-disabled: var(--bgColor-disabled);
  --controlKnob-bgColor-checked: #ffffff;
  --controlTrack-bgColor-disabled: var(--fgColor-disabled);
  --controlTrack-borderColor-disabled: var(--fgColor-disabled);
  --controlTrack-borderColor-rest: var(--borderColor-default);
  --controlTrack-fgColor-rest: var(--fgColor-muted);
  --counter-bgColor-emphasis: var(--bgColor-neutral-emphasis);
  --counter-bgColor-muted: var(--bgColor-neutral-muted);
  --diffBlob-additionLine-bgColor: var(--bgColor-success-muted);
  --diffBlob-additionLine-fgColor: var(--fgColor-default);
  --diffBlob-additionNum-fgColor: var(--fgColor-default);
  --diffBlob-additionWord-fgColor: var(--fgColor-default);
  --diffBlob-deletionLine-bgColor: var(--bgColor-danger-muted);
  --diffBlob-deletionLine-fgColor: var(--fgColor-default);
  --diffBlob-deletionNum-fgColor: var(--fgColor-default);
  --diffBlob-deletionWord-fgColor: var(--fgColor-default);
  --diffBlob-emptyLine-bgColor: var(--bgColor-muted);
  --diffBlob-emptyNum-bgColor: var(--bgColor-muted);
  --diffBlob-expander-iconColor: var(--fgColor-muted);
  --diffBlob-hunkLine-bgColor: var(--bgColor-accent-muted);
  --diffBlob-hunkLine-fgColor: var(--fgColor-muted);
  --diffBlob-hunkNum-bgColor-hover: var(--bgColor-accent-emphasis);
  --diffBlob-hunkNum-fgColor-rest: var(--fgColor-default);
  --fgColor-black: #010409;
  --fgColor-closed: var(--fgColor-danger);
  --fgColor-onEmphasis: #ffffff;
  --fgColor-onInverse: #010409;
  --fgColor-open: var(--fgColor-success);
  --fgColor-upsell: var(--fgColor-done);
  --fgColor-white: #ffffff;
  --focus-outlineColor: var(--borderColor-accent-emphasis);
  --header-fgColor-default: #ffffffb3;
  --overlay-bgColor: #010409;
  --page-header-bgColor: var(--bgColor-default);
  --reactionButton-selected-fgColor-rest: var(--fgColor-link);
  --selectMenu-borderColor: var(--borderColor-default);
  --selection-bgColor: #1f6febb3;
  --shadow-floating-legacy: 0px 6px 12px -3px #01040966, 0px 6px 18px 0px #01040966;
  --shadow-inset: inset 0px 1px 0px 0px #0104093d;
  --shadow-resting-medium: 0px 1px 1px 0px #01040966, 0px 3px 6px 0px #010409cc;
  --shadow-resting-small: 0px 1px 1px 0px #01040999, 0px 1px 3px 0px #01040999;
  --shadow-resting-xsmall: 0px 1px 1px 0px #010409cc;
  --tooltip-bgColor: var(--bgColor-emphasis);
  --treeViewItem-leadingVisual-iconColor-rest: var(--fgColor-muted);
  --underlineNav-iconColor-rest: var(--fgColor-muted);
  --avatar-borderColor: var(--borderColor-translucent);
  --border-closed-emphasis: var(--border-danger-emphasis);
  --border-closed-muted: var(--border-danger-muted);
  --border-muted: 0.0625rem solid #3d444db3;
  --border-neutral-emphasis: 0.0625rem solid #656c76;
  --border-open-emphasis: var(--border-success-emphasis);
  --border-open-muted: var(--border-success-muted);
  --border-upsell-emphasis: 0.0625rem solid #8957e5;
  --border-upsell-muted: 0.0625rem solid #ab7df866;
  --borderColor-neutral-muted: var(--borderColor-muted);
  --button-danger-bgColor-disabled: var(--control-bgColor-disabled);
  --button-danger-borderColor-rest: var(--control-borderColor-rest);
  --button-default-bgColor-disabled: var(--control-bgColor-disabled);
  --button-default-borderColor-disabled: var(--control-borderColor-disabled);
  --button-default-borderColor-rest: var(--control-borderColor-rest);
  --button-default-fgColor-rest: var(--control-fgColor-rest);
  --button-invisible-fgColor-active: var(--control-fgColor-rest);
  --button-invisible-fgColor-disabled: var(--control-fgColor-disabled);
  --button-invisible-fgColor-hover: var(--control-fgColor-rest);
  --button-invisible-fgColor-rest: var(--control-fgColor-rest);
  --button-invisible-iconColor-disabled: var(--control-fgColor-disabled);
  --button-outline-bgColor-disabled: var(--control-bgColor-disabled);
  --button-primary-borderColor-rest: var(--borderColor-translucent);
  --button-primary-fgColor-rest: var(--fgColor-white);
  --button-primary-iconColor-rest: var(--fgColor-white);
  --control-checked-borderColor-disabled: var(--control-checked-bgColor-disabled);
  --control-checked-borderColor-rest: var(--control-checked-bgColor-rest);
  --control-checked-fgColor-rest: var(--fgColor-onEmphasis);
  --controlKnob-bgColor-disabled: var(--control-bgColor-disabled);
  --controlKnob-bgColor-rest: var(--bgColor-inset);
  --controlKnob-borderColor-checked: var(--control-checked-bgColor-rest);
  --controlKnob-borderColor-disabled: var(--control-bgColor-disabled);
  --controlKnob-borderColor-rest: var(--control-borderColor-emphasis);
  --controlTrack-fgColor-disabled: var(--fgColor-onEmphasis);
  --diffBlob-hunkNum-fgColor-hover: var(--fgColor-onEmphasis);
  --focus-outline: 2px solid #1f6feb;
  --overlay-borderColor: var(--borderColor-muted);
  --tooltip-fgColor: var(--fgColor-onEmphasis);
  --border-neutral-muted: 0.0625rem solid #3d444db3;
  --button-danger-borderColor-hover: var(--button-primary-borderColor-rest);
  --button-default-borderColor-active: var(--button-default-borderColor-rest);
  --button-default-borderColor-hover: var(--button-default-borderColor-rest);
  --button-primary-borderColor-active: var(--button-primary-borderColor-rest);
  --button-primary-borderColor-hover: var(--button-primary-borderColor-rest);
  --shadow-floating-large: 0px 0px 0px 1px #3d444d, 0px 24px 48px 0px #010409;
  --shadow-floating-medium: 0px 0px 0px 1px #3d444d, 0px 8px 16px -4px #01040966, 0px 4px 32px -4px #01040966, 0px 24px 48px -12px #01040966, 0px 48px 96px -24px #01040966;
  --shadow-floating-small: 0px 0px 0px 1px #3d444d, 0px 6px 12px -3px #01040966, 0px 6px 18px 0px #01040966;
  --shadow-floating-xlarge: 0px 0px 0px 1px #3d444d, 0px 32px 64px 0px #010409;
  --underlineNav-borderColor-hover: var(--borderColor-neutral-muted);
  --button-danger-borderColor-active: var(--button-danger-borderColor-hover);
  --button-outline-borderColor-hover: var(--button-default-borderColor-hover);
  --button-outline-borderColor-active: var(--button-outline-borderColor-hover);
}
@media (prefers-color-scheme: dark) {
  [data-color-mode][data-color-mode="auto"][data-dark-theme="dark"],
  [data-color-mode][data-color-mode="auto"][data-dark-theme="dark"] ::backdrop {
    --button-danger-fgColor-rest: #fa5e55;
    --button-primary-bgColor-active: #2e9a40;
    --button-primary-bgColor-disabled: #105823;
    --button-primary-bgColor-hover: #29903b;
    --button-primary-borderColor-disabled: #105823;
    --color-ansi-cyan: #39c5cf;
    --color-ansi-cyan-bright: #56d4dd;
    --control-checked-bgColor-active: #3685f3;
    --control-checked-bgColor-hover: #2a7aef;
    --fgColor-accent: #4493f8;
    --reactionButton-selected-bgColor-hover: #3a8cfd5c;
    --avatar-shadow: 0px 0px 0px 2px #0d1117;
    --avatarStack-fade-bgColor-default: #3d444d;
    --avatarStack-fade-bgColor-muted: #2a313c;
    --bgColor-accent-emphasis: #1f6feb;
    --bgColor-accent-muted: #388bfd1a;
    --bgColor-attention-emphasis: #9e6a03;
    --bgColor-attention-muted: #bb800926;
    --bgColor-danger-emphasis: #da3633;
    --bgColor-danger-muted: #f851491a;
    --bgColor-default: #0d1117;
    --bgColor-disabled: #212830;
    --bgColor-done-emphasis: #8957e5;
    --bgColor-done-muted: #ab7df826;
    --bgColor-emphasis: #3d444d;
    --bgColor-muted: #151b23;
    --bgColor-neutral-emphasis: #656c76;
    --bgColor-neutral-muted: #656c7633;
    --bgColor-severe-emphasis: #bd561d;
    --bgColor-severe-muted: #db6d281a;
    --bgColor-sponsors-emphasis: #bf4b8a;
    --bgColor-sponsors-muted: #db61a21a;
    --bgColor-success-emphasis: #238636;
    --bgColor-success-muted: #2ea04326;
    --bgColor-transparent: #00000000;
    --borderColor-accent-emphasis: #1f6feb;
    --borderColor-accent-muted: #388bfd66;
    --borderColor-attention-emphasis: #9e6a03;
    --borderColor-attention-muted: #bb800966;
    --borderColor-danger-emphasis: #da3633;
    --borderColor-danger-muted: #f8514966;
    --borderColor-default: #3d444d;
    --borderColor-disabled: #656c761a;
    --borderColor-done-emphasis: #8957e5;
    --borderColor-done-muted: #ab7df866;
    --borderColor-emphasis: #656c76;
    --borderColor-severe-emphasis: #bd561d;
    --borderColor-severe-muted: #db6d2866;
    --borderColor-sponsors-emphasis: #bf4b8a;
    --borderColor-sponsors-muted: #db61a266;
    --borderColor-success-emphasis: #238636;
    --borderColor-success-muted: #2ea04366;
    --borderColor-transparent: #00000000;
    --button-danger-bgColor-hover: #b62324;
    --button-danger-iconColor-rest: var(--button-danger-fgColor-rest);
    --button-danger-shadow-selected: 0px 0px 0px 0px #000000;
    --button-default-shadow-resting: 0px 0px 0px 0px #000000;
    --button-inactive-bgColor: #262c36;
    --button-inactive-fgColor: #9198a1;
    --button-invisible-bgColor-disabled: #00000000;
    --button-invisible-borderColor-disabled: #00000000;
    --button-outline-bgColor-active: #0d419d;
    --button-outline-bgColor-rest: #f0f6fc;
    --button-outline-fgColor-disabled: #4493f880;
    --button-outline-fgColor-hover: #58a6ff;
    --button-outline-fgColor-rest: #388bfd;
    --button-outline-shadow-selected: 0px 0px 0px 0px #000000;
    --button-primary-shadow-selected: 0px 0px 0px 0px #000000;
    --button-star-iconColor: #e3b341;
    --buttonCounter-danger-bgColor-rest: #49020233;
    --buttonCounter-default-bgColor-rest: #2f3742;
    --buttonCounter-outline-bgColor-hover: #051d4d33;
    --buttonCounter-outline-bgColor-rest: #051d4d33;
    --buttonCounter-outline-fgColor-disabled: #4493f880;
    --buttonCounter-outline-fgColor-hover: #58a6ff;
    --buttonCounter-outline-fgColor-rest: #388bfd;
    --buttonCounter-primary-bgColor-rest: #04260f33;
    --codeMirror-syntax-fgColor-comment: #656c76;
    --codeMirror-syntax-fgColor-constant: #79c0ff;
    --codeMirror-syntax-fgColor-entity: #d2a8ff;
    --codeMirror-syntax-fgColor-keyword: #ff7b72;
    --codeMirror-syntax-fgColor-storage: #ff7b72;
    --codeMirror-syntax-fgColor-string: #a5d6ff;
    --codeMirror-syntax-fgColor-support: #79c0ff;
    --codeMirror-syntax-fgColor-variable: #ffa657;
    --color-ansi-black: #2f3742;
    --color-ansi-black-bright: #656c76;
    --color-ansi-blue: #58a6ff;
    --color-ansi-blue-bright: #79c0ff;
    --color-ansi-gray: #656c76;
    --color-ansi-green: #3fb950;
    --color-ansi-green-bright: #56d364;
    --color-ansi-magenta: #be8fff;
    --color-ansi-magenta-bright: #d2a8ff;
    --color-ansi-red: #ff7b72;
    --color-ansi-red-bright: #ffa198;
    --color-ansi-white: #f0f6fc;
    --color-ansi-yellow: #d29922;
    --color-ansi-yellow-bright: #e3b341;
    --color-prettylights-syntax-brackethighlighter-angle: #9198a1;
    --color-prettylights-syntax-brackethighlighter-unmatched: #f85149;
    --color-prettylights-syntax-carriage-return-bg: #b62324;
    --color-prettylights-syntax-carriage-return-text: #f0f6fc;
    --color-prettylights-syntax-comment: #9198a1;
    --color-prettylights-syntax-constant: #79c0ff;
    --color-prettylights-syntax-constant-other-reference-link: #a5d6ff;
    --color-prettylights-syntax-entity: #d2a8ff;
    --color-prettylights-syntax-entity-tag: #7ee787;
    --color-prettylights-syntax-invalid-illegal-bg: #8e1519;
    --color-prettylights-syntax-invalid-illegal-text: #f0f6fc;
    --color-prettylights-syntax-keyword: #ff7b72;
    --color-prettylights-syntax-markup-bold: #f0f6fc;
    --color-prettylights-syntax-markup-changed-bg: #5a1e02;
    --color-prettylights-syntax-markup-changed-text: #ffdfb6;
    --color-prettylights-syntax-markup-deleted-bg: #67060c;
    --color-prettylights-syntax-markup-deleted-text: #ffdcd7;
    --color-prettylights-syntax-markup-heading: #1f6feb;
    --color-prettylights-syntax-markup-ignored-bg: #1158c7;
    --color-prettylights-syntax-markup-ignored-text: #f0f6fc;
    --color-prettylights-syntax-markup-inserted-bg: #033a16;
    --color-prettylights-syntax-markup-inserted-text: #aff5b4;
    --color-prettylights-syntax-markup-italic: #f0f6fc;
    --color-prettylights-syntax-markup-list: #f2cc60;
    --color-prettylights-syntax-meta-diff-range: #d2a8ff;
    --color-prettylights-syntax-storage-modifier-import: #f0f6fc;
    --color-prettylights-syntax-string: #a5d6ff;
    --color-prettylights-syntax-string-regexp: #7ee787;
    --color-prettylights-syntax-sublimelinter-gutter-mark: #3d444d;
    --color-prettylights-syntax-variable: #ffa657;
    --contribution-default-bgColor-0: #151b23;
    --contribution-default-bgColor-1: #033a16;
    --contribution-default-bgColor-2: #196c2e;
    --contribution-default-bgColor-3: #2ea043;
    --contribution-default-bgColor-4: #56d364;
    --contribution-default-borderColor-0: #0104090d;
    --contribution-halloween-bgColor-1: #fac68f;
    --contribution-halloween-bgColor-2: #c46212;
    --contribution-halloween-bgColor-3: #984b10;
    --contribution-halloween-bgColor-4: #e3d04f;
    --contribution-winter-bgColor-1: #0c2d6b;
    --contribution-winter-bgColor-2: #1158c7;
    --contribution-winter-bgColor-3: #58a6ff;
    --contribution-winter-bgColor-4: #cae8ff;
    --control-bgColor-active: #2a313c;
    --control-bgColor-hover: #262c36;
    --control-bgColor-rest: #212830;
    --control-borderColor-selected: #f0f6fc;
    --control-checked-borderColor-active: var(--control-checked-bgColor-active);
    --control-checked-borderColor-hover: var(--control-checked-bgColor-hover);
    --control-danger-fgColor-hover: #ff7b72;
    --control-transparent-bgColor-active: #656c7640;
    --control-transparent-bgColor-hover: #656c7633;
    --control-transparent-bgColor-rest: #00000000;
    --control-transparent-bgColor-selected: #656c7633;
    --control-transparent-borderColor-active: #00000000;
    --control-transparent-borderColor-hover: #00000000;
    --control-transparent-borderColor-rest: #00000000;
    --controlTrack-bgColor-active: #2f3742;
    --controlTrack-bgColor-hover: #2a313c;
    --controlTrack-bgColor-rest: #262c36;
    --counter-borderColor: #00000000;
    --data-auburn-color-emphasis: #a86f6b;
    --data-auburn-color-muted: #271817;
    --data-blue-color-emphasis: #0576ff;
    --data-blue-color-muted: #001a47;
    --data-brown-color-emphasis: #94774c;
    --data-brown-color-muted: #241c14;
    --data-coral-color-emphasis: #e1430e;
    --data-coral-color-muted: #351008;
    --data-gray-color-emphasis: #576270;
    --data-gray-color-muted: #1c1c1c;
    --data-green-color-emphasis: #2f6f37;
    --data-green-color-muted: #122117;
    --data-lemon-color-emphasis: #977b0c;
    --data-lemon-color-muted: #291d00;
    --data-lime-color-emphasis: #5f892f;
    --data-lime-color-muted: #141f0f;
    --data-olive-color-emphasis: #7a8321;
    --data-olive-color-muted: #171e0b;
    --data-orange-color-emphasis: #984b10;
    --data-orange-color-muted: #311708;
    --data-pine-color-emphasis: #18915e;
    --data-pine-color-muted: #082119;
    --data-pink-color-emphasis: #d34591;
    --data-pink-color-muted: #2d1524;
    --data-plum-color-emphasis: #b643ef;
    --data-plum-color-muted: #2a0e3f;
    --data-purple-color-emphasis: #975bf1;
    --data-purple-color-muted: #211047;
    --data-red-color-emphasis: #eb3342;
    --data-red-color-muted: #3c0614;
    --data-teal-color-emphasis: #106c70;
    --data-teal-color-muted: #041f25;
    --data-yellow-color-emphasis: #895906;
    --data-yellow-color-muted: #2e1a00;
    --diffBlob-additionNum-bgColor: #3fb9504d;
    --diffBlob-additionWord-bgColor: #2ea04366;
    --diffBlob-deletionNum-bgColor: #f851494d;
    --diffBlob-deletionWord-bgColor: #f8514966;
    --diffBlob-hunkNum-bgColor-rest: #0c2d6b;
    --display-auburn-bgColor-emphasis: #87534f;
    --display-auburn-bgColor-muted: #271817;
    --display-auburn-borderColor-emphasis: #a86f6b;
    --display-auburn-borderColor-muted: #3a2422;
    --display-auburn-fgColor: #bf9592;
    --display-auburn-scale-0: #271817;
    --display-auburn-scale-1: #3a2422;
    --display-auburn-scale-2: #543331;
    --display-auburn-scale-3: #6d4340;
    --display-auburn-scale-4: #87534f;
    --display-auburn-scale-5: #a86f6b;
    --display-auburn-scale-6: #bf9592;
    --display-auburn-scale-7: #c6a19f;
    --display-auburn-scale-8: #d4b7b5;
    --display-auburn-scale-9: #dfcac8;
    --display-blue-bgColor-emphasis: #005bd1;
    --display-blue-bgColor-muted: #001a47;
    --display-blue-borderColor-emphasis: #0576ff;
    --display-blue-borderColor-muted: #002766;
    --display-blue-fgColor: #4da0ff;
    --display-blue-scale-0: #001a47;
    --display-blue-scale-1: #002766;
    --display-blue-scale-2: #00378a;
    --display-blue-scale-3: #0046a8;
    --display-blue-scale-4: #005bd1;
    --display-blue-scale-5: #0576ff;
    --display-blue-scale-6: #4da0ff;
    --display-blue-scale-7: #61adff;
    --display-blue-scale-8: #85c2ff;
    --display-blue-scale-9: #a3d3ff;
    --display-brown-bgColor-emphasis: #755e3e;
    --display-brown-bgColor-muted: #241c14;
    --display-brown-borderColor-emphasis: #94774c;
    --display-brown-borderColor-muted: #342a1d;
    --display-brown-fgColor: #b69a6d;
    --display-brown-scale-0: #241c14;
    --display-brown-scale-1: #342a1d;
    --display-brown-scale-2: #483a28;
    --display-brown-scale-3: #5d4a32;
    --display-brown-scale-4: #755e3e;
    --display-brown-scale-5: #94774c;
    --display-brown-scale-6: #b69a6d;
    --display-brown-scale-7: #bfa77d;
    --display-brown-scale-8: #cdbb98;
    --display-brown-scale-9: #dbceb3;
    --display-coral-bgColor-emphasis: #c31328;
    --display-coral-bgColor-muted: #3c0614;
    --display-coral-borderColor-emphasis: #eb3342;
    --display-coral-borderColor-muted: #58091a;
    --display-coral-fgColor: #f27d83;
    --display-coral-scale-0: #351008;
    --display-coral-scale-1: #51180b;
    --display-coral-scale-2: #72220d;
    --display-coral-scale-3: #902a0e;
    --display-coral-scale-4: #b3350f;
    --display-coral-scale-5: #e1430e;
    --display-coral-scale-6: #f7794b;
    --display-coral-scale-7: #fa8c61;
    --display-coral-scale-8: #fdaa86;
    --display-coral-scale-9: #ffc0a3;
    --display-cyan-bgColor-emphasis: #036a8c;
    --display-cyan-bgColor-muted: #001f29;
    --display-cyan-borderColor-emphasis: #0587b3;
    --display-cyan-borderColor-muted: #002e3d;
    --display-cyan-fgColor: #07ace4;
    --display-cyan-scale-0: #001f29;
    --display-cyan-scale-1: #002e3d;
    --display-cyan-scale-2: #014156;
    --display-cyan-scale-3: #02536f;
    --display-cyan-scale-4: #036a8c;
    --display-cyan-scale-5: #0587b3;
    --display-cyan-scale-6: #07ace4;
    --display-cyan-scale-7: #09b7f1;
    --display-cyan-scale-8: #45cbf7;
    --display-cyan-scale-9: #80dbf9;
    --display-gray-bgColor-emphasis: #576270;
    --display-gray-bgColor-muted: #1c1c1c;
    --display-gray-borderColor-emphasis: #6e7f96;
    --display-gray-borderColor-muted: #2a2b2d;
    --display-gray-fgColor: #92a1b5;
    --display-gray-scale-0: #1c1c1c;
    --display-gray-scale-1: #2a2b2d;
    --display-gray-scale-2: #393d41;
    --display-gray-scale-3: #474e57;
    --display-gray-scale-4: #576270;
    --display-gray-scale-5: #6e7f96;
    --display-gray-scale-6: #92a1b5;
    --display-gray-scale-7: #9babbf;
    --display-gray-scale-8: #b3c0d1;
    --display-gray-scale-9: #c4cfde;
    --display-green-bgColor-emphasis: #2f6f37;
    --display-green-bgColor-muted: #122117;
    --display-green-borderColor-emphasis: #388f3f;
    --display-green-borderColor-muted: #182f1f;
    --display-green-fgColor: #41b445;
    --display-green-scale-0: #122117;
    --display-green-scale-1: #182f1f;
    --display-green-scale-2: #214529;
    --display-green-scale-3: #285830;
    --display-green-scale-4: #2f6f37;
    --display-green-scale-5: #388f3f;
    --display-green-scale-6: #41b445;
    --display-green-scale-7: #46c144;
    --display-green-scale-8: #75d36f;
    --display-green-scale-9: #99e090;
    --display-indigo-bgColor-emphasis: #514ed4;
    --display-indigo-bgColor-muted: #1b183f;
    --display-indigo-borderColor-emphasis: #7070e1;
    --display-indigo-borderColor-muted: #25215f;
    --display-indigo-fgColor: #9899ec;
    --display-indigo-scale-0: #1b183f;
    --display-indigo-scale-1: #25215f;
    --display-indigo-scale-2: #312c90;
    --display-indigo-scale-3: #3935c0;
    --display-indigo-scale-4: #514ed4;
    --display-indigo-scale-5: #7070e1;
    --display-indigo-scale-6: #9899ec;
    --display-indigo-scale-7: #a2a5f1;
    --display-indigo-scale-8: #b7baf6;
    --display-indigo-scale-9: #c8cbf9;
    --display-lemon-bgColor-emphasis: #786008;
    --display-lemon-bgColor-muted: #291d00;
    --display-lemon-borderColor-emphasis: #977b0c;
    --display-lemon-borderColor-muted: #372901;
    --display-lemon-fgColor: #ba9b12;
    --display-lemon-scale-0: #291d00;
    --display-lemon-scale-1: #372901;
    --display-lemon-scale-2: #4f3c02;
    --display-lemon-scale-3: #614c05;
    --display-lemon-scale-4: #786008;
    --display-lemon-scale-5: #977b0c;
    --display-lemon-scale-6: #ba9b12;
    --display-lemon-scale-7: #c4a717;
    --display-lemon-scale-8: #d7bc1d;
    --display-lemon-scale-9: #e3d04f;
    --display-lime-bgColor-emphasis: #496c28;
    --display-lime-bgColor-muted: #141f0f;
    --display-lime-borderColor-emphasis: #5f892f;
    --display-lime-borderColor-muted: #1f3116;
    --display-lime-fgColor: #7dae37;
    --display-lime-scale-0: #141f0f;
    --display-lime-scale-1: #1f3116;
    --display-lime-scale-2: #2c441d;
    --display-lime-scale-3: #375421;
    --display-lime-scale-4: #496c28;
    --display-lime-scale-5: #5f892f;
    --display-lime-scale-6: #7dae37;
    --display-lime-scale-7: #89ba36;
    --display-lime-scale-8: #9fcc3e;
    --display-lime-scale-9: #bcda67;
    --display-olive-bgColor-emphasis: #5e681d;
    --display-olive-bgColor-muted: #171e0b;
    --display-olive-borderColor-emphasis: #7a8321;
    --display-olive-borderColor-muted: #252d10;
    --display-olive-fgColor: #a2a626;
    --display-olive-scale-0: #171e0b;
    --display-olive-scale-1: #252d10;
    --display-olive-scale-2: #374115;
    --display-olive-scale-3: #485219;
    --display-olive-scale-4: #5e681d;
    --display-olive-scale-5: #7a8321;
    --display-olive-scale-6: #a2a626;
    --display-olive-scale-7: #b2af24;
    --display-olive-scale-8: #cbc025;
    --display-olive-scale-9: #e2d04b;
    --display-orange-bgColor-emphasis: #984b10;
    --display-orange-bgColor-muted: #311708;
    --display-orange-borderColor-emphasis: #c46212;
    --display-orange-borderColor-muted: #43200a;
    --display-orange-fgColor: #ed8326;
    --display-orange-scale-0: #311708;
    --display-orange-scale-1: #43200a;
    --display-orange-scale-2: #632f0d;
    --display-orange-scale-3: #7b3c0e;
    --display-orange-scale-4: #984b10;
    --display-orange-scale-5: #c46212;
    --display-orange-scale-6: #ed8326;
    --display-orange-scale-7: #f1933b;
    --display-orange-scale-8: #f6b06a;
    --display-orange-scale-9: #fac68f;
    --display-pine-bgColor-emphasis: #14714c;
    --display-pine-bgColor-muted: #082119;
    --display-pine-borderColor-emphasis: #18915e;
    --display-pine-borderColor-muted: #0b3224;
    --display-pine-fgColor: #1bb673;
    --display-pine-scale-0: #082119;
    --display-pine-scale-1: #0b3224;
    --display-pine-scale-2: #0e4430;
    --display-pine-scale-3: #115a3e;
    --display-pine-scale-4: #14714c;
    --display-pine-scale-5: #18915e;
    --display-pine-scale-6: #1bb673;
    --display-pine-scale-7: #1ac176;
    --display-pine-scale-8: #1bda81;
    --display-pine-scale-9: #3eea97;
    --display-pink-bgColor-emphasis: #ac2f74;
    --display-pink-bgColor-muted: #2d1524;
    --display-pink-borderColor-emphasis: #d34591;
    --display-pink-borderColor-muted: #451c35;
    --display-pink-fgColor: #e57bb2;
    --display-pink-scale-0: #2d1524;
    --display-pink-scale-1: #451c35;
    --display-pink-scale-2: #65244a;
    --display-pink-scale-3: #842a5d;
    --display-pink-scale-4: #ac2f74;
    --display-pink-scale-5: #d34591;
    --display-pink-scale-6: #e57bb2;
    --display-pink-scale-7: #ec8dbd;
    --display-pink-scale-8: #f4a9cd;
    --display-pink-scale-9: #f9bed9;
    --display-plum-bgColor-emphasis: #9518d8;
    --display-plum-bgColor-muted: #2a0e3f;
    --display-plum-borderColor-emphasis: #b643ef;
    --display-plum-borderColor-muted: #40125e;
    --display-plum-fgColor: #d07ef7;
    --display-plum-scale-0: #2a0e3f;
    --display-plum-scale-1: #40125e;
    --display-plum-scale-2: #5c1688;
    --display-plum-scale-3: #7517ab;
    --display-plum-scale-4: #9518d8;
    --display-plum-scale-5: #b643ef;
    --display-plum-scale-6: #d07ef7;
    --display-plum-scale-7: #d889fa;
    --display-plum-scale-8: #e4a5fd;
    --display-plum-scale-9: #edbdff;
    --display-purple-bgColor-emphasis: #7730e8;
    --display-purple-bgColor-muted: #211047;
    --display-purple-borderColor-emphasis: #975bf1;
    --display-purple-borderColor-muted: #31146b;
    --display-purple-fgColor: #b687f7;
    --display-purple-scale-0: #211047;
    --display-purple-scale-1: #31146b;
    --display-purple-scale-2: #481a9e;
    --display-purple-scale-3: #5b1cca;
    --display-purple-scale-4: #7730e8;
    --display-purple-scale-5: #975bf1;
    --display-purple-scale-6: #b687f7;
    --display-purple-scale-7: #c398fb;
    --display-purple-scale-8: #d2affd;
    --display-purple-scale-9: #e1c7ff;
    --display-red-bgColor-emphasis: #c31328;
    --display-red-bgColor-muted: #3c0614;
    --display-red-borderColor-emphasis: #eb3342;
    --display-red-borderColor-muted: #58091a;
    --display-red-fgColor: #f27d83;
    --display-red-scale-0: #3c0614;
    --display-red-scale-1: #58091a;
    --display-red-scale-2: #790c20;
    --display-red-scale-3: #990f24;
    --display-red-scale-4: #c31328;
    --display-red-scale-5: #eb3342;
    --display-red-scale-6: #f27d83;
    --display-red-scale-7: #f48b8d;
    --display-red-scale-8: #f7adab;
    --display-red-scale-9: #f9c1be;
    --display-teal-bgColor-emphasis: #106c70;
    --display-teal-bgColor-muted: #041f25;
    --display-teal-borderColor-emphasis: #158a8a;
    --display-teal-borderColor-muted: #073036;
    --display-teal-fgColor: #1cb0ab;
    --display-teal-scale-0: #041f25;
    --display-teal-scale-1: #073036;
    --display-teal-scale-2: #0a464d;
    --display-teal-scale-3: #0c555a;
    --display-teal-scale-4: #106c70;
    --display-teal-scale-5: #158a8a;
    --display-teal-scale-6: #1cb0ab;
    --display-teal-scale-7: #1fbdb2;
    --display-teal-scale-8: #24d6c4;
    --display-teal-scale-9: #5fe3d1;
    --display-yellow-bgColor-emphasis: #895906;
    --display-yellow-bgColor-muted: #2e1a00;
    --display-yellow-borderColor-emphasis: #aa7109;
    --display-yellow-borderColor-muted: #3d2401;
    --display-yellow-fgColor: #d3910d;
    --display-yellow-scale-0: #2e1a00;
    --display-yellow-scale-1: #3d2401;
    --display-yellow-scale-2: #5a3702;
    --display-yellow-scale-3: #6d4403;
    --display-yellow-scale-4: #895906;
    --display-yellow-scale-5: #aa7109;
    --display-yellow-scale-6: #d3910d;
    --display-yellow-scale-7: #df9e11;
    --display-yellow-scale-8: #edb431;
    --display-yellow-scale-9: #f0ca6a;
    --fgColor-attention: #d29922;
    --fgColor-danger: #f85149;
    --fgColor-default: #f0f6fc;
    --fgColor-disabled: #656c76;
    --fgColor-done: #ab7df8;
    --fgColor-link: var(--fgColor-accent);
    --fgColor-muted: #9198a1;
    --fgColor-neutral: #9198a1;
    --fgColor-severe: #db6d28;
    --fgColor-sponsors: #db61a2;
    --fgColor-success: #3fb950;
    --header-bgColor: #151b23f2;
    --header-borderColor-divider: #656c76;
    --header-fgColor-logo: #f0f6fc;
    --headerSearch-bgColor: #0d1117;
    --headerSearch-borderColor: #2a313c;
    --highlight-neutral-bgColor: #d2992266;
    --label-auburn-bgColor-active: #543331;
    --label-auburn-bgColor-hover: #3a2422;
    --label-auburn-bgColor-rest: #271817;
    --label-auburn-fgColor-active: #d4b7b5;
    --label-auburn-fgColor-hover: #c6a19f;
    --label-auburn-fgColor-rest: #bf9592;
    --label-blue-bgColor-active: #00378a;
    --label-blue-bgColor-hover: #002766;
    --label-blue-bgColor-rest: #001a47;
    --label-blue-fgColor-active: #85c2ff;
    --label-blue-fgColor-hover: #61adff;
    --label-blue-fgColor-rest: #4da0ff;
    --label-brown-bgColor-active: #483a28;
    --label-brown-bgColor-hover: #342a1d;
    --label-brown-bgColor-rest: #241c14;
    --label-brown-fgColor-active: #cdbb98;
    --label-brown-fgColor-hover: #bfa77d;
    --label-brown-fgColor-rest: #b69a6d;
    --label-coral-bgColor-active: #72220d;
    --label-coral-bgColor-hover: #51180b;
    --label-coral-bgColor-rest: #351008;
    --label-coral-fgColor-active: #fdaa86;
    --label-coral-fgColor-hover: #fa8c61;
    --label-coral-fgColor-rest: #f7794b;
    --label-cyan-bgColor-active: #014156;
    --label-cyan-bgColor-hover: #002e3d;
    --label-cyan-bgColor-rest: #001f29;
    --label-cyan-fgColor-active: #45cbf7;
    --label-cyan-fgColor-hover: #09b7f1;
    --label-cyan-fgColor-rest: #07ace4;
    --label-gray-bgColor-active: #393d41;
    --label-gray-bgColor-hover: #2a2b2d;
    --label-gray-bgColor-rest: #1c1c1c;
    --label-gray-fgColor-active: #b3c0d1;
    --label-gray-fgColor-hover: #9babbf;
    --label-gray-fgColor-rest: #92a1b5;
    --label-green-bgColor-active: #214529;
    --label-green-bgColor-hover: #182f1f;
    --label-green-bgColor-rest: #122117;
    --label-green-fgColor-active: #75d36f;
    --label-green-fgColor-hover: #46c144;
    --label-green-fgColor-rest: #41b445;
    --label-indigo-bgColor-active: #312c90;
    --label-indigo-bgColor-hover: #25215f;
    --label-indigo-bgColor-rest: #1b183f;
    --label-indigo-fgColor-active: #b7baf6;
    --label-indigo-fgColor-hover: #a2a5f1;
    --label-indigo-fgColor-rest: #9899ec;
    --label-lemon-bgColor-active: #4f3c02;
    --label-lemon-bgColor-hover: #372901;
    --label-lemon-bgColor-rest: #291d00;
    --label-lemon-fgColor-active: #d7bc1d;
    --label-lemon-fgColor-hover: #c4a717;
    --label-lemon-fgColor-rest: #ba9b12;
    --label-lime-bgColor-active: #2c441d;
    --label-lime-bgColor-hover: #1f3116;
    --label-lime-bgColor-rest: #141f0f;
    --label-lime-fgColor-active: #9fcc3e;
    --label-lime-fgColor-hover: #89ba36;
    --label-lime-fgColor-rest: #7dae37;
    --label-olive-bgColor-active: #374115;
    --label-olive-bgColor-hover: #252d10;
    --label-olive-bgColor-rest: #171e0b;
    --label-olive-fgColor-active: #cbc025;
    --label-olive-fgColor-hover: #b2af24;
    --label-olive-fgColor-rest: #a2a626;
    --label-orange-bgColor-active: #632f0d;
    --label-orange-bgColor-hover: #43200a;
    --label-orange-bgColor-rest: #311708;
    --label-orange-fgColor-active: #f6b06a;
    --label-orange-fgColor-hover: #f1933b;
    --label-orange-fgColor-rest: #ed8326;
    --label-pine-bgColor-active: #0e4430;
    --label-pine-bgColor-hover: #0b3224;
    --label-pine-bgColor-rest: #082119;
    --label-pine-fgColor-active: #1bda81;
    --label-pine-fgColor-hover: #1ac176;
    --label-pine-fgColor-rest: #1bb673;
    --label-pink-bgColor-active: #65244a;
    --label-pink-bgColor-hover: #451c35;
    --label-pink-bgColor-rest: #2d1524;
    --label-pink-fgColor-active: #f4a9cd;
    --label-pink-fgColor-hover: #ec8dbd;
    --label-pink-fgColor-rest: #e57bb2;
    --label-plum-bgColor-active: #5c1688;
    --label-plum-bgColor-hover: #40125e;
    --label-plum-bgColor-rest: #2a0e3f;
    --label-plum-fgColor-active: #e4a5fd;
    --label-plum-fgColor-hover: #d889fa;
    --label-plum-fgColor-rest: #d07ef7;
    --label-purple-bgColor-active: #481a9e;
    --label-purple-bgColor-hover: #31146b;
    --label-purple-bgColor-rest: #211047;
    --label-purple-fgColor-active: #d2affd;
    --label-purple-fgColor-hover: #c398fb;
    --label-purple-fgColor-rest: #b687f7;
    --label-red-bgColor-active: #790c20;
    --label-red-bgColor-hover: #58091a;
    --label-red-bgColor-rest: #3c0614;
    --label-red-fgColor-active: #f7adab;
    --label-red-fgColor-hover: #f48b8d;
    --label-red-fgColor-rest: #f27d83;
    --label-teal-bgColor-active: #0a464d;
    --label-teal-bgColor-hover: #073036;
    --label-teal-bgColor-rest: #041f25;
    --label-teal-fgColor-active: #24d6c4;
    --label-teal-fgColor-hover: #1fbdb2;
    --label-teal-fgColor-rest: #1cb0ab;
    --label-yellow-bgColor-active: #5a3702;
    --label-yellow-bgColor-hover: #3d2401;
    --label-yellow-bgColor-rest: #2e1a00;
    --label-yellow-fgColor-active: #edb431;
    --label-yellow-fgColor-hover: #df9e11;
    --label-yellow-fgColor-rest: #d3910d;
    --menu-bgColor-active: #151b23;
    --overlay-backdrop-bgColor: #21283066;
    --reactionButton-selected-bgColor-rest: #388bfd33;
    --reactionButton-selected-fgColor-hover: #79c0ff;
    --selectMenu-bgColor-active: #0c2d6b;
    --sideNav-bgColor-selected: #212830;
    --skeletonLoader-bgColor: #656c7633;
    --timelineBadge-bgColor: #212830;
    --topicTag-borderColor: #00000000;
    --underlineNav-borderColor-active: #f78166;
    --avatar-bgColor: #ffffff1a;
    --bgColor-black: #010409;
    --bgColor-closed-emphasis: var(--bgColor-danger-emphasis);
    --bgColor-closed-muted: var(--bgColor-danger-muted);
    --bgColor-inset: #010409;
    --bgColor-inverse: #ffffff;
    --bgColor-open-emphasis: var(--bgColor-success-emphasis);
    --bgColor-open-muted: var(--bgColor-success-muted);
    --bgColor-upsell-emphasis: var(--bgColor-done-emphasis);
    --bgColor-upsell-muted: var(--bgColor-done-muted);
    --bgColor-white: #ffffff;
    --border-accent-emphasis: 0.0625rem solid #1f6feb;
    --border-accent-muted: 0.0625rem solid #388bfd66;
    --border-attention-emphasis: 0.0625rem solid #9e6a03;
    --border-attention-muted: 0.0625rem solid #bb800966;
    --border-danger-emphasis: 0.0625rem solid #da3633;
    --border-danger-muted: 0.0625rem solid #f8514966;
    --border-default: 0.0625rem solid #3d444d;
    --border-disabled: 0.0625rem solid #656c761a;
    --border-done-emphasis: 0.0625rem solid #8957e5;
    --border-done-muted: 0.0625rem solid #ab7df866;
    --border-emphasis: 0.0625rem solid #656c76;
    --border-severe-emphasis: 0.0625rem solid #bd561d;
    --border-severe-muted: 0.0625rem solid #db6d2866;
    --border-sponsors-emphasis: 0.0625rem solid #bf4b8a;
    --border-sponsors-muted: 0.0625rem solid #db61a266;
    --border-success-emphasis: 0.0625rem solid #238636;
    --border-success-muted: 0.0625rem solid #2ea04366;
    --border-transparent: 0.0625rem solid #00000000;
    --borderColor-closed-emphasis: var(--borderColor-danger-emphasis);
    --borderColor-closed-muted: var(--borderColor-danger-muted);
    --borderColor-muted: #3d444db3;
    --borderColor-neutral-emphasis: var(--borderColor-emphasis);
    --borderColor-open-emphasis: var(--borderColor-success-emphasis);
    --borderColor-open-muted: var(--borderColor-success-muted);
    --borderColor-translucent: #ffffff26;
    --borderColor-upsell-emphasis: var(--borderColor-done-emphasis);
    --borderColor-upsell-muted: var(--borderColor-done-muted);
    --button-danger-bgColor-active: var(--bgColor-danger-emphasis);
    --button-danger-bgColor-rest: var(--control-bgColor-rest);
    --button-danger-fgColor-active: #ffffff;
    --button-danger-fgColor-disabled: #f8514980;
    --button-danger-fgColor-hover: #ffffff;
    --button-danger-iconColor-hover: #ffffff;
    --button-default-bgColor-active: var(--control-bgColor-active);
    --button-default-bgColor-hover: var(--control-bgColor-hover);
    --button-default-bgColor-rest: var(--control-bgColor-rest);
    --button-default-bgColor-selected: var(--control-bgColor-active);
    --button-invisible-bgColor-active: var(--control-transparent-bgColor-active);
    --button-invisible-bgColor-hover: var(--control-transparent-bgColor-hover);
    --button-invisible-bgColor-rest: var(--control-transparent-bgColor-rest);
    --button-invisible-borderColor-hover: var(--control-transparent-borderColor-hover);
    --button-invisible-borderColor-rest: var(--control-transparent-borderColor-rest);
    --button-invisible-iconColor-hover: var(--fgColor-muted);
    --button-invisible-iconColor-rest: var(--fgColor-muted);
    --button-outline-bgColor-hover: var(--control-bgColor-hover);
    --button-outline-fgColor-active: #ffffff;
    --button-primary-bgColor-rest: var(--bgColor-success-emphasis);
    --button-primary-fgColor-disabled: #ffffff66;
    --buttonCounter-danger-bgColor-disabled: #da36330d;
    --buttonCounter-danger-bgColor-hover: #ffffff33;
    --buttonCounter-danger-fgColor-disabled: #f8514980;
    --buttonCounter-danger-fgColor-hover: #ffffff;
    --buttonCounter-danger-fgColor-rest: var(--fgColor-danger);
    --buttonCounter-invisible-bgColor-rest: var(--bgColor-neutral-muted);
    --buttonCounter-outline-bgColor-disabled: #1f6feb0d;
    --card-bgColor: var(--bgColor-muted);
    --codeMirror-activeline-bgColor: var(--bgColor-neutral-muted);
    --codeMirror-bgColor: var(--bgColor-default);
    --codeMirror-cursor-fgColor: var(--fgColor-default);
    --codeMirror-fgColor: var(--fgColor-default);
    --codeMirror-gutterMarker-fgColor-default: var(--bgColor-default);
    --codeMirror-gutterMarker-fgColor-muted: var(--fgColor-muted);
    --codeMirror-gutters-bgColor: var(--bgColor-default);
    --codeMirror-lineNumber-fgColor: var(--fgColor-muted);
    --codeMirror-lines-bgColor: var(--bgColor-default);
    --codeMirror-matchingBracket-fgColor: var(--fgColor-default);
    --codeMirror-selection-bgColor: var(--borderColor-accent-muted);
    --color-ansi-white-bright: #ffffff;
    --contribution-default-borderColor-1: var(--contribution-default-borderColor-0);
    --contribution-default-borderColor-2: var(--contribution-default-borderColor-0);
    --contribution-default-borderColor-3: var(--contribution-default-borderColor-0);
    --contribution-default-borderColor-4: var(--contribution-default-borderColor-0);
    --control-bgColor-disabled: var(--bgColor-disabled);
    --control-bgColor-selected: var(--control-bgColor-rest);
    --control-borderColor-danger: var(--borderColor-danger-emphasis);
    --control-borderColor-disabled: var(--borderColor-disabled);
    --control-borderColor-emphasis: var(--borderColor-emphasis);
    --control-borderColor-rest: var(--borderColor-default);
    --control-borderColor-success: var(--borderColor-success-emphasis);
    --control-borderColor-warning: var(--borderColor-attention-emphasis);
    --control-checked-bgColor-disabled: var(--fgColor-disabled);
    --control-checked-bgColor-rest: var(--bgColor-accent-emphasis);
    --control-checked-fgColor-disabled: #010409;
    --control-danger-bgColor-active: #f8514966;
    --control-danger-bgColor-hover: var(--bgColor-danger-muted);
    --control-danger-fgColor-rest: var(--fgColor-danger);
    --control-fgColor-disabled: var(--fgColor-disabled);
    --control-fgColor-placeholder: var(--fgColor-muted);
    --control-fgColor-rest: var(--fgColor-default);
    --control-iconColor-rest: var(--fgColor-muted);
    --control-transparent-bgColor-disabled: var(--bgColor-disabled);
    --controlKnob-bgColor-checked: #ffffff;
    --controlTrack-bgColor-disabled: var(--fgColor-disabled);
    --controlTrack-borderColor-disabled: var(--fgColor-disabled);
    --controlTrack-borderColor-rest: var(--borderColor-default);
    --controlTrack-fgColor-rest: var(--fgColor-muted);
    --counter-bgColor-emphasis: var(--bgColor-neutral-emphasis);
    --counter-bgColor-muted: var(--bgColor-neutral-muted);
    --diffBlob-additionLine-bgColor: var(--bgColor-success-muted);
    --diffBlob-additionLine-fgColor: var(--fgColor-default);
    --diffBlob-additionNum-fgColor: var(--fgColor-default);
    --diffBlob-additionWord-fgColor: var(--fgColor-default);
    --diffBlob-deletionLine-bgColor: var(--bgColor-danger-muted);
    --diffBlob-deletionLine-fgColor: var(--fgColor-default);
    --diffBlob-deletionNum-fgColor: var(--fgColor-default);
    --diffBlob-deletionWord-fgColor: var(--fgColor-default);
    --diffBlob-emptyLine-bgColor: var(--bgColor-muted);
    --diffBlob-emptyNum-bgColor: var(--bgColor-muted);
    --diffBlob-expander-iconColor: var(--fgColor-muted);
    --diffBlob-hunkLine-bgColor: var(--bgColor-accent-muted);
    --diffBlob-hunkLine-fgColor: var(--fgColor-muted);
    --diffBlob-hunkNum-bgColor-hover: var(--bgColor-accent-emphasis);
    --diffBlob-hunkNum-fgColor-rest: var(--fgColor-default);
    --fgColor-black: #010409;
    --fgColor-closed: var(--fgColor-danger);
    --fgColor-onEmphasis: #ffffff;
    --fgColor-onInverse: #010409;
    --fgColor-open: var(--fgColor-success);
    --fgColor-upsell: var(--fgColor-done);
    --fgColor-white: #ffffff;
    --focus-outlineColor: var(--borderColor-accent-emphasis);
    --header-fgColor-default: #ffffffb3;
    --overlay-bgColor: #010409;
    --page-header-bgColor: var(--bgColor-default);
    --reactionButton-selected-fgColor-rest: var(--fgColor-link);
    --selectMenu-borderColor: var(--borderColor-default);
    --selection-bgColor: #1f6febb3;
    --shadow-floating-legacy: 0px 6px 12px -3px #01040966, 0px 6px 18px 0px #01040966;
    --shadow-inset: inset 0px 1px 0px 0px #0104093d;
    --shadow-resting-medium: 0px 1px 1px 0px #01040966, 0px 3px 6px 0px #010409cc;
    --shadow-resting-small: 0px 1px 1px 0px #01040999, 0px 1px 3px 0px #01040999;
    --shadow-resting-xsmall: 0px 1px 1px 0px #010409cc;
    --tooltip-bgColor: var(--bgColor-emphasis);
    --treeViewItem-leadingVisual-iconColor-rest: var(--fgColor-muted);
    --underlineNav-iconColor-rest: var(--fgColor-muted);
    --avatar-borderColor: var(--borderColor-translucent);
    --border-closed-emphasis: var(--border-danger-emphasis);
    --border-closed-muted: var(--border-danger-muted);
    --border-muted: 0.0625rem solid #3d444db3;
    --border-neutral-emphasis: 0.0625rem solid #656c76;
    --border-open-emphasis: var(--border-success-emphasis);
    --border-open-muted: var(--border-success-muted);
    --border-upsell-emphasis: 0.0625rem solid #8957e5;
    --border-upsell-muted: 0.0625rem solid #ab7df866;
    --borderColor-neutral-muted: var(--borderColor-muted);
    --button-danger-bgColor-disabled: var(--control-bgColor-disabled);
    --button-danger-borderColor-rest: var(--control-borderColor-rest);
    --button-default-bgColor-disabled: var(--control-bgColor-disabled);
    --button-default-borderColor-disabled: var(--control-borderColor-disabled);
    --button-default-borderColor-rest: var(--control-borderColor-rest);
    --button-default-fgColor-rest: var(--control-fgColor-rest);
    --button-invisible-fgColor-active: var(--control-fgColor-rest);
    --button-invisible-fgColor-disabled: var(--control-fgColor-disabled);
    --button-invisible-fgColor-hover: var(--control-fgColor-rest);
    --button-invisible-fgColor-rest: var(--control-fgColor-rest);
    --button-invisible-iconColor-disabled: var(--control-fgColor-disabled);
    --button-outline-bgColor-disabled: var(--control-bgColor-disabled);
    --button-primary-borderColor-rest: var(--borderColor-translucent);
    --button-primary-fgColor-rest: var(--fgColor-white);
    --button-primary-iconColor-rest: var(--fgColor-white);
    --control-checked-borderColor-disabled: var(--control-checked-bgColor-disabled);
    --control-checked-borderColor-rest: var(--control-checked-bgColor-rest);
    --control-checked-fgColor-rest: var(--fgColor-onEmphasis);
    --controlKnob-bgColor-disabled: var(--control-bgColor-disabled);
    --controlKnob-bgColor-rest: var(--bgColor-inset);
    --controlKnob-borderColor-checked: var(--control-checked-bgColor-rest);
    --controlKnob-borderColor-disabled: var(--control-bgColor-disabled);
    --controlKnob-borderColor-rest: var(--control-borderColor-emphasis);
    --controlTrack-fgColor-disabled: var(--fgColor-onEmphasis);
    --diffBlob-hunkNum-fgColor-hover: var(--fgColor-onEmphasis);
    --focus-outline: 2px solid #1f6feb;
    --overlay-borderColor: var(--borderColor-muted);
    --tooltip-fgColor: var(--fgColor-onEmphasis);
    --border-neutral-muted: 0.0625rem solid #3d444db3;
    --button-danger-borderColor-hover: var(--button-primary-borderColor-rest);
    --button-default-borderColor-active: var(--button-default-borderColor-rest);
    --button-default-borderColor-hover: var(--button-default-borderColor-rest);
    --button-primary-borderColor-active: var(--button-primary-borderColor-rest);
    --button-primary-borderColor-hover: var(--button-primary-borderColor-rest);
    --shadow-floating-large: 0px 0px 0px 1px #3d444d, 0px 24px 48px 0px #010409;
    --shadow-floating-medium: 0px 0px 0px 1px #3d444d, 0px 8px 16px -4px #01040966, 0px 4px 32px -4px #01040966, 0px 24px 48px -12px #01040966, 0px 48px 96px -24px #01040966;
    --shadow-floating-small: 0px 0px 0px 1px #3d444d, 0px 6px 12px -3px #01040966, 0px 6px 18px 0px #01040966;
    --shadow-floating-xlarge: 0px 0px 0px 1px #3d444d, 0px 32px 64px 0px #010409;
    --underlineNav-borderColor-hover: var(--borderColor-neutral-muted);
    --button-danger-borderColor-active: var(--button-danger-borderColor-hover);
    --button-outline-borderColor-hover: var(--button-default-borderColor-hover);
    --button-outline-borderColor-active: var(--button-outline-borderColor-hover);
  }
}


/*# sourceMappingURL=dark.scss.map */

/*# sourceMappingURL=dark-4c99b647644f.css.map*/