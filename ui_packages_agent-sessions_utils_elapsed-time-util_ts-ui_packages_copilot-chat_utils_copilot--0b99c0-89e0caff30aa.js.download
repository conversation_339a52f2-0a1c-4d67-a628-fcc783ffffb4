"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_agent-sessions_utils_elapsed-time-util_ts-ui_packages_copilot-chat_utils_copilot--0b99c0"],{1574:(e,t,r)=>{r.d(t,{Y:()=>n,w:()=>o});let n={InProgress:"in_progress",Completed:"completed",Failed:"failed",Idle:"idle",WaitingForUser:"waiting_for_user",TimedOut:"timed_out",Cancelled:"cancelled"};function o(e){switch(e){case n.InProgress:return"In progress";case n.Completed:return"Ready for review";case n.Failed:return"Failed";case n.Idle:return"Idle";case n.WaitingForUser:return"Waiting for user";case n.TimedOut:return"Timed out";case n.Cancelled:return"Cancelled";default:return"Unknown"}}},99723:(e,t,r)=>{r.d(t,{Y:()=>n});function n(e,t){let r=new Date(e).getTime(),n=Math.floor(((t&&"0001-01-01T00:00:00Z"!==t?new Date(t).getTime():Date.now())-r)/1e3),o=[{value:Math.floor(n/86400),label:"d"},{value:Math.floor(n%86400/3600),label:"h"},{value:Math.floor(n%3600/60),label:"m"},{value:n%60,label:"s"}].filter(e=>e.value>0);return 0===o.length?"0s":1===o.length?`${o[0]?.value??0}${o[0]?.label??""}`:o[1]?`${o[0]?.value}${o[0]?.label} ${o[1].value}${o[1].label??""}`:`${o[0]?.value??0}${o[0]?.label??""}`}},45260:(e,t,r)=>{r.d(t,{I:()=>s,j:()=>FetchCAPI401Error});var n=r(54187),o=r(22069);let FetchCAPI401Error=class FetchCAPI401Error extends Error{constructor(e){super(e),this.name="FetchCAPI401Error"}};async function s({path:e,method:t="GET",streamingResponse:r=!1}){let s=new n.l,i=await s.getAuthToken();if(!i)throw Error("No token available");let a=await (0,o.p)({authToken:i,basePath:"https://api.githubcopilot.com",method:t,path:e,streamingResponse:r});if(!a.ok){if(401===a.status)throw new FetchCAPI401Error("Unauthorized access - token may be invalid or expired");throw Error("Failed to fetch agent sessions")}return a}},53005:(e,t,r)=>{r.d(t,{O:()=>i,S:()=>s});var n=r(96679);let o=n.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",s="X-GitHub-Client-Version";function i(){return o}},54187:(e,t,r)=>{r.d(t,{l:()=>AgentSessionsTokenProvider});var n=r(879);let AgentSessionsTokenProvider=class AgentSessionsTokenProvider extends n.J{constructor(){super([],"/copilot/agent-sessions/token","AGENT_SESSIONS_TOKEN")}}},92520:(e,t,r)=>{function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,{Y:()=>AuthToken});let AuthToken=class AuthToken{get authorizationHeaderValue(){return`GitHub-Bearer ${this.value}`}needsRefreshing(e){return this.isExpired||this.ssoChanged(e)}get isExpired(){let e=new Date(this.expiration);return new Date(Date.UTC(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds()))<new Date(Date.now()+15e3)}ssoChanged(e){return!(this.ssoOrgIDs.every(t=>e.includes(t))&&e.every(e=>this.ssoOrgIDs.includes(e)))}static fromResult(e,t){return new AuthToken(e.token,e.expiration,t)}serialize(){return{value:this.value,expiration:this.expiration,ssoOrgIDs:this.ssoOrgIDs}}static deserialize(e){return new AuthToken(e.value,e.expiration,e.ssoOrgIDs)}constructor(e,t,r){n(this,"value",void 0),n(this,"expiration",void 0),n(this,"ssoOrgIDs",void 0),this.value=e,this.expiration=t,this.ssoOrgIDs=r}}},879:(e,t,r)=>{r.d(t,{J:()=>CopilotAuthTokenProvider});var n=r(92520),o=r(85351),s=r(60039);function i(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let CopilotAuthTokenProvider=class CopilotAuthTokenProvider{async getAuthToken(){let e=this.getLocalStorageAuthToken();return e?this.validateAuthToken(e):this.fetchAuthToken()}setLocalStorageAuthToken(e){this.copilotLocalStorage.setItem(this.storageKey,JSON.stringify(e.serialize()))}getLocalStorageAuthToken(){let e=this.copilotLocalStorage.getItem(this.storageKey);return e?n.Y.deserialize(JSON.parse(e)):null}removeLocalStorageAuthToken(){this.copilotLocalStorage.removeItem(this.storageKey)}async validateAuthToken(e){return e.needsRefreshing(this.ssoOrgIDs)?this.fetchAuthToken():e}fetchAuthToken(){return this.currentAuthTokenRequest||(this.currentAuthTokenRequest=this._fetchAuthToken()),this.currentAuthTokenRequest}async _fetchAuthToken(){let e=await (0,s.lS)(this.tokenEndpoint,{method:"POST"});if(e.ok){let t=await e.json();this.currentAuthTokenRequest=null;let r=n.Y.fromResult(t,this.ssoOrgIDs);return this.setLocalStorageAuthToken(r),r}throw this.currentAuthTokenRequest=null,Error("Failed to mint new auth token")}constructor(e,t="/github-copilot/chat/token",r="COPILOT_AUTH_TOKEN"){i(this,"tokenEndpoint",void 0),i(this,"storageKey",void 0),i(this,"ssoOrgIDs",void 0),i(this,"currentAuthTokenRequest",void 0),i(this,"copilotLocalStorage",void 0),this.ssoOrgIDs=e,this.currentAuthTokenRequest=null,this.copilotLocalStorage=(0,o.A)("localStorage",{throwQuotaErrorsOnSet:!1,ttl:864e5}),this.storageKey=r,this.tokenEndpoint=t}}},99377:(e,t,r)=>{r.d(t,{C6:()=>o,UH:()=>l,Wp:()=>u,hs:()=>i,mF:()=>a,wh:()=>n,xP:()=>s});let n={explain:"explain",conversation:"conversation",suggest:"suggest",discussFileDiff:"discuss-file-diff",explainFileDiff:"explain-file-diff",reviewPr:"review-pull-request",actionsAgent:"actions-agent"},o=["exception","filtered","publicCode","contentTooLarge","rateLimit","agentUnauthorized","agentRequest","networkError","multipleAgentsAttempt"],s=["bing-search","codesearch","semantic-code-search","lexical-code-search","kb-search","getfile","getfilechanges","getdiscussion","get-actions-job-logs","getalert","planskill","get-github-data","support-search","get-figma","codesearchagentskill","draft-issue","repository-metadata"],i={Experiments:"experiements",Prompt:"prompt",None:"none"},a={Unlicensed:"unlicensed",LicensedFull:"licensed_full",LicensedLimited:"licensed_limited"},l={IndividualFree:"free",IndividualPro:"pro",IndividualProPlus:"pro_plus",Business:"business",Enterprise:"enterprise"},u="NULL_MESSAGE"},26559:(e,t,r)=>{r.d(t,{jC:()=>l,kt:()=>i,tV:()=>a});var n=r(53005),o=r(27851),s=r(88191);function i(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,s.wE)(e)};return(0,o.G7)("client_version_header")&&(t={...t,[n.S]:(0,n.O)()}),t}function a(e,t){for(let[r,n]of Object.entries(i(t)))e.set(r,n)}function l(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,r)=>{r.d(t,{$r:()=>i,M1:()=>a,li:()=>o,pS:()=>u,wE:()=>l});var n=r(96679);let o="X-Fetch-Nonce",s=new Set;function i(e){s.add(e)}function a(){return s.values().next().value||""}function l(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[o]=a():s.has(e)?t[o]=e:t[o]=Array.from(s).join(","),t}function u(){let e=n.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&i(e)}},1948:(e,t,r)=>{r.d(t,{Fn:()=>l,eS:()=>a});var n=r(74848),o=r(21728),s=r(96540);let i=(0,s.createContext)(void 0);function a(){let e=(0,s.useContext)(i);if(!e)throw Error("useConsecutiveCAPI401sContext must be used within a ConsecutiveCAPI401sProvider");return e}function l(e){let t,r,a,l,c,d=(0,o.c)(7),{children:h}=e,[g,p]=(0,s.useState)(0);return d[0]===Symbol.for("react.memo_cache_sentinel")?(r=()=>p(u),a=()=>p(0),d[0]=r,d[1]=a):(r=d[0],a=d[1]),d[2]!==g?(l={numberOf401s:g,increment401s:r,reset401s:a},d[2]=g,d[3]=l):l=d[3],t=l,d[4]!==h||d[5]!==t?(c=(0,n.jsx)(i.Provider,{value:t,children:h}),d[4]=h,d[5]=t,d[6]=c):c=d[6],c}function u(e){return e+1}try{i.displayName||(i.displayName="ConsecutiveCAPI401sContext")}catch{}try{l.displayName||(l.displayName="ConsecutiveCAPI401sProvider")}catch{}},47360:(e,t,r)=>{r.d(t,{mE:()=>l,mV:()=>a});var n=r(74848),o=r(21728),s=r(96540);let i=(0,s.createContext)(void 0);function a(){let e=(0,s.useContext)(i);if(!e)throw Error("usePullContext must be used within a PullContextProvider");return e}function l(e){let t,r,s,a=(0,o.c)(5),{pull:l,children:u}=e;return a[0]!==l?(r={pull:l},a[0]=l,a[1]=r):r=a[1],t=r,a[2]!==u||a[3]!==t?(s=(0,n.jsx)(i.Provider,{value:t,children:u}),a[2]=u,a[3]=t,a[4]=s):s=a[4],s}try{i.displayName||(i.displayName="PullContext")}catch{}try{l.displayName||(l.displayName="PullContextProvider")}catch{}},91768:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(97286),o=r(45260),s=r(1948),i=r(54187),a=r(60183),l=r(96540);function u(...e){let t=(0,n.I)(...e),{numberOf401s:r,increment401s:c,reset401s:d}=(0,s.eS)(),{isError:h,isLoading:g,error:p}=t,f=(0,a.u)("copilot_coding_agent_refresh_token_on_401");return(0,l.useEffect)(()=>{!f&&(h&&p instanceof o.j?c():h||g?r>5&&(new i.l().removeLocalStorageAuthToken(),d()):d())},[h,g,p,c,d,r,f]),t}},96316:(e,t,r)=>{r.d(t,{t:()=>c});var n=r(21728),o=r(91768),s=r(1574),i=r(29710),a=r(88795),l=r(47360),u=r(45260);function c(e){let t,r,s,c,h=(0,n.c)(16),{initialData:g,useMockData:p,sessionsPollingInterval:f}=e,m=(0,a.t)(),{pull:v}=(0,l.mV)(),{id:_}=v;return h[0]!==_||h[1]!==m.name||h[2]!==m.ownerLogin||h[3]!==p?(t=["agent-sessions",m.ownerLogin,m.name,_,p],h[0]=_,h[1]=m.name,h[2]=m.ownerLogin,h[3]=p,h[4]=t):t=h[4],h[5]!==_||h[6]!==p?(r=async()=>{if(p)return i.M;let e=await (0,u.I)({path:`/agents/sessions/resource/pull/${_}`});return(await e.json()).sessions},h[5]=_,h[6]=p,h[7]=r):r=h[7],h[8]!==f||h[9]!==p?(s=e=>{if(p)return!1;let t=e.state.data;return t?t.some(d)?f:10*f:f},h[8]=f,h[9]=p,h[10]=s):s=h[10],h[11]!==g||h[12]!==t||h[13]!==r||h[14]!==s?(c={queryKey:t,queryFn:r,initialData:g,refetchInterval:s},h[11]=g,h[12]=t,h[13]=r,h[14]=s,h[15]=c):c=h[15],(0,o.b)(c)}function d(e){return e.state===s.Y.InProgress||e.state===s.Y.Idle||e.state===s.Y.WaitingForUser}},88795:(e,t,r)=>{r.d(t,{d:()=>a,t:()=>l});var n=r(74848),o=r(21728),s=r(96540);let i=s.createContext({});function a(e){let t,r=(0,o.c)(3),{repository:s,children:a}=e;return r[0]!==a||r[1]!==s?(t=(0,n.jsxs)(i.Provider,{value:s,children:[" ",a," "]}),r[0]=a,r[1]=s,r[2]=t):t=r[2],t}function l(){return s.useContext(i)}try{i.displayName||(i.displayName="CurrentRepositoryContext")}catch{}try{a.displayName||(a.displayName="CurrentRepositoryProvider")}catch{}},29710:e=>{e.exports=JSON.parse('{"M":[{"id":"6eab781c-49c1-41e1-ab63-ef7872033702","name":"","user_id":10053402,"agent_id":1143301,"state":"completed","logs":"","owner_id":9919,"repo_id":956201709,"resource_type":"pull","resource_id":1,"last_updated_at":"2025-06-27T15:28:12.164448308Z","created_at":"2025-06-27T15:22:56.301948291Z","completed_at":"2025-06-27T15:32:56.301948291Z","premium_requests":2,"workflow_run_id":12345,"log_entries":[],"error":null},{"id":"4dfb781c-49c1-41e1-ab63-ef7872033704","name":"review by @actor","user_id":10053402,"agent_id":1143301,"state":"in_progress","logs":"","owner_id":9919,"repo_id":956201709,"resource_type":"pull","resource_id":2,"last_updated_at":"2025-06-27T15:28:12.164448308Z","created_at":"2025-06-27T17:22:56.301948291Z","completed_at":null,"premium_requests":0,"workflow_run_id":12346,"log_entries":[],"error":null},{"id":"4dfb781c-49c1-41e1-ab63-ef7872033702","name":"Test Session","user_id":10053402,"agent_id":1143301,"state":"in_progress","logs":"","owner_id":9919,"repo_id":956201709,"resource_type":"pull","resource_id":2,"last_updated_at":"2025-06-27T15:28:12.164448308Z","created_at":"2025-06-27T17:22:56.301948291Z","completed_at":null,"premium_requests":0,"workflow_run_id":12346,"log_entries":[],"error":null}]}')}}]);
//# sourceMappingURL=ui_packages_agent-sessions_utils_elapsed-time-util_ts-ui_packages_copilot-chat_utils_copilot--0b99c0-4345bf6c944b.js.map