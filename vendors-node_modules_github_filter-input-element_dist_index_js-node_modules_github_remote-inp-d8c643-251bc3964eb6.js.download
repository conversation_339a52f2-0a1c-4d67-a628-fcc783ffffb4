"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643"],{62044:(t,e,n)=>{n.d(e,{A:()=>s});let FilterInputElement=class FilterInputElement extends HTMLElement{constructor(){super(),this.currentQuery=null,this.filter=null,this.debounceInputChange=function(t){let e;return function(){clearTimeout(e),e=setTimeout(()=>{clearTimeout(e),t()},300)}}(()=>i(this,!0)),this.boundFilterResults=()=>{i(this,!1)}}static get observedAttributes(){return["aria-owns"]}attributeChangedCallback(t,e){e&&"aria-owns"===t&&i(this,!1)}connectedCallback(){let t=this.input;t&&(t.setAttribute("autocomplete","off"),t.setAttribute("spellcheck","false"),t.addEventListener("focus",this.boundFilterResults),t.addEventListener("change",this.boundFilterResults),t.addEventListener("input",this.debounceInputChange))}disconnectedCallback(){let t=this.input;t&&(t.removeEventListener("focus",this.boundFilterResults),t.removeEventListener("change",this.boundFilterResults),t.removeEventListener("input",this.debounceInputChange))}get input(){let t=this.querySelector("input");return t instanceof HTMLInputElement?t:null}reset(){let t=this.input;t&&(t.value="",t.dispatchEvent(new Event("change",{bubbles:!0})))}};async function i(t,e=!1){let n=t.input;if(!n)return;let s=n.value.trim(),a=t.getAttribute("aria-owns");if(!a)return;let l=document.getElementById(a);if(!l)return;let o=l.hasAttribute("data-filter-list")?l:l.querySelector("[data-filter-list]");if(!o||(t.dispatchEvent(new CustomEvent("filter-input-start",{bubbles:!0})),e&&t.currentQuery===s))return;t.currentQuery=s;let d=t.filter||r,u=o.childElementCount,c=0,f=!1;for(let t of Array.from(o.children)){var h;if(!(t instanceof HTMLElement))continue;let e=(((h=t).querySelector("[data-filter-item-text]")||h).textContent||"").trim(),n=d(t,e,s);!0===n.hideNew&&(f=n.hideNew),t.hidden=!n.match,n.match&&c++}let m=l.querySelector("[data-filter-new-item]"),b=!!m&&s.length>0&&!f;m instanceof HTMLElement&&(m.hidden=!b,b&&function(t,e){let n=t.querySelector("[data-filter-new-item-text]");n&&(n.textContent=e);let i=t.querySelector("[data-filter-new-item-value]");(i instanceof HTMLInputElement||i instanceof HTMLButtonElement)&&(i.value=e)}(m,s)),function(t,e){let n=t.querySelector("[data-filter-empty-state]");n instanceof HTMLElement&&(n.hidden=e)}(l,c>0||b),t.dispatchEvent(new CustomEvent("filter-input-updated",{bubbles:!0,detail:{count:c,total:u}}))}function r(t,e,n){return{match:-1!==e.toLowerCase().indexOf(n.toLowerCase()),hideNew:e===n}}let s=FilterInputElement;window.customElements.get("filter-input")||(window.FilterInputElement=FilterInputElement,window.customElements.define("filter-input",FilterInputElement))},27552:(t,e,n)=>{n.d(e,{A:()=>a});let i=new WeakMap;let RemoteInputElement=class RemoteInputElement extends HTMLElement{constructor(){super();let t=r.bind(null,this,!0),e={currentQuery:null,oninput:function(t){let e;return function(n){clearTimeout(e),e=setTimeout(()=>{clearTimeout(e),t(n)},300)}}(e=>t(e)),fetch:t,controller:null};i.set(this,e)}static get observedAttributes(){return["src"]}attributeChangedCallback(t,e){e&&"src"===t&&r(this,!1)}connectedCallback(){let t=this.input;if(!t)return;t.setAttribute("autocomplete","off"),t.setAttribute("spellcheck","false");let e=i.get(this);e&&(t.addEventListener("focus",e.fetch),t.addEventListener("change",e.fetch),t.addEventListener("input",e.oninput))}disconnectedCallback(){let t=this.input;if(!t)return;let e=i.get(this);e&&(t.removeEventListener("focus",e.fetch),t.removeEventListener("change",e.fetch),t.removeEventListener("input",e.oninput))}get input(){let t=this.querySelector("input, textarea");return t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement?t:null}get src(){return this.getAttribute("src")||""}set src(t){this.setAttribute("src",t)}};async function r(t,e,n){let r,a=t.input;if(!a)return;let l=i.get(t);if(!l)return;let o=a.value;if(e&&l.currentQuery===o)return;l.currentQuery=o;let d=t.src;if(!d)return;let u=document.getElementById(t.getAttribute("aria-owns")||"");if(!u)return;let c=new URL(d,window.location.href),f=new URLSearchParams(c.search);f.append(t.getAttribute("param")||"q",o),c.search=f.toString(),l.controller?l.controller.abort():(t.dispatchEvent(new CustomEvent("loadstart")),t.setAttribute("loading","")),l.controller="AbortController"in window?new AbortController:{signal:null,abort(){}};let h="";try{r=await s(t,c.toString(),{signal:l.controller.signal,credentials:"same-origin",headers:{accept:"text/fragment+html"}}),h=await r.text(),t.removeAttribute("loading"),l.controller=null}catch(e){e instanceof Error&&"AbortError"!==e.name&&(t.removeAttribute("loading"),l.controller=null);return}r&&r.ok?(u.innerHTML=h,t.dispatchEvent(new CustomEvent("remote-input-success",{bubbles:!0,detail:{eventType:n?n.type:void 0}}))):t.dispatchEvent(new CustomEvent("remote-input-error",{bubbles:!0}))}async function s(t,e,n){try{let i=await fetch(e,n);return t.dispatchEvent(new CustomEvent("load")),t.dispatchEvent(new CustomEvent("loadend")),i}catch(e){throw e instanceof Error&&(null==e?void 0:e.name)!=="AbortError"&&(t.dispatchEvent(new CustomEvent("error")),t.dispatchEvent(new CustomEvent("loadend"))),e}}let a=RemoteInputElement;window.customElements.get("remote-input")||(window.RemoteInputElement=RemoteInputElement,window.customElements.define("remote-input",RemoteInputElement))},49728:(t,e,n)=>{function i(t){return Array.from(t.querySelectorAll('[role="tablist"] [role="tab"]')).filter(e=>e instanceof HTMLElement&&e.closest(t.tagName)===t)}n.d(e,{A:()=>TabContainerElement});let TabContainerElement=class TabContainerElement extends HTMLElement{constructor(){super(),this.addEventListener("keydown",t=>{let e=t.target;if(!(e instanceof HTMLElement)||e.closest(this.tagName)!==this||"tab"!==e.getAttribute("role")&&!e.closest('[role="tablist"]'))return;let n=i(this),s=n.indexOf(n.find(t=>t.matches('[aria-selected="true"]')));if("ArrowRight"===t.code){let t=s+1;t>=n.length&&(t=0),r(this,t)}else if("ArrowLeft"===t.code){let t=s-1;t<0&&(t=n.length-1),r(this,t)}else"Home"===t.code?(r(this,0),t.preventDefault()):"End"===t.code&&(r(this,n.length-1),t.preventDefault())}),this.addEventListener("click",t=>{let e=i(this);if(!(t.target instanceof Element)||t.target.closest(this.tagName)!==this)return;let n=t.target.closest('[role="tab"]');n instanceof HTMLElement&&n.closest('[role="tablist"]')&&r(this,e.indexOf(n))})}connectedCallback(){for(let t of i(this))t.hasAttribute("aria-selected")||t.setAttribute("aria-selected","false"),t.hasAttribute("tabindex")||("true"===t.getAttribute("aria-selected")?t.setAttribute("tabindex","0"):t.setAttribute("tabindex","-1"))}};function r(t,e){let n=i(t),r=Array.from(t.querySelectorAll('[role="tabpanel"]')).filter(e=>e.closest(t.tagName)===t),s=n[e],a=r[e];if(t.dispatchEvent(new CustomEvent("tab-container-change",{bubbles:!0,cancelable:!0,detail:{relatedTarget:a}}))){for(let t of n)t.setAttribute("aria-selected","false"),t.setAttribute("tabindex","-1");for(let t of r)t.hidden=!0,t.hasAttribute("tabindex")||t.hasAttribute("data-tab-container-no-tabstop")||t.setAttribute("tabindex","0");s.setAttribute("aria-selected","true"),s.setAttribute("tabindex","0"),s.focus(),a.hidden=!1,t.dispatchEvent(new CustomEvent("tab-container-changed",{bubbles:!0,detail:{relatedTarget:a}}))}}window.customElements.get("tab-container")||(window.TabContainerElement=TabContainerElement,window.customElements.define("tab-container",TabContainerElement))},80147:(t,e,n)=>{n.d(e,{u:()=>ModalDialogElement});var i,r,s,a,l=n(69676),o=n(55966),d=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)},u=function(t,e,n,i,r){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!r)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!r:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?r.call(t,n):r?r.value=n:e.set(t,n),n};function c(t){document.activeElement!==t&&t?.focus()}let f=[];function h(t){let e=t.target,n=e?.closest("button");if(!n||n.hasAttribute("disabled")||"true"===n.getAttribute("aria-disabled"))return;let i=n?.getAttribute("data-show-dialog-id");if(i){t.stopPropagation();let e=document.getElementById(i);if(e instanceof ModalDialogElement){e.openButton=n,e.show(),t.preventDefault();return}}if(f.length&&(i=n.getAttribute("data-close-dialog-id")||n.getAttribute("data-submit-dialog-id"))){let t=document.getElementById(i);if(t instanceof ModalDialogElement){let e=f.findIndex(t=>t.id===i);f.splice(e,1),t.close(n.hasAttribute("data-submit-dialog-id"))}}}function m(t){!(t instanceof KeyboardEvent)||"keydown"!==t.type||"Enter"!==t.key||t.ctrlKey||t.altKey||t.metaKey||t.shiftKey||h(t)}function b(t){let e=t.target;if(e?.closest("button"))return;let n=f[f.length-1];n&&(e.closest(`#${n.getAttribute("id")}`)||e.ownerDocument.addEventListener("mouseup",t=>{t.target===e&&(f.pop(),n.close())},{once:!0}))}let ModalDialogElement=class ModalDialogElement extends HTMLElement{constructor(){super(...arguments),i.add(this),r.set(this,new AbortController)}get open(){return this.hasAttribute("open")}set open(t){if(t)this.open||(this.setAttribute("open",""),this.setAttribute("aria-disabled","false"),document.body.style.paddingRight=`${window.innerWidth-document.body.clientWidth}px`,document.body.style.overflow="hidden",d(this,i,"a",s)?.classList.remove("Overlay--hidden"),d(this,r,"f").signal.aborted&&u(this,r,new AbortController,"f"),(0,l.iE)(this,this.querySelector("[autofocus]"),d(this,r,"f").signal),f.push(this));else{if(!this.open)return;this.removeAttribute("open"),this.setAttribute("aria-disabled","true"),d(this,i,"a",s)?.classList.add("Overlay--hidden"),document.body.style.paddingRight="0",document.body.style.overflow="initial",d(this,r,"f").abort();let t=this.openButton?.closest("details")||this.openButton?.closest("action-menu");t?c((0,o.Z0)(t)):c(this.openButton),this.openButton=null}}get showButtons(){return document.querySelectorAll(`button[data-show-dialog-id='${this.id}']`)}connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","dialog"),document.addEventListener("click",h),document.addEventListener("keydown",m),document.addEventListener("mousedown",b),this.addEventListener("keydown",t=>d(this,i,"m",a).call(this,t))}show(){this.open=!0}close(t=!1){if(!1===this.open)return;let e=new Event(t?"close":"cancel");this.dispatchEvent(e),this.open=!1}};r=new WeakMap,i=new WeakSet,s=function(){return this.parentElement?.hasAttribute("data-modal-dialog-overlay")?this.parentElement:null},a=function(t){if(t instanceof KeyboardEvent&&!t.isComposing&&this.open)switch(t.key){case"Escape":this.close(),t.preventDefault(),t.stopPropagation();break;case"Enter":t.target.getAttribute("data-close-dialog-id")===this.id&&t.stopPropagation()}},window.customElements.get("modal-dialog")||(window.ModalDialogElement=ModalDialogElement,window.customElements.define("modal-dialog",ModalDialogElement))},72705:(t,e,n)=>{n.d(e,{A:()=>TaskListsElement});let i=new WeakMap,r=null;function s(t,e){return t.closest("task-lists")===e.closest("task-lists")}function a(t){if(t.currentTarget!==t.target)return;let e=t.currentTarget;if(!(e instanceof Element))return;let n=e.closest(".contains-task-list");if(!n||(e.classList.add("is-ghost"),t.dataTransfer&&t.dataTransfer.setData("text/plain",(e.textContent||"").trim()),!e.parentElement))return;let s=Array.from(e.parentElement.children),a=s.indexOf(e),l=i.get(e);l&&l.sortStarted(n),r={didDrop:!1,dragging:e,dropzone:e,sourceList:n,sourceSibling:s[a+1]||null,sourceIndex:a}}function l(t){if(!r)return;let e=t.currentTarget;if(e instanceof Element){if(!s(r.dragging,e))return void t.stopPropagation();t.preventDefault(),t.dataTransfer&&(t.dataTransfer.dropEffect="move"),r.dropzone!==e&&(r.dragging.classList.add("is-dragging"),r.dropzone=e,function(t,e){if(t.parentNode===e.parentNode){let n=t;for(;n;){if(n===e)return!0;n=n.previousElementSibling}}return!1}(r.dragging,e)?e.before(r.dragging):e.after(r.dragging))}}function o(t){if(!r)return;t.preventDefault(),t.stopPropagation();let e=t.currentTarget;if(!(e instanceof Element)||(r.didDrop=!0,!r.dragging.parentElement))return;let n=Array.from(r.dragging.parentElement.children).indexOf(r.dragging),s=e.closest(".contains-task-list");if(!s||r.sourceIndex===n&&r.sourceList===s)return;r.sourceList===s&&r.sourceIndex<n&&n++;let a={list:r.sourceList,index:r.sourceIndex},l={list:s,index:n},o=i.get(r.dragging);o&&o.sortFinished({src:a,dst:l})}function d(){r&&(r.dragging.classList.remove("is-dragging"),r.dragging.classList.remove("is-ghost"),r.didDrop||r.sourceList.insertBefore(r.dragging,r.sourceSibling),r=null)}function u(t){if(!r)return;let e=t.currentTarget;if(e instanceof Element){if(!s(r.dragging,e))return void t.stopPropagation();t.preventDefault(),t.dataTransfer&&(t.dataTransfer.dropEffect="move")}}let c=new WeakMap;let TaskListsElement=class TaskListsElement extends HTMLElement{connectedCallback(){this.addEventListener("change",t=>{let e=t.target;e instanceof HTMLInputElement&&e.classList.contains("task-list-item-checkbox")&&this.dispatchEvent(new CustomEvent("task-lists-check",{bubbles:!0,detail:{position:function(t){let e=v(t);if(!e)throw Error(".contains-task-list not found");let n=t.closest(".task-list-item"),i=Array.from(e.children).filter(t=>"LI"===t.tagName),r=n?i.indexOf(n):-1;return[function(t){let e=t.closest("task-lists");if(!e)throw Error("parent not found");return L(e).indexOf(t)}(e),r]}(e),checked:e.checked}}))});let t=new MutationObserver(w.bind(null,this));c.set(this,t),t.observe(this,{childList:!0,subtree:!0}),w(this)}disconnectedCallback(){let t=c.get(this);t&&t.disconnect()}get disabled(){return this.hasAttribute("disabled")}set disabled(t){t?this.setAttribute("disabled",""):this.removeAttribute("disabled")}get sortable(){return this.hasAttribute("sortable")}set sortable(t){t?this.setAttribute("sortable",""):this.removeAttribute("sortable")}static get observedAttributes(){return["disabled"]}attributeChangedCallback(t,e,n){e!==n&&"disabled"===t&&A(this)}};let f=document.createElement("template"),h=document.createElement("span");h.classList.add("handle");let m=document.createElementNS("http://www.w3.org/2000/svg","svg");m.classList.add("drag-handle"),m.setAttribute("aria-hidden","true"),m.setAttribute("width","16"),m.setAttribute("height","16");let b=document.createElementNS("http://www.w3.org/2000/svg","path");b.setAttribute("d","M10 13a1 1 0 100-2 1 1 0 000 2zm-4 0a1 1 0 100-2 1 1 0 000 2zm1-5a1 1 0 11-2 0 1 1 0 012 0zm3 1a1 1 0 100-2 1 1 0 000 2zm1-5a1 1 0 11-2 0 1 1 0 012 0zM6 5a1 1 0 100-2 1 1 0 000 2z"),f.content.appendChild(h),h.appendChild(m),m.appendChild(b);let g=new WeakMap;function p(t){let e=t.currentTarget;if(!(e instanceof Element))return;let n=e.closest("task-lists");n instanceof TaskListsElement&&n.sortable&&!n.disabled&&e.classList.add("hovered")}function E(t){let e=t.currentTarget;e instanceof Element&&e.classList.remove("hovered")}function v(t){let e=t.parentElement;return e?e.closest(".contains-task-list"):null}function w(t){for(let e of t.querySelectorAll(".contains-task-list > .task-list-item"))v(e)===function t(e){let n=v(e);return n?t(n)||n:null}(e)&&function(t){if(g.get(t))return;g.set(t,!0);let e=t.closest("task-lists");if(!(e instanceof TaskListsElement)||e.querySelectorAll(".task-list-item").length<=1)return;let n=f.content.cloneNode(!0),r=n.querySelector(".handle");if(t.prepend(n),!r)throw Error("handle not found");r.addEventListener("mouseenter",C),r.addEventListener("mouseleave",x),i.set(t,{sortStarted:k,sortFinished:T}),t.addEventListener("dragstart",a),t.addEventListener("dragenter",l),t.addEventListener("dragend",d),t.addEventListener("drop",o),t.addEventListener("dragover",u),t.addEventListener("mouseenter",p),t.addEventListener("mouseleave",E)}(e);A(t)}function A(t){for(let e of t.querySelectorAll(".task-list-item"))e.classList.toggle("enabled",!t.disabled);for(let e of t.querySelectorAll(".task-list-item-checkbox"))e instanceof HTMLInputElement&&(e.disabled=t.disabled)}function L(t){return Array.from(t.querySelectorAll("ol, ul")).filter(t=>!t.closest("tracking-block"))}let y=new WeakMap;function k(t){let e=t.closest("task-lists");if(!e)throw Error("parent not found");y.set(e,L(e))}function T({src:t,dst:e}){let n=t.list.closest("task-lists");if(!n)return;let i=y.get(n);i&&(y.delete(n),n.dispatchEvent(new CustomEvent("task-lists-move",{bubbles:!0,detail:{src:[i.indexOf(t.list),t.index],dst:[i.indexOf(e.list),e.index]}})))}function C(t){let e=t.currentTarget;if(!(e instanceof Element))return;let n=e.closest(".task-list-item");if(!n)return;let i=n.closest("task-lists");i instanceof TaskListsElement&&i.sortable&&!i.disabled&&n.setAttribute("draggable","true")}function x(t){if(r)return;let e=t.currentTarget;if(!(e instanceof Element))return;let n=e.closest(".task-list-item");n&&n.setAttribute("draggable","false")}window.customElements.get("task-lists")||(window.TaskListsElement=TaskListsElement,window.customElements.define("task-lists",TaskListsElement))}}]);
//# sourceMappingURL=vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643-d23790fcb80c.js.map