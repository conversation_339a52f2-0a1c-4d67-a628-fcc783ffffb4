"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_behaviors_dist_esm_index_mjs"],{69676:(e,t,n)=>{let i;n.d(t,{z0:()=>d,NK:()=>E,eb:()=>T,iE:()=>function e(t,n,o){let l,r=new AbortController,a=null!=o?o:r.signal;t.setAttribute("data-focus-trap","active");let s=document.createElement("span");s.setAttribute("class","sentinel"),s.setAttribute("tabindex","0"),s.setAttribute("aria-hidden","true"),s.onfocus=()=>{let e=(0,u.Z0)(t,!0);null==e||e.focus()};let d=document.createElement("span");d.setAttribute("class","sentinel"),d.setAttribute("tabindex","0"),d.setAttribute("aria-hidden","true"),d.onfocus=()=>{let e=(0,u.Z0)(t);null==e||e.focus()},t.prepend(s),t.append(d);let f=function(e,t){let n=new MutationObserver(n=>{for(let i of n)if("childList"===i.type&&i.addedNodes.length){if(Array.from(i.addedNodes).filter(e=>e instanceof HTMLElement&&e.classList.contains("sentinel")&&"SPAN"===e.tagName).length)return;let n=e.firstElementChild,o=e.lastElementChild,[l,r]=t;(null==n?void 0:n.classList.contains("sentinel"))||e.insertAdjacentElement("afterbegin",l),(null==o?void 0:o.classList.contains("sentinel"))||e.insertAdjacentElement("beforeend",r)}});return n.observe(e,{childList:!0}),n}(t,[s,d]);function c(e){if(e instanceof HTMLElement&&document.contains(t))if(t.contains(e)){l=e;return}else{if(l&&(0,u.AO)(l)&&t.contains(l))return void l.focus();if(n&&t.contains(n))return void n.focus();let e=(0,u.Z0)(t);return void(null==e||e.focus())}}let g=function(e){let t=new AbortController;return e.addEventListener("abort",()=>{t.abort()}),t}(a);if(i){let e=i;i.container.setAttribute("data-focus-trap","suspended"),i.controller.abort(),h.push(e)}g.signal.addEventListener("abort",()=>{i=void 0}),a.addEventListener("abort",()=>{t.removeAttribute("data-focus-trap");let n=t.getElementsByClassName("sentinel");for(;n.length>0;)n[0].remove();let i=h.findIndex(e=>e.container===t);i>=0&&h.splice(i,1),f.disconnect();let o=h.pop();o&&e(o.container,o.initialFocus,o.originalSignal)}),document.addEventListener("focus",e=>{c(e.target)},{signal:g.signal,capture:!0}),c(document.activeElement),i={container:t,controller:g,initialFocus:n,originalSignal:a};let p=h.findIndex(e=>e.container===t);if(p>=0&&h.splice(p,1),!o)return r},zB:()=>L,uG:()=>r,yX:()=>y,y8:()=>A,cY:()=>O,eg:()=>M,Nt:()=>x,uw:()=>C,Rt:()=>H});let o={"outside-top":["outside-bottom","outside-right","outside-left","outside-bottom"],"outside-bottom":["outside-top","outside-right","outside-left","outside-bottom"],"outside-left":["outside-right","outside-bottom","outside-top","outside-bottom"],"outside-right":["outside-left","outside-bottom","outside-top","outside-bottom"]},l={start:["end","center"],end:["start","center"],center:["end","start"]};function r(e,t,n={}){let i=function(e){if(function(e){var t;if("DIALOG"===e.tagName)return!0;try{if(e.matches(":popover-open")&&/native code/.test(null==(t=document.body.showPopover)?void 0:t.toString()))return!0}catch(e){}return!1}(e))return document.body;let t=e.parentNode;for(;null!==t;){if(t instanceof HTMLElement&&"static"!==getComputedStyle(t).position)return t;t=t.parentNode}return document.body}(e),d=function(e){let t=e;for(;null!==t&&t instanceof Element&&"visible"===getComputedStyle(t).overflow;)t=t.parentNode;let n=t!==document.body&&t instanceof HTMLElement?t:document.body,i=n.getBoundingClientRect(),o=getComputedStyle(n),[l,r,a,s]=[o.borderTopWidth,o.borderLeftWidth,o.borderRightWidth,o.borderBottomWidth].map(e=>parseInt(e,10)||0);return{top:i.top+l,left:i.left+r,width:i.width-a-r,height:Math.max(i.height-l-s,n===document.body?window.innerHeight:-1/0)}}(i),u=getComputedStyle(i),f=i.getBoundingClientRect(),[c,g]=[u.borderTopWidth,u.borderLeftWidth].map(e=>parseInt(e,10)||0);return function(e,t,n,i,{side:r,align:a,allowOutOfBounds:d,anchorOffset:u,alignmentOffset:f}){let c={top:e.top-t.top,left:e.left-t.left,width:e.width,height:e.height},g=s(n,i,r,a,u,f),h=r,p=a;if(g.top-=t.top,g.left-=t.left,!d){var m,b,v,w,A,E,T,y;let d=o[r],L=0;if(d){let e=r;for(;L<d.length&&(m=e,b=g,v=c,w=n,"outside-top"===m||"outside-bottom"===m?b.top<v.top||b.top+w.height>v.height+v.top:b.left<v.left||b.left+w.width>v.width+v.left);){let o=d[L++];e=o,g=s(n,i,o,a,u,f),g.top-=t.top,g.left-=t.left,h=o}}let H=l[a],O=0;if(H){let e=a;for(;O<H.length&&(A=e,E=g,T=c,y=n,"end"===A?E.left<T.left:"start"===A||"center"===A?E.left+y.width>T.left+T.width||E.left<T.left:void 0);){let o=H[O++];e=o,g=s(n,i,h,o,u,f),g.top-=t.top,g.left-=t.left,p=o}}g.top<c.top&&(g.top=c.top),g.left<c.left&&(g.left=c.left),g.left+n.width>e.width+c.left&&(g.left=e.width+c.left-n.width),d&&L<d.length&&g.top+n.height>e.height+c.top&&(g.top=Math.max(e.height+c.top-n.height,0))}return Object.assign(Object.assign({},g),{anchorSide:h,anchorAlign:p})}(d,{top:f.top+c,left:f.left+g},e.getBoundingClientRect(),t instanceof Element?t.getBoundingClientRect():t,function(e={}){var t,n,i,o,l;let r=null!=(t=e.side)?t:a.side,s=null!=(n=e.align)?n:a.align;return{side:r,align:s,anchorOffset:null!=(i=e.anchorOffset)?i:"inside-center"===r?0:a.anchorOffset,alignmentOffset:null!=(o=e.alignmentOffset)?o:"center"!==s&&r.startsWith("inside")?a.alignmentOffset:0,allowOutOfBounds:null!=(l=e.allowOutOfBounds)?l:a.allowOutOfBounds}}(n))}let a={side:"outside-bottom",align:"start",anchorOffset:4,alignmentOffset:4,allowOutOfBounds:!1};function s(e,t,n,i,o,l){let r=t.left+t.width,a=t.top+t.height,s=-1,d=-1;return"outside-top"===n?s=t.top-o-e.height:"outside-bottom"===n?s=a+o:"outside-left"===n?d=t.left-o-e.width:"outside-right"===n&&(d=r+o),("outside-top"===n||"outside-bottom"===n)&&(d="start"===i?t.left+l:"center"===i?t.left-(e.width-t.width)/2+l:r-e.width-l),("outside-left"===n||"outside-right"===n)&&(s="start"===i?t.top+l:"center"===i?t.top-(e.height-t.height)/2+l:a-e.height-l),"inside-top"===n?s=t.top+o:"inside-bottom"===n?s=a-o-e.height:"inside-left"===n?d=t.left+o:"inside-right"===n?d=r-o-e.width:"inside-center"===n&&(d=(r+t.left)/2-e.width/2+o),"inside-top"===n||"inside-bottom"===n?d="start"===i?t.left+l:"center"===i?t.left-(e.width-t.width)/2+l:r-e.width-l:("inside-left"===n||"inside-right"===n||"inside-center"===n)&&(s="start"===i?t.top+l:"center"===i?t.top-(e.height-t.height)/2+l:a-e.height-l),{top:s,left:d}}var d,u=n(55966);let f=!1;function c(){}try{let e=Object.create({},{signal:{get(){f=!0}}});window.addEventListener("test",c,e),window.removeEventListener("test",c,e)}catch(e){}function g(){f||(!function(){if("undefined"==typeof window)return;let e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(t,n,i){return"object"==typeof i&&"signal"in i&&i.signal instanceof AbortSignal&&e.call(i.signal,"abort",()=>{this.removeEventListener(t,n,i)}),e.call(this,t,n,i)}}(),f=!0)}g();let h=[];var p=n(84366);let m=1e4;g(),function(e){e[e.ArrowHorizontal=1]="ArrowHorizontal",e[e.ArrowVertical=2]="ArrowVertical",e[e.JK=4]="JK",e[e.HL=8]="HL",e[e.HomeAndEnd=16]="HomeAndEnd",e[e.PageUpDown=256]="PageUpDown",e[e.WS=32]="WS",e[e.AD=64]="AD",e[e.Tab=128]="Tab",e[e.Backspace=512]="Backspace",e[e.ArrowAll=3]="ArrowAll",e[e.HJKL=12]="HJKL",e[e.WASD=96]="WASD",e[e.All=511]="All"}(d||(d={}));let b={ArrowLeft:d.ArrowHorizontal,ArrowDown:d.ArrowVertical,ArrowUp:d.ArrowVertical,ArrowRight:d.ArrowHorizontal,h:d.HL,j:d.JK,k:d.JK,l:d.HL,a:d.AD,s:d.WS,w:d.WS,d:d.AD,Tab:d.Tab,Home:d.HomeAndEnd,End:d.HomeAndEnd,PageUp:d.PageUpDown,PageDown:d.PageUpDown,Backspace:d.Backspace},v={ArrowLeft:"previous",ArrowDown:"next",ArrowUp:"previous",ArrowRight:"next",h:"previous",j:"next",k:"previous",l:"next",a:"previous",s:"next",w:"previous",d:"next",Tab:"next",Home:"start",End:"end",PageUp:"start",PageDown:"end",Backspace:"previous"};function w(e){let t=v[e.key];if("Tab"===e.key&&e.shiftKey)return"previous";let n=(0,p.U)();if(n&&e.metaKey||!n&&e.ctrlKey){if("ArrowLeft"===e.key||"ArrowUp"===e.key)return"start";else if("ArrowRight"===e.key||"ArrowDown"===e.key)return"end"}return t}let A="data-is-active-descendant",E="activated-directly",T="activated-indirectly",y="data-has-active-descendant";function L(e,t){var n,i,o,l,r;let a,s,f,c=[],g=new WeakMap,h=null!=(n=null==t?void 0:t.bindKeys)?n:((null==t?void 0:t.getNextFocusable)?d.ArrowAll:d.ArrowVertical)|d.HomeAndEnd,L=null!=(i=null==t?void 0:t.focusOutBehavior)?i:"stop",H=null!=(o=null==t?void 0:t.focusInStrategy)?o:"previous",O=null==t?void 0:t.activeDescendantControl,x=null==t?void 0:t.onActiveDescendantChanged,M=null!=(l=null==t?void 0:t.preventScroll)&&l;function C(e,t=!1){let n=a;if(a=e,O)return void(e&&document.activeElement===O?N(n,e,t):D());n&&n!==e&&g.has(n)&&n.setAttribute("tabindex","-1"),null==e||e.setAttribute("tabindex","0")}function N(t,n,i=!1){n.id||n.setAttribute("id",`__primer_id_${m++}`),t&&t!==n&&t.removeAttribute(A),O&&(i||O.getAttribute("aria-activedescendant")!==n.id)&&(O.setAttribute("aria-activedescendant",n.id),e.setAttribute(y,n.id),n.setAttribute(A,i?E:T),null==x||x(n,t,i))}function D(t=a){for(let n of("first"===H&&(a=void 0),null==O||O.removeAttribute("aria-activedescendant"),e.removeAttribute(y),null==t||t.removeAttribute(A),e.querySelectorAll(`[${A}]`)))null==n||n.removeAttribute(A);null==x||x(void 0,t,!1)}function k(...e){let n=e.filter(e=>{var n,i;return null==(i=null==(n=null==t?void 0:t.focusableElementFilter)?void 0:n.call(t,e))||i});if(0!==n.length){for(let e of(c.splice(function(e){let t=e[0];if(0===c.length)return 0;let n=0,i=c.length-1;for(;n<=i;){var o;let e=Math.floor((n+i)/2);(o=t,(c[e].compareDocumentPosition(o)&Node.DOCUMENT_POSITION_PRECEDING)>0)?i=e-1:n=e+1}return n}(n),0,...n),n))g.has(e)||g.set(e,e.getAttribute("tabindex")),e.setAttribute("tabindex","-1");a||C(c[0])}}function S(...e){for(let t of e){let e=c.indexOf(t);e>=0&&c.splice(e,1);let n=g.get(t);void 0!==n&&(null===n?t.removeAttribute("tabindex"):t.setAttribute("tabindex",n),g.delete(t)),t===a&&C(c[0])}}let P={reverse:null==t?void 0:t.reverse,strict:null==t?void 0:t.strict,onlyTabbable:null==t?void 0:t.onlyTabbable};k(...(0,u.K1)(e,P)),C("function"==typeof H?H(document.body):c[0]),new MutationObserver(e=>{for(let t of e){for(let e of t.removedNodes)e instanceof HTMLElement&&S(...(0,u.K1)(e));"attributes"===t.type&&null===t.oldValue&&t.target instanceof HTMLElement&&S(t.target)}for(let t of e){for(let e of t.addedNodes)e instanceof HTMLElement&&k(...(0,u.K1)(e,P));"attributes"===t.type&&null!==t.oldValue&&t.target instanceof HTMLElement&&k(t.target)}}).observe(e,{subtree:!0,childList:!0,attributeFilter:["hidden","disabled"],attributeOldValue:!0});let W=new AbortController,B=null!=(r=null==t?void 0:t.abortSignal)?r:W.signal;B.addEventListener("abort",()=>{S(...c)}),e.addEventListener("mousedown",e=>{e.target instanceof HTMLElement&&e.target!==document.activeElement&&(s=c.indexOf(e.target))},{signal:B}),O?(e.addEventListener("focusin",e=>{e.target instanceof HTMLElement&&c.includes(e.target)&&(O.focus({preventScroll:M}),C(e.target))},{signal:B}),e.addEventListener("mousemove",({target:e})=>{if(!(e instanceof Node))return;let t=c.find(t=>t.contains(e));t&&C(t)},{signal:B,capture:!0}),O.addEventListener("focusin",()=>{a?N(void 0,a):C(c[0])},{signal:B}),O.addEventListener("focusout",()=>{D()},{signal:B})):e.addEventListener("focusin",t=>{if(t.target instanceof HTMLElement){if(void 0!==s)s>=0&&c[s]!==a&&C(c[s]),s=void 0;else if("previous"===H)C(t.target);else if("closest"===H||"first"===H)if(t.relatedTarget instanceof Element&&!e.contains(t.relatedTarget)){let e="previous"===f?c.length-1:0,t=c[e];null==t||t.focus({preventScroll:M});return}else C(t.target);else if("function"==typeof H)if(t.relatedTarget instanceof Element&&!e.contains(t.relatedTarget)){let e=H(t.relatedTarget);if((e?c.indexOf(e):-1)>=0&&e instanceof HTMLElement)return void e.focus({preventScroll:M});console.warn("Element requested is not a known focusable element.")}else C(t.target)}f=void 0},{signal:B});let R=null!=O?O:e;return"closest"===H&&document.addEventListener("keydown",e=>{"Tab"===e.key&&(f=w(e))},{signal:B,capture:!0}),R.addEventListener("keydown",n=>{var i;if(n.key in v){let o=b[n.key];if(!n.defaultPrevented&&(o&h)>0&&!function(e,t){let n=e.key,i=[...n].length,o=t instanceof HTMLInputElement&&"text"===t.type||t instanceof HTMLTextAreaElement;if(o&&(1===i||"Home"===n||"End"===n)||t instanceof HTMLSelectElement&&(1===i||"ArrowDown"===n&&(0,p.U)()&&!e.metaKey||"ArrowDown"===n&&!(0,p.U)()&&e.altKey)||t instanceof HTMLTextAreaElement&&("PageUp"===n||"PageDown"===n))return!0;if(o){let e=0===t.selectionStart&&0===t.selectionEnd,i=t.selectionStart===t.value.length&&t.selectionEnd===t.value.length;if("ArrowLeft"===n&&!e||"ArrowRight"===n&&!i||t instanceof HTMLTextAreaElement&&("ArrowUp"===n&&!e||"ArrowDown"===n&&!i))return!0}return!1}(n,document.activeElement)){let o,l=w(n);if((null==t?void 0:t.getNextFocusable)&&(o=t.getNextFocusable(l,null!=(i=document.activeElement)?i:void 0,n)),!o){let t=function(){if(!a)return 0;let t=c.indexOf(a),n=a===e?-1:0;return -1!==t?t:n}(),i=t;"previous"===l?i-=1:"start"===l?i=0:"next"===l?i+=1:i=c.length-1,i<0&&(i="wrap"===L&&"Tab"!==n.key?c.length-1:0),i>=c.length&&(i="wrap"===L&&"Tab"!==n.key?0:c.length-1),t!==i&&(o=c[i])}O?C(o||a,!0):o&&(f=l,o.focus({preventScroll:M})),("Tab"!==n.key||o)&&n.preventDefault()}}},{signal:B}),W}function H(e,t,{direction:n="vertical",startMargin:i=0,endMargin:o=0,behavior:l="smooth"}={}){let r="vertical"===n?"top":"left",a="vertical"===n?"bottom":"right",s="vertical"===n?"scrollTop":"scrollLeft",{[r]:d,[a]:u}=e.getBoundingClientRect(),{[r]:f,[a]:c}=t.getBoundingClientRect();if(d<f+i){let e=d-f+t[s];t.scrollTo({behavior:l,[r]:e-i})}else if(u>c-o){let e=u-c+t[s];t.scrollTo({behavior:l,[r]:e+o})}}function O(e){let t=e.getBoundingClientRect();return{top:t.top+window.pageYOffset,left:t.left+window.pageXOffset}}function x(e){let t=e,n=t.ownerDocument;if(!n||!t.offsetParent)return;let i=n.defaultView.HTMLElement;if(t!==n.body){for(;t!==n.body;){if(!(t.parentElement instanceof i))return;let{position:e,overflowY:n,overflowX:o}=getComputedStyle(t=t.parentElement);if("fixed"===e||"auto"===n||"auto"===o||"scroll"===n||"scroll"===o)break}return t instanceof Document?null:t}}function M(e,t){let n=t,i=e.ownerDocument;if(!i)return;let o=i.documentElement;if(!o||e===o)return;let l=C(e,n);if(!l)return;let r=(n=l._container)===i.documentElement&&i.defaultView?{top:i.defaultView.pageYOffset,left:i.defaultView.pageXOffset}:{top:n.scrollTop,left:n.scrollLeft},a=l.top-r.top,s=l.left-r.left,d=n.clientHeight,u=n.clientWidth,f=d-(a+e.offsetHeight),c=u-(s+e.offsetWidth);return{top:a,left:s,bottom:f,right:c,height:d,width:u}}function C(e,t){let n,i,o,l=e,r=l.ownerDocument;if(!r)return;let a=r.documentElement;if(!a)return;let s=r.defaultView.HTMLElement,d=0,u=0,f=l.offsetHeight,c=l.offsetWidth;for(;l!==r.body&&l!==t;){if(d+=l.offsetTop||0,u+=l.offsetLeft||0,!(l.offsetParent instanceof s))return;l=l.offsetParent}if(t&&t!==r&&t!==r.defaultView&&t!==r.documentElement&&t!==r.body)if(!(t instanceof s))return;else o=t,n=t.scrollHeight,i=t.scrollWidth;else{var g,h,p,m;o=a,g=r.body,h=a,n=Math.max(g.scrollHeight,h.scrollHeight,g.offsetHeight,h.offsetHeight,h.clientHeight),p=r.body,m=a,i=Math.max(p.scrollWidth,m.scrollWidth,p.offsetWidth,m.offsetWidth,m.clientWidth)}let b=n-(d+f),v=i-(u+c);return{top:d,left:u,bottom:b,right:v,_container:o}}},55966:(e,t,n)=>{function*i(e,t={}){var n,o;let a=null!=(n=t.strict)&&n,s=null!=(o=t.onlyTabbable)&&o?r:l,d=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e instanceof HTMLElement&&s(e,a)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),u=null;if(!t.reverse&&s(e,a)&&(yield e),t.reverse){let e=d.lastChild();for(;e;)u=e,e=d.lastChild()}else u=d.firstChild();for(;u instanceof HTMLElement;)yield u,u=t.reverse?d.previousNode():d.nextNode();t.reverse&&s(e,a)&&(yield e)}function o(e,t=!1){return i(e,{reverse:t,strict:!0,onlyTabbable:!0}).next().value}function l(e,t=!1){let n=["BUTTON","INPUT","SELECT","TEXTAREA","OPTGROUP","OPTION","FIELDSET"].includes(e.tagName)&&e.disabled,i=e.hidden,o=e instanceof HTMLInputElement&&"hidden"===e.type,r=e.classList.contains("sentinel");if(n||i||o||r)return!1;if(t){let t=getComputedStyle(e),n=0===e.offsetWidth||0===e.offsetHeight,i=["hidden","collapse"].includes(t.visibility),o="none"===t.display||!e.offsetParent,l=0===e.getClientRects().length;if(n||i||l||o)return!1}return null!=e.getAttribute("tabindex")||"true"===e.getAttribute("contenteditable")||"plaintext-only"===e.getAttribute("contenteditable")||(!(e instanceof HTMLAnchorElement)||null!=e.getAttribute("href"))&&-1!==e.tabIndex}function r(e,t=!1){return l(e,t)&&"-1"!==e.getAttribute("tabindex")}n.d(t,{AO:()=>r,K1:()=>i,Z0:()=>o,tp:()=>l})},84366:(e,t,n)=>{let i;function o(){return void 0===i&&(i=/^mac/i.test(window.navigator.platform)),i}n.d(t,{U:()=>o})}}]);
//# sourceMappingURL=vendors-node_modules_primer_behaviors_dist_esm_index_mjs-1a20f77a6df9.js.map