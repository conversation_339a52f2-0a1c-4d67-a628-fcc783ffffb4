"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_paths_index_ts"],{50723:(e,t,n)=>{n.d(t,{$$:()=>g,Al:()=>p,Aw:()=>$,D:()=>u,LH:()=>i,Lo:()=>c,NG:()=>r,RS:()=>a,af:()=>U,k7:()=>l,kD:()=>Q,m2:()=>o,xG:()=>m});var s=n(30450);function r({category:e,publisher:t,task:n}={}){if(!e&&!n&&!t)return"/marketplace/models/catalog";let i=new URLSearchParams;return i.append("type","models"),e&&i.append("category",e),n&&i.append("task",(0,s.y9)(n)),t&&i.append("publisher",t),`/marketplace?${i.toString()}`}let i=({registry:e,name:t})=>`/marketplace/models/${e}/${t}`,o=e=>`${i(e)}/playground`,a=()=>"/marketplace/models",$=e=>`${i(e)}/feedback`;function u({commit:e,filePath:t,repoOwner:n,repoName:s,beginLine:r,endLine:o,...a}){let $=new URLSearchParams;if(e&&$.set("c",e),t&&$.set("path",t),n&&$.set("l",n),s&&$.set("n",s),null!=r&&null!=o){let e="";e=r===o?`${Math.max(r-10,0)}-${o+10}`:`${r}-${o}`,$.set("lines",e)}let u=`${i(a)}/prompt`,c=$.toString();return c.length<1?u:`${u}?${c}`}let c=({org:e})=>`/organizations/${e}/settings/models/billing`,g=({owner:e,repo:t})=>`/${e}/${t}/settings/models/access-policy`;function p({repo:e,action:t,allModels:n=!1}){let r=["",e.ownerLogin,e.name,"models"];t&&r.push(t);let i=new URLSearchParams;n&&i.set("all_models","true");let o=i.toString(),a=r.map(s.QU).join("/");return o.length<1?a:`${a}?${o}`}function U({repo:e,commitish:t,action:n,path:r}){let i=["",e.ownerLogin,e.name,"models","prompt",n];return t&&i.push(t),r&&"/"!==r&&i.push(r),i.map(s.QU).join("/")}let l=e=>`${p({repo:e})}/prompt/new`,Q=(e,t)=>"isCustom"in t&&t.isCustom?`${p({repo:e})}/${t.registry}/${t.publisher}/${t.name}/playground`:`${p({repo:e})}/${t.registry}/${t.name}/playground`,m=(e,t,n)=>n?`/enterprises/${n}/settings/models`:e&&""!==t.trim()?`/organizations/${t}/settings/models/access-policy`:"/settings/models"},96235:(e,t,n)=>{n.d(t,{$$h:()=>r.$$h,$66:()=>r.$66,$Qz:()=>r.$Qz,$RX:()=>r.$RX,$jn:()=>r.$jn,A3O:()=>r.A3O,AGh:()=>r.AGh,ALm:()=>r.ALm,A_D:()=>r.A_D,Alr:()=>r.Alr,Aoe:()=>r.Aoe,AwS:()=>r.AwS,B2_:()=>r.B2_,BV1:()=>r.BV1,Blz:()=>r.Blz,BmX:()=>r.BmX,CF0:()=>r.CF0,CZ9:()=>r.CZ9,Cft:()=>r.Cft,ClY:()=>r.ClY,D2m:()=>r.D2m,Da2:()=>r.Da2,Do2:()=>r.Do2,Dso:()=>r.Dso,Dx2:()=>r.Dx2,E5w:()=>r.E5w,EKo:()=>r.EKo,FeW:()=>r.FeW,G7Q:()=>r.G7Q,GEj:()=>r.GEj,GGt:()=>r.GGt,GNm:()=>r.GNm,GZ8:()=>r.GZ8,Gll:()=>r.Gll,GpY:()=>r.GpY,Gy_:()=>r.Gy_,H1k:()=>r.H1k,H1o:()=>r.H1o,H26:()=>r.H26,HPl:()=>r.HPl,I6i:()=>r.I6i,I7E:()=>r.I7E,IDI:()=>s.ID,IIf:()=>r.IIf,IIj:()=>r.IIj,IO9:()=>r.IO9,IUS:()=>r.IUS,Ib9:()=>r.Ib9,IcB:()=>r.IcB,Ixh:()=>r.Ixh,JdH:()=>r.JdH,Jjz:()=>r.Jjz,Ju:()=>r.Ju,K11:()=>r.K11,L2v:()=>r.L2v,LHP:()=>r.LHP,LIP:()=>r.LIP,LnB:()=>r.LnB,LoF:()=>r.LoF,M1I:()=>r.M1I,MCR:()=>r.MCR,MFq:()=>r.MFq,MLt:()=>r.MLt,MN7:()=>r.MN7,MYd:()=>r.MYd,Mdp:()=>r.Mdp,MeY:()=>r.MeY,Mo3:()=>r.Mo3,MsP:()=>r.MsP,MtG:()=>r.MtG,MtY:()=>r.MtY,N2B:()=>r.N2B,NGT:()=>r.NGT,NZR:()=>r.NZR,N_y:()=>r.N_y,Nbg:()=>r.Nbg,Nyd:()=>r.Nyd,O0X:()=>r.O0X,OdG:()=>r.OdG,OqZ:()=>r.OqZ,P0E:()=>r.P0E,PK0:()=>r.PK0,PTA:()=>r.PTA,PUd:()=>r.PUd,PUr:()=>r.PUr,Pe3:()=>r.Pe3,Pmq:()=>r.Pmq,QDD:()=>r.QDD,QU3:()=>s.QU,Qe$:()=>r.Qe$,QpY:()=>r.QpY,QqB:()=>r.QqB,R0:()=>r.R0,R3J:()=>r.R3J,RSg:()=>r.RSg,RT3:()=>r.RT3,RTk:()=>r.RTk,SHX:()=>r.SHX,T3I:()=>r.T3I,T7T:()=>r.T7T,Ti7:()=>r.Ti7,Tk8:()=>r.Tk8,Tok:()=>r.Tok,Ty5:()=>r.Ty5,Uix:()=>r.Uix,Utu:()=>r.Utu,UzS:()=>r.UzS,V$4:()=>r.V$4,VB1:()=>r.VB1,VWf:()=>r.VWf,VvT:()=>r.VvT,WJ9:()=>r.WJ9,WJS:()=>r.WJS,WKX:()=>r.WKX,X6K:()=>r.X6K,XFG:()=>r.XFG,Xod:()=>r.Xod,Xub:()=>r.Xub,XyE:()=>r.XyE,Y65:()=>r.Y65,Y8Y:()=>r.Y8Y,YPg:()=>r.YPg,YQF:()=>r.YQF,ZD8:()=>r.ZD8,ZQn:()=>r.ZQn,Zn1:()=>r.Zn1,ZpJ:()=>r.ZpJ,_37:()=>r._37,_ON:()=>r._ON,_U0:()=>r._U0,__m:()=>r.__m,_nf:()=>r._nf,_qn:()=>r._qn,_w2:()=>r._w2,a3t:()=>r.a3t,a9m:()=>r.a9m,aFD:()=>r.aFD,aS1:()=>r.aS1,aT_:()=>r.aT_,afT:()=>r.afT,bSK:()=>r.bSK,bSP:()=>r.bSP,bdU:()=>r.bdU,buO:()=>r.buO,c6Y:()=>r.c6Y,cBf:()=>r.cBf,cP7:()=>r.cP7,cTB:()=>r.cTB,cgr:()=>r.cgr,clg:()=>r.clg,cmy:()=>r.cmy,cpj:()=>r.cpj,czP:()=>r.czP,dCN:()=>r.dCN,dQy:()=>r.dQy,dRG:()=>r.dRG,dX8:()=>r.dX8,e87:()=>r.e87,eOD:()=>r.eOD,eOR:()=>r.eOR,eXd:()=>r.eXd,ev_:()=>r.ev_,exA:()=>r.exA,fPe:()=>r.fPe,fQd:()=>r.fQd,ffV:()=>r.ffV,fjg:()=>r.fjg,fkb:()=>r.fkb,flg:()=>r.flg,gId:()=>r.gId,gT4:()=>r.gT4,ggd:()=>r.ggd,guj:()=>r.guj,h6D:()=>r.h6D,hgQ:()=>r.hgQ,hif:()=>r.hif,hmx:()=>r.hmx,i9S:()=>r.i9S,iT3:()=>r.iT3,ibI:()=>r.ibI,iv9:()=>r.iv9,j$Y:()=>r.j$Y,jIv:()=>r.jIv,jMU:()=>r.jMU,jQC:()=>r.jQC,jVo:()=>r.jVo,jnS:()=>r.jnS,jrm:()=>r.jrm,jzr:()=>r.jzr,k5q:()=>r.k5q,k7O:()=>r.k7O,kDW:()=>r.kDW,kT_:()=>r.kT_,lPv:()=>r.lPv,lVp:()=>r.lVp,l_Z:()=>r.l_Z,ldz:()=>r.ldz,lfg:()=>r.lfg,lkl:()=>r.lkl,ln4:()=>r.ln4,luI:()=>r.luI,lxH:()=>r.lxH,m0O:()=>r.m0O,m24:()=>r.m24,m2O:()=>r.m2O,mJ6:()=>r.mJ6,mMZ:()=>r.mMZ,mWC:()=>r.mWC,mir:()=>r.mir,n7E:()=>r.n7E,nD_:()=>r.nD_,nGs:()=>r.nGs,nSV:()=>r.nSV,n_x:()=>r.n_x,nc0:()=>r.nc0,nsQ:()=>r.nsQ,o8w:()=>r.o8w,oDn:()=>r.oDn,oLj:()=>r.oLj,oLy:()=>r.oLy,oc1:()=>r.oc1,oef:()=>r.oef,ofs:()=>r.ofs,ohj:()=>r.ohj,ouN:()=>r.ouN,oyk:()=>r.oyk,p01:()=>r.p01,p3r:()=>r.p3r,p7A:()=>r.p7A,pMt:()=>r.pMt,pTz:()=>r.pTz,pY7:()=>r.pY7,pjc:()=>r.pjc,q0K:()=>r.q0K,qsO:()=>r.qsO,rX4:()=>r.rX4,rZ8:()=>r.rZ8,rzN:()=>r.rzN,s6C:()=>r.s6C,s7H:()=>r.s7H,sOD:()=>r.sOD,t0c:()=>r.t0c,t4j:()=>r.t4j,tTz:()=>r.tTz,uSD:()=>r.uSD,udC:()=>r.udC,uxJ:()=>r.uxJ,vK6:()=>r.vK6,vc8:()=>r.vc8,vhE:()=>r.vhE,vki:()=>r.vki,w7M:()=>r.w7M,w8S:()=>r.w8S,wAV:()=>r.wAV,wHS:()=>r.wHS,wYU:()=>r.wYU,w_K:()=>r.w_K,wgM:()=>r.wgM,whC:()=>r.whC,x21:()=>r.x21,x8I:()=>r.x8I,xGA:()=>r.xGA,xZU:()=>r.xZU,xlN:()=>r.xlN,xwZ:()=>r.xwZ,y15:()=>r.y15,y2B:()=>r.y2B,yH8:()=>s.yH,yIb:()=>r.yIb,yMx:()=>r.yMx,yf2:()=>r.yf2,ygX:()=>r.ygX,ylB:()=>r.ylB,zUz:()=>r.zUz,zc2:()=>r.zc2,zg2:()=>r.zg2,zvd:()=>r.zvd});var s=n(30450),r=n(50860)},48541:(e,t,n)=>{n.d(t,{f:()=>r});var s=n(30450);let r=({org:e})=>`/organizations/${(0,s.QU)(e)}/settings/custom-models`},50860:(e,t,n)=>{n.d(t,{$$h:()=>i.$$,$66:()=>ez,$Qz:()=>er,$RX:()=>ej,$jn:()=>n9,A3O:()=>L,AGh:()=>tL,ALm:()=>V,A_D:()=>ty,Alr:()=>i.Al,Aoe:()=>eF,AwS:()=>i.Aw,B2_:()=>tK,BV1:()=>es,Blz:()=>eR,BmX:()=>ts,CF0:()=>T,CZ9:()=>eH,Cft:()=>sy,ClY:()=>eJ,D2m:()=>tx,Da2:()=>i.D,Do2:()=>K,Dso:()=>nu,Dx2:()=>j,E5w:()=>tD,EKo:()=>tj,FeW:()=>I,G7Q:()=>tQ,GEj:()=>nQ,GGt:()=>$,GNm:()=>nN,GZ8:()=>n0,Gll:()=>eK,GpY:()=>eg,Gy_:()=>P,H1k:()=>e5,H1o:()=>tc,H26:()=>nj,HPl:()=>nr,I6i:()=>th,I7E:()=>tp,IIf:()=>se,IIj:()=>t2,IO9:()=>W,IUS:()=>st,Ib9:()=>tH,IcB:()=>ew,Ixh:()=>nI,JdH:()=>nO,Jjz:()=>te,Ju:()=>t4,K11:()=>sn,L2v:()=>t1,LHP:()=>i.LH,LIP:()=>sh,LnB:()=>tw,LoF:()=>i.Lo,M1I:()=>sa,MCR:()=>nb,MFq:()=>tV,MLt:()=>eu,MN7:()=>tC,MYd:()=>Y,Mdp:()=>tX,MeY:()=>sU,Mo3:()=>e$,MsP:()=>sc,MtG:()=>nR,MtY:()=>ep,N2B:()=>nA,NGT:()=>i.NG,NZR:()=>tE,N_y:()=>nT,Nbg:()=>e8,Nyd:()=>Z,O0X:()=>t8,OdG:()=>tI,OqZ:()=>nt,P0E:()=>nY,PK0:()=>nF,PTA:()=>D,PUd:()=>eL,PUr:()=>ss,Pe3:()=>no,Pmq:()=>d,QDD:()=>n6,Qe$:()=>sp,QpY:()=>C,QqB:()=>t0,R0:()=>ne,R3J:()=>ti,RSg:()=>i.RS,RT3:()=>ee,RTk:()=>eY,SHX:()=>y,T3I:()=>en,T7T:()=>ey,Ti7:()=>eB,Tk8:()=>tg,Tok:()=>nh,Ty5:()=>p,Uix:()=>nX,Utu:()=>t5,UzS:()=>sm,V$4:()=>nS,VB1:()=>ng,VWf:()=>eo,VvT:()=>n3,WJ9:()=>v,WJS:()=>tU,WKX:()=>tm,X6K:()=>eV,XFG:()=>n4,Xod:()=>t9,Xub:()=>nM,XyE:()=>e6,Y65:()=>N,Y8Y:()=>u,YPg:()=>n$,YQF:()=>e1,ZD8:()=>eT,ZQn:()=>nB,Zn1:()=>nV,ZpJ:()=>tA,_37:()=>nZ,_ON:()=>su,_U0:()=>s_,__m:()=>tW,_nf:()=>Q,_qn:()=>nq,_w2:()=>n_,a3t:()=>eP,a9m:()=>tT,aFD:()=>sr,aS1:()=>tl,aT_:()=>so,afT:()=>i.af,bSK:()=>nl,bSP:()=>ek,bdU:()=>eS,buO:()=>B,c6Y:()=>tN,cBf:()=>nd,cP7:()=>nm,cTB:()=>S,cgr:()=>nG,clg:()=>ec,cmy:()=>x,cpj:()=>k,czP:()=>ns,dCN:()=>m,dQy:()=>nw,dRG:()=>e7,dX8:()=>ef,e87:()=>tu,eOD:()=>eC,eOR:()=>eD,eXd:()=>nx,ev_:()=>t6,exA:()=>nv,fPe:()=>o.f,fQd:()=>eQ,ffV:()=>nE,fjg:()=>tO,fkb:()=>f,flg:()=>n8,gId:()=>e3,gT4:()=>tM,ggd:()=>G,guj:()=>ni,h6D:()=>nz,hgQ:()=>tS,hif:()=>s$,hmx:()=>n1,i9S:()=>e9,iT3:()=>tt,ibI:()=>n7,iv9:()=>nL,j$Y:()=>tB,jIv:()=>tk,jMU:()=>H,jQC:()=>el,jVo:()=>tb,jnS:()=>em,jrm:()=>eX,jzr:()=>w,k5q:()=>si,k7O:()=>i.k7,kDW:()=>i.kD,kT_:()=>ev,lPv:()=>eA,lVp:()=>to,l_Z:()=>a,ldz:()=>tP,lfg:()=>sg,lkl:()=>eE,ln4:()=>nH,luI:()=>M,lxH:()=>nf,m0O:()=>tG,m24:()=>nK,m2O:()=>i.m2,mJ6:()=>eq,mMZ:()=>nn,mWC:()=>n5,mir:()=>nW,n7E:()=>e4,nD_:()=>X,nGs:()=>eI,nSV:()=>eZ,n_x:()=>F,nc0:()=>b,nsQ:()=>ta,o8w:()=>nD,oDn:()=>eG,oLj:()=>tR,oLy:()=>tY,oc1:()=>t$,oef:()=>c,ofs:()=>eM,ohj:()=>sl,ouN:()=>sQ,oyk:()=>td,p01:()=>eW,p3r:()=>U,p7A:()=>z,pMt:()=>sf,pTz:()=>tz,pY7:()=>O,pjc:()=>nC,q0K:()=>nP,qsO:()=>_,rX4:()=>n2,rZ8:()=>e0,rzN:()=>J,s6C:()=>tF,s7H:()=>eO,sOD:()=>tr,t0c:()=>ea,t4j:()=>np,tTz:()=>h,uSD:()=>nU,udC:()=>ny,uxJ:()=>ei,vK6:()=>t7,vc8:()=>tn,vhE:()=>tZ,vki:()=>eU,w7M:()=>ex,w8S:()=>e2,wAV:()=>sd,wHS:()=>R,wYU:()=>t_,w_K:()=>l,wgM:()=>tf,whC:()=>eh,x21:()=>tq,x8I:()=>nk,xGA:()=>i.xG,xZU:()=>t3,xlN:()=>E,xwZ:()=>e_,y15:()=>tJ,y2B:()=>na,yIb:()=>eN,yMx:()=>g,yf2:()=>tv,ygX:()=>A,ylB:()=>eb,zUz:()=>ed,zc2:()=>nc,zg2:()=>nJ,zvd:()=>q});var s=n(96679),r=n(30450),i=n(50723),o=n(48541);let a=({path:e})=>new URL(e,s.fV.origin).toString(),$=({searchTerm:e})=>`/search?q=${(0,r.QU)(`${e}`)}&type=code`,u=({owner:e,repo:t,searchTerm:n})=>`/search?q=${(0,r.QU)(`repo:${e}/${t} ${n}`)}&type=code`,c=()=>"/search",g=()=>"/search/advanced",p=()=>"/search/stats",U=({owner:e})=>`/${(0,r.QU)(e)}`,l=({owner:e})=>`/${(0,r.QU)(e)}.png`,Q=({bot_slug:e})=>`/copilot/hovercard?bot=${(0,r.QU)(e)}`,m=({owner:e})=>`/users/${(0,r.QU)(e)}/hovercard`,f=({owner:e,team:t})=>`/orgs/${(0,r.QU)(e)}/teams/${(0,r.QU)(t)}`,d=({owner:e,team:t})=>`/orgs/${(0,r.QU)(e)}/teams/${(0,r.QU)(t)}/hovercard`,_=({owner:e})=>`/orgs/${(0,r.QU)(e)}/hovercard`;function y({owner:e,repo:t,action:n}){return n?`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/${n}`:`/${(0,r.QU)(e)}/${(0,r.QU)(t)}`}let h=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/stats`,w=({owner:e,repo:t,framework:n,packageManager:s})=>{let i=`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/deploy`,o=new URLSearchParams;return n&&o.append("framework",n),s&&o.append("package_manager",s),o.toString()?`${i}?${o.toString()}`:i},j=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/graphs/participation`,b=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/graphs/commit-activity`,L=({owner:e,repo:t,refName:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/branches/${(0,r.QU)(n)}/rename_ref_check`,S=({owner:e,repo:t,branchName:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/branches/rename_form/${(0,r.QU)(n)}`,z=({owner:e,repo:t,refName:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/branches/${(0,r.QU)(n)}`,I=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/branches/check_tag_name_exists`,v=({owner:e,repo:t,branch:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/tree/${(0,r.QU)(n)}`,k=({owner:e,repo:t,tag:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/releases/tag/${(0,r.QU)(n)}`,P=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/invitations`,T=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/actions/immutable_actions/migrate`,R=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/actions/workflows/immutable-actions-migration/migrate_release`,D=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/settings/rules`,x=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/settings/rules/new?target=branch&enforcement=disabled`,G=({owner:e})=>`/organizations/${(0,r.QU)(e)}/settings/blocked_users`,M=({owner:e})=>`/organizations/${(0,r.QU)(e)}/settings/rules/deferred_target_counts`,C=({owner:e})=>`/stafftools/users/${(0,r.QU)(e)}/organization_rules/deferred_target_counts`,q=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/settings/rules/deferred_target_counts`,Y=({owner:e,repo:t})=>`/stafftools/repositories/${(0,r.QU)(e)}/${(0,r.QU)(t)}/repository_rules/deferred_target_counts`,O=()=>"/repos/validate_regex",A=()=>"/repos/validate_regex/value",X=({owner:e,repo:t,commitish:n,filePath:s,lineNumber:i,plain:o})=>{let a=o?`?plain=${o}`:"",$=i?`#L${i}`:"";return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/blob/${(0,r.QU)(n)}/${(0,r.QU)(s)}${a}${$}`},N=({owner:e,repo:t,commitish:n,filePath:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/delete/${(0,r.QU)(n)}/${(0,r.QU)(s)}`,Z=({owner:e,repo:t,commitish:n,filePath:s,hash:i,lineNumber:o,returnToPrPath:a})=>{let $=i||(o?`#L${o}`:""),u=a?`?pr=${encodeURI(a)}`:"";return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/edit/${(0,r.QU)(n)}/${(0,r.QU)(s)}${$}${u}`},B=({owner:e,repo:t,commitish:n,filePath:s,lineNumber:i})=>{let o=i?`#L${i}`:"";return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/blame/${(0,r.QU)(n)}/${(0,r.QU)(s)}${o}`};function H({login:e}){return`/users/${(0,r.QU)(e)}/dismiss_repository_notice`}function V({repo:e,commitish:t,path:n}){return["",e.ownerLogin,e.name,"deferred-metadata",t,n].map(r.QU).join("/")}function J({repo:e,commitish:t,path:n}){return["",e.ownerLogin,e.name,"deferred-ast",t,n].map(r.QU).join("/")}function K(e){return`/${(0,r.QU)(e.ownerLogin)}/${(0,r.QU)(e.name)}`}function E(e){return`/${(0,r.QU)(e.ownerLogin)}/${(0,r.QU)(e.name)}?files=1`}function W({repo:e,commitish:t,action:n,path:s}){let i=["",e.ownerLogin,e.name,n,t];return s&&"/"!==s&&i.push(s),i.map(r.QU).join("/")}let F=({owner:e,repo:t})=>`/${e}/${t}/sidepanel-metadata`;function ee(e,t,n){return`/${e.ownerLogin}/${e.name}/detect_language?filename=${t}${n?"&full_details=true":""}`}let et=({owner:e,repo:t,filePath:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/wiki/${n.substring(0,n.lastIndexOf("."))}`,en=({owner:e,repo:t,filePath:n,commitish:s})=>`${et({owner:e,repo:t,filePath:n})}/${s}`,es=({owner:e,repo:t,commitish:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/wiki/_compare/${n}`,er=({owner:e,repo:t,commitish:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/commit/${n}`,ei=({owner:e,repo:t,entry:n,sha1:s,sha2:i})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/diffs/${n}/diff-lines?sha1=${s}&sha2=${i}`,eo=({owner:e,repo:t,sha1:n,sha2:s,oid:i})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/diffs?commit=${i}&sha2=${s}${null!=n?`&sha1=${n}`:""}`,ea=({owner:e,repo:t,commitish:n})=>`${er({owner:e,repo:t,commitish:n})}/context_lines`,e$=({owner:e,repo:t,commitOid:n,beforeCommentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/commit/${n}/discussion_comments${s?`?before_comment_id=${s}`:""}`,eu=({owner:e,repo:t,commitOid:n,path:s,position:i})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/commit/${n}/inline_comments/?path=${(0,r.QU)(s)}&position=${(0,r.QU)(i)}`,ec=({owner:e,repo:t,commitOid:n,untilCommentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/commit/${n}/deferred_comment_data${s?`?until_comment_id=${s}`:""}`,eg=({owner:e,repo:t,ref:n,path:s})=>{let i=`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/commits`;return n?s?`${i}/${(0,r.QU)(n)}/${(0,r.QU)(function(e){return e.startsWith("/")?e.slice(1):e}(s))}`:`${i}/${(0,r.QU)(n)}`:i};function ep({repo:e,branch:t,path:n,author:s}){let i=[e.ownerLogin,e.name,"commits",t,n].map(r.QU).join("/");return`/${i}?author=${(0,r.QU)(s)}`}function eU(e,t){return`/${e.ownerLogin}/${e.name}/commit/${t}/status-details`}function el({repo:e,author:t}){let n=[e.ownerLogin,e.name];return`/${n.map(r.QU).join("/")}/commits?author=${(0,r.QU)(t)}`}let eQ=({owner:e,repo:t,commitish:n})=>`/${(0,r.QU)(`${e}/${t}/commit/${n}/hovercard`)}`,em=({owner:e,repo:t,commitish:n})=>`/${(0,r.QU)(`${e}/${t}/branch_commits/${n}`)}`,ef=({topicName:e})=>`/topics/${e}`,ed=({categoryName:e})=>`/marketplace/category/${(0,r.y9)(e)}`,e_=({slug:e})=>`/marketplace/actions/${(0,r.QU)(e)}`,ey=({owner:e,repo:t,runId:n,attempt:s})=>{let i=[e,t,"actions"];return n&&i.push("runs",n),n&&s&&i.push("attempts",s),`/${i.map(r.QU).join("/")}`},eh=({owner:e,repo:t,runId:n})=>{let s=[e,t,"actions"];return n&&s.push("runs",n,"workflow"),`/${s.map(r.QU).join("/")}`},ew=({owner:e,repo:t,workflowFileName:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/actions/workflows/${(0,r.QU)(n)}`,ej=({owner:e,repo:t,commitish:n,filePath:s})=>`/${e}/${t}/codeowners-validity/${n}/${s}`;function eb(e){return e.split("/").slice(0,-1).join("/")}function eL({repo:e,baseUrl:t="",branch:n,filter:s,pagination:i,perPage:o=30}){let a=[e.ownerLogin,e.name,"activity"],$=[];return n&&$.push(`ref=${(0,r.QU)(n)}`),s&&(s.activityType&&"all"!==s.activityType.toLocaleLowerCase()&&$.push(`activity_type=${(0,r.QU)(s.activityType)}`),s.actor?.login&&$.push(`actor=${(0,r.QU)(s.actor.login)}`),s.timePeriod&&"all"!==s.timePeriod.toLocaleLowerCase()&&$.push(`time_period=${(0,r.QU)(s.timePeriod)}`),s.sort&&"desc"!==s.sort.toLocaleLowerCase()&&$.push(`sort=${(0,r.QU)(s.sort)}`)),i&&(i.after?$.push(`after=${(0,r.QU)(i.after)}`):i.before&&$.push(`before=${i.before}`)),o&&30!==o&&$.push(`per_page=${o}`),`${t}/${a.map(r.QU).join("/")}${$.length>0?`?${$.join("&")}`:""}`}function eS({repo:e,baseUrl:t="",branch:n,timePeriod:s}){let i=[e.ownerLogin,e.name,"activity","actors"],o=[];return n&&o.push(`ref=${(0,r.QU)(n)}`),s&&o.push(`time_period=${(0,r.QU)(s)}`),`${t}/${i.map(r.QU).join("/")}${o.length>0?`?${o.join("&")}`:""}`}function ez(){return"insights/actors"}function eI(){return"bypass_requests/requesters"}function ev(){return"bypass_requests/approvers"}function ek({repo:e,base:t,head:n}){let s=[e.ownerLogin,e.name,"compare"].map(r.QU).join("/"),i=t?`${(0,r.QU)(t)}...${(0,r.QU)(n)}`:(0,r.QU)(n);return`/${s}/${i}`}function eP({repo:e,base:t,head:n}){let s=[e.ownerLogin,e.name,"branches","pre_mergeable"].map(r.QU).join("/"),i=`${(0,r.QU)(t)}...${(0,r.QU)(n)}`;return`/${s}/${i}`}function eT({repo:e}){let t=[e.ownerLogin,e.name,"toggle_generic_feature"];return`/${t.map(r.QU).join("/")}`}function eR(e,t){return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pulls`}function eD(e,t){return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/stargazers`}function ex({repo:e,refName:t}){return`/${[e.ownerLogin,e.name,"pull","new",t].map(r.QU).join("/")}`}function eG({repo:e,number:t}){return`/${[e.ownerLogin,e.name,"pull",t.toString()].map(r.QU).join("/")}`}let eM=({owner:e,repo:t,number:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}`,eC=({owner:e,repo:t,number:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}#issue-${s}`,eq=({owner:e,repo:t,number:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/page_data/diff_entry_lines`,eY=({owner:e,repo:t,number:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}#issuecomment-${s}`,eO=({owner:e,repo:t,number:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}#pullrequestreview-${s}`,eA=({owner:e,repo:t,number:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}#discussion_r${s}`,eX=({owner:e,repo:t,number:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/files`,eN=({owner:e,repo:t,number:n,commit:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/commits/${s}`,eZ=({owner:e,repo:t,number:n,base:s,head:i})=>s?`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/files/${s}..${i}`:`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/files/${i}`,eB=({owner:e,repo:t,number:n,baseOid:s,headOid:i})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/copilot_diff_chat/?base_oid=${s}&head_oid=${i}`,eH=({owner:e,repo:t,number:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/partials/processing_indicator`;function eV({repo:e,refName:t,discard:n}){return`/${[e.ownerLogin,e.name,"branches","fetch_and_merge",t].map(r.QU).join("/")}${n?"?discard_changes=true":""}`}function eJ({repo:e,commitOid:t,includeDirectories:n=!1}){let s=`/${[e.ownerLogin,e.name,"tree-list",t].map(r.QU).join("/")}`;return n?`${s}?include_directories=${n}`:s}function eK(e){let t=[e.ownerLogin,e.name,"branch-and-tag-count"];return`/${t.map(r.QU).join("/")}`}function eE({repo:e,type:t,q:n,language:s,row:i,column:o,ref:a,path:$,codeNavContext:u,symbolKind:c}){let g=[e.ownerLogin,e.name].map(r.QU).join("/"),p=new URLSearchParams;return p.append("q",n),p.append("language",s),p.append("row",String(i)),p.append("col",String(o)),p.append("ref",a),p.append("blob_path",$),p.append("code_nav_context",u),c&&p.append("symbol_kind",c),`/${g}/find-react-${t}?${p.toString()}`}let eW=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/discussions`,eF=({owner:e,repo:t,discussionNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/discussions/${n}`,e0=({owner:e,repo:t,discussionNumber:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/discussions/${n}#discussioncomment-${s}`,e2=({owner:e,repo:t,discussionNumber:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/discussions/${n}#discussion-${s}`;function e1(e,t){return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/issues`}let e7=({owner:e,repo:t,issueNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/issues/${n}`,e3=({owner:e,repo:t,issueNumber:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/issues/${n}#issuecomment-${s}`,e6=({owner:e,repo:t,issueNumber:n,contentId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/issues/${n}#issue-${s}`,e8=({owner:e,repo:t,issueNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/issues/${n}/hovercard`,e9=({owner:e,repo:t,pullRequestNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/pull/${n}/hovercard`;function e4(e,t,n){var s,r;let i=(s="/",r=4,e.split("/",4).join(s).length),o=e.substring(i);return o===`/${t}`?"":o.startsWith(`/${t}/`)?o.substring(t.length+2):n}let e5=e=>`/${(0,r.QU)(`${e.ownerLogin}/${e.name}`)}/settings/transfer`,te=()=>"/repositories/check-name",tt=()=>"/repositories",tn=e=>`/${(0,r.QU)(`${e.ownerLogin}/${e.name}`)}/settings/transfer`,ts=e=>`/${(0,r.QU)(`${e.ownerLogin}/${e.name}`)}/settings/abort_transfer`,tr=()=>"/new/import",ti=()=>"/repositories/new/templates",to=()=>"/repositories/forms/owner_items",ta=e=>`/repositories/forms/fork_owner_items?repo_id=${e}`;function t$(e,t,n){let s=new URLSearchParams({owner:e});return t&&n&&(s.set("form",t),s.set("repo_id",n.toString())),`/repositories/forms/owner_detail?${s.toString()}`}let tu=e=>`/${(0,r.QU)(e.ownerLogin)}/${(0,r.QU)(e.name)}/graphs/contributors`,tc=e=>`/${(0,r.QU)(e.ownerLogin)}/${(0,r.QU)(e.name)}/settings/access`;function tg({pathPrefix:e,sourceName:t,propertyName:n}){let s=["",e,(0,r.QU)(t),"settings"];return n?(s.push("custom-property"),s.push((0,r.QU)(n))):s.push("custom-properties"),s.join("/")}function tp({pathPrefix:e,sourceName:t,propertyName:n}){let s=`/${e}/${(0,r.QU)(t)}/settings/custom-property`;return n?`${s}/${(0,r.QU)(n)}`:s}let tU=({business:e,org:t,propertyName:n})=>`/enterprises/${(0,r.QU)(e)}/settings/custom-property/organizations/${(0,r.QU)(t)}/${(0,r.QU)(n)}`,tl=({business:e,org:t,propertyName:n})=>`/enterprises/${(0,r.QU)(e)}/settings/custom-property/organizations/${(0,r.QU)(t)}/${(0,r.QU)(n)}/promote`;function tQ({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/custom-properties/values`}function tm({org:e,repo:t}){return`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/settings/custom-properties/values`}function tf({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/custom-properties/list-repos-values`}let td=({pathPrefix:e,sourceName:t,propertyName:n})=>["",e,(0,r.QU)(t),"settings","custom-properties-usage",(0,r.QU)(n)].join("/"),t_=({sourceName:e,propertyName:t})=>`/enterprises/${(0,r.QU)(e)}/settings/property_definition_name_check/${(0,r.QU)(t)}`;function ty({org:e,repo:t}){return`/${(0,r.QU)(`${e}/${t}`)}/settings/custom-properties`}function th({owner:e,repo:t}){return`/${(0,r.QU)(`${e}/${t}`)}/fork`}function tw({owner:e,repo:t}){return`/${(0,r.QU)(`${e}/${t}`)}/forks`}function tj({org:e}){return e?`/organizations/${(0,r.QU)(e)}/repositories/new`:"/new"}function tb({org:e}){return`/orgs/${(0,r.QU)(e)}/repos_list`}function tL({topic:e,org:t}){return`/search?q=topic%3A${(0,r.QU)(e)}+org%3A${(0,r.QU)(t)}&type=Repositories`}function tS({repo:e,pagination:t={before:null,after:null},perPage:n=30,query:s}){let i=[e.ownerLogin,e.name,"attestations"],o=[];return t&&(t.after?o.push(`after=${(0,r.QU)(t.after)}`):t.before&&o.push(`before=${(0,r.QU)(t.before)}`)),s&&o.push(`q=${s}`),n&&30!==n&&o.push(`per_page=${n}`),`/${i.map(r.QU).join("/")}${o.length>0?`?${o.join("&")}`:""}`}function tz({repo:e,attestationId:t}){return`/${[e.ownerLogin,e.name,"attestations",t.toString()].map(r.QU).join("/")}`}function tI({repo:e,attestationId:t}){return`/${[e.ownerLogin,e.name,"attestations",t.toString(),"download"].map(r.QU).join("/")}`}function tv({repo:e}){return`/${[e.ownerLogin,e.name,"branches"].map(r.QU).join("/")}`}function tk({repo:e}){return`/${[e.ownerLogin,e.name,"tags"].map(r.QU).join("/")}`}function tP({org:e}){return`/orgs/${(0,r.QU)(e)}/organization_onboarding/advanced_security`}function tT({enterprise:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis`}function tR({enterprise:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/apply_configuration`}function tD({enterprise:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/apply_confirmation_summary`}function tx({org:e,tip:t,q:n}){let s=`/organizations/${(0,r.QU)(e)}/settings/security_products`;return t&&(s+=`?tip=${t}`),n&&(s+=`${t?"&":"?"}q=${(0,r.QU)(n)}`),s}function tG({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configurations/new`}function tM({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configurations`}function tC({org:e,id:t}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configurations/edit/${t}`}function tq({org:e,id:t}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configurations/${t}`}function tY({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/repositories`}function tO({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/repositories/apply_confirmation_summary`}function tA({org:e,id:t}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configuration/${t}/repositories`}function tX({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configuration/repositories`}function tN({org:e,id:t}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/configuration/${t}/repositories_count`}function tZ({org:e,id:t,tip:n}){let s=`/organizations/${(0,r.QU)(e)}/settings/security_products/configurations/view/${t}`;return n&&(s+=`?tip=${n}`),s}function tB({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/repositories/advanced_security_license_summary`}function tH({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/in_progress`}function tV({org:e}){return`/organizations/${(0,r.QU)(e)}/settings/security_products/refresh`}function tJ(e){return`/organizations/${(0,r.QU)(e)}/settings/security_products/actions_runners_labels`}function tK({business:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis`}function tE({business:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis_policies`}function tW(e){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/actions_runners_labels`}function tF({business:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/configurations/new`}function t0({business:e}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/configurations`}function t2({business:e,id:t}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/configurations/${t}/edit`}function t1({business:e,id:t}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/configurations/${t}`}function t7({business:e,id:t}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/configurations/${t}/view`}function t3({business:e,id:t}){return`/enterprises/${(0,r.QU)(e)}/settings/security_analysis/configurations/${t}/repositories_count`}function t6(){return"/users/settings/security_products"}function t8({id:e}){return`/users/settings/security_products/configuration/${e}/repositories_count`}function t9(){return"/users/settings/security_products/configurations/new"}function t4({id:e}){return`/users/settings/security_products/configurations/edit/${e}`}function t5({id:e}){return`/users/settings/security_products/configurations/view/${e}`}function ne(){return"/users/settings/security_products/configurations"}function nt({id:e}){return`/users/settings/security_products/configurations/${e}`}function nn({id:e}){return`/users/settings/security_products/configuration/${e}/apply_configuration`}function ns(){return"/users/settings/security_products/configuration/apply_confirmation_summary"}function nr(){return"/users/settings/security_products/configuration/detach_configuration"}function ni(){return"/contact-sales"}function no(){return"/enterprise/contact/thanks"}let na=()=>"/contact";function n$({report:e}){let t=new URLSearchParams({report:e});return`/contact/report-abuse?${t}`}function nu({repo:e}){return`/${(0,r.QU)(`${e.ownerLogin}/${e.name}`)}/settings/security_analysis`}function nc({org:e}){return`/orgs/${(0,r.QU)(e)}/dismiss_notice`}function ng({noticeName:e}){return`/settings/dismiss-notice/${e}`}let np=({slug:e})=>`/enterprises/${e}`,nU=({slug:e})=>`/enterprises/${(0,r.QU)(e)}/enterprise_roles`;function nl({slug:e,page:t,query:n}){let s=`/enterprises/${(0,r.QU)(e)}/enterprise_role_assignments`,i=new URLSearchParams;t&&i.append("page",t.toString()),n&&i.append("query",n);let o=i.toString();return o?`${s}?${o}`:s}function nQ({slug:e,page:t,query:n}){let s=`/stafftools/enterprises/${(0,r.QU)(e)}/custom_roles/enterprise_role_assignments`,i=new URLSearchParams;t&&i.append("page",t.toString()),n&&i.append("query",n);let o=i.toString();return o?`${s}?${o}`:s}let nm=({slug:e,actorId:t,actorType:n,roleId:s})=>`/enterprises/${(0,r.QU)(e)}/enterprise_role_assignments/${n}/${t}/${s}`,nf=({slug:e,actorId:t,actorType:n,roleId:s})=>`/organizations/${(0,r.QU)(e)}/settings/org_role_assignments/${n}/${t}/${s}`,nd=({slug:e})=>`/enterprises/${(0,r.QU)(e)}/enterprise_role_assignments/new`,n_=({slug:e})=>`/enterprises/${(0,r.QU)(e)}/enterprise_role_assignment_queries`;function ny({slug:e,page:t,query:n}){let s=`/organizations/${(0,r.QU)(e)}/settings/org_role_assignments`,i=new URLSearchParams;t&&i.append("page",t.toString()),n&&i.append("query",n);let o=i.toString();return o?`${s}?${o}`:s}let nh=({slug:e})=>`/organizations/${e}/settings/org_role_assignments/new`,nw=({slug:e})=>`/organizations/${e}/settings/org_role_assignment_queries`;function nj({slug:e,page:t,query:n}){let s=`/stafftools/users/${(0,r.QU)(e)}/org_role_assignments`,i=new URLSearchParams;t&&i.append("page",t.toString()),n&&i.append("query",n);let o=i.toString();return o?`${s}?${o}`:s}function nb(e){let t=new URLSearchParams({email:e});return`/settings/emails/subscriptions/topics_by_email?${t}`}function nL(e){let t=new URLSearchParams(e);return`/settings/emails/subscriptions/topics_by_params?${t}`}function nS(){return"/settings/emails/subscriptions/link-request/new"}let nz=({login:e})=>`/users/${(0,r.QU)(e)}/pulls/settings/file_tree_visibility`,nI=({owner:e,repo:t,alertNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/code-scanning/${n}`,nv=({owner:e,repo:t,alertNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/code-scanning/${n}/assignees`,nk=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/code-scanning/available-assignees`,nP=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/alerts/code-scanning/alert-list`,nT=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/alerts/code-scanning/alert-group-list`,nR=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/alerts/code-scanning/repository-list.json`,nD=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/alerts/code-scanning/rule-list.json`,nx=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/alerts/code-scanning/tag-list.json`,nG=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/alerts/code-scanning/tool-list.json`,nM=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/overview`,nC=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/options`,nq=({owner:e,repo:t})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality`,nY=({owner:e,repo:t,ruleId:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/rules/${encodeURIComponent(n)}`,nO=({owner:e,repo:t,category:n,severity:s})=>{let i=[];return n&&i.push(`category=${encodeURIComponent(n.toLowerCase())}`),s&&i.push(`severity=${encodeURIComponent(s.toLowerCase())}`),`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/rules${i.length>0?`?${i.join("&")}`:""}`},nA=({owner:e,repo:t,ruleId:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/rules/${encodeURIComponent(n)}/files`,nX=({owner:e,repo:t,ruleId:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/rules/${encodeURIComponent(n)}/findings`,nN=({owner:e,repo:t,findingStableId:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/findings/${encodeURIComponent(n)}/state`,nZ=({owner:e,repo:t,ruleId:n,findingStableId:s})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/rules/${encodeURIComponent(n)}/findings/${encodeURIComponent(s)}/autofixes`,nB=({owner:e,repo:t,findingStableId:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/quality/findings/${encodeURIComponent(n)}/autofix/commit`,nH=({org:e,state:t})=>{let n=[];return t&&n.push(`state=${t}`),`/orgs/${(0,r.QU)(e)}/security/campaigns${n.length>0?`?${n.join("&")}`:""}`},nV=({org:e,query:t,templateId:n,sourceCampaignNumber:s})=>{let i=[];return t&&i.push(`query=${(0,r.QU)(t)}`),n&&i.push(`template=${(0,r.QU)(n)}`),s&&i.push(`source_campaign_number=${s}`),`/orgs/${(0,r.QU)(e)}/security/campaigns/new${i.length>0?`?${i.join("&")}`:""}`},nJ=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/counts`,nK=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns`,nE=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/drafts`,nW=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}`,nF=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}`,n0=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}/close`,n2=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}/reopen`,n1=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/drafts/${t}`,n7=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}/publish`,n3=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/alerts/summary`,n6=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}`,n8=({org:e,query:t,campaignName:n,campaignDescription:s,sourceCampaignNumber:i})=>{let o=[`query=${(0,r.QU)(t)}`];return n&&o.push(`campaign_name=${(0,r.QU)(n)}`),s&&o.push(`campaign_description=${(0,r.QU)(s)}`),i&&o.push(`source_campaign_number=${i}`),`/orgs/${(0,r.QU)(e)}/security/campaigns/publish?${o.join("&")}`},n9=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}/alerts`,n4=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}/alerts-groups`,n5=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/${t}/repositories-summary`,se=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/open/list`,st=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/closed`,sn=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/closed/list`,ss=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/drafts`,sr=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/drafts/${t}`,si=({org:e,securityCampaignNumber:t})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/drafts/${t}/publish`,so=({org:e})=>`/orgs/${(0,r.QU)(e)}/security/campaigns/managers`,sa=({owner:e,repo:t,campaignNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/campaigns/${n}`,s$=({owner:e,repo:t,securityCampaignNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/campaigns/${n}/alerts`,su=({owner:e,repo:t,securityCampaignNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/campaigns/${n}/assign-to-copilot`,sc=({owner:e,repo:t,securityCampaignNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/campaigns/${n}/branches`,sg=({owner:e,repo:t,securityCampaignNumber:n})=>`/${(0,r.QU)(e)}/${(0,r.QU)(t)}/security/campaigns/${n}/alerts`;function sp({owner:e,query:t}){return`/orgs/${(0,r.QU)(e)}/repositories?q=${(0,r.QU)(t)}`}let sU=({businessSlug:e})=>`/enterprises/${(0,r.QU)(e)}/organizations/suggestions`,sl=({businessSlug:e})=>`/enterprises/${(0,r.QU)(e)}/organizations/suggestions/validate`,sQ=({businessSlug:e})=>`/enterprises/${(0,r.QU)(e)}/check_team_name`,sm=()=>"/codespaces/",sf=({returnTo:e}={})=>`/login${e?`?return_to=${encodeURIComponent(e)}`:""}`,sd=({org:e})=>`/organizations/${(0,r.QU)(e)}/settings/billing/summary`,s_=({org:e,query:t})=>`/organizations/${(0,r.QU)(e)}/settings/billing/usage?query=${(0,r.QU)(t)}`,sy=({businessSlug:e})=>`/enterprises/${(0,r.QU)(e)}/enterprise_licensing`,sh=({basePath:e,groupPath:t})=>(0,r.QU)(`${e}/${t}`)},30450:(e,t,n)=>{n.d(t,{ID:()=>r,QU:()=>o,y9:()=>a,yH:()=>i});var s=n(96679);function r(e,t,n){let r=e(t),i=new URL(r,s.fV.origin||"https://github.com");for(let[e,t]of(r===s.fV.pathname&&(i.search=new URLSearchParams(s.fV.search).toString()),Object.entries(n||{})))null==t?i.searchParams.delete(e):i.searchParams.set(e,t.toString());return i}function i(e,t,n){let s=r(e,t,n);return s.href.replace(s.origin,"")}function o(e){return e.split("/").map(encodeURIComponent).join("/")}function a(e){return e.replace(/[^a-zA-Z0-9]/g,"-").toLowerCase()}}}]);
//# sourceMappingURL=ui_packages_paths_index_ts-643525aacdb4.js.map