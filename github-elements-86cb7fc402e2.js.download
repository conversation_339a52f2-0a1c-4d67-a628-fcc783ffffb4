"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["github-elements"],{19723:(e,t,i)=>{i(86643),i(20761),i(74057),i(91707),i(62044),i(90204);var n=i(94147);i(78143),i(27552),i(72705),i(81028),i(44911),i(92284);var r=i(26559),s=i(96679);s.cg&&(s.cg.IncludeFragmentElement.prototype.fetch=function(e){let t=this.getAttribute("data-nonce")||"";return(0,r.tV)(e.headers,t),window.fetch(e)}),i(62643);var a=i(39595);function o(e,t,i,n){var r,s=arguments.length,a=s<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,n);else for(var o=e.length-1;o>=0;o--)(r=e[o])&&(a=(s<3?r(a):s>3?r(t,i,a):r(t,i))||a);return s>3&&a&&Object.defineProperty(t,i,a),a}let l=class GitCloneHelpElement extends HTMLElement{updateURL(e){let t=e.currentTarget,i=t.getAttribute("data-url")||"";if(this.helpField.value=i,t.matches(".js-git-protocol-clone-url"))for(let e of this.helpTexts)e.textContent=i;for(let e of this.cloneURLButtons)e.classList.remove("selected");t.classList.add("selected")}};function u(e,t,i){if(!t.has(e))throw TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function d(e,t){var i=u(e,t,"get");return i.get?i.get.call(e):i.value}function h(e,t,i){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,i)}function c(e,t,i){var n=u(e,t,"set");if(n.set)n.set.call(e,i);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=i}return i}function m(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}o([a.aC],l.prototype,"helpField",void 0),o([a.zV],l.prototype,"helpTexts",void 0),o([a.zV],l.prototype,"cloneURLButtons",void 0),l=o([a.p_],l);var p=new WeakMap,f=new WeakMap,v=new WeakMap,g=new WeakMap;let b=class MarkedTextElement extends HTMLElement{get query(){return this.ownerInput?this.ownerInput.value:this.getAttribute("query")||""}set query(e){this.setAttribute("query",e)}get ownerInput(){let e=this.ownerDocument.getElementById(this.getAttribute("data-owner-input")||"");return e instanceof HTMLInputElement?e:null}connectedCallback(){this.handleEvent(),this.ownerInput?.addEventListener("input",this),c(this,v,new MutationObserver(()=>this.handleEvent()))}handleEvent(){d(this,g)&&cancelAnimationFrame(d(this,g)),c(this,g,requestAnimationFrame(()=>this.mark()))}disconnectedCallback(){this.ownerInput?.removeEventListener("input",this),d(this,v).disconnect()}mark(){let e=this.textContent||"",t=this.query;if(e===d(this,p)&&t===d(this,f))return;c(this,p,e),c(this,f,t),d(this,v).disconnect();let i=0,n=document.createDocumentFragment();for(let r of(this.positions||function(e,t){let i=[],n=0;for(let r=0;r<e.length;r++){let s=e[r],a=t.indexOf(s,n);if(-1===a)break;n=a+1,i.push(a)}return i})(t,e)){if(Number(r)!==r||r<i||r>e.length)continue;""!==e.slice(i,r)&&n.appendChild(document.createTextNode(e.slice(i,r))),i=r+1;let t=document.createElement("mark");t.textContent=e[r],n.appendChild(t)}n.appendChild(document.createTextNode(e.slice(i))),this.replaceChildren(n),d(this,v).observe(this,{attributes:!0,childList:!0,subtree:!0})}constructor(...e){super(...e),h(this,p,{writable:!0,value:""}),h(this,f,{writable:!0,value:""}),h(this,v,{writable:!0,value:void 0}),h(this,g,{writable:!0,value:void 0}),m(this,"positions",void 0)}};m(b,"observedAttributes",["query","data-owner-input"]),window.customElements.get("marked-text")||(window.MarkedTextElement=b,window.customElements.define("marked-text",b));var w=i(80558);let y=class PasswordStrengthElement extends HTMLElement{connectedCallback(){this.addEventListener("input",E)}disconnectedCallback(){this.removeEventListener("input",E)}};function E(e){let t=e.currentTarget;if(!(t instanceof y))return;let i=e.target;if(!(i instanceof HTMLInputElement))return;let n=i.form;if(!(n instanceof HTMLFormElement))return;let r=function(e,t){let i={valid:!1,hasMinimumCharacterCount:e.length>=t.minimumCharacterCount,hasMinimumPassphraseLength:0!==t.passphraseLength&&e.length>=t.passphraseLength,hasLowerCase:/[a-z]/.test(e),hasNumber:/\d/.test(e)};return i.valid=i.hasMinimumPassphraseLength||i.hasMinimumCharacterCount&&i.hasLowerCase&&i.hasNumber,i}(i.value,{minimumCharacterCount:Number(t.getAttribute("minimum-character-count")),passphraseLength:Number(t.getAttribute("passphrase-length"))});if(r.valid){i.setCustomValidity("");let e=t.querySelector("dl.form-group");e&&(e.classList.remove("errored"),e.classList.add("successed"))}else"true"!==t.getAttribute("skip-custom-validity")&&i.setCustomValidity(t.getAttribute("invalid-message")||"Invalid");(function(e,t){let i=e.querySelector("[data-more-than-n-chars]"),n=e.querySelector("[data-min-chars]"),r=e.querySelector("[data-number-requirement]"),s=e.querySelector("[data-letter-requirement]"),a=e.getAttribute("error-class")?.split(" ").filter(e=>e.length>0)||[],o=e.getAttribute("pass-class")?.split(" ").filter(e=>e.length>0)||[];for(let e of[i,n,r,s])e?.classList.remove(...a,...o);if(t.hasMinimumPassphraseLength&&i)i.classList.add(...o);else if(t.valid)n.classList.add(...o),r.classList.add(...o),s.classList.add(...o);else{let e=t.hasMinimumCharacterCount?o:a,l=t.hasNumber?o:a,u=t.hasLowerCase?o:a;i?.classList.add(...a),n.classList.add(...e),r.classList.add(...l),s.classList.add(...u)}})(t,r),(0,w.t)(n)}function C(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function x(e,t,i,n){var r,s=arguments.length,a=s<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,n);else for(var o=e.length-1;o>=0;o--)(r=e[o])&&(a=(s<3?r(a):s>3?r(t,i,a):r(t,i))||a);return s>3&&a&&Object.defineProperty(t,i,a),a}window.customElements.get("password-strength")||(window.PasswordStrengthElement=y,window.customElements.define("password-strength",y)),i(45323);let L=class PollIncludeFragmentElement extends n.T{async fetch(e,t){let i=await super.fetch(e),n=t||this.intervalMilliseconds;(!n||isNaN(n))&&(n=1e3);let r=isNaN(this.backoffMultiplier)?1.5:this.backoffMultiplier;return 202===i.status?(await new Promise(e=>{this.pollingTimeout=setTimeout(e,n)}),this.fetch(e,n*r)):i}refetch(){return this.cancelPolling(),super.refetch()}connectedCallback(){super.connectedCallback(),this.retryButton&&this.retryButton.addEventListener("click",()=>{this.refetch()})}disconnectedCallback(){this.cancelPolling()}cancelPolling(){this.pollingTimeout&&clearTimeout(this.pollingTimeout)}constructor(...e){super(...e),C(this,"intervalMilliseconds",1e3),C(this,"backoffMultiplier",1.5),C(this,"pollingTimeout",null)}};x([a.aC],L.prototype,"retryButton",void 0),x([a.CF],L.prototype,"intervalMilliseconds",void 0),x([a.CF],L.prototype,"backoffMultiplier",void 0),L=x([a.p_],L);var k=i(35908);let M=e=>void 0===e||/\n/.test(e),A=["position:absolute;","overflow:auto;","word-wrap:break-word;","top:0px;","left:-9999px;"],T=["box-sizing","font-family","font-size","font-style","font-variant","font-weight","height","letter-spacing","line-height","max-height","min-height","padding-bottom","padding-left","padding-right","padding-top","border-bottom","border-left","border-right","border-top","text-decoration","text-indent","text-transform","width","word-spacing"],_=new WeakMap;function S(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}let q=new WeakMap,I=class SlashCommandExpander{destroy(){this.input.removeEventListener("paste",this.onpaste),this.input.removeEventListener("input",this.oninput),this.input.removeEventListener("keydown",this.onkeydown),this.input.removeEventListener("blur",this.onblur)}activate(e,t){this.input===document.activeElement&&this.setMenu(e,t)}deactivate(){let e=this.menu,t=this.combobox;return!!e&&!!t&&(this.createLink?.removeEventListener("hotkey-fire",this.onCreateCommandLinkKeyDown),this.menu=null,this.createLink=null,this.combobox=null,e.removeEventListener("combobox-commit",this.oncommit),e.removeEventListener("mousedown",this.onmousedown),t.destroy(),e.remove(),!0)}setMenu(e,t){this.deactivate(),this.menu=t,this.createLink=t.querySelector(".js-slash-command-menu-create-link"),t.id||(t.id=`text-expander-${Math.floor(1e5*Math.random()).toString()}`),this.expander.append(t);let i=t.querySelector(".js-command-list-container"),n={tabInsertsSuggestions:!1};i?this.combobox=new k.A(this.input,i,n):this.combobox=new k.A(this.input,t,n);let{top:r,left:s}=function(e,t=e.selectionEnd){let{mirror:i,marker:n}=function(e,t){let i,n,r=e.nodeName.toLowerCase();if("textarea"!==r&&"input"!==r)throw Error("expected textField to a textarea or input");let s=_.get(e);if(s&&s.parentElement===e.parentElement)s.textContent="";else{s=document.createElement("div"),_.set(e,s);let t=window.getComputedStyle(e),i=A.slice(0);"textarea"===r?i.push("white-space:pre-wrap;"):i.push("white-space:nowrap;");for(let e=0,n=T.length;e<n;e++){let n=T[e];i.push(`${n}:${t.getPropertyValue(n)};`)}s.style.cssText=i.join(" ")}let a=document.createElement("span");if(a.style.cssText="position: absolute;",a.textContent="\xa0","number"==typeof t){let r=e.value.substring(0,t);r&&(i=document.createTextNode(r)),(r=e.value.substring(t))&&(n=document.createTextNode(r))}else{let t=e.value;t&&(i=document.createTextNode(t))}if(i&&s.appendChild(i),s.appendChild(a),n&&s.appendChild(n),!s.parentElement){if(!e.parentElement)throw Error("textField must have a parentElement to mirror");e.parentElement.insertBefore(s,e)}return s.scrollTop=e.scrollTop,s.scrollLeft=e.scrollLeft,{mirror:s,marker:a}}(e,t),r=i.getBoundingClientRect(),s=n.getBoundingClientRect();return setTimeout(()=>{i.remove()},5e3),{top:s.top-r.top,left:s.left-r.left}}(this.input,e.position),a=parseInt(window.getComputedStyle(this.input).fontSize);t.style.top=`${r+a}px`,t.style.left=`${s}px`,this.combobox.start(),t.addEventListener("combobox-commit",this.oncommit),t.addEventListener("mousedown",this.onmousedown),this.createLink?.addEventListener("hotkey-fire",this.onCreateCommandLinkKeyDown),this.combobox.navigate(1)}setValue(e){if(null==e)return;let t=this.match;if(!t)return;let{cursor:i,value:n}=this.replaceCursorMark(e);n=n?.length===0?n:`${n} `;let r=t.position-t.key.length,s=t.position+t.text.length;this.input.focus();let a=!1;try{this.input.setSelectionRange(r,s),a=document.execCommand("insertText",!1,n)}catch{a=!1}if(!a){let e=this.input.value.substring(0,t.position-t.key.length),i=this.input.value.substring(t.position+t.text.length);this.input.value=e+n+i}this.deactivate(),i=r+(i||n.length),this.input.selectionStart=i,this.input.selectionEnd=i}replaceCursorMark(e){let t=/%cursor%/gm,i=t.exec(e);return i?{cursor:i.index,value:e.replace(t,"")}:{cursor:null,value:e}}async onCommit({target:e}){if(!(e instanceof HTMLElement)||!this.combobox)return;let t=this.match;if(!t)return;let n={item:e,key:t.key,value:null},r=new CustomEvent("text-expander-value",{cancelable:!0,detail:n}),s=!this.expander.dispatchEvent(r),{onValue:a}=await i.e("app_assets_modules_github_slash-command-expander-element_slash-command-suggester_ts").then(i.bind(i,45369));await a(this.expander,t.key,e),!s&&n.value&&this.setValue(n.value)}onBlur(){if(this.interactingWithMenu){this.interactingWithMenu=!1;return}this.deactivate()}onPaste(){this.justPasted=!0}async delay(e){return new Promise(t=>setTimeout(t,e))}async onInput(){if(this.justPasted){this.justPasted=!1;return}let e=this.findMatch();if(e){if(this.match=e,await this.delay(this.appropriateDelay()),this.match!==e)return;let t=await this.notifyProviders(e);if(!this.match)return;t?this.activate(e,t):this.deactivate()}else this.match=null,this.deactivate()}appropriateDelay(){return 250}findMatch(){let e=this.input.selectionEnd,t=this.input.value;for(let i of this.expander.getKeys()){let n=function(e,t,i){let n=e.lastIndexOf(t,i-1);if(-1===n||e.lastIndexOf(" ",i-1)>n||e.lastIndexOf(`
`,i-1)>n)return;let r=e[n-1];if(!r||`
`===r)return{word:e.substring(n+t.length,i),position:n+t.length,beginningOfLine:M(r)}}(t,i,e);if(n)return{text:n.word,key:i,position:n.position,beginningOfLine:n.beginningOfLine}}}async notifyProviders(e){let t=[],n=e=>t.push(e),r=new CustomEvent("text-expander-change",{cancelable:!0,detail:{provide:n,text:e.text,key:e.key}});if(!this.expander.dispatchEvent(r))return;let{onChange:s}=await i.e("app_assets_modules_github_slash-command-expander-element_slash-command-suggester_ts").then(i.bind(i,45369));return s(this.expander,e.key,n,e.text),(await Promise.all(t)).filter(e=>e.matched).map(e=>e.fragment)[0]}onMousedown(){this.interactingWithMenu=!0}onKeydown(e){if("Tab"===e.key&&this.createLink){e.stopImmediatePropagation(),e.preventDefault();let t=this.combobox;this.combobox=null,this.createLink.focus(),this.combobox=t;return}"Escape"===e.key&&this.deactivate()&&(e.stopImmediatePropagation(),e.preventDefault())}onCreateCommandLinkKeyDown(e){e.stopImmediatePropagation(),e.preventDefault(),this.input?.focus()}constructor(e,t){S(this,"expander",void 0),S(this,"input",void 0),S(this,"menu",void 0),S(this,"createLink",void 0),S(this,"oninput",void 0),S(this,"onkeydown",void 0),S(this,"onpaste",void 0),S(this,"oncommit",void 0),S(this,"onblur",void 0),S(this,"onmousedown",void 0),S(this,"combobox",void 0),S(this,"match",void 0),S(this,"justPasted",void 0),S(this,"interactingWithMenu",void 0),this.expander=e,this.input=t,this.combobox=null,this.menu=null,this.createLink=null,this.match=null,this.justPasted=!1,this.oninput=this.onInput.bind(this),this.onpaste=this.onPaste.bind(this),this.onkeydown=this.onKeydown.bind(this),this.oncommit=this.onCommit.bind(this),this.onmousedown=this.onMousedown.bind(this),this.onblur=this.onBlur.bind(this),this.onCreateCommandLinkKeyDown=this.onCreateCommandLinkKeyDown.bind(this),this.interactingWithMenu=!1,t.addEventListener("paste",this.onpaste),t.addEventListener("input",this.oninput),t.addEventListener("keydown",this.onkeydown),t.addEventListener("blur",this.onblur)}},j=class SlashCommandExpanderElement extends HTMLElement{getKeys(){let e=this.getAttribute("keys");return e?e.split(" "):[]}connectedCallback(){let e=this.querySelector('input[type="text"], textarea');if(!(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement))return;let t=new I(this,e);q.set(this,t)}reconnect(){if(null!==this.querySelector(".js-slash-command-menu:not(.d-none)"))return;let e=q.get(this);e&&(e.destroy(),q.delete(this)),this.connectedCallback()}disconnectedCallback(){let e=q.get(this);e&&(e.destroy(),q.delete(this))}setValue(e){let t=q.get(this);t&&t.setValue(e)}setMenu(e,t=!1){let i=q.get(this);i&&i.match&&(t&&(i.interactingWithMenu=!0),i.setMenu(i.match,e))}closeMenu(){let e=q.get(this);e&&e.setValue("")}isLoading(){let e=this.getElementsByClassName("js-slash-command-expander-loading")[0];if(e){let t=e.cloneNode(!0);t.classList.remove("d-none"),this.setMenu(t)}}showError(){let e=this.getElementsByClassName("js-slash-command-expander-error")[0];if(e){let t=e.cloneNode(!0);t.classList.remove("d-none"),this.setMenu(t)}}};function N(e,t,i,n){var r,s=arguments.length,a=s<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,i,n);else for(var o=e.length-1;o>=0;o--)(r=e[o])&&(a=(s<3?r(a):s>3?r(t,i,a):r(t,i))||a);return s>3&&a&&Object.defineProperty(t,i,a),a}window.customElements.get("slash-command-expander")||(window.SlashCommandExpanderElement=j,window.customElements.define("slash-command-expander",j));let z=class TextSuggesterElement extends HTMLElement{acceptSuggestion(){this.suggestion?.textContent&&(this.input.value=this.suggestion.textContent,this.input.dispatchEvent(new Event("input")),this.suggestionContainer&&(this.suggestionContainer.hidden=!0),this.input.focus())}};function V(e,t,i){if(!t.has(e))throw TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function P(e,t){var i=V(e,t,"get");return i.get?i.get.call(e):i.value}function F(e,t,i){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,i)}function O(e,t,i){var n=V(e,t,"set");if(n.set)n.set.call(e,i);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=i}return i}N([a.aC],z.prototype,"input",void 0),N([a.aC],z.prototype,"suggestionContainer",void 0),N([a.aC],z.prototype,"suggestion",void 0),z=N([a.p_],z);var R=new WeakMap,H=new WeakMap,W=new WeakMap,B=new WeakMap,D=new WeakMap,X=new WeakMap;let K=class VirtualFilterInputElement extends HTMLElement{static get observedAttributes(){return["src","loading","data-property","aria-owns"]}get filtered(){if(P(this,X))return P(this,X);if(this.hasAttribute("aria-owns")){let e=this.ownerDocument.getElementById(this.getAttribute("aria-owns")||"");e&&(e instanceof Set||e&&"object"==typeof e&&"size"in e&&"add"in e&&"delete"in e&&"clear"in e)&&O(this,X,e)}return O(this,X,P(this,X)||new Set)}set filtered(e){O(this,X,e)}get input(){return this.querySelector("input, textarea")}get src(){return this.getAttribute("src")||""}set src(e){this.setAttribute("src",e)}get loading(){return"lazy"===this.getAttribute("loading")?"lazy":"eager"}set loading(e){this.setAttribute("loading",e)}get accept(){return this.getAttribute("accept")||""}set accept(e){this.setAttribute("accept",e)}get property(){return this.getAttribute("data-property")||""}set property(e){this.setAttribute("data-property",e)}reset(){this.filtered.clear(),O(this,D,new Set)}clear(){this.input&&(this.input.value="",this.input.dispatchEvent(new Event("input")))}attributeChangedCallback(e,t,i){let n=this.isConnected&&this.src,r="eager"===this.loading,s=t!==i;("src"===e||"data-property"===e)&&s&&(O(this,W,null),P(this,B)&&clearTimeout(P(this,B))),n&&r&&("src"===e||"loading"===e||"accept"===e||"data-property"===e)&&s?(cancelAnimationFrame(P(this,H)),O(this,H,requestAnimationFrame(()=>this.load()))):"aria-owns"===e&&O(this,X,null)}connectedCallback(){this.src&&"eager"===this.loading&&(cancelAnimationFrame(P(this,H)),O(this,H,requestAnimationFrame(()=>this.load())));let e=this.input;if(!e)return;let t=this.getAttribute("aria-owns");null!==t&&this.attributeChangedCallback("aria-owns","",t),e.setAttribute("autocomplete","off"),e.setAttribute("spellcheck","false"),this.src&&"lazy"===this.loading&&(document.activeElement===e?this.load():e.addEventListener("focus",()=>{this.load()},{once:!0})),e.addEventListener("input",this)}disconnectedCallback(){this.input?.removeEventListener("input",this)}handleEvent(e){"input"===e.type&&(P(this,B)&&clearTimeout(P(this,B)),O(this,B,window.setTimeout(()=>this.filterItems(),(this.input?.value?.length,300))))}async load(){P(this,R)?.abort(),O(this,R,new AbortController);let{signal:e}=P(this,R);if(!this.src)throw Error("missing src");if(await new Promise(e=>setTimeout(e,0)),!e.aborted){this.dispatchEvent(new Event("loadstart"));try{let t=await this.fetch(this.request(),{signal:e,headers:{...(0,r.kt)()}});if(location.origin+this.src!==t.url)return;if(!t.ok)throw Error(`Failed to load resource: the server responded with a status of ${t.status}`);O(this,D,new Set((await t.json())[this.property])),O(this,W,null),this.dispatchEvent(new Event("loadend"))}catch(t){if(e.aborted)return void this.dispatchEvent(new Event("loadend"));throw(async()=>{this.dispatchEvent(new Event("error")),this.dispatchEvent(new Event("loadend"))})(),t}this.filtered.clear(),this.filterItems()}}request(){return new Request(this.src,{method:"GET",credentials:"same-origin",headers:{Accept:this.accept||"application/json"}})}fetch(e,t){return fetch(e,t)}filterItems(){let e,t=this.input?.value.trim()??"",i=P(this,W);if(O(this,W,t),t!==i){for(let n of(this.dispatchEvent(new CustomEvent("virtual-filter-input-filter")),i&&t.includes(i)?e=this.filtered:(e=P(this,D),this.filtered.clear()),e))this.filter(n,t)?this.filtered.add(n):this.filtered.delete(n);this.dispatchEvent(new CustomEvent("virtual-filter-input-filtered"))}}constructor(...e){super(...e),F(this,R,{writable:!0,value:void 0}),F(this,H,{writable:!0,value:0}),F(this,W,{writable:!0,value:null}),F(this,B,{writable:!0,value:void 0}),F(this,D,{writable:!0,value:new Set}),F(this,X,{writable:!0,value:null}),function(e,t,i){t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i}(this,"filter",(e,t)=>String(e).includes(t))}};function $(e,t,i){if(!t.has(e))throw TypeError("attempted to "+i+" private field on non-instance");return t.get(e)}function G(e,t){var i=$(e,t,"get");return i.get?i.get.call(e):i.value}function J(e,t,i){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,i)}function U(e,t,i){var n=$(e,t,"set");if(n.set)n.set.call(e,i);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=i}return i}function Y(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}window.customElements.get("virtual-filter-input")||(window.VirtualFilterInputElement=K,window.customElements.define("virtual-filter-input",K));let Q=new IntersectionObserver(e=>{for(let t of e)t.isIntersecting&&t.target instanceof ea&&"eager"===t.target.updating&&t.target.update()});var Z=new WeakMap,ee=new WeakMap,et=new WeakMap,ei=new WeakMap,en=new WeakMap,er=new WeakMap,es=new WeakMap;let ea=class VirtualListElement extends HTMLElement{static get observedAttributes(){return["data-updating","aria-activedescendant"]}get updating(){return"lazy"===this.getAttribute("data-updating")?"lazy":"eager"}set updating(e){this.setAttribute("data-updating",e)}get size(){return G(this,ee).size}get range(){let e=this.getBoundingClientRect().height,{scrollTop:t}=this,i=`${t}-${e}`;if(G(this,en).has(i))return G(this,en).get(i);let n=0,r=0,s=0,a=0,o=G(this,et);for(let i of G(this,ee)){let l=o.get(i)||G(this,ei);if(s+l<t)s+=l,n+=1,r+=1;else if(a-l<e)a+=l,r+=1;else if(a>=e)break}return[n,r]}get list(){let e=this.querySelector("ul, ol, tbody");if(!e)throw Error("virtual-list must have a container element inside: any of <ul>, <ol>, <tbody>");return e}attributeChangedCallback(e,t,i){if(t===i||!this.isConnected)return;let n="data-updating"===e&&"eager"===i,r="data-sorted"===e&&this.hasAttribute("data-sorted");if((n||r)&&this.update(),"aria-activedescendant"===e){let e=this.getIndexByElementId(i);this.dispatchEvent(new ActiveDescendantChangedEvent(e,i)),"eager"===this.updating&&this.update()}}connectedCallback(){this.addEventListener("scroll",()=>this.update()),this.updateSync=this.updateSync.bind(this),Q.observe(this)}update(){G(this,es)&&cancelAnimationFrame(G(this,es)),!G(this,Z)&&this.hasAttribute("data-sorted")?U(this,es,requestAnimationFrame(()=>{this.dispatchEvent(new CustomEvent("virtual-list-sort",{cancelable:!0}))&&this.sort()})):U(this,es,requestAnimationFrame(this.updateSync))}renderItem(e){let t={item:e,fragment:document.createDocumentFragment()};return this.dispatchEvent(new CustomEvent("virtual-list-render-item",{detail:t})),t.fragment.children[0]}recalculateHeights(e){let t=this.list;if(!t)return;let i=this.renderItem(e);if(!i)return;t.append(i);let n=t.children[0].getBoundingClientRect().height;t.replaceChildren(),n&&(U(this,ei,n),G(this,et).set(e,n))}getIndexByElementId(e){if(!e)return -1;let t=0;for(let[,i]of G(this,er)){if(i.id===e||i.querySelector(`#${e}`))return t;t++}return -1}updateSync(){let e=this.list,[t,i]=this.range;if(i<t||!this.dispatchEvent(new CustomEvent("virtual-list-update",{cancelable:!0})))return;let n=new Map,r=G(this,er),s=-1,a=!0,o=0,l=0,u=0;for(let e of G(this,ee)){if(-1!==s||Number.isFinite(G(this,ei))&&0!==G(this,ei)||this.recalculateHeights(e),s+=1,u=G(this,et).get(e)||G(this,ei),s<t){o+=u,l=o;continue}if(s>i){a=!1;break}let d=null;if(r.has(e))d=r.get(e);else{if(!(d=this.renderItem(e)))continue;d.querySelector("[aria-setsize]")?.setAttribute("aria-setsize",G(this,ee).size.toString()),d.querySelector("[aria-posinset]")?.setAttribute("aria-posinset",(s+1).toString()),r.set(e,d)}d.querySelector("[tabindex]")?.setAttribute("data-scrolltop",l.toString()),l+=u,n.set(e,d)}e.replaceChildren(...n.values()),e.style.paddingTop=`${o}px`;let d=this.size*G(this,ei);e.style.height=`${d||0}px`;let h=!1,c=this.getBoundingClientRect().bottom;for(let[e,t]of n){let{height:i,bottom:n}=t.getBoundingClientRect();h=h||n>=c,G(this,et).set(e,i)}if(!a&&this.size>n.size&&!h)return G(this,en).delete(`${this.scrollTop}-${this.getBoundingClientRect().height}`),this.update();this.dispatchEvent(new RenderedEvent(r)),this.dispatchEvent(new CustomEvent("virtual-list-updated"))}resetRenderCache(){U(this,er,new Map)}has(e){return G(this,ee).has(e)}add(e){return G(this,ee).add(e),U(this,Z,!1),Number.isFinite(G(this,ei))||this.recalculateHeights(e),this.resetRenderCache(),this.dispatchEvent(new Event("virtual-list-data-updated")),"eager"===this.updating&&this.update(),this}delete(e){let t=G(this,ee).delete(e);return U(this,Z,!1),G(this,et).delete(e),this.resetRenderCache(),this.dispatchEvent(new Event("virtual-list-data-updated")),"eager"===this.updating&&this.update(),t}clear(){G(this,ee).clear(),G(this,et).clear(),U(this,ei,1/0),U(this,Z,!0),this.resetRenderCache(),this.dispatchEvent(new Event("virtual-list-data-updated")),"eager"===this.updating&&this.update()}forEach(e,t){for(let i of this)e.call(t,i,i,this)}entries(){return G(this,ee).entries()}values(){return G(this,ee).values()}keys(){return G(this,ee).keys()}[Symbol.iterator](){return G(this,ee)[Symbol.iterator]()}sort(e){return U(this,ee,new Set(Array.from(this).sort(e))),U(this,Z,!0),this.dispatchEvent(new Event("virtual-list-data-updated")),"eager"===this.updating&&this.update(),this}constructor(...e){super(...e),J(this,Z,{writable:!0,value:!1}),J(this,ee,{writable:!0,value:new Set}),J(this,et,{writable:!0,value:new Map}),J(this,ei,{writable:!0,value:1/0}),J(this,en,{writable:!0,value:new Map}),J(this,er,{writable:!0,value:new Map}),J(this,es,{writable:!0,value:0}),Y(this,Symbol.toStringTag,"VirtualListElement")}};let ActiveDescendantChangedEvent=class ActiveDescendantChangedEvent extends Event{constructor(e,t){super("virtual-list-activedescendant-changed"),Y(this,"index",void 0),Y(this,"id",void 0),this.index=e,this.id=t}};let RenderedEvent=class RenderedEvent extends Event{constructor(e){super("virtual-list-rendered"),Y(this,"rowsCache",void 0),this.rowsCache=e}};window.customElements.get("virtual-list")||(window.VirtualListElement=ea,window.customElements.define("virtual-list",ea))},80558:(e,t,i)=>{i.d(t,{t:()=>l});var n=i(36175),r=i(21403),s=i(97797);function a(e){let t=e.getAttribute("data-required-value"),i=e.getAttribute("data-required-value-prefix");if(e.value===t)e.setCustomValidity("");else{let n=t;i&&(n=i+n),e.setCustomValidity(n)}}(0,n.eC)("[data-required-value]",function(e){a(e.currentTarget)}),(0,s.on)("change","[data-required-value]",function(e){let t=e.currentTarget;a(t),l(t.form)}),(0,n.eC)("[data-required-trimmed]",function(e){let t=e.currentTarget;""===t.value.trim()?t.setCustomValidity(t.getAttribute("data-required-trimmed")):t.setCustomValidity("")}),(0,s.on)("change","[data-required-trimmed]",function(e){let t=e.currentTarget;""===t.value.trim()?t.setCustomValidity(t.getAttribute("data-required-trimmed")):t.setCustomValidity(""),l(t.form)}),(0,n.uE)("input[pattern],input[required],textarea[required],input[data-required-change],textarea[data-required-change],input[data-required-value],textarea[data-required-value]",e=>{let t=e.checkValidity();function i(){let i=e.checkValidity();i!==t&&e.form&&l(e.form),t=i}e.addEventListener("input",i),e.addEventListener("blur",function t(){e.removeEventListener("input",i),e.removeEventListener("blur",t)})});let o=new WeakMap;function l(e){let t=e.checkValidity();for(let i of e.querySelectorAll("button[data-disable-invalid]"))i.disabled=!t}(0,r.lB)("button[data-disable-invalid]",{constructor:HTMLButtonElement,initialize(e){let t=e.form;t&&(o.get(t)||(t.addEventListener("change",()=>l(t)),o.set(t,!0)),e.disabled=!t.checkValidity())}}),(0,r.lB)("input[data-required-change], textarea[data-required-change]",function(e){let t="radio"===e.type&&e.form?e.form.elements.namedItem(e.name).value:null;function i(i){let n=e.form;if(i&&"radio"===e.type&&n&&t)for(let i of n.elements.namedItem(e.name))i instanceof HTMLInputElement&&i.setCustomValidity(e.value===t?"unchanged":"");else e.setCustomValidity(e.value===(t||e.defaultValue)?"unchanged":"")}e.addEventListener("input",i),e.addEventListener("change",i),i(),e.form&&l(e.form)}),document.addEventListener("reset",function(e){if(e.target instanceof HTMLFormElement){let t=e.target;setTimeout(()=>l(t))}})},62643:(e,t,i)=>{i.d(t,{A:()=>h});var n=i(91385);let r=(e,t,i)=>{if(!(0,n.qA)(e,t))return-1/0;let r=(0,n.fN)(e,t);return r<i?-1/0:r},s=(e,t,i)=>{e.textContent="";let r=0;for(let s of(0,n.Xq)(t,i)){""!==i.slice(r,s)&&e.appendChild(document.createTextNode(i.slice(r,s))),r=s+1;let t=document.createElement("mark");t.textContent=i[s],e.appendChild(t)}e.appendChild(document.createTextNode(i.slice(r)))},a=new WeakMap,o=new WeakMap,l=new WeakMap,u=e=>{if(!l.has(e)&&e instanceof HTMLElement){let t=(e.getAttribute("data-value")||e.textContent||"").trim();return l.set(e,t),t}return l.get(e)||""},d=class FuzzyListElement extends HTMLElement{connectedCallback(){let e=this.querySelector("ul");if(!e)return;let t=new Set(e.querySelectorAll("li")),i=this.querySelector("input");i instanceof HTMLInputElement&&i.addEventListener("input",()=>{this.value=i.value});let r=new MutationObserver(e=>{let i=!1;for(let r of e)if("childList"===r.type&&r.addedNodes.length){for(let e of r.addedNodes)if(e instanceof HTMLLIElement&&!t.has(e)){let r=u(e);i=i||(0,n.qA)(this.value,r),t.add(e)}}i&&this.sort()});r.observe(e,{childList:!0});let s={handler:r,items:t,lazyItems:new Map,timer:null};o.set(this,s)}disconnectedCallback(){let e=o.get(this);e&&(e.handler.disconnect(),o.delete(this))}addLazyItems(e,t){let i=o.get(this);if(!i)return;let{lazyItems:r}=i,{value:s}=this,a=!1;for(let i of e)r.set(i,t),a=a||!!s&&(0,n.qA)(s,i);a&&this.sort()}sort(){let e=a.get(this);e&&(e.aborted=!0);let t={aborted:!1};a.set(this,t);let{minScore:i,markSelector:n,maxMatches:d,value:h}=this,c=o.get(this);if(!c||!this.dispatchEvent(new CustomEvent("fuzzy-list-will-sort",{cancelable:!0,detail:h})))return;let{items:m,lazyItems:p}=c,f=this.hasAttribute("mark-selector"),v=this.querySelector("ul");if(!v)return;let g=[];if(h){for(let e of m){let t=r(h,u(e),i);t!==-1/0&&g.push({item:e,score:t})}for(let[e,t]of p){let n=r(h,e,i);n!==-1/0&&g.push({text:e,render:t,score:n})}g.sort((e,t)=>t.score-e.score).splice(d)}else{let e=g.length;for(let t of m){if(e>=d)break;g.push({item:t,score:1}),e+=1}for(let[t,i]of p){if(e>=d)break;g.push({text:t,render:i,score:1}),e+=1}}requestAnimationFrame(()=>{if(t.aborted)return;let e=v.querySelector('input[type="radio"]:checked');v.textContent="";let i=0,r=()=>{if(t.aborted)return;let a=Math.min(g.length,i+100),o=document.createDocumentFragment();for(let e=i;e<a;e+=1){let t=g[e],i=null;if("render"in t&&"text"in t){let{render:e,text:n}=t;i=e(n),m.add(i),l.set(i,n),p.delete(n)}else"item"in t&&(i=t.item);i instanceof HTMLElement&&(f&&s(n&&i.querySelector(n)||i,f?h:"",u(i)),o.appendChild(i))}i=a;let d=!1;if(e instanceof HTMLInputElement)for(let t of o.querySelectorAll('input[type="radio"]:checked'))t instanceof HTMLInputElement&&t.value!==e.value&&(t.checked=!1,d=!0);if(this.getAttribute("data-tab-only-first")){let e=this.querySelectorAll("button.js-emoji-button");for(let t of e)t.setAttribute("tabindex","-1");e.item(0)?.setAttribute("tabindex","0")}else for(let e of o.querySelectorAll('button[tabindex="-1"]'))e.setAttribute("tabindex","0");if(v.appendChild(o),e&&d&&e.dispatchEvent(new Event("change",{bubbles:!0})),a<g.length)requestAnimationFrame(r);else{v.hidden=0===g.length;let e=this.querySelector("[data-fuzzy-list-show-on-empty]");e&&(e.hidden=g.length>0),this.dispatchEvent(new CustomEvent("fuzzy-list-sorted",{detail:g.length}))}};r()})}get value(){return this.getAttribute("value")||""}set value(e){this.setAttribute("value",e)}get markSelector(){return this.getAttribute("mark-selector")||""}set markSelector(e){e?this.setAttribute("mark-selector",e):this.removeAttribute("mark-selector")}get minScore(){return Number(this.getAttribute("min-score")||0)}set minScore(e){Number.isNaN(e)||this.setAttribute("min-score",String(e))}get maxMatches(){return Number(this.getAttribute("max-matches")||1/0)}set maxMatches(e){Number.isNaN(e)||this.setAttribute("max-matches",String(e))}get ariaLiveElement(){let e=this.getAttribute("data-aria-live-element");if(!e)return;let t=document.getElementById(e);if(t)return t}static get observedAttributes(){return["value","mark-selector","min-score","max-matches"]}attributeChangedCallback(e,t,i){if(t===i)return;let n=o.get(this);n&&(n.timer&&window.clearTimeout(n.timer),n.timer=window.setTimeout(()=>this.sort(),100))}},h=d;window.customElements.get("fuzzy-list")||(window.FuzzyListElement=d,window.customElements.define("fuzzy-list",d))},45323:(e,t,i)=>{function n(){return/Windows/.test(navigator.userAgent)?"windows":/Macintosh/.test(navigator.userAgent)?"mac":null}i.d(t,{u:()=>n}),(0,i(21403).lB)(".js-remove-unless-platform",function(e){!function(e){let t=(e.getAttribute("data-platforms")||"").split(","),i=n();return!!(i&&t.includes(i))}(e)&&e.remove()})},7799:(e,t,i)=>{let n;function r(){if(!n)throw Error("Client env was requested before it was loaded. This likely means you are attempting to use client env at the module level in SSR, which is not supported. Please move your client env usage into a function.");return n}function s(){return n?.locale??"en-US"}function a(){return!!r().login}i.d(t,{JK:()=>s,M3:()=>a,_$:()=>r});!function(){if("undefined"!=typeof document){let e=document.getElementById("client-env");if(e)try{n=JSON.parse(e.textContent||"")}catch(e){console.error("Error parsing client-env",e)}}}()},53005:(e,t,i)=>{i.d(t,{O:()=>a,S:()=>s});var n=i(96679);let r=n.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",s="X-GitHub-Client-Version";function a(){return r}},27851:(e,t,i)=>{i.d(t,{G7:()=>l,XY:()=>u,fQ:()=>o});var n=i(5225),r=i(7799);function s(){return new Set((0,r._$)().featureFlags)}let a=i(96679).X3||function(){try{return process?.env?.STORYBOOK==="true"}catch{return!1}}()?s:(0,n.A)(s);function o(){return Array.from(a())}function l(e){return a().has(e)}let u={isFeatureEnabled:l}},26559:(e,t,i)=>{i.d(t,{jC:()=>l,kt:()=>a,tV:()=>o});var n=i(53005),r=i(27851),s=i(88191);function a(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,s.wE)(e)};return(0,r.G7)("client_version_header")&&(t={...t,[n.S]:(0,n.O)()}),t}function o(e,t){for(let[i,n]of Object.entries(a(t)))e.set(i,n)}function l(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,i)=>{i.d(t,{$r:()=>a,M1:()=>o,li:()=>r,pS:()=>u,wE:()=>l});var n=i(96679);let r="X-Fetch-Nonce",s=new Set;function a(e){s.add(e)}function o(){return s.values().next().value||""}function l(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[r]=o():s.has(e)?t[r]=e:t[r]=Array.from(s).join(","),t}function u(){let e=n.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&a(e)}},36175:(e,t,i)=>{i.d(t,{Ff:()=>l,eC:()=>u,uE:()=>o});var n=i(6986);let r=!1,s=new n.A;function a(e){let t=e.target;if(t instanceof HTMLElement&&t.nodeType!==Node.DOCUMENT_NODE)for(let e of s.matches(t))e.data.call(null,t)}function o(e,t){r||(r=!0,document.addEventListener("focus",a,!0)),s.add(e,t),document.activeElement instanceof HTMLElement&&document.activeElement.matches(e)&&t(document.activeElement)}function l(e,t,i){function n(t){let r=t.currentTarget;r&&(r.removeEventListener(e,i),r.removeEventListener("blur",n))}o(t,function(t){t.addEventListener(e,i),t.addEventListener("blur",n)})}function u(e,t){function i(e){let{currentTarget:n}=e;n&&(n.removeEventListener("input",t),n.removeEventListener("blur",i))}o(e,function(e){e.addEventListener("input",t),e.addEventListener("blur",i)})}},96679:(e,t,i)=>{i.d(t,{KJ:()=>n.KJ,Kn:()=>r.Kn,X3:()=>n.X3,XC:()=>r.XC,cg:()=>r.cg,fV:()=>r.fV,g5:()=>n.g5});var n=i(28583),r=i(46570)},46570:(e,t,i)=>{i.d(t,{Kn:()=>a,XC:()=>r,cg:()=>s,fV:()=>o});let n="undefined"!=typeof FORCE_SERVER_ENV&&FORCE_SERVER_ENV,r="undefined"==typeof document||n?void 0:document,s="undefined"==typeof window||n?void 0:window,a="undefined"==typeof history||n?void 0:history,o="undefined"==typeof location||n?{pathname:"",origin:"",search:"",hash:"",href:""}:location},28583:(e,t,i)=>{i.d(t,{KJ:()=>s,X3:()=>r,g5:()=>a});var n=i(46570);let r=void 0===n.XC,s=!r;function a(){return!!r||!n.XC||!!(n.XC.querySelector('react-app[data-ssr="true"]')||n.XC.querySelector('react-partial[data-ssr="true"][partial-name="repos-overview"]'))}}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_github_selector-observer_dist_index_esm_js","vendors-node_modules_github_relative-time-element_dist_index_js","vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78","vendors-node_modules_github_text-expander-element_dist_index_js","vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643","vendors-node_modules_github_markdown-toolbar-element_dist_index_js","vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-cadbad"],()=>t(19723)),e.O()}]);
//# sourceMappingURL=github-elements-00781b907c5f.js.map