(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["behaviors"],{38962:(e,t,n)=>{"use strict";let r,i,a,o,s,l,c,u,d,m,f;var h=n(70396),p=n(97797);(0,p.on)("deprecatedAjaxSend","[data-remote]",function(e){e.currentTarget===e.target&&(e.defaultPrevented||e.currentTarget.classList.add("loading"))}),(0,p.on)("deprecatedAjaxComplete","[data-remote]",function(e){e.currentTarget===e.target&&e.currentTarget.classList.remove("loading")});var g=n(12559);(0,g.JW)("form.js-ajax-pagination, .js-ajax-pagination form",async function(e,t){let n,r=e.closest(".js-ajax-pagination");try{n=await t.html()}catch(e){if(e.response&&404===e.response.status)return void r.remove();throw e}r.replaceWith(n.html),(0,p.h)(e,"page:loaded")}),n(63166);var b=n(21403),v=n(7479);let y=["system","disabled"].map(e=>`html[data-a11y-animated-images="${e}"] img[data-animated-image]`).join(", ");(0,b.lB)(y,e=>{if(!(e instanceof HTMLImageElement)||e.closest("a")&&!(e.parentElement instanceof HTMLAnchorElement))return;let t=e.parentElement,n=null;if(t instanceof HTMLAnchorElement){if(t.childElementCount>1)return;(n=t).setAttribute("data-target","animated-image.originalLink"),t=n.parentElement}e.removeAttribute("data-animated-image"),e.setAttribute("data-target","animated-image.originalImage");let r=n?n.cloneNode(!0):e.cloneNode(!0),i=document.createElement("animated-image");i.appendChild(r),t?.replaceChild(i,n||e),(0,v.i)({incrementKey:"ANIMATED_IMAGE_PLAYER_WRAPPED",requestUrl:window.location.href})});var w=n(52811),E=n(80558);let S=new WeakMap;function A(e,t){t.classList.remove("is-loading","successed","errored","warn"),e.classList.remove("is-autocheck-loading","is-autocheck-successful","is-autocheck-errored");let n=t.querySelector("p.note");if(n){let e=S.get(n);e&&(n.innerHTML=e)}"DL"===t.tagName?(t.querySelector("dd.error")?.remove(),t.querySelector("dd.warning")?.remove(),t.querySelector("dd.success")?.remove()):(t.querySelector("div.error")?.remove(),t.querySelector("div.warning")?.remove(),t.querySelector("div.success")?.remove())}function j(e){e&&(0,E.t)(e)}(0,b.lB)("auto-check",function(e){let t;if(e.classList.contains("js-prevent-default-behavior"))return;let n=e.querySelector("input");if(!n||n.classList.contains("js-prevent-default-behavior"))return;let r=n.closest(".form-group")||e,i=n.form;function a(){return t||(t=`input-check-${(1e4*Math.random()).toFixed(0)}`),t}let o=n.getAttribute("aria-describedby");n.addEventListener("focusout:delay",()=>{n.classList.contains("js-nux-blank-field")||n.setAttribute("aria-describedby",[t,o].join(" "))}),n.classList.contains("js-nux-input")&&n.addEventListener("focusin",()=>{n.classList.contains("js-nux-blank-field")||n.setAttribute("aria-describedby",[t,o].join(" "))});let s=r.querySelector("p.note");s&&(s.id||(s.id=a()),S.set(s,s.innerHTML)),e.addEventListener("loadstart",()=>{A(n,r),r.classList.add("is-loading"),n.classList.add("is-autocheck-loading"),j(i)}),e.addEventListener("loadend",()=>{r.classList.remove("is-loading"),n.classList.remove("is-autocheck-loading")}),n.addEventListener("auto-check-success",async e=>{n.classList.add("is-autocheck-successful"),r.classList.add("successed"),j(i);let{response:t}=e.detail;if(!t)return;let o=await t.text();if(o){if(s instanceof HTMLElement)s.innerHTML=o,(0,w.C)(s);else{let e=200===t.status,i="DL"===r.tagName?"dd":"div",s=document.createElement(i);s.id=a(),e?(s.classList.add("success"),s.classList.add("js-nux-sr-only")):s.classList.add("warning"),s.innerHTML=o,r.append(s),r.classList.add(e?"successed":"warn"),(0,w.C)(s),e&&(s.hidden=document.activeElement!==n)}(0,p.h)(n,"auto-check-message-updated")}}),n.addEventListener("auto-check-error",async e=>{n.classList.add("is-autocheck-errored"),r.classList.add("errored"),j(i);let{response:t}=e.detail;if(!t)return;let o=await t.text();if(s instanceof HTMLElement)s.innerHTML=o||"Something went wrong",(0,w.C)(s);else{let e="DL"===r.tagName?"dd":"div",t=document.createElement(e);t.id=a(),t.classList.add("error"),t.innerHTML=o||"Something went wrong",r.append(t),(0,w.C)(t)}}),n.addEventListener("input",()=>{if(n.removeAttribute("aria-describedby"),n.classList.contains("js-nux-input")&&n.setAttribute("aria-describedby",[o].join(" ")),document.getElementById("captcha-container-nux")){let e=r.querySelector(".success");e&&e.remove();let t=n.nextElementSibling?.nextElementSibling;t?.textContent?.includes("cannot be blank")&&(t.remove(),n.classList.remove("is-autocheck-errored"),n.classList.remove("js-nux-blank-field"))}n.value||A(n,r)}),n.addEventListener("blur",()=>{let e=document.getElementById("captcha-container-nux"),t=r.querySelector(".success");e&&t?(t.classList.add("js-nux-sr-only"),(0,w.C)(t)):t&&(t.hidden=!0)}),n.addEventListener("focus",()=>{let e=r.querySelector(".success");e&&(e.hidden=!1,e.classList.add("js-nux-sr-only"))}),i?.addEventListener("reset",()=>{A(n,r)})});var L=n(96907);function T(e){let t=e.closest("form");if(!t)return;let n=t.querySelector(".js-auto-complete-button");n instanceof HTMLButtonElement&&(n.disabled=!e.value)}(0,b.lB)("auto-complete",function(e){e.addEventListener("loadstart",()=>e.classList.add("is-auto-complete-loading")),e.addEventListener("loadend",()=>e.classList.remove("is-auto-complete-loading"))}),(0,b.lB)("auto-complete",{constructor:L.Ay,initialize:T}),(0,p.on)("auto-complete-change","auto-complete",function(e){T(e.currentTarget)});var C=n(97325),k=n(1739),q=n(66871),x=n(26559);let M=null;(0,p.on)("submit","[data-autosearch-results-container]",async function(e){let t=e.currentTarget;if(!(t instanceof HTMLFormElement))return;e.preventDefault(),M?.abort(),t.classList.add("is-sending");let n=new URL(t.action,window.location.origin),i=t.method,a=new FormData(t),o=(0,C.K3)(n,a),s=null;"get"===i?n.search=o:s=a;let{signal:l}=M=new AbortController,c=new Request(n.toString(),{method:i,body:s,signal:l,headers:{Accept:"text/html",...(0,x.kt)()}}),u=null;try{u=await fetch(c)}catch{}if(t.classList.remove("is-sending"),!u||!u.ok||l.aborted)return;let d=t.getAttribute("data-autosearch-results-container"),m=d?document.getElementById(d):null;if(m){let e=m.style.height;m.style.height=getComputedStyle(m).height,m.textContent="",void 0!==r&&clearTimeout(r);let t=m.hasAttribute("data-delay-results"),n=await u.text(),i=(0,k.B)(document,n).querySelector("[data-autosearch-results]")||(0,k.B)(document,n).firstElementChild;r=setTimeout(()=>{m.appendChild((0,k.B)(document,n)),(0,w.C)(i),requestAnimationFrame(()=>{m.style.height=e})},500*!!t)}(0,q.bj)(`?${o}`)});var _=n(76899),H=n(36175);(0,H.uE)("input[data-autoselect], textarea[data-autoselect]",async function(e){await (0,_.k2)(),e.select()});var P=n(70170),O=n(22247);function I(e){let t=e.target;if(!(t instanceof HTMLInputElement)&&!(t instanceof HTMLSelectElement))return;let n=t.form;(0,C.k_)(n)}(0,p.on)("change","form[data-autosubmit]",function(e){let t=e.currentTarget;(0,C.k_)(t)}),(0,p.on)("change","input[data-autosubmit], select[data-autosubmit]",I);let R=(0,P.s)(I,300);(0,b.lB)("input[data-throttled-autosubmit]",{subscribe:e=>(0,O.Rt)(e,"input",R)}),n(57233);var $=n(62004),B=n(27811),N=n(59519);let D=[".unstyled-additional-seats-price-obj",".unstyled-base-price-obj",".unstyled-final-price-obj"],W=null;async function F(e){let t=e.getAttribute("data-item-name")||"items",n=e.value,r=new URL(e.getAttribute("data-url"),window.location.origin),i=new URLSearchParams(r.search.slice(1)),a=parseInt(e.getAttribute("data-item-minimum"))||0,o=parseInt(e.getAttribute("data-item-maximum"))||1e3,s=parseInt(e.getAttribute("data-item-count"))||0,l=Math.max(a,parseInt(n)||0),c=l>o,u=document.querySelector(".js-downgrade-button"),d=document.getElementById("downgrade-disabled-message");u instanceof HTMLButtonElement&&(u.disabled=l===s),d instanceof HTMLElement&&u instanceof HTMLButtonElement&&(d.hidden=!u.disabled),i.append(t,l.toString()),document.querySelector(".js-transform-user")&&i.append("transform_user","1"),r.search=i.toString(),W?.abort();let{signal:m}=W=new AbortController,f=null;try{let e=await fetch(r.toString(),{signal:m,headers:{Accept:"application/json"}});if(!e.ok)return;f=await e.json()}catch{}if(m.aborted||!f)return;let h=document.querySelector(".js-contact-us");h&&h.classList.toggle("d-none",!c);let p=document.querySelector(".js-payment-summary");p&&p.classList.toggle("d-none",c);let g=document.querySelector(".js-submit-billing");if(g instanceof HTMLElement&&(g.hidden=c),!f.url.includes("organizations/signup_billing")){let e=document.querySelector(".js-billing-section");e&&e.classList.toggle("has-removed-contents",f.free||f.is_enterprise_cloud_trial)}let b=document.querySelector(".js-upgrade-info");b&&b.classList.toggle("d-none",l<=0);let v=document.querySelector(".js-downgrade-info");v&&v.classList.toggle("d-none",l>=0);let y=document.querySelector(".js-extra-seats-line-item");y&&y.classList.toggle("d-none",f.no_additional_seats),document.querySelector(".js-seat-field")&&function(e){for(let t of document.querySelectorAll(".js-seat-field")){let n=t.getAttribute("data-item-maximum"),r=t?.parentNode?.querySelector(".Popover");n&&n.length&&(parseInt(e,10)>parseInt(n,10)?(t.classList.add("color-border-danger-emphasis"),r?.removeAttribute("hidden")):(t.classList.remove("color-border-danger-emphasis"),r?.setAttribute("hidden","true")))}}(n);let w=document.querySelector(".js-minimum-seats-disclaimer");w&&(w.classList.toggle("tooltipped",5===f.seats),w.classList.toggle("tooltipped-nw",5===f.seats));let E=f.selectors;for(let e in E)for(let t of document.querySelectorAll(e)){var S,A;if(A=e,D.includes(A)&&"string"!=typeof(S=E[e])&&"number"!=typeof S&&"default_currency"in S&&"local_currency"in S)t.textContent="",t.appendChild(U("default-currency",E[e].default_currency)),t.appendChild(U("local-currency",E[e].local_currency));else t.textContent=E[e]}(0,q.bj)(f.url)}function U(e,t){let n=document.createElement("span");return n.classList.add(e),n.textContent=t,n}(0,p.on)("click",".js-org-signup-duration-change",e=>{e.preventDefault();let t=e.currentTarget.getAttribute("data-plan-duration");for(let e of(function(e){let t="year"===e?"month":"year";for(let t of document.querySelectorAll(".js-plan-duration-text"))t.textContent=e;for(let t of document.querySelectorAll(".unstyled-available-plan-duration-adjective"))t.textContent=`${e}ly`;for(let e of document.querySelectorAll(".js-org-signup-duration-change"))e.setAttribute("data-plan-duration",t);let n=document.getElementById("signup-plan-duration");n&&(n.value=e)}(t),function(e){for(let t of document.querySelectorAll(".js-seat-field")){let n=new URL(t.getAttribute("data-url"),window.location.origin),r=new URLSearchParams(n.search.slice(1));r.delete("plan_duration"),r.append("plan_duration",e),n.search=r.toString(),t.setAttribute("data-url",n.toString())}}(t),document.querySelectorAll(".js-seat-field")))F(e);for(let e of document.querySelectorAll(".js-unit-price"))e.hidden=!e.hidden}),(0,p.on)("change",".js-org-signup-duration-toggle",function({currentTarget:e}){let t=new URL(e.getAttribute("data-url"),window.location.origin);(0,N.softNavigate)(t.toString())}),(0,b.lB)(".js-addon-purchase-field",{constructor:HTMLInputElement,add(e){(0,B.A)(e)&&F(e),(0,$.Up)(e,function(){F(e)})}}),(0,b.lB)(".js-addon-downgrade-field",{constructor:HTMLSelectElement,add(e){(0,B.A)(e)&&F(e),e.addEventListener("change",function(){F(e)})}}),(0,p.on)("details-menu-selected",".js-organization-container",function(e){let t=document.querySelector(".js-addon-purchase-field"),n=e.target.querySelector("input:checked");if(t instanceof HTMLInputElement&&n instanceof HTMLInputElement){let e=n.getAttribute("data-upgrade-url");e&&(t.setAttribute("data-url",e),t.value="0",F(t))}},{capture:!0}),(0,H.eC)(".js-csv-filter-field",function(e){let t=e.target.value.toLowerCase();for(let e of document.querySelectorAll(".js-csv-data tbody tr"))e instanceof HTMLElement&&e.textContent&&(e.hidden=!!t&&!e.textContent.toLowerCase().includes(t))});var z=n(38007);function V(e,t=!1){for(let[n,r]of Object.entries({"tooltipped-nw":"tooltipped-sw","tooltipped-n":"tooltipped-s","tooltipped-ne":"tooltipped-se"})){let i=t?r:n,a=t?n:r;for(let t of e.querySelectorAll(`.${i}`))t.classList.replace(i,a)}}(0,b.lB)(".js-blob-header.is-stuck",{add(e){V(e)},remove(e){V(e,!0)}}),(0,p.on)("click",".js-blob-dropdown-click",e=>{let t=e.currentTarget.getAttribute("data-dropdown-tracking");if(!t)return;let n=JSON.parse(t);(0,z.BI)(n.type,n.context)}),(0,p.on)("change",".js-branch-protection-integration-select-input",function(e){let t=e.target,n=t?.closest(".js-branch-protection-integration-select"),r=n?.querySelector(".js-branch-protection-integration-select-current"),i=t?.closest(".js-branch-protection-integration-select-item"),a=i?.querySelector(".js-branch-protection-integration-select-label");r&&a&&n&&(r.innerHTML=a.innerHTML,n.open=!1)});let G=null;async function X(e){let t=e.target;if(!(t instanceof HTMLElement))return;let n=t.querySelector(".js-bulk-actions"),r=!!t.querySelector(".js-bulk-actions-toggle:checked");G?.abort();let{signal:i}=G=new AbortController,a="";try{let e=await fetch(function(e){let t=new URL(e.getAttribute("data-bulk-actions-url"),window.location.origin),n=new URLSearchParams(t.search.slice(1)),r=e.getAttribute("data-bulk-actions-parameter"),i=Array.from(e.querySelectorAll(".js-bulk-actions-toggle:checked"));if(r)for(let e of i.map(e=>e.closest(".js-bulk-actions-item").getAttribute("data-bulk-actions-id")).sort())n.append(`${r}[]`,e);else for(let e of i.sort((e,t)=>e.value>t.value?1:-1))n.append(e.name,e.value);return t.search=n.toString(),t.toString()}(t),{signal:i,headers:{...(0,x.kt)()}});if(!e.ok)return;a=await e.text()}catch{}!i.aborted&&a&&(r?(K(t),n.innerHTML=a):(n.innerHTML=a,K(t)),(0,p.h)(t,"bulk-actions:updated"))}function K(e){let t=document.querySelector(".js-membership-tabs");if(t){let n=e.querySelectorAll(".js-bulk-actions-toggle:checked");t.classList.toggle("d-none",n.length>0)}}(0,p.on)("change",".js-bulk-actions-toggle",function(e){let t=e.currentTarget.closest(".js-bulk-actions-container");(0,p.h)(t,"bulk-actions:update")}),(0,p.on)("bulk-actions:update",".js-bulk-actions-container",(0,P.s)(X,100));var J=n(32475);function Y(e,t){try{return window.localStorage.setItem(e,JSON.stringify(t)),{kind:"ok",value:null}}catch(e){return{kind:"err",value:e}}}function Z(){let e=function(){let e={};for(let t of document.getElementsByTagName("script")){let n=t.src.match(/\/([\w-]+)-[0-9a-f]{8,}\.js$/);n&&(e[`${n[1]}.js`]=t.src)}for(let t of document.getElementsByTagName("link")){let n=t.href.match(/\/([\w-]+)-[0-9a-f]{8,}\.css$/);n&&(e[`${n[1]}.css`]=t.href)}return e}(),t=function(e){try{let t=window.localStorage.getItem(e);return{kind:"ok",value:t?JSON.parse(t):null}}catch(e){return{kind:"err",value:e}}}("bundle-urls");if("err"===t.kind)return void Y("bundle-urls",e);let n=t.value||{},r=Object.keys(e).filter(t=>n[t]!==e[t]);r.length&&"ok"===Y("bundle-urls",{...n,...e}).kind&&(0,v.i)({downloadedBundles:r})}function Q(e){e.preventDefault(),e.stopPropagation()}(async()=>{await J.K,window.requestIdleCallback(Z)})(),(0,b.lB)("a.btn.disabled",{subscribe:e=>(0,O.Rt)(e,"click",Q)});var ee=n(55760),et=n(15955);(0,b.lB)(".js-check-all-container",{constructor:HTMLElement,subscribe:et.A});var en=n(8367);let er="logout-was-successful";if((0,en.OR)(er).length>0){for(let e of[sessionStorage,localStorage])try{e.clear()}catch{}(0,en.Yj)(er)}var ei=n(27851);function ea(e){es.delete(e),eo(e)}function eo(e){let t=e.querySelector(".js-clipboard-copy-icon"),n=e.querySelector(".js-clipboard-check-icon");e.classList.toggle("ClipboardButton--success"),t&&t.classList.toggle("d-none"),n&&(n.classList.contains("d-sm-none")?n.classList.toggle("d-sm-none"):n.classList.toggle("d-none"))}(0,p.on)("clipboard-copy","[data-copy-feedback]",e=>{let t=e.currentTarget,n=t.getAttribute("data-copy-feedback"),r=t.getAttribute("aria-label"),i=t.getAttribute("data-tooltip-direction")||"s",a=t.getAttribute("data-announce-selector")||void 0,o=a?document.querySelector(a):void 0;t.setAttribute("aria-label",n),t.classList.add("tooltipped",`tooltipped-${i}`),t instanceof HTMLElement&&((0,ei.G7)("arianotify_partial_migration")&&"ariaNotify"in Element.prototype?t.ariaNotify(t.textContent||"",{priority:"high"}):(0,w.C)(t,{element:o??void 0}),setTimeout(()=>{r?t.setAttribute("aria-label",r):t.removeAttribute("aria-label"),t.classList.remove("tooltipped",`tooltipped-${i}`)},2e3))});let es=new WeakMap;(0,p.on)("clipboard-copy",".js-clipboard-copy:not([data-view-component])",function({currentTarget:e}){if(!(e instanceof HTMLElement))return;let t=es.get(e);t?clearTimeout(t):eo(e),es.set(e,window.setTimeout(ea,2e3,e))}),(0,p.on)("click",".readme-edit .js-readme-task-button",function(e){let t=e.currentTarget,n=t.nextElementSibling;if(n&&n.classList.contains("js-readme-form")){t.hidden=!0,n.hidden=!1;let e=n.querySelector("textarea");e&&e.focus()}}),(0,p.on)("click",".readme-edit .js-comment-edit-button",function(e){let t=e.currentTarget.closest(".js-comment");if(t){t.classList.add("is-comment-editing");let n=t.querySelector(".js-readme-form");n&&(n.hidden=!1);let r=n?.querySelector("textarea");r&&r.focus(),e.preventDefault()}}),(0,p.on)("click",".readme-edit .js-readme-form .js-comment-cancel-button",function(e){let t=e.currentTarget,n=t.closest(".js-readme-form"),r=n?.previousElementSibling,i=t.closest(".js-comment");if(n&&r&&r.classList.contains("js-readme-task-button"))r.hidden=!1,n.hidden=!0,e.preventDefault();else if(i){i.classList.remove("is-comment-editing");let t=i.querySelector(".js-readme-form");t&&(t.hidden=!0),e.preventDefault()}}),(0,p.on)("submit",".readme-edit .js-readme-form form",function(e){let t=e.currentTarget.closest(".js-readme-form"),n=t?.previousElementSibling,r=t?.querySelector("textarea"),i=r?.value||"";if(t){let r=()=>{if(i.trim())window.location.reload();else if(n&&n.classList.contains("js-readme-task-button"))t.hidden=!0,n.hidden=!1;else{let e=t?.closest(".js-comment");e&&(e.classList.remove("is-comment-editing"),t.hidden=!0),window.location.reload()}},a=e.currentTarget;a.addEventListener("ajax:success",r),a.addEventListener("ajax:complete",r);let o=e=>{e.target===a&&(r(),document.removeEventListener("ajax:complete",o))};document.addEventListener("ajax:complete",o),setTimeout(function(){window.location.reload()},2e3)}});var el=n(22353),ec=n(82939);(0,p.on)("click",".errored.js-remove-error-state-on-click",function({currentTarget:e}){e.classList.remove("errored")}),(0,g.JW)(".js-new-comment-form",async function(e,t){let n,r=e.querySelector(".js-comment-form-error");r instanceof HTMLElement&&(r.hidden=!0);try{n=await t.json()}catch(t){(0,el.N7)(t),function(e,t){let n="There was a problem saving your comment.",r="Please try again.";if(t.response)if(422===t.response.status){let e=t.response.json;e.errors&&(Array.isArray(e.errors)?n+=` Your comment ${e.errors.join(", ")}.`:n=e.errors)}else 200===t.response.status&&(r="Please reload the page and try again.");n+=` ${r}`;let i=e.querySelector(".js-comment-form-error");if(i instanceof HTMLElement){i.textContent=n,i.hidden=!1;let e=i.closest("div.form-group.js-remove-error-state-on-click");e&&e.classList.add("errored")}}(e,t)}if(!n)return;for(let t of(e.reset(),e.querySelectorAll(".js-resettable-field")))(0,C.m$)(t,t.getAttribute("data-reset-value")||"");let i=e.querySelector(".js-write-tab");i instanceof HTMLElement&&function(e){let t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}(i)&&i.click();let a=n.json.updateContent;for(let e in a){let t=a[e],n=document.querySelector(e);n instanceof HTMLElement?(0,ec.Uv)(n,t):console.warn(`couldn't find ${e} for immediate update`)}(0,p.h)(e,"comment:success")});let eu=(e,t)=>{let n=e.querySelector(".js-form-action-text")||e;n.textContent=t?e.getAttribute("data-comment-text"):n.getAttribute("data-default-action-text")},ed=e=>{let t;return n=>{let r=n.currentTarget.value.trim();r!==t&&(t=r,eu(e,!!r))}};function em(e,t){let n=e.closest(".js-write-bucket");n&&n.classList.toggle("focused",t)}function ef(e){let t=e.currentTarget;t instanceof Element&&em(t,!1)}(0,b.lB)(".js-comment-and-button",{constructor:HTMLButtonElement,initialize(e){let t=e.form.querySelector(".js-comment-field"),n=ed(e);return{add(){t.addEventListener("input",n),t.addEventListener("change",n)},remove(){t.removeEventListener("input",n),t.removeEventListener("change",n)}}}}),n(44709),(0,H.uE)(".js-comment-field",function(e){em(e,!0),e.addEventListener("blur",ef,{once:!0})});var eh=n(35707),ep=n(44358),eg=n(20784),eb=n(12153),ev=n(95754);function ey(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let ew=new WeakMap,eE=class CaretPosition{get top(){return this.coords.top}get left(){return this.coords.left}get height(){return this.coords.height}currentChar(e=1){return this.textArea.value.substring(this.index-e,this.index)}checkLine(e){return e<this.coords.top?-1:+(e>this.coords.top+this.coords.height)}xDistance(e){return Math.abs(this.left-e)}constructor(e,t,n){ey(this,"index",void 0),ey(this,"coords",void 0),ey(this,"textArea",void 0),this.index=e,this.coords=t,this.textArea=n}};function eS(e,t){let n;if(ew.has(e)?n=ew.get(e):(n=new Map,ew.set(e,n)),n.has(t))return n.get(t);{let r=new eE(t,(0,ev.A)(e,t),e);return n.set(t,r),r}}let eA=(e,t,n,r,i,a)=>{if(n===t)return n;let o=e=>{let t=e.filter(e=>0===e.checkLine(i)).sort((e,t)=>e.xDistance(r)>t.xDistance(r)?1:-1);return 0===t.length?n:t[0].index};if(n-t==1)return o([eS(e,t),eS(e,n)]);if(n-t==2){let r=eS(e,t);return o([r,eS(e,n-1),eS(e,n)])}let s=Math.floor((n+t)/2);if(s===t||s===n)return s;let l=eS(e,s);return i>l.top+l.height?eA(e,s+1,n,r,i,a+1):i<l.top?eA(e,t,s-1,r,i,a+1):3>l.xDistance(r)?s:l.left<r?0!==eS(e,s+1).checkLine(i)?s:eA(e,s+1,n,r,i,a+1):l.left>r?0!==eS(e,s-1).checkLine(i)?s:eA(e,t,s-1,r,i,a+1):s},ej=(e,t,n)=>{let r=e.value.length;return eA(e,0,r,t,n,0)};var eL=n(25794);let eT=new Map;(0,b.lB)(".js-paste-markdown",{initialize(e){let t,n=e.hasAttribute("data-paste-url-links-as-plain-text");return{add(){t=(0,ep.B1)(e,{defaultPlainTextPaste:{urlLinks:n}}).unsubscribe},remove(){t()}}}});let eC=new WeakMap;function ek(e){return["video/mp4","video/quicktime"].includes(e.file.type)}function eq(e){if(ek(e))return`
Uploading ${e.file.name}\u{2026}
`;let t=e.isImage()?"!":"";return`${t}[Uploading ${e.file.name}\u{2026}]()`}function ex(e){let t=e.target.closest("form");if(t){let e=t.querySelector(".btn-primary");e&&(e.disabled=!0)}}function eM(e){let t=e.target.closest("form");if(t){let e=t.querySelector(".btn-primary");e&&(e.disabled=!1)}}async function e_(e){var t,n;let r,{attachment:i}=e.detail,a=e.currentTarget;eR("",i.isImage()?await eH(i):ek(i)?(t=i,`
${t.href}
`):(n=i,`[${n.file.name}](${n.href})`),e,a),eT.size>0&&function(){for(let e of document.querySelectorAll(".issue-form-textarea"))for(let[t,n]of eT)e.value.includes(t)&&((0,eh.ee)(e,t,n,document.activeElement===e),eT.delete(t))}()}async function eH(e){let t=await eP(e.file),n=e.file.name.replace(/[[\]\\"<>&]/g,".").replace(/\.{2,}/g,".").replace(/^\.|\.$/gi,"").replace(/\.[^.]+$/,"").replace(/\./g," "),r=e.href||"";return(0,eb.TG)(t,r,n)}async function eP(e){let t=null;try{t=await (0,eb.pF)(e)}catch{return{width:0,height:0,ppi:0}}return t||{width:0,height:0,ppi:0}}function eO(e){let t=eq(e);return ek(e)?`
${t}
`:`${t}
`}function eI(e){var t;let n=e.currentTarget.querySelector(".js-comment-field"),r=(t=e.detail.attachment,eC.get(t)||eO(t));if(n)n.setCustomValidity(""),(0,eh.ee)(n,r,"");else{let t=e$(e.currentTarget);if(!t)return void(0,eL.R)("upload:editor:change",e.currentTarget,{state:"failed",placeholder:r,replacementText:""});let n=t.getSearchCursor(r);n.findNext(),n.replace("")}}function eR(e,t,n,r){let i=(r||n.currentTarget).querySelector(".js-comment-field"),a=(r||n.currentTarget).querySelector(".js-file-upload-loading-text"),o=eq(n.detail.attachment),{batch:s}=n.detail;if(i){let r=i.value.substring(i.selectionStart,i.selectionEnd);if("uploading"===e){let e;e=r.length?(0,eh.tJ)(i,r,o):(0,eh.bc)(i,o,{appendNewline:!0}),eC.set(n.detail.attachment,e)}else i.value.includes(o)||eT.set(o,t),(0,eh.ee)(i,o,t,document.activeElement===i);s.isFinished()?eM(n):ex(n)}else{let i=e$(r||n.currentTarget);if(i)if("uploading"===e)if(i.getSelection().length)i.replaceSelection(o);else{let e=i.getCursor(),t=eO(n.detail.attachment);i.replaceRange(t,e)}else{let e=i.getSearchCursor(o);e.findNext(),e.replace(t)}else(0,eL.R)("upload:editor:change",r||n.currentTarget,{state:""===e?"uploaded":"uploading",placeholder:o,replacementText:""===e?t:eO(n.detail.attachment)});s.isFinished()?eM(n):ex(n)}if(a){let e=a.getAttribute("data-file-upload-message");a.textContent=`${e} (${s.uploaded()+1}/${s.size})`}}function e$(e){let t=e.querySelector(".js-code-editor");if(!t)return;let n=(0,eg.j)(t);if(n)return n.editor}function eB(e){e.stopPropagation();let t=e.currentTarget;if(!t)return;let n=t.querySelector(".js-comment-field");if(n){var r=n,i=e;let t=r.getBoundingClientRect();"dragenter"===i.type&&ew.delete(r);let a=i.clientX-t.left,o=i.clientY-t.top+r.scrollTop,s=ej(r,a,o);r.setSelectionRange(s,s)}else{let n=e$(t);if((0,eL.R)("upload:editor:cursor",t,{left:e.clientX,top:e.clientY}),n){let t=n.coordsChar({left:e.pageX,top:e.pageY});n.setCursor(t),n.focus()}}}(0,p.on)("upload:setup",".js-upload-markdown-image",function(e){eR("uploading","",e)}),(0,p.on)("upload:complete",".js-upload-markdown-image",e_),(0,p.on)("upload:error",".js-upload-markdown-image",function(e){eI(e);let{batch:t}=e.detail;t.isFinished()?eM(e):ex(e)}),(0,p.on)("dragenter","file-attachment",eB),(0,p.on)("dragover","file-attachment",eB),(0,p.on)("upload:invalid",".js-upload-markdown-image",function(e){eI(e);let{batch:t}=e.detail;t.isFinished()?eM(e):ex(e)});var eN=n(49728),eD=n(5225);function eW(e){let t=e.closest(".js-previewable-comment-form"),n=e.classList.contains("js-preview-tab");if(n){let e=t.querySelector(".js-write-bucket"),n=t.querySelector(".js-preview-body");e.clientHeight>0&&(n.style.minHeight=`${e.clientHeight}px`)}t.classList.toggle("preview-selected",n),t.classList.toggle("write-selected",!n);let r=t.querySelector('.tabnav-tab.selected, .tabnav-tab[aria-selected="true"]');r.setAttribute("aria-selected","false"),r.classList.remove("selected"),e.classList.add("selected"),e.setAttribute("aria-selected","true");let i=t.querySelector(".js-write-tab");return n?i.setAttribute("data-hotkey","Mod+Shift+P"):i.removeAttribute("data-hotkey"),t}function eF(e){let t=e.getAttribute("data-preview-url"),n=function(e){let t=e.querySelector(".js-comment-field").value,n=e.querySelector(".js-path")?.value,r=e.querySelector(".js-line-number")?.value,i=e.querySelector(".js-start-line-number")?.value,a=e.querySelector(".js-side")?.value,o=e.querySelector(".js-start-side")?.value,s=e.querySelector(".js-start-commit-oid")?.value,l=e.querySelector(".js-end-commit-oid")?.value,c=e.querySelector(".js-base-commit-oid")?.value,u=e.querySelector(".js-comment-id")?.value,d=new FormData;return d.append("text",t),d.append("authenticity_token",function(e){let t=e.querySelector(".js-data-preview-url-csrf"),n=e.closest("form").elements.namedItem("authenticity_token");if(t instanceof HTMLInputElement)return t.value;if(n instanceof HTMLInputElement)return n.value;throw Error("Comment preview authenticity token not found")}(e)),n&&d.append("path",n),r&&d.append("line_number",r),i&&d.append("start_line_number",i),a&&d.append("side",a),o&&d.append("start_side",o),s&&d.append("start_commit_oid",s),l&&d.append("end_commit_oid",l),c&&d.append("base_commit_oid",c),u&&d.append("comment_id",u),d}(e);return(0,p.h)(e,"preview:setup",{data:n}),eU(t,n)}(0,p.on)("click",".js-write-tab",function(e){let t=e.currentTarget,n=t.closest(".js-previewable-comment-form");if(n instanceof eN.A)return void setTimeout(()=>{n.querySelector(".js-comment-field").focus()});let r=eW(t);(0,p.h)(n,"preview:toggle:off");let i=n.querySelector(".js-discussion-poll-form-component");i&&(0,p.h)(i,"poll-preview:toggle:off"),setTimeout(()=>{r.querySelector(".js-comment-field").focus()});let a=n.querySelector("markdown-toolbar");a instanceof HTMLElement&&(a.hidden=!1)}),(0,p.on)("click",".js-preview-tab",function(e){let t=e.currentTarget,n=t.closest(".js-previewable-comment-form");if(n instanceof eN.A)return;let r=eW(t);(0,p.h)(n,"preview:toggle:on"),setTimeout(()=>{eG(r)});let i=n.querySelector("markdown-toolbar");i instanceof HTMLElement&&(i.hidden=!0),e.stopPropagation(),e.preventDefault()}),(0,p.on)("tab-container-change",".js-previewable-comment-form",function(e){let t=e.detail.relatedTarget,n=t&&t.classList.contains("js-preview-panel"),r=e.currentTarget,i=r.querySelector(".js-write-tab");if(n){let e=r.querySelector(".js-write-bucket"),t=r.querySelector(".js-preview-body");!t.hasAttribute("data-skip-sizing")&&e.clientHeight>0&&(t.style.minHeight=`${e.clientHeight}px`),i.setAttribute("data-hotkey","Mod+Shift+P"),eG(r);let n=r.querySelector("markdown-toolbar");n instanceof HTMLElement&&(n.hidden=!0)}else{i.removeAttribute("data-hotkey");let e=r.querySelector("markdown-toolbar");e instanceof HTMLElement&&(e.hidden=!1);let t=document.querySelector(".js-discussion-poll-form-component");t&&(0,p.h)(t,"poll-preview:toggle:off")}r.classList.toggle("preview-selected",!!n),r.classList.toggle("write-selected",!n)}),(0,p.on)("preview:render",".js-previewable-comment-form",function(e){let t=eW(e.target.querySelector(".js-preview-tab"));setTimeout(()=>{eG(t);let e=t.querySelector("markdown-toolbar");e instanceof HTMLElement&&(e.hidden=!0)})});let eU=(0,eD.A)(eV,{hash:function(e,t){let n=[...t.entries()].toString();return`${e}:${n}`}}),ez=null;async function eV(e,t){ez?.abort();let{signal:n}=ez=new AbortController,r=await fetch(e,{method:"post",body:t,signal:n});if(!r.ok)throw Error("something went wrong");return r.text()}async function eG(e){let t=e.querySelector(".comment-body");t.innerHTML="<p>Loading preview&hellip;</p>";try{t.innerHTML=await eF(e)||"<p>Nothing to preview</p>",(0,p.h)(e,"preview:rendered")}catch(e){"AbortError"!==e.name&&(t.innerHTML="<p>Error rendering preview</p>")}}(0,b.lB)(".js-preview-tab",function(e){e.addEventListener("mouseenter",async()=>{let t=e.closest(".js-previewable-comment-form");try{await eF(t)}catch{}})}),(0,H.Ff)("keydown",".js-comment-field",function(e){let t=e.target;if((e.ctrlKey||e.metaKey)&&e.shiftKey&&"P"===e.key.toUpperCase()){let n=t.closest(".js-previewable-comment-form");n.classList.contains("write-selected")&&(n instanceof eN.A?n.querySelector(".js-preview-tab").click():(t.blur(),n.dispatchEvent(new CustomEvent("preview:render",{bubbles:!0,cancelable:!1}))),e.preventDefault(),e.stopImmediatePropagation())}});let eX=/^(\+1|-1|:\+1?|:-1?)$/,eK=e=>{let t=!1;for(let n of e.split(`
`)){let e=n.trim();if(!(!e||e.startsWith(">"))){if(t&&!1===eX.test(e))return!1;!t&&eX.test(e)&&(t=!0)}}return t};function eJ(e){let t=e.target,n=t.value,r=t.closest(".js-reaction-suggestion");if(r)if(eK(n)){r.classList.remove("hide-reaction-suggestion"),r.classList.add("reaction-suggestion");let e=r.getAttribute("data-reaction-markup");r.setAttribute("data-reaction-suggestion-message",e)}else eY(r)}function eY(e){e.classList.remove("reaction-suggestion"),e.classList.add("hide-reaction-suggestion"),e.removeAttribute("data-reaction-suggestion-message")}(0,p.on)("focusout","#new_comment_field",function(e){let t=e.currentTarget.closest(".js-reaction-suggestion");t&&eY(t)}),(0,p.on)("focusin","#new_comment_field",function(e){eJ(e)}),(0,H.Ff)("keyup","#new_comment_field",function(e){eJ(e)});var eZ=n(57909);(0,p.on)("navigation:keydown",".js-commits-list-item",function(e){(0,eZ.$$)(e.detail.originalEvent)&&e.target instanceof Element&&"c"===e.detail.hotkey&&e.target.querySelector(".js-navigation-open").click()}),n(54765),(0,H.eC)(".js-company-name-input",function(e){let t=e.target,n=t.form,r=n.querySelector(".js-corp-tos-link"),i=n.querySelector(".js-tos-link");i&&(i.classList.add("d-none"),i.setAttribute("aria-hidden","true"),r&&(r.classList.remove("d-none"),r.setAttribute("aria-hidden","false")));let a=n.querySelectorAll(".js-company-name-text");if(0!==a.length)for(let e of a)if(t.value)if(e.hasAttribute("data-wording")){let n=e.getAttribute("data-wording");e.textContent=` ${n} ${t.value}`}else e.textContent=t.value;else e.textContent=""}),(0,b.lB)(".js-company-owned:not(:checked)",{constructor:HTMLInputElement,add(e){let t=e.form.querySelector(".js-company-name-input"),n=document.querySelector(".js-company-name-text"),r=document.querySelector(".js-corp-tos-link"),i=document.querySelector(".js-tos-link");t&&(e.getAttribute("data-optional")&&t.removeAttribute("required"),(0,C.m$)(t,"")),i.classList.remove("d-none"),i.setAttribute("aria-hidden","false"),r.classList.add("d-none"),r.setAttribute("aria-hidden","true"),n&&(n.textContent="")}}),(0,b.lB)(".js-company-owned:checked",{constructor:HTMLInputElement,add(e){let t=e.form.querySelector(".js-company-name-input");t&&(t.setAttribute("required",""),(0,p.h)(t,"focus"),(0,p.h)(t,"input"))}}),(0,b.lB)(".js-company-owned-autoselect",{constructor:HTMLInputElement,add(e){function t(){if(e.checked&&e.form){let t=e.form.querySelector(".js-company-owned");(0,C.m$)(t,!0)}}e.addEventListener("change",t),t()}});var eQ=n(77176),e0=n(73238),e1=n(61430);let e2=null;function e5({currentTarget:e}){if(e.hasAttribute("open")){let t=e.querySelector("[autofocus]");t&&t.focus()}else{let t=e.querySelector("summary");t&&t.focus()}}function e3({currentTarget:e}){e.hasAttribute("open")?(e2&&e2!==e&&e2.removeAttribute("open"),e2=e):e===e2&&(e2=null)}document.addEventListener("keydown",function(e){!e.defaultPrevented&&"Escape"===e.key&&e2&&e2.removeAttribute("open")}),(0,b.lB)(".js-dropdown-details",{subscribe:e=>(0,O.Zz)((0,O.Rt)(e,"toggle",e3),(0,O.Rt)(e,"toggle",e5))}),(0,b.lB)("[data-deferred-details-content-url]:not([data-details-no-preload-on-hover])",{subscribe:e=>{let t=e.querySelector("summary");return(0,O.Rt)(t,"mouseenter",e1.s)}}),(0,b.lB)("[data-deferred-details-content-url]",{subscribe:e=>(0,O.Rt)(e,"toggle",e1.s)}),(0,p.on)("click","[data-toggle-for]",function(e){let t=e.currentTarget.getAttribute("data-toggle-for")||"",n=document.getElementById(t);n&&(n.hasAttribute("open")?n.removeAttribute("open"):n.setAttribute("open","open"))}),(0,e0.A)(function({target:e}){if(!e||e.closest("summary"))return;let t=e.parentElement;for(;t;)(t=t.closest("details"))&&(t.hasAttribute("open")||t.setAttribute("open",""),t=t.parentElement)});var e7=n(27552);(0,b.lB)("details.select-menu details-menu include-fragment",function(e){let t=e.closest("details");t&&(e.addEventListener("loadstart",function(){t.classList.add("is-loading"),t.classList.remove("has-error")}),e.addEventListener("error",function(){t.classList.add("has-error")}),e.addEventListener("loadend",function(){t.classList.remove("is-loading");let e=t.querySelector(".js-filterable-field");e&&(0,p.h)(e,"filterable:change")}))}),(0,b.lB)("details details-menu .js-filterable-field",{constructor:HTMLInputElement,add(e){let t=e.closest("details");t.addEventListener("toggle",function(){t.hasAttribute("open")||(e.value="",(0,p.h)(e,"filterable:change"))})}}),(0,b.lB)("details-menu[role=menu] [role=menu]",e=>{let t=e.closest("details-menu[role]");t&&t!==e&&t.removeAttribute("role")}),(0,b.lB)("details details-menu remote-input input",{constructor:HTMLInputElement,add(e){let t=e.closest("details");t.addEventListener("toggle",function(){t.hasAttribute("open")||(e.value="")})}}),(0,b.lB)("form details-menu",e=>{let t=e.closest("form");t.addEventListener("reset",()=>{setTimeout(()=>(function(e){for(let t of e.querySelectorAll("details-menu [role=menuitemradio] input[type=radio]:checked"))(0,p.h)(t,"change")})(t),0)})}),(0,H.Ff)("keypress","details-menu .js-filterable-field, details-menu filter-input input",e=>{if("Enter"===e.key){let t=e.currentTarget.closest("details-menu").querySelector('[role^="menuitem"]:not([hidden])');t instanceof HTMLElement&&!t.classList.contains("select-menu-clear-item")&&t.click(),e.preventDefault()}}),(0,p.on)("details-menu-selected","details-menu",e=>{let t=e.currentTarget.querySelector(".js-filterable-field");t instanceof HTMLInputElement&&t.value&&t.focus()},{capture:!0});let e4=e=>{if(!(e.target instanceof Element))return;let t=e.target.getAttribute("data-menu-input"),n=document.getElementById(t);(n instanceof HTMLInputElement||n instanceof HTMLTextAreaElement)&&(n.value=(e.detail.relatedTarget||e.detail.item.querySelector("button")).value)};async function e9({currentTarget:e}){let t=e.hasAttribute("open");if(t){let t=e.querySelector(".js-filterable-field");t instanceof HTMLInputElement&&t.focus()}(0,p.h)(e,t?"menu:activate":"menu:deactivate"),await (0,_.k2)(),(0,p.h)(e,t?"menu:activated":"menu:deactivated")}(0,p.on)("itemActivated","[data-menu-input]",e4,{capture:!0}),(0,p.on)("details-menu-selected","[data-menu-input]",e4,{capture:!0}),(0,b.lB)("details-menu remote-input",{constructor:e7.A,initialize(e){let t=document.getElementById(e.getAttribute("aria-owns")||"");if(!t)return;let n=null;e.addEventListener("load",()=>{n=document.activeElement&&t.contains(document.activeElement)&&document.activeElement.id?document.activeElement.id:null}),e.addEventListener("loadend",()=>{if(n){let r=t.querySelector(`#${n}`)||t.querySelector('[role^="menu"]');r instanceof HTMLElement?r.focus():e.input&&e.input.focus()}})}}),(0,p.on)("details-menu-selected","details-menu[data-menu-max-options]",e=>{let t=+e.currentTarget.getAttribute("data-menu-max-options")===e.currentTarget.querySelectorAll('[role="menuitemcheckbox"][aria-checked="true"]').length;for(let n of(e.currentTarget.querySelector("[data-menu-max-options-warning]").hidden=!t,e.currentTarget.querySelectorAll('[role="menuitemcheckbox"] input')))n.disabled=t&&!n.checked},{capture:!0}),(0,b.lB)("details > details-menu",{subscribe(e){let t=e.closest("details");return(0,O.Rt)(t,"toggle",e9)}}),(0,b.lB)("details > details-menu[preload]:not([src])",{subscribe:e=>(0,O.Rt)(e.parentElement,"mouseover",function(e){let t=e.currentTarget.querySelector("include-fragment[src]");t?.load()})}),(0,b.lB)("button[data-show-dialog-id]",e=>{e?.addEventListener("mouseenter",()=>{let t=e.getAttribute("data-show-dialog-id"),n=e.ownerDocument.getElementById(t);n?.querySelector("include-fragment[loading=lazy]")?.setAttribute("loading","eager")})}),(0,b.lB)("summary[data-show-dialog-id]",e=>{e?.addEventListener("click",()=>{let t=e.getAttribute("data-show-dialog-id");if(!t)return;let n=e.ownerDocument.getElementById(t);n?.show()})});let e6=new WeakMap;function e8(e){return[Array.from(e.querySelectorAll("input[type=submit][data-disable-with], button[data-disable-with]")),Array.from(document.querySelectorAll(`button[data-disable-with][form="${e.id}"]`))].flat()}function te(e){for(let t of e8(e)){let n=e6.get(t);null!=n&&(t instanceof HTMLInputElement?t.value=n:t.innerHTML=n,(!t.hasAttribute("data-disable-invalid")||e.checkValidity())&&(t.disabled=!1),e6.delete(t))}}(0,p.on)("submit","form",function(e){for(let t of e8(e.currentTarget)){e6.set(t,t instanceof HTMLInputElement?t.value||"Submit":t.innerHTML||"");let e=t.getAttribute("data-disable-with");e&&(t instanceof HTMLInputElement?t.value=e:t.textContent=e),t.disabled=!0}},{capture:!0}),(0,p.on)("deprecatedAjaxComplete","form",function({currentTarget:e,target:t}){e===t&&te(e)}),(0,g.ZV)(te),n(5012);var tt=n(21715),tn=n(98831);async function tr(e){let t=e.getAttribute("data-feature-preview-indicator-src"),n=await ti(t);for(let t of e.querySelectorAll(".js-feature-preview-indicator"))t.hidden=!n}async function ti(e){try{let t=await fetch(e,{headers:{Accept:"application/json"}});if(!t.ok)return!1;return(await t.json()).show_indicator}catch{return!1}}(0,b.lB)("[data-favicon-override]",{add(e){let t=e.getAttribute("data-favicon-override");setTimeout(()=>(0,tn.Ow)(t))},remove(){(0,tn.gd)()}}),(0,tn.uQ)(),document.addEventListener(tt.z.SUCCESS,tn.uQ),window.matchMedia("(prefers-color-scheme: dark)").addListener(()=>{(0,tn.uQ)()}),(0,b.lB)(".js-feature-preview-indicator-container",e=>{tr(e)});var ta=n(24791),to=n(20451);function ts(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}(0,p.on)("click","[data-feature-preview-trigger-url]",async e=>{let t=e.currentTarget,n=t.getAttribute("data-feature-preview-trigger-url"),r=await (0,ta.r)({content:(0,to.Ts)(document,n),dialogClass:"feature-preview-dialog"}),i=t.getAttribute("data-feature-preview-close-details"),a=t.getAttribute("data-feature-preview-close-hmac");for(let e of(r.addEventListener("dialog:remove",()=>{(0,v.i)({hydroEventPayload:i,hydroEventHmac:a},!0)}),document.querySelectorAll(".js-feature-preview-indicator")))e.hidden=!0}),(0,g.JW)(".js-feature-preview-unenroll",async(e,t)=>{await t.text();let n=e.querySelector(".js-feature-preview-slug").value;(0,p.h)(e,`feature-preview-unenroll:${n}`)}),(0,g.JW)(".js-feature-preview-enroll",async(e,t)=>{await t.text();let n=e.querySelector(".js-feature-preview-slug").value;(0,p.h)(e,`feature-preview-enroll:${n}`)});let tl="X-Digest-Sha256",tc="X-Digest-Sha256-Hmac",tu=class AttachmentUpload{async process(e){var t,n;let r=window.performance.now(),i=new Headers(this.policy.header||{}),a=new XMLHttpRequest;for(let[e,t]of(a.open("POST",this.policy.upload_url,!0),i))a.setRequestHeader(e,t);a.onloadstart=()=>{e.attachmentUploadDidStart(this.attachment,this.policy)},a.upload.onprogress=t=>{if(t.lengthComputable){let n=Math.round(t.loaded/t.total*100);e.attachmentUploadDidProgress(this.attachment,n)}},await (t=a,n=function(e,t){let n=new FormData;for(let e in t.same_origin&&n.append("authenticity_token",t.upload_authenticity_token),t.form)n.append(e,t.form[e]);return n.append("file",e.file),n}(this.attachment,this.policy),new Promise((e,r)=>{t.onload=()=>e(t),t.onerror=r,t.send(n)}));let o=a.getResponseHeader(tl),s=a.getResponseHeader(tc),l={};o&&s&&(l[tl]=o,l[tc]=s),204===a.status?(td(this.policy,l),e.attachmentUploadDidComplete(this.attachment,this.policy,{})):201===a.status?(td(this.policy,l),e.attachmentUploadDidComplete(this.attachment,this.policy,JSON.parse(a.responseText))):e.attachmentUploadDidError(this.attachment,{status:a.status,body:a.responseText});let c={duration:window.performance.now()-r,size:this.attachment?.file?.size,fileType:this.attachment?.file?.type,success:204===a.status||201===a.status};(0,v.i)({uploadTiming:c},!0)}constructor(e,t){ts(this,"attachment",void 0),ts(this,"policy",void 0),this.attachment=e,this.policy=t}};function td(e,t){let n="string"==typeof e.asset_upload_url?e.asset_upload_url:null,r="string"==typeof e.asset_upload_authenticity_token?e.asset_upload_authenticity_token:null;if(!(n&&r))return;let i=new FormData;i.append("authenticity_token",r),fetch(n,{method:"PUT",body:i,credentials:"same-origin",headers:{Accept:"application/json",...(0,x.kt)(),...t}})}async function tm(e,t){var n,r;tb(t,"is-uploading");let i=(n=e,r=t,{attachmentUploadDidStart(e,t){e.saving(0),tb(r,"is-uploading"),(0,p.h)(r,"upload:start",{batch:n,attachment:e,policy:t})},attachmentUploadDidProgress(e,t){e.saving(t),(0,p.h)(r,"upload:progress",{batch:n,attachment:e})},attachmentUploadDidComplete(e,t,i){var a,o;e.saved((a=i,o=t,{id:(null==a.id?null:String(a.id))||(null==o.asset.id?null:String(o.asset.id)),href:("string"==typeof a.href?a.href:null)||("string"==typeof o.asset.href?o.asset.href:null),name:o.asset.name})),(0,p.h)(r,"upload:complete",{batch:n,attachment:e}),n.isFinished()&&tb(r,"is-default")},attachmentUploadDidError(e,t){n.setAttachmentAsFailed(e),(0,p.h)(r,"upload:error",{batch:n,attachment:e});let{state:i}=th(t);tb(r,i)}});for(let n of e.attachments){let r=await tf(e,n,t);if(!r)return;try{let e=new tu(n,r);await e.process(i)}catch{e.setAttachmentAsFailed(n),(0,p.h)(t,"upload:error",{batch:e,attachment:n}),tb(t,"is-failed");return}}}async function tf(e,t,n){let r=function(e,t){let n=t.querySelector(".js-data-upload-policy-url-csrf").value,r=t.getAttribute("data-upload-repository-id"),i=t.getAttribute("data-subject-type"),a=t.getAttribute("data-subject-param"),o=t.getAttribute("data-upload-container-type"),s=t.getAttribute("data-upload-container-id"),l=e.file,c=new FormData;return c.append("name",l.name),c.append("size",String(l.size)),c.append("content_type",l.type),c.append("authenticity_token",n),i&&c.append("subject_type",i),a&&c.append("subject",a),r&&c.append("repository_id",r),e.directory&&c.append("directory",e.directory),o&&c.append("upload_container_type",o),o&&s&&c.append("upload_container_id",s),c}(t,n),i=[];(0,p.h)(n,"upload:setup",{batch:e,attachment:t,form:r,preprocess:i});try{var a,o;await Promise.all(i);let s=await fetch((a=r,o=n,new Request(o.getAttribute("data-upload-policy-url"),{method:"POST",body:a,credentials:"same-origin",headers:{Accept:"application/json",...(0,x.kt)()}})));if(s.ok)return await s.json();e.setAttachmentAsFailed(t),(0,p.h)(n,"upload:invalid",{batch:e,attachment:t});let l=await s.text(),c=s.status,{state:u,messaging:d}=th({status:c,body:l},t.file);tb(n,u,d)}catch{e.setAttachmentAsFailed(t),(0,p.h)(n,"upload:invalid",{batch:e,attachment:t}),tb(n,"is-failed")}return null}function th(e,t){if(400===e.status)return{state:"is-bad-file"};if(422!==e.status)return{state:"is-failed"};let n=JSON.parse(e.body);if(!n||!n.errors)return{state:"is-failed"};for(let e of n.errors)switch(e.field){case"size":{let n=t?t.size:null;if(null!=n&&0===n)return{state:"is-empty"};return{state:"is-too-big",messaging:{message:tp(e.message),target:".js-upload-too-big"}}}case"file_count":return{state:"is-too-many"};case"width":case"height":return{state:"is-bad-dimensions"};case"name":if("already_exists"===e.code)return{state:"is-duplicate-filename"};return{state:"is-bad-file"};case"content_type":return{state:"is-bad-file"};case"uploader_id":return{state:"is-bad-permissions"};case"repository_id":return{state:"is-repository-required"};case"format":return{state:"is-bad-format"}}return{state:"is-failed"}}let tp=e=>e.startsWith("size")?e.substring(5):e,tg=["is-default","is-uploading","is-bad-file","is-duplicate-filename","is-too-big","is-too-many","is-hidden-file","is-failed","is-bad-dimensions","is-empty","is-bad-permissions","is-repository-required","is-bad-format"];function tb(e,t,n){if(e.classList.remove(...tg),n){let{message:t,target:r}=n,i=e.querySelector(r);i&&(i.innerHTML=t)}if("is-uploading"!==t&&"is-default"!==t){let n=function(e,t){let n={"is-duplicate-filename":"#is-duplicate-filename-message","is-bad-file":"#is-bad-file-message","is-too-big":"#is-too-big-message","is-empty":"#is-empty-message","is-failed":"#is-failed-message","is-too-many":"#is-too-many-message"}[e];if(n){let e=t.querySelector(n);if(e)return(e.textContent?.trim()||"").replace(/\s+/g," ").trim()}return""}(t,e);n&&(0,w.i)(n)}e.classList.add(t)}function tv(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let ty=class Batch{percent(){return Math.round(tw(this.attachments,e=>e.file.size*e.percent/100)/this.total*100)}uploaded(){return tw(this.attachments,e=>+!!e.isSaved())}isFinished(){return this.attachments.every(e=>this.failedAttachments.includes(e)||e.isSaved())}setAttachmentAsFailed(e){this.attachments.includes(e)&&!this.failedAttachments.includes(e)&&this.failedAttachments.push(e)}constructor(e){tv(this,"attachments",void 0),tv(this,"size",void 0),tv(this,"total",void 0),tv(this,"failedAttachments",void 0),this.attachments=e,this.failedAttachments=[],this.size=this.attachments.length,this.total=tw(this.attachments,e=>e.file.size)}};function tw(e,t){return e.reduce((e,n)=>e+t(n),0)}(0,b.lB)("file-attachment[hover]",{add(e){e.classList.add("dragover")},remove(e){e.classList.remove("dragover")}}),(0,p.on)("file-attachment-accept","file-attachment",function(e){let{attachments:t}=e.detail;0===t.length&&(tb(e.currentTarget,"is-hidden-file"),e.preventDefault())}),(0,p.on)("file-attachment-accepted","file-attachment",function(e){let t=e.currentTarget.querySelector(".drag-and-drop");if(t&&t.hidden)return;let{attachments:n}=e.detail;tm(new ty(n),e.currentTarget)}),(0,p.on)("click","button[data-file-attachment-for]",function(e){let t=e.currentTarget.getAttribute("data-file-attachment-for");document.querySelector(`input[type=file]#${t}`).click()});let tE=0;function tS(e){return Array.from(e.types).indexOf("Files")>=0}function tA(e){let t=e.dataTransfer;t&&tS(t)&&e.preventDefault()}function tj(e){let t=e.dataTransfer;t&&tS(t)&&e.preventDefault()}function tL({currentTarget:e}){tb(e.querySelector("file-attachment"),"is-default")}(0,b.lB)("file-attachment",{add(e){0==tE++&&(document.addEventListener("drop",tA),document.addEventListener("dragover",tj));let t=e.closest("form");t&&t.addEventListener("reset",tL)},remove(e){0==--tE&&(document.removeEventListener("drop",tA),document.removeEventListener("dragover",tj));let t=e.closest("form");t&&t.removeEventListener("reset",tL)}});var tT=n(62044);function tC(e,t,n,r={}){let i=r.limit??1/0,a=0;for(let r of e.children){let e=n(r,t);null==e||(e&&a<i?(a++,tk(r,!0)):tk(r,!1))}return a}function tk(e,t){e.style.display=t?"":"none",e.hidden=!t}(0,p.on)("filter-input-updated","filter-input",e=>{let t=e.target,n=t.input;if(!(document.activeElement&&document.activeElement===n))return;let{count:r,total:i}=e.detail,a=function(e){let t=e.getAttribute("data-aria-live-element");if(!t)return{};let n=document.getElementById(t);return n?{element:n}:{}}(t);(0,w.i)(`Found ${r} out of ${i} ${1===i?"item":"items"}`,a)}),(0,p.on)("toggle","details",e=>{setTimeout(()=>(function(e){let t=e.querySelector("filter-input");t&&!e.hasAttribute("open")&&t.reset()})(e.target),0)},{capture:!0}),(0,p.on)("tab-container-changed","tab-container",e=>{if(!(e.target instanceof HTMLElement))return;let{relatedTarget:t}=e.detail,n=e.target.querySelector("filter-input");t&&n instanceof tT.A&&n.setAttribute("aria-owns",t.id)},{capture:!0});var tq=n(30695),tx=n(13617);let tM=new WeakMap;function t_(e,t,n){let r=t.toLowerCase(),i=n.limit,a=tM.get(e),o=e.querySelector('input[type="radio"]:checked'),s=Array.from(e.children);if(a){if(e.classList.contains("filter-sort-list-refresh"))for(let t of(e.classList.remove("filter-sort-list-refresh"),Array.from(e.children)))a.includes(t)||a.push(t)}else a=Array.from(e.children),tM.set(e,a);for(let t of s)e.removeChild(t),t instanceof HTMLElement&&(t.style.display="");let l=r?(0,tx.d)(a,n.sortKey,tq.UD):a,c=null==i?l:l.slice(0,i),u=c.length,d=document.createDocumentFragment();for(let e of c)d.appendChild(e);let m=!1;if(o instanceof HTMLInputElement)for(let e of d.querySelectorAll('input[type="radio"]:checked'))e instanceof HTMLInputElement&&e.value!==o.value&&(e.checked=!1,m=!0);return e.appendChild(d),o&&m&&o.dispatchEvent(new Event("change",{bubbles:!0})),u}var tH=n(88402);let tP=new AbortController,tO=new WeakMap,tI=new WeakMap,tR=new WeakMap;async function t$(e,t,n){var r;n&&!tO.has(e)&&(r=e,tO.set(r,{lastSearchResult:{suggestions:[],users:[]},cachedSuggestions:[],userResultCache:new Map}));let i=await tB(e,t,n);return e.hasAttribute("data-filterable-data-pre-rendered")&&(i.suggestions=function(e,t){let n=[],r=e.querySelectorAll(".js-filterable-suggested-user");if(r.length>0)for(let t of e.querySelectorAll(".js-filterable-suggested-user"))t.classList.remove("js-filterable-suggested-user"),n.push({name:t.querySelector(".js-description").textContent,login:t.querySelector(".js-username").textContent,selected:"true"===t.getAttribute("aria-checked"),element:t,suggestion:!0});if(t){let t=tO.get(e);return r.length>0&&(t.cachedSuggestions=n,t.userResultCache.clear()),t.cachedSuggestions}return n}(e,n)),i}async function tB(e,t,n){let r=new URL(e.getAttribute("data-filterable-src")||"",window.location.origin);if("/"===r.pathname)throw Error("could not get data-filterable-src");if(n){let n=tO.get(e),i=t.trim();if(n.lastSearchText===i)return n.lastSearchResult;let a=void 0===n.lastSearchText;n.lastSearchText=i;let o=e.getAttribute("data-filterable-for")||"",s=document.getElementById(o);if(tP.abort(),""===i)n.lastSearchResult={suggestions:[],users:[]};else{tP=new AbortController;let e={headers:{Accept:"application/json",...(0,x.kt)()},signal:tP.signal},i=r.searchParams||new URLSearchParams;i.set("q",t),i.set("typeAhead","true"),r.search=i.toString(),a||s?.classList.add("is-loading");let o=await fetch(r.toString(),e);n.lastSearchResult=await o.json()}return s?.classList.remove("is-loading"),n.lastSearchResult}{let e={headers:{Accept:"application/json",...(0,x.kt)()}},t=await fetch(r.toString(),e);return await t.json()}}async function tN(e,t,n){tR.set(e,t),await (0,tH.A)();let r=e.hasAttribute("data-filterable-show-suggestion-header"),i=e.hasAttribute("data-filterable-type-ahead"),a=tI.get(e);if(!a)try{a=await t$(e,t,i),i||tI.set(e,a)}catch(e){if("AbortError"===e.name)return -1;throw e}if(!i&&tR.get(e)!==t)return -1;let o=n.limit,s=e.querySelector("template"),l={};for(let t of e.querySelectorAll("input[type=hidden]"))l[`${t.name}${t.value}`]=t;let c=s.nextElementSibling;for(;c;){let e=c;c=e.nextElementSibling,e instanceof HTMLElement&&(i||"true"===e.getAttribute("aria-checked")||e.classList.contains("select-menu-divider"))?e.hidden=!0:e.remove()}let u=0,d=""===t.trim(),m=document.createDocumentFragment(),f=e.querySelector(".js-divider-suggestions"),h=e.querySelector(".js-divider-rest"),p=tO.get(e);function g(e){let n=`${e.login} ${e.name}`.toLowerCase().trim().includes(t),r=!(null!=o&&u>=o)&&n;if(r||e.selected||e.suggestion){let t=function(e,t,n,r){if(null!=e.element)return e.element;if(r?.userResultCache.has(e.id))return r.userResultCache.get(e.id);let i=t.content.cloneNode(!0),a=i.querySelector("input[type=checkbox], input[type=radio]");e.type&&(a.name=`reviewer_${e.type}_ids[]`),a.value=e.id;let o=`${a.name}${e.id}`,s=e.selected;n[o]&&(s=!0,n[o].remove(),delete n[o]);let l=i.querySelector("[role^=menuitem]");s&&(l.setAttribute("aria-checked","true"),a.checked=!0),e.disabled&&l.setAttribute("aria-disabled","true"),e.ccr_quota_limited&&a.setAttribute("data-ccr-quota-limited","true");let c=i.querySelector(".js-username");c&&(c.textContent=e.login);let u=i.querySelector(".js-description");u&&(u.textContent=e.name,e.ccr_quota_limited&&u.classList.add("description-attention","ml-1"));let d=i.querySelector(".js-extended-description");d&&(e.description?d.textContent=e.description:d.remove()),"copilot-swe-agent"===e.login&&(c.textContent="Copilot",u.textContent="Your AI pair programmer");let m=i.querySelector(".js-avatar");return"Copilot"===e.login||"copilot-swe-agent"===e.login?(i.querySelector(".js-copilot-avatar").hidden=!1,m.hidden=!0):(m.className=`${m.className} ${e.class}`,m.src=e.avatar),e.element=l,r?.userResultCache.set(e.id,l),e.element}(e,s,l,p);t.hidden=!r,r&&u++,m.appendChild(t)}}let b=!1;if(f&&(a.suggestions?.length>0||r&&a.users.length>0)){let e=a.suggestions??[],t=e.filter(e=>e.selected),n=e.filter(e=>!e.selected);for(let e of t)g(e);m.appendChild(f);let o=u;for(let e of n)g(e);f.hidden=!(b=u>o)||i&&!d,r&&a.users.length>0&&(f.hidden=!d)}h&&m.appendChild(h);let v=u;for(let e of a.users)g(e);return h&&(h.hidden=v===u||!b),e.append(m),u}let tD=new AbortController,tW=new WeakMap,tF=new WeakMap,tU=new WeakMap;async function tz(e,t,n){await (0,tH.A)(),tU.set(e,t);let r=tF.get(e);if(!r)try{r=await tV(e,t)}catch(e){if("AbortError"===e.name)return -1;throw e}let i={};for(let t of e.querySelectorAll("label[aria-checked=true] > div > input[hidden]"))i[`${t.name}${t.value}`]=t;let a=e.querySelector("template"),o=a.nextElementSibling;for(;o;){let e=o;o=e.nextElementSibling,e instanceof HTMLElement&&("true"===e.getAttribute("aria-checked")||e.classList.contains("select-menu-divider"))?e.hidden=!0:e.remove()}let s=document.createDocumentFragment(),l=tW.get(e),c=n.limit,u=0;for(let e of r.labels){let n=`${e.name}`.toLowerCase().trim().includes(t.toLocaleLowerCase()),r=!(null!=c&&u>=c)&&n;if(r||e.selected){let t=function(e,t,n,r){if(null!=e.element)return e.element;let i=r?.labelResultCache.get(e.id);if(i)return i;let a=t.content.cloneNode(!0),o=a.querySelector("input[type=checkbox]");o.value=e.id,o.setAttribute("data-label-name",e.name);let s=`${o.name}${e.id}`,l=e.selected;n[s]&&(l=!0,n[s].remove(),delete n[s]);let c=a.querySelector("[role^=menuitem]");l&&(c.setAttribute("aria-checked","true"),o.checked=!0);let u=a.querySelector(".js-label-id");u&&u.setAttribute("data-name",e.id);let d=a.querySelector(".js-label-color");if(d){let t=d.getAttribute("style")?.replace("background-color:",`background-color:#${e.color};`);d.setAttribute("style",t)}let m=a.querySelector(".js-label-name-html");m&&(m.innerHTML=e.htmlName);let f=a.querySelector(".js-label-description");return f&&(e.description?f.textContent=e.description:f.remove()),e.element=c,r?.labelResultCache.set(e.id,c),e.element}(e,a,i,l);t.hidden=!r,r&&u++,s.appendChild(t)}}return e.append(s),u}async function tV(e,t){return(tW.has(e)||tW.set(e,{lastSearchResult:{labels:[]},cachedSuggestions:[],labelResultCache:new Map}),e.hasAttribute("data-filterable-data-pre-rendered"))?function(e){let t=[],n=e.querySelectorAll(".js-filterable-label");if(e.removeAttribute("data-filterable-data-pre-rendered"),n.length>0)for(let n of e.querySelectorAll(".js-filterable-label"))n.classList.remove("js-filterable-label"),t.push({id:n.querySelector("input[hidden]").getAttribute("value")||"",name:n.querySelector("input[hidden]").getAttribute("data-label-name")||"",htmlName:n.querySelector(".js-label-name-html").textContent,description:n.querySelector(".js-label-description")?.textContent||"",color:n.querySelector(".js-label-color").getAttribute("label-color")||"",selected:"true"===n.getAttribute("aria-checked"),element:n});let r=tW.get(e);return t.length>0&&(r.cachedSuggestions=t,r.lastSearchText="",r.lastSearchResult={labels:t}),r.lastSearchResult}(e):await tG(e,t)}async function tG(e,t){let n=new URL(e.getAttribute("data-filterable-src")||"",window.location.origin);if("/"===n.pathname)throw Error("could not get data-filterable-src");let r=tW.get(e),i=t.trim();if(r.lastSearchText===i)return r.lastSearchResult;r.lastSearchText=i;let a=e.getAttribute("data-filterable-for")||"",o=document.getElementById(a);tD.abort(),tD=new AbortController;let s={headers:{Accept:"application/json",...(0,x.kt)()},signal:tD.signal},l=n.searchParams||new URLSearchParams;l.set("q",t),l.set("typeAhead","true"),n.search=l.toString(),o?.classList.add("is-loading");let c=await fetch(n.toString(),s);return r.lastSearchResult=await c.json(),o?.classList.remove("is-loading"),r.lastSearchResult}async function tX(e,t){let n=parseInt(e.getAttribute("data-filterable-limit"),10)||null,r=0;switch(e.getAttribute("data-filterable-type")){case"fuzzy-prio":{let i=t.toLowerCase();r=t_(e,t,{limit:n,sortKey:e=>{let t=e.getAttribute("data-prio-filter-value").toLowerCase().trim(),n=e.textContent.toLowerCase().trim(),r=2*(0,tq.dt)(t,i,.01),a=(0,tq.dt)(n,i,.01);return r>a&&(a=r),a>0?{score:a,text:n}:null}});break}case"fuzzy":{let i=t.toLowerCase();r=t_(e,t,{limit:n,sortKey:e=>{let t=e.hasAttribute("data-filter-value")?e.getAttribute("data-filter-value").toLowerCase().trim():e.textContent.toLowerCase().trim(),n=(0,tq.dt)(t,i);return n>0?{score:n,text:t}:null}});break}case"substring":r=tC(e,t.toLowerCase(),tJ,{limit:n});break;case"substring-memory":r=await tN(e,t,{limit:n});break;case"labels-typeahead":r=await tz(e,t,{limit:n});break;default:r=tC(e,t.toLowerCase(),tK,{limit:n})}return e.classList.toggle("filterable-active",t.length>0),e.classList.toggle("filterable-empty",0===r),r}function tK(e,t){return e.textContent.toLowerCase().trim().startsWith(t)}function tJ(e,t){return e.hasAttribute("data-skip-substring-filter")||e.classList.contains("select-menu-no-results")?null:(e.querySelector("[data-filterable-item-text]")||e).textContent.toLowerCase().trim().includes(t)}(0,b.lB)(".js-filterable-field",{constructor:HTMLInputElement,initialize(e){e.autocomplete||(e.autocomplete="off");let t=e.hasAttribute("type-ahead")?200:null,n=e.value;async function r(e){n!==e.value&&(n=e.value,await (0,_.k2)(),(0,p.h)(e,"filterable:change"))}async function i(){n=e.value,await (0,_.k2)(),(0,p.h)(e,"filterable:change")}return{add(e){e.addEventListener("focus",i),(0,$.Up)(e,r,{wait:t}),document.activeElement===e&&i()},remove(e){e.removeEventListener("focus",i),(0,$.NB)(e,r)}}}}),(0,p.on)("filterable:change",".js-filterable-field",async function(e){let t=e.currentTarget,n=t.value.trim().toLowerCase();for(let e of document.querySelectorAll(`[data-filterable-for="${t.id}"]`)){let r=await tX(e,n);if(-1===r)return;document.activeElement&&t===document.activeElement&&(0,w.i)(`${r} results found.`),e.dispatchEvent(new CustomEvent("filterable:change",{bubbles:!0,cancelable:!1,detail:{inputField:t}}))}}),(0,p.on)("filterable:change","details-menu .select-menu-list",function(e){let t=e.currentTarget,n=t.querySelector(".js-new-item-form");n&&function(e,t,n){let r=n.length>0&&!function(e,t){for(let n of e.querySelectorAll("[data-menu-button-text]"))if(n.textContent.toLowerCase().trim()===t.toLowerCase())return!0;return!1}(e,n);if(e.classList.toggle("is-showing-new-item-form",r),!r)return;t.querySelector(".js-new-item-name").textContent=n;let i=t.querySelector(".js-new-item-value");(i instanceof HTMLInputElement||i instanceof HTMLButtonElement)&&(i.value=n)}(t,n,e.detail.inputField.value)}),(0,b.lB)("tab-container .select-menu-list .filterable-empty, details-menu .select-menu-list .filterable-empty",{add(e){e.closest(".select-menu-list").classList.add("filterable-empty")},remove(e){e.closest(".select-menu-list").classList.remove("filterable-empty")}});var tY=n(63159);function tZ(){!document.firstElementChild.classList.contains("js-skip-scroll-target-into-view")&&(0,tY.Oc)(document)&&(0,tY.GO)(document)}(0,e0.A)(tZ),(0,p.on)("click",'a[href^="#"]',function(e){let{currentTarget:t}=e;t instanceof HTMLAnchorElement&&setTimeout(tZ,0)});let tQ=e=>{let t=Array.from(Array.from(document.querySelectorAll('h1:not([hidden]),h2:not([hidden]),h3:not([hidden]),button:not([disabled]):not([hidden]),a:not([hidden]),input:not([disabled]):not([hidden]), select:not([disabled]):not([hidden]), textarea:not([disabled]):not([hidden]), [tabindex]:not([tabindex="-1"]):not([disabled]):not([hidden])'))).filter(e=>{if(!e.getAttribute("aria-hidden")&&!(e.offsetWidth<=0&&e.offsetHeight<=0)&&e?.offsetParent?.style.visibility!=="hidden")return!0}),n=t.indexOf(e);if(-1!==n&&n>0){let e=t[n-1];e&&("H1"===e.tagName||"H2"===e.tagName||"H3"===e.tagName)&&e.setAttribute("tabindex","-1"),e?.focus()}};(0,p.on)("click",".js-flash-close",function(e){let t=e.currentTarget.closest(".flash-messages");tQ(e.target),e.currentTarget.closest(".flash").remove(),t&&!t.querySelector(".flash")&&t.remove()}),!async function(){await J.K;let e=document.querySelector('.js-flash-alert[role="alert"]');e&&setTimeout(()=>{let t=document.createTextNode("\xa0"),n=document.createElement("span");n.classList.add("sr-only"),n.appendChild(t),e.appendChild(n)},200)}();var t0=n(78134);let t1=["flash-notice","flash-error","flash-message","flash-warn","flash-success"];(0,b.lB)("template.js-flash-template",{constructor:HTMLTemplateElement,add(e){for(let{key:t,value:n}of t1.flatMap(en.OR)){let r;(0,en.Yj)(t);try{r=atob(decodeURIComponent(n))}catch{continue}e.after(new t0.i4(e,{className:t,message:r}))}}});let t2=new WeakMap;document.addEventListener("focus",function(e){let t=e.target;t instanceof Element&&!t2.get(t)&&((0,p.h)(t,"focusin:delay"),t2.set(t,!0))},{capture:!0}),document.addEventListener("blur",function(e){setTimeout(function(){let t=e.target;t instanceof Element&&t!==document.activeElement&&((0,p.h)(t,"focusout:delay"),t2.delete(t))},200)},{capture:!0}),(0,g.JW)(".js-form-toggle-target",async function(e,t){try{await t.text()}catch{return}let n=e.closest(".js-form-toggle-container").querySelector(".js-form-toggle-target[hidden]");n.hidden=!1,e.hidden=!0;let r=e.getAttribute("data-sr-feedback")||"";r&&(0,w.i)(r),n?.querySelector("input[type=submit], button[type=submit]")?.focus()});var t5=n(62643);function t3(e){if(!(e instanceof CustomEvent))return;let t={};e.target instanceof t5.A&&(t.element=e.target.ariaLiveElement),(0,w.i)(`${e.detail} results found.`,t)}function t7(e){for(let t of document.querySelectorAll(".js-hook-event-checkbox"))t.checked=t.matches(e)}(0,b.lB)("fuzzy-list",{constructor:t5.A,subscribe:e=>(0,O.Rt)(e,"fuzzy-list-sorted",t3)}),(0,p.on)("click",".email-hidden-toggle",function(e){let t=e.currentTarget.nextElementSibling;t instanceof HTMLElement&&(t.style.display="",t.classList.toggle("expanded"),e.preventDefault())}),n(52232),(0,b.lB)(".js-hook-url-field",{constructor:HTMLInputElement,add(e){function t(){let t,n=e.form;if(!n)return;try{t=new URL(e.value)}catch{}let r=n.querySelector(".js-ssl-hook-fields");r instanceof HTMLElement&&(r.hidden=!(t&&"https:"===t.protocol))}(0,$.Up)(e,t),t()}}),(0,p.on)("change",".js-hook-event-choice",function(e){let t=e.currentTarget,n=t.checked&&"custom"===t.value,r=t.closest(".js-hook-events-field");r&&r.classList.toggle("is-custom",n);let i=document.getElementsByClassName("js-hook-event-selector")[0];i&&n?i.hidden=!1:i&&!n&&!1===i.hidden&&(i.hidden=!0),t.checked&&(n?document.querySelector(".js-hook-wildcard-event").checked=!1:"push"===t.value?t7('[value="push"]'):"all"===t.value&&t7(".js-hook-wildcard-event"))}),(0,p.on)("click",".js-hook-deliveries-pagination-button",async function(e){let t=e.currentTarget;t.disabled=!0;let n=t.parentElement,r=t.getAttribute("data-url");n.before(await (0,to.Ts)(document,r)),n.remove()}),(0,g.JW)(".js-redeliver-hook-form",async function(e,t){let n;try{n=await t.html()}catch{e.classList.add("failed");return}document.querySelector(".js-hook-deliveries-container").replaceWith(n.html)}),!function(){let e=document.getElementById("insecure_ssl_verification"),t=document.getElementById("insecure_ssl_verification_submit"),n=document.getElementById("insecure_ssl_0"),r=document.getElementById("insecure_ssl_1");e&&t&&n&&r&&(r.addEventListener("change",t=>{t.stopPropagation(),n.checked=!0,e instanceof HTMLDialogElement?e.showModal():e.show()}),t.addEventListener("click",()=>{r.checked=!0}),e.addEventListener("dialog:remove",()=>{r.checked=!0}))}();var t4=n(65461);(0,b.lB)("[data-hotkey]",{constructor:HTMLElement,add(e){if((0,eZ.zw)())(0,t4.ai)(e);else{let n=e.getAttribute("data-hotkey");if(n){var t;let r=(t=n,(0,t4.SK)(t).filter(e=>(0,eZ.GI)(e)).join(","));r.length>0?(e.setAttribute("data-hotkey",r),(0,t4.ai)(e)):(e.removeAttribute("data-hotkey"),(0,t4.JC)(e))}}},remove(e){(0,t4.JC)(e)}});var t9=n(69676),t6=n(67105);let t8=()=>{if(void 0!==d)return d;let e=document.querySelector("meta[name=hovercards-preference]");return!e||(d="true"===e.content)},ne=document.querySelector(".js-hovercard-content");(0,b.lB)(".js-hovercard-content",{add:e=>{ne=e},remove:()=>{ne=document.querySelector(".js-hovercard-content")}});let nt=(0,eD.A)(to.Ts),nn=null,nr=0;function ni(e){return"Popover-message--"+e}function na(){if(!(ne instanceof HTMLElement))return;window.removeEventListener("keydown",ng),ne.style.display="none",ne.removeAttribute("data-hovercard-target-url");let e=ne.querySelector(".Popover-message");e instanceof HTMLElement&&(e.textContent=""),i=null,f&&f.abort()}async function no(e,t){let n;if("ontouchstart"in document)return;let r=e instanceof MouseEvent,a=e.currentTarget;if(e instanceof MouseEvent&&(nr=e.clientX),!(a instanceof Element)||i===a||a.closest(".js-hovercard-content")||!function(e){let t=e.getAttribute("data-hovercard-type");return"pull_request"===t||"issue"===t?!!e.closest("[data-issue-and-pr-hovercards-enabled]"):"team"===t?!!e.closest("[data-team-hovercards-enabled]"):"repository"===t?!!e.closest("[data-repository-hovercards-enabled]"):"commit"===t?!!e.closest("[data-commit-hovercards-enabled]"):"project"===t?!!e.closest("[data-project-hovercards-enabled]"):"discussion"===t?!!e.closest("[data-discussion-hovercards-enabled]"):"acv_badge"===t?!!e.closest("[data-acv-badge-hovercards-enabled]"):"sponsors_listing"!==t||!!e.closest("[data-sponsors-listing-hovercards-enabled]")}(a))return;na(),i=a;let o=function(e){let t=e.getAttribute("data-hovercard-url");if(t){let n=function(e){let t=e.closest("[data-hovercard-subject-tag]");if(t)return t.getAttribute("data-hovercard-subject-tag");let n=document.head&&document.head.querySelector('meta[name="hovercard-subject-tag"]');return n?n.getAttribute("content"):null}(e);if(n){let e=new URL(t,window.location.origin),r=new URLSearchParams(e.search.slice(1));return r.append("subject",n),r.append("current_path",window.location.pathname+window.location.search),e.search=r.toString(),e.toString()}return t}return""}(a);try{let e=new Promise(e=>window.setTimeout(e,t,0));await e,a===i&&(n=await nt(document,o))}catch(t){let e=t.response;if(e&&404===e.status)a.setAttribute("aria-label","Hovercard is unavailable"),a.classList.add("tooltipped","tooltipped-ne");else if(e&&410===e.status){let t=await e.clone().json();a.setAttribute("aria-label",t.message),a.classList.add("tooltipped","tooltipped-ne")}return}a===i&&n&&(f=function(e,t,n){if(!(ne instanceof HTMLElement))return;let r=ne.querySelector(".Popover-message");if(!(r instanceof HTMLElement))return;window.addEventListener("keydown",ng),r.textContent="";let i=document.createElement("div");for(let t of e.children)i.appendChild(t.cloneNode(!0));if(r.appendChild(i),!function(e,t){if(!(ne instanceof HTMLElement))return;ne.style.visibility="hidden",ne.style.display="block",t.classList.remove(ni("bottom-left"),ni("bottom-right"),ni("right-top"),ni("right-bottom"),ni("top-left"),ni("top-right"));let{containerTop:n,containerLeft:r,contentClassSuffix:i}=function(e){let{width:t,height:n}=ne.getBoundingClientRect(),{left:r,top:i,height:a,width:o}=function(e){let t=e.getClientRects(),n=t[0]||e.getBoundingClientRect()||{top:0,left:0,height:0,width:0};if(t.length>0){for(let e of t)if(e.left<nr&&e.right>nr){n=e;break}}return n}(e),s=window.innerHeight-i,l=i>n,c=s>n,u=i>=s;if(e.classList.contains("js-hovercard-left")){let e=i+a/2;return{containerTop:l||c?l?e-n+17+8:e-17-8:u?e-n+17+8:e-17-8,containerLeft:r-t-12,contentClassSuffix:l?"right-bottom":"right-top"}}{let e=window.innerWidth-r>t,s=r+o/2;return{containerTop:l||c||e?l?i-n-12:i+a+12:u?i-n-12:i+a+12,containerLeft:e?s-24:s-t+24,contentClassSuffix:l?e?"bottom-left":"bottom-right":e?"top-left":"top-right"}}}(e);t.classList.add(ni(i));let a=function(e,t){let n="data-hovercard-fixed-positioning";return e.getAttribute(n)||t.getAttribute(n)}(e,ne),o=a?0:window.pageYOffset,s=a?0:window.pageXOffset;ne.style.setProperty("top",`${n+o}px`,"important"),ne.style.setProperty("bottom","auto","important"),ne.style.left=`${r+s}px`,function(e,t){let n=e.getAttribute("data-hovercard-z-index-override");n?t.style.zIndex=n:t.style.zIndex="100"}(e,ne),ne.style.visibility=""}(t,r),setTimeout(()=>{if(document.body&&document.body.contains(i)){let e=i.querySelector("[data-hydro-view]");e instanceof HTMLElement&&(0,t6.$3)(e)}},500),ne.style.display="block",ne.setAttribute("data-hovercard-target-url",t.getAttribute("data-hovercard-url")||""),ne.setAttribute("aria-label",function(e){let t,n=e.getAttribute("data-hovercard-type");if(!n)return"Hovercard";switch(n){case"copilot":t="Copilot";break;case"user":t="User";break;case"organization":t="Organization";break;case"pull_request":t="Pull Request";break;case"issue":t="Issue";break;case"team":t="Team";break;case"repository":t="Repository";break;case"commit":t="Commit";break;case"project":t="Project";break;case"discussion":t="Discussion";break;case"sponsors_listing":t="Sponsors";break;case"acv_badge":return"Arctic Code Vault Badge"}return t?`${t} Hovercard`:"Hovercard"}(t)),ne.setAttribute("role","region"),!n)return(0,t9.iE)(ne)}(n,a,r))}function ns(e){if(i){if(e instanceof MouseEvent&&e.relatedTarget instanceof HTMLElement){let t=e.relatedTarget;if(t.closest(".js-hovercard-content")||t.closest("[data-hovercard-url]"))return}na()}}function nl(e){e instanceof KeyboardEvent&&e.altKey&&"ArrowUp"===e.key&&e.preventDefault()}function nc(e){if(e instanceof KeyboardEvent)switch(e.key){case"ArrowUp":e.altKey&&(nn=document.activeElement,no(e,0));break;case"Escape":ns(e)}}function nu(){a&&clearTimeout(a)}function nd(e){e instanceof KeyboardEvent&&"Escape"===e.key&&(ns(e),nn?.focus())}function nm(e){let t=i;a=window.setTimeout(()=>{i===t&&ns(e)},100)}function nf(e){no(e,500)}function nh(){nu()}function np(){nu()}function ng(e){e instanceof KeyboardEvent&&"Escape"===e.key&&na()}ne&&t8()&&((0,b.lB)("[data-hovercard-url]",{subscribe:e=>(0,O.Zz)((0,O.Rt)(e,"mouseover",nf),(0,O.Rt)(e,"mouseleave",nm),(0,O.Rt)(e,"keyup",nc),(0,O.Rt)(e,"keydown",nl))}),(()=>{if(void 0!==m)return m;if(!t8())return!1;let e=document.querySelector("meta[name=announcement-preference-hovercard]");return!e||(m="true"===e.content)})()&&(0,b.lB)("[data-hovercard-url]",{add(e){e.setAttribute("aria-keyshortcuts","Alt+ArrowUp")}}),(0,b.lB)("[data-hovercard-url]",{remove(e){i===e&&na()}}),(0,b.lB)(".js-hovercard-content",{subscribe:e=>(0,O.Zz)((0,O.Rt)(e,"mouseover",nh),(0,O.Rt)(e,"focusin",np),(0,O.Rt)(e,"mouseleave",nm),(0,O.Rt)(e,"keydown",nd))}),(0,p.on)("menu:activated","details",na),window.addEventListener("turbo:load",na),window.addEventListener("statechange",na));var nb=n(78284);function nv(e={}){let t=(0,nb.fX)();return t?{...e,react_app:t}:e}!async function(){document.addEventListener(tt.z.FRAME_UPDATE,()=>(0,z.lA)(nv({turbo:"true"}))),document.addEventListener(tt.z.SUCCESS,()=>{"turbo.frame"!==(0,nb.di)()&&(0,z.lA)(nv({turbo:"true"}))}),await J.K,(0,z.lA)(nv())}(),(0,p.on)("click","[data-octo-click]",function(e){let t=e.currentTarget;if(!(t instanceof HTMLElement))return;let n=t.getAttribute("data-octo-click")||"",r={};if(t.hasAttribute("data-ga-click")){let e=t.getAttribute("data-ga-click").split(",");r.category=e[0].trim(),r.action=e[1].trim()}if(t.hasAttribute("data-octo-dimensions"))for(let e of t.getAttribute("data-octo-dimensions").split(",")){let[t,n]=e.split(/:(.+)/);t&&(r[t]=n||"")}(0,z.BI)(n,r)});let{getItem:ny}=(0,n(85351).A)("localStorage");(0,p.on)("click","[data-hydro-click]",function(e){let t=e.currentTarget,n=t.getAttribute("data-hydro-click")||"",r=t.getAttribute("data-hydro-click-hmac")||"",i=t.getAttribute("data-hydro-client-context")||"";(0,t6.Vb)(n,r,i)}),(0,g.JW)(".js-immediate-updates",async function(e,t){let n;try{n=(await t.json()).json.updateContent}catch(e){e.response.json&&(n=e.response.json.updateContent)}if(n)for(let e in n){let t=n[e],r=document.querySelector(e);r instanceof HTMLElement&&(0,ec.Uv)(r,t)}}),(0,b.lB)("[data-indeterminate]",{constructor:HTMLInputElement,initialize(e){e.indeterminate=!0}});let nw=!1;async function nE(){if(nw)return;nw=!0;let e={contexts:document.querySelector("meta[name=github-keyboard-shortcuts]").content},t=`/site/keyboard_shortcuts?${new URLSearchParams(e).toString()}`,n=await (0,ta.r)({content:(0,to.Ts)(document,t),labelledBy:"keyboard-shortcuts-heading"});n.style.width="800px",n.addEventListener("dialog:remove",function(){nw=!1},{once:!0})}function nS(e){let t=e.currentTarget;if(!(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement))return;let n=parseInt(t.getAttribute("data-input-max-length")||"",10),r=parseInt(t.getAttribute("data-warning-length")||"",10)||5,i=t.value.replace(/(\r\n|\n|\r)/g,`\r
`),a=n-i.length;if(a<=0){let e=i.substr(0,n);e.endsWith("\r")?(e=e.substr(0,n-1),a=1):a=0,t.value=e}let o=t.getAttribute("data-warning-text"),s=t.closest(".js-length-limited-input-container").querySelector(".js-length-limited-input-warning");a<=r?(s.textContent=o.replace(/{{remaining}}/g,`${a}`),s.classList.remove("d-none")):(s.textContent="",s.classList.add("d-none"))}(0,p.on)("click",".js-keyboard-shortcuts",nE),(0,b.lB)(".js-modifier-key",{constructor:HTMLElement,add(e){if(/Macintosh/.test(navigator.userAgent)){let t=e.textContent;t&&(e.textContent=t=(t=t.replace(/ctrl/,"\u2318")).replace(/alt/,"\u2325"))}}}),(0,b.lB)(".js-length-limited-input",{add(e){e.addEventListener("input",nS),e.addEventListener("change",nS)},remove(e){e.removeEventListener("input",nS),e.removeEventListener("change",nS)}}),(0,p.on)("click",".js-member-search-filter",function(e){e.preventDefault();let t=e.currentTarget.getAttribute("data-filter"),n=e.currentTarget.closest("[data-filter-on]").getAttribute("data-filter-on"),r=document.querySelector(".js-member-filter-field"),i=r.value,a=RegExp(`${n}:(?:[a-z]|_|((').*(')))+`),o=i.toString().trim().replace(a,"");r.value=`${o} ${t}`.replace(/\s\s/," ").trim(),r.focus(),(0,p.h)(r,"input")}),(0,g.JW)(".js-notice-dismiss",async function(e,t){await t.text(),e.closest(".js-notice").remove()}),(0,p.on)("submit",".js-notice-dismiss-remote",async function(e){let t,n=e.currentTarget;e.preventDefault();try{t=await fetch(n.action,{method:n.method,body:new FormData(n),headers:{Accept:"application/json",...(0,x.kt)()}})}catch{(0,h.n)();return}t&&!t.ok?(0,h.n)():n.closest(".js-notice").remove()}),(0,p.on)("click",".js-github-dev-shortcut",function(e){for(let t of(e.preventDefault(),document.querySelectorAll("textarea.js-comment-field")))if(t.value&&function(e){try{let t=e.getBoundingClientRect();if(0===t.height&&0===t.width||"0"===e.style.opacity||"hidden"===e.style.visibility)return!1}catch{}return!0}(t)&&!confirm("Are you sure you want to open github.dev?"))return;let t=e.currentTarget;"A"!==t.tagName&&(t=t.querySelector("a")),t.pathname=window.location.pathname,t.hash=window.location.hash,window.location.href=t.href}),(0,p.on)("click",".js-github-dev-new-tab-shortcut",function(e){let t=e.currentTarget;t.pathname=window.location.pathname,t.hash=window.location.hash}),(0,p.on)("click",".js-permalink-shortcut",function(e){let t=e.currentTarget;try{(0,q.bj)(t.href+window.location.hash)}catch{window.location.href=t.href+window.location.hash}for(let e of document.querySelectorAll(".js-permalink-replaceable-link"))e instanceof HTMLAnchorElement&&(e.href=e.getAttribute("data-permalink-href"));e.preventDefault()}),(0,g.JW)(".js-permission-menu-form",async function(e,t){let n,r=e.querySelector(".js-permission-success"),i=e.querySelector(".js-permission-error");r.hidden=!0,i.hidden=!0,e.classList.add("is-loading");try{n=await t.json()}catch{e.classList.remove("is-loading"),i.hidden=!1;return}if(200===n.status&&e.querySelector("select-panel, select-panel-experimental")){let t=e.querySelector("span.Button-label");t&&(t.textContent=`Role: ${n.json.action}`)}e.classList.remove("is-loading"),r.hidden=!1;let a=e.closest(".js-org-repo");if(a){let e=n.json;a.classList.toggle("with-higher-access",e.members_with_higher_access)}});let nA=null,nj="last_turbo_request",nL="turbo_start",nT="turbo_end";async function nC(){if(await (0,_.k2)(),!window.performance.getEntriesByName(nL).length)return;window.performance.mark(nT),window.performance.measure(nj,nL,nT);let e=window.performance.getEntriesByName(nj).pop(),t=e?e.duration:null;t&&(nA&&(0,v.i)({requestUrl:nA,turboDuration:Math.round(t)}),window.performance.clearMarks(nL),window.performance.clearMarks(nT),window.performance.clearMeasures(nj))}"getEntriesByName"in window.performance&&(document.addEventListener("turbo:before-fetch-request",function(e){e.defaultPrevented&&(window.performance.mark(nL),nA=e.detail.url.toString())}),document.addEventListener("turbo:render",nC)),(0,b.lB)("body.js-print-popup",()=>{window.print(),setTimeout(window.close,1e3)}),(0,b.lB)("poll-include-fragment[data-redirect-url]",function(e){let t=e.getAttribute("data-redirect-url");e.addEventListener("load",function(){window.location.href=t})}),(0,b.lB)("poll-include-fragment[data-reload]",function(e){e.addEventListener("load",function(){window.location.reload()})}),(0,H.Ff)("keydown",".js-quick-submit",function(e){var t=e;let n=t.target;if((t.ctrlKey||t.metaKey)&&"Enter"===t.key){let e=n.form,r=e.querySelector("input[type=submit], button[type=submit]");if(t.shiftKey){let t=e.querySelector(".js-quick-submit-alternative");(t instanceof HTMLInputElement||t instanceof HTMLButtonElement)&&!t.disabled&&(0,C.k_)(e,t)}else(r instanceof HTMLInputElement||r instanceof HTMLButtonElement)&&r.disabled||(0,C.k_)(e);t.preventDefault()}});var nk=n(66661);function nq(e){return"DIV"===e.nodeName&&e.classList.contains("highlight")}(0,b.lB)(".js-comment-quote-reply",function(e){e.hidden=e.closest(".js-quote-selection-container")?.querySelector(".js-inline-comment-form-container textarea, .js-new-comment-form textarea, .js-discussions-previewable-comment-form")==null});let nx={PRE(e){let t=e.parentElement;if(t&&nq(t)){let n=t.className.match(/highlight-source-(\S+)/),r=n?n[1]:"",i=(e.textContent||"").replace(/\n+$/,"");e.textContent=`\`\`\`${r}
${i}
\`\`\``,e.append(`

`)}return e},A(e){let t=e.textContent||"";return e.classList.contains("user-mention")||e.classList.contains("team-mention")||e.classList.contains("issue-link")&&/^#\d+$/.test(t)?t:e},IMG(e){let t=e.getAttribute("alt");return t&&e.classList.contains("emoji")?t:e},DIV(e){if(e.classList.contains("js-suggested-changes-blob"))e.remove();else if(e.classList.contains("blob-wrapper-embedded")){let t=e.parentElement,n=t.querySelector("a[href]"),r=document.createElement("p");r.textContent=n.href,t.replaceWith(r)}else if(e.classList.contains("js-render-enrichment-target")){let t=e.closest(".js-render-needs-enrichment").getAttribute("data-type"),n=e.getAttribute("data-plain"),r=document.createElement("pre");return r.textContent=`\`\`\`${t}
${n}\`\`\``,r}return e}},nM=!1;(0,b.lB)(".js-comment-quote-reply",e=>{e.addEventListener("hotkey-fire",e=>{nM=e.detail?.path?.join()==="r"})}),(0,p.on)("click",".js-comment-quote-reply",function({isTrusted:e,currentTarget:t}){let n=t,r=new nk.P,i=!e&&nM;if(nM=!1,i){if(r.range.collapsed||null===r.range.startContainer.parentElement)return;n=r.range.startContainer.parentElement}let a=n.closest(".js-comment"),s=a.querySelector(".js-comment-body"),l=a.querySelector(".js-comment-body").cloneNode(!0),c=a.closest(".js-quote-selection-container");for(let e of s.querySelectorAll("button.js-convert-to-issue-button, span.js-clear"))e.remove();if(c.hasAttribute("data-quote-markdown")&&(r=new nk.g(c.getAttribute("data-quote-markdown")||"",e=>{let t=r.range.startContainer.parentElement,n=t&&t.closest("pre");if(n instanceof HTMLElement){let t=n.parentElement;if(t&&nq(t)){let n=document.createElement("div");n.className=t.className,n.appendChild(e),e.appendChild(n)}}let i=document.createNodeIterator(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.nodeName in nx&&("IMG"===e.nodeName||null!=e.firstChild)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),a=[],o=i.nextNode();for(;o;)o instanceof HTMLElement&&a.push(o),o=i.nextNode();for(let e of(a.reverse(),a))e.replaceWith(nx[e.nodeName](e))})),o&&s.contains(o.anchorNode)&&!o.range.collapsed&&""!==o.range.toString().trim()?r.range=o.range:(r.range.collapsed||""===r.range.toString().trim())&&r.select(s),r.closest(".js-quote-selection-container")!==c)return;let u=r.range;for(let e of(c.dispatchEvent(new CustomEvent("quote-selection",{bubbles:!0,detail:r})),r.range=u,Array.from(c.querySelectorAll("textarea")).reverse()))if((0,B.A)(e)&&!e.closest("tracking-block")){r.insert(e);break}a.querySelector(".js-comment-body").replaceWith(l)}),document.addEventListener("selectionchange",(0,P.s)(function(){let e,t=window.getSelection();try{e=t.getRangeAt(0)}catch{s=null;return}s={anchorNode:t.anchorNode,range:e}},100)),document.addEventListener("toggle",()=>{o=s},{capture:!0});let n_=new ResizeObserver(e=>{for(let t of e)t.contentRect.height>40&&function(e){let t=.7*e.offsetWidth,n=e.querySelectorAll(".js-reaction-group-button"),r=e.querySelector(".js-all-reactions-popover"),i=0;for(let e of n)i+=e.clientWidth;if(t<(i+=r?.clientWidth||0)){let e=t;for(let t of(r&&(r.removeAttribute("hidden"),e-=r.offsetWidth),n)){let n=t.offsetWidth;n>e?t.setAttribute("hidden","hidden"):t.removeAttribute("hidden"),e-=n}}}(t.target)});(0,b.lB)(".js-reactions-container",function(e){n_.observe(e)});let nH=(0,P.s)(async e=>{let t,n=e.target;try{t=await fetch(n.action,{method:n.method,headers:new Headers({...(0,x.kt)()}),body:new FormData(n)})}catch{(0,h.n)()}if(t&&!t.ok&&(0,h.n)(),t&&200===t.status){let e=await t.json(),r=n.closest(".js-comment"),i=r?.querySelector(".js-reactions-container"),a=r?.querySelector(".js-comment-header-reaction-button");if(e&&i&&a){let t=(0,k.B)(document,e.reactions_container.trim()),n=(0,k.B)(document,e.comment_header_reaction_button.trim());i.replaceWith(t),a.replaceWith(n)}let o=r?.querySelector(".js-reactions-focus");o&&o.focus()}},200);function nP(e){let t=e.target,n=t.getAttribute("data-reaction-label"),r=t.closest(".js-add-reaction-popover").querySelector(".js-reaction-description");r.hasAttribute("data-default-text")||r.setAttribute("data-default-text",r.textContent||""),r.textContent=n}function nO(e){let t=e.target.closest(".js-add-reaction-popover").querySelector(".js-reaction-description"),n=t.getAttribute("data-default-text");n&&(t.textContent=n)}(0,p.on)("submit",".js-pick-reaction",e=>{e.preventDefault(),nH(e)}),(0,p.on)("toggle",".js-reaction-popover-container",function(e){let t=e.currentTarget.hasAttribute("open");for(let n of e.target.querySelectorAll(".js-reaction-option-item"))t?(n.addEventListener("mouseenter",nP),n.addEventListener("mouseleave",nO)):(n.removeEventListener("mouseenter",nP),n.removeEventListener("mouseleave",nO))},{capture:!0});var nI=n(94982);(0,p.on)("click","form button:not([type]), form button[type=submit], form input[type=submit]",function(e){let t=e.currentTarget;t.form&&!e.defaultPrevented&&(0,nI.A)(t)}),(0,g.JW)("form[data-remote]",function(e,t,n){"json"===e.getAttribute("data-type")&&n.headers.set("Accept","application/json"),(0,p.h)(e,"deprecatedAjaxSend",{request:n}),t.text().catch(e=>{if(e.response)return e.response;throw e}).then(t=>{t.status<300?(0,p.h)(e,"deprecatedAjaxSuccess"):(0,p.h)(e,"deprecatedAjaxError",{error:t.statusText,status:t.status,text:t.text})},t=>{(0,p.h)(e,"deprecatedAjaxError",{error:t.message,status:0,text:null})}).then(()=>{(0,p.h)(e,"deprecatedAjaxComplete")})}),(0,p.on)("deprecatedAjaxComplete","form",function({currentTarget:e}){let t=(0,nI.C)(e);t&&t.remove()}),(0,g.ZV)(e=>{let t=(0,nI.C)(e);t&&t.remove()}),(0,g.Ax)(tH.A),(0,p.on)("click",".js-remote-submit-button",async function(e){let t,n=e.currentTarget.form;e.preventDefault();try{t=await fetch(n.action,{method:n.method,body:new FormData(n),headers:{Accept:"application/json",...(0,x.kt)()}})}catch{}t&&!t.ok&&(0,h.n)()}),(0,b.lB)(".has-removed-contents",function(){let e;return{add(t){for(let n of e=Array.from(t.childNodes))t.removeChild(n);let n=t.closest("form");n&&(0,p.h)(n,"change")},remove(t){for(let n of e)t.appendChild(n);let n=t.closest("form");n&&(0,p.h)(n,"change")}}}),n(20087),n(47233),(0,g.JW)("form[data-replace-remote-form]",async function(e,t){e.classList.remove("is-error"),e.classList.add("is-loading");try{let n=e,r=await t.html(),i=e.closest("[data-replace-remote-form-target]");if(i){let e=i.getAttribute("data-replace-remote-form-target");n=e?document.getElementById(e):i}n.replaceWith(r.html)}catch{e.classList.remove("is-loading"),e.classList.add("is-error")}}),n(889);var nR=n(80147);(0,p.on)("click",".js-saved-reply-menu.ActionListWrap",function(e){if(!(e.target instanceof Element))return;let t=e.target.closest('button[role="menuitem"]')?.querySelector(".js-saved-reply-body");if(!t)return;let n=(t.textContent||"").trim(),r=e.target.closest(".js-previewable-comment-form").querySelector("textarea.js-comment-field");(0,eh.bc)(r,n),e.target.closest("dialog, modal-dialog")?.close(),setTimeout(()=>r.focus(),0)},{capture:!0}),(0,p.on)("details-menu-select",".js-saved-reply-menu",function(e){if(!(e.target instanceof Element))return;let t=e.detail.relatedTarget.querySelector(".js-saved-reply-body");if(!t)return;let n=(t.textContent||"").trim(),r=e.target.closest(".js-previewable-comment-form").querySelector("textarea.js-comment-field");(0,eh.bc)(r,n),setTimeout(()=>r.focus(),0)},{capture:!0}),(0,H.Ff)("keydown",".js-saved-reply-shortcut-comment-field",function(e){if("Control+."===(0,t4.Vy)(e)){let t=e.target.closest(".js-previewable-comment-form").querySelector(".js-saved-reply-container");t instanceof HTMLDialogElement?t.showModal():t instanceof nR.u?t.show():t.setAttribute("open",""),e.preventDefault()}}),(0,H.Ff)("keydown",".js-saved-reply-filter-input",function(e){if(/^Control\+[1-9]$/.test((0,t4.Vy)(e))){let t=e.target.closest(".js-saved-reply-container"),n=Number(e.key),r=t.querySelectorAll(`[role="menuitem"][data-shortcut="${n}"]`)[0];r instanceof HTMLElement&&(r.click(),e.preventDefault())}else if("Enter"===e.key){let t=e.target.closest(".js-saved-reply-container").querySelectorAll('[role="menuitem"]');t.length>0&&t[0]instanceof HTMLButtonElement&&t[0].click(),e.preventDefault()}}),(async()=>{for(let e of(await J.G,document.querySelectorAll(".js-saved-reply-container")))new MutationObserver(t=>{for(let n of t)if("attributes"===n.type&&"open"===n.attributeName&&null===n.oldValue){let t=e.querySelector(".js-saved-reply-filter-input");t&&t.focus()}}).observe(e,{attributes:!0});for(let e of document.querySelectorAll(".js-saved-reply-include-fragment")){let t=e.closest(".js-saved-reply-container");e.addEventListener("load",()=>{if(t){let e=t.querySelector(".js-saved-reply-filter-input");e&&e.focus()}})}})();var n$=n(48234),nB=n(62660);function nN(e,t,n,r){let i=(0,n$.Py)(e,e=>t.querySelector(`[data-line-number-content="${e}"]`)||t.querySelector(`#LC${e}`));if(!i)return;if(n){let e=(0,eh.kN)(i.startContainer.textContent,i.startOffset);if(-1===e)return;i.setStart(i.startContainer,e)}if(r){let e=(0,eh.kN)(i.endContainer.textContent,i.endOffset);if(-1===e)return;i.setEnd(i.endContainer,e)}let a=document.createElement("span");a.classList.add("text-bold","hx_keyword-hl","rounded-2","d-inline-block"),(0,nB.t)(i,a)}(0,b.lB)(".js-highlight-code-snippet-columns",function(e){let t=function(e){let t=parseInt(e.getAttribute("data-start-line")),n=parseInt(e.getAttribute("data-end-line")),r=parseInt(e.getAttribute("data-start-column")),i=parseInt(e.getAttribute("data-end-column"));return t===n&&r===i?null:{start:{line:t,column:r},end:{line:n,column:0!==i?i:null}}}(e);null!==t&&function(e,t){if(e.start.line!==e.end.line){nN({start:{line:e.start.line,column:e.start.column},end:{line:e.start.line,column:null}},t,!0,!1);for(let n=e.start.line+1;n<e.end.line;n+=1)nN({start:{line:n,column:0},end:{line:n,column:null}},t,!1,!1);nN({start:{line:e.end.line,column:0},end:{line:e.end.line,column:e.end.column}},t,!1,!0)}else nN(e,t,!0,!0)}(t,e)}),n(31901),n(93885);var nD=n(21244),nW=n(92811);async function nF(e){try{await e.text()}catch{}}function nU(){let e=function(){let e=new URLSearchParams(window.location.search),t=(0,nD.t)(e);if(t){let e=new URL(window.location.href,window.location.origin);return e.search=t.toString(),e.toString()}}();e&&(0,q.bj)(e)}async function nz(){await J.K;let e=document.querySelector(".js-mark-notification-form");e instanceof HTMLFormElement&&(0,C.k_)(e)}function nV(e,t){let n,r;if(e.closest("jump-to"))return;let i=document.querySelector(".js-site-search-form");document.querySelector(".js-site-search").classList.toggle("scoped-search",t),t?(n=i.getAttribute("data-scoped-search-url"),r=e.getAttribute("data-scoped-placeholder")):(n=i.getAttribute("data-unscoped-search-url"),r=e.getAttribute("data-unscoped-placeholder")),i.setAttribute("action",n),e.setAttribute("placeholder",r)}(async function e(){return(0,g.JW)(".js-notification-shelf .js-notification-action form",async function(e,t){if(e.hasAttribute("data-redirect-to-inbox-on-submit")){await nF(t);let e=document.querySelector(".js-notifications-back-to-inbox");e&&e.click();return}(0,nW.T)(e,e),await nF(t)})})(),nU(),document.addEventListener(tt.z.SUCCESS,nU),document.addEventListener("turbo:before-fetch-request",function(e){let t=(0,nD.d)(e.detail.url.pathname);if(t){let n=new URLSearchParams(e.detail.url.search);for(let[e,r]of Object.entries(t))r&&n.set(e,r);e.detail.url.search=n.toString()}}),(0,p.on)("submit",".js-mark-notification-form",async function(e){let t=e.currentTarget;e.preventDefault();try{await fetch(t.action,{method:t.method,body:new FormData(t),headers:{Accept:"application/json",...(0,x.kt)()}})}catch{}}),document.addEventListener(tt.z.SUCCESS,nz),nz(),(0,H.Ff)("keyup",".js-site-search-field",function(e){let t=e.target,n=0===t.value.length;n&&"Backspace"===e.key&&t.classList.contains("is-clearable")&&nV(t,!1),n&&"Escape"===e.key&&nV(t,!0),t.classList.toggle("is-clearable",n)}),(0,H.uE)(".js-site-search-focus",function(e){let t=e.closest(".js-chromeless-input-container");t&&(t.classList.add("focus"),e.addEventListener("blur",function n(){t?.classList.remove("focus"),0===e.value.length&&e.classList.contains("js-site-search-field")&&nV(e,!0),e.removeEventListener("blur",n)}))}),(0,p.on)("submit",".js-site-search-form",function(e){e.target instanceof Element&&(e.target.querySelector(".js-site-search-type-field").value=new URLSearchParams(window.location.search).get("type")||"")});let nG=new ResizeObserver(e=>{for(let{target:t}of e){let e=t.classList.contains("regular-search-input");(t.classList.contains("sm-search-input")||e)&&function(e,t){window.innerWidth<768?t?(0,t4.JC)(e):(0,t4.ai)(e):window.innerWidth>=768&&(t?(0,t4.ai)(e):(0,t4.JC)(e))}(t,e)}});(0,b.lB)(".regular-search-input",{constructor:HTMLElement,add(e){nG.observe(e)},remove(e){(0,t4.JC)(e),nG.unobserve(e)}}),(0,b.lB)(".sm-search-input",{constructor:HTMLElement,add(e){nG.observe(e)},remove(e){(0,t4.JC)(e),nG.unobserve(e)}}),(0,p.on)("click",".js-toggle-appheader-search",function(){let e=document.querySelector(".js-global-bar-second-row");if(e&&(e.toggleAttribute("hidden"),!e.getAttribute("hidden"))){let t=e.querySelector(".js-site-search-focus");t&&t.focus()}});var nX=n(5497);(0,b.lB)("textarea.js-size-to-fit",{constructor:HTMLTextAreaElement,subscribe:e=>CSS?.supports?.("field-sizing","content")?{unsubscribe(){}}:(0,nX.A)(e)});var nK=n(12747);(0,p.on)("click",".js-smoothscroll-anchor",function(e){let t=e.currentTarget;if(!(t instanceof HTMLAnchorElement))return;let n=(0,nK.rG)(document,t.hash);if(!n&&"#top"===t.hash){let t=document.querySelector("html");if(t){let n=t.style.scrollBehavior;t.style.scrollBehavior="smooth",window.location.hash="",t.scrollIntoView({behavior:"smooth"}),t.style.scrollBehavior=n,e.preventDefault();return}}if(!n)return;n.focus();let r=window.matchMedia("(prefers-reduced-motion: reduce)");r&&r.matches?n.scrollIntoView():n.scrollIntoView({behavior:"smooth"}),e.preventDefault()});let nJ=new WeakMap,nY=document.querySelector("#snippet-clipboard-copy-button"),nZ=document.querySelector("#snippet-clipboard-copy-button-unpositioned");async function nQ(e,t){let n=e.getAttribute("data-snippet-clipboard-copy-content");if(null===n)return;e.removeAttribute("data-snippet-clipboard-copy-content");let r=!!e.closest(".js-snippet-clipboard-copy-unpositioned"),i=r?nZ:nY;if(!(i instanceof HTMLTemplateElement))return;let a=i.content.cloneNode(!0).children[0];if(!(a instanceof HTMLElement))return;let o=a.children[0];if(o instanceof HTMLElement){if(o.setAttribute("value",n),!r){document.addEventListener("selectionchange",()=>{let t=document.getSelection();if(t&&e.contains(t.anchorNode)){let e=t?.toString();o.style.display=""===e.trim()?"inherit":"none"}},{signal:t});let n=e.querySelector("pre");if(null!==n){let e;n.addEventListener("scroll",()=>{e&&clearTimeout(e),o.style.display="none",e=setTimeout(()=>{o.style.display="inherit"},1e3)},{signal:t})}}e.appendChild(a)}}function n0(e,t,n){return n1(e,t),n&&e.classList.toggle("on"),Promise.all(Array.from(e.querySelectorAll(".js-social-updatable"),e=>(0,ec.le)(e)))}function n1(e,t){for(let n of e.querySelectorAll(".js-social-count")){n.textContent=t,n.setAttribute("title",t);let e=n.getAttribute("data-singular-suffix"),r=n.getAttribute("data-plural-suffix"),i="1"===t?e:r;i&&n.setAttribute("aria-label",`${t} ${i}`)}for(let n of e.querySelectorAll(".btn-with-aria-count")){let e=n.getAttribute("data-aria-prefix");e&&n.setAttribute("aria-label",`${e} (${t})`)}}(0,b.lB)("[data-snippet-clipboard-copy-content]",{constructor:HTMLElement,add(e){let t=new AbortController;nJ.set(e,t),nQ(e,t.signal)}}),(0,b.lB)(".snippet-clipboard-content clipboard-copy",{constructor:HTMLElement,remove(e){let t=nJ.get(e);t&&t.abort()}}),(0,g.JW)(".js-social-form",async function(e,t){let n,r=e.closest(".js-social-container"),i=e.classList.contains("js-deferred-toggler-target");try{if(n=await t.json(),r){let e;await n0(r,n.json.count,i);for(let t of r.querySelectorAll(":scope > *")){let n=!1;if(t.checkVisibility)n=t.checkVisibility();else{let e=window.getComputedStyle(t);n="none"!==e.display&&"hidden"!==e.visibility}n&&(e=t.querySelector('button[type="submit"]'))}e?.focus(),r.dispatchEvent(new CustomEvent("social:success",{detail:n,bubbles:!0}))}}catch(t){if(t.response?.status===409&&t.response.json.confirmationDialog){let n=t.response.json.confirmationDialog,a=document.querySelector(n.templateSelector),o=e.querySelector(".js-confirm-csrf-token")?.value;if(a instanceof HTMLTemplateElement&&o){let t=new t0.i4(a,{confirmUrl:e.action,confirmCsrfToken:o,...n.inputs||{}}),s=await (0,ta.r)({content:t});s.addEventListener("social-confirmation-form:success",async e=>{e instanceof CustomEvent&&r&&await n0(r,e.detail.count,i)}),s.addEventListener("social-confirmation-form:error",()=>{(0,h.n)()})}}else r&&!i&&r.classList.toggle("on"),(0,h.n)()}}),(0,g.JW)(".js-social-confirmation-form",async function(e,t){try{let n=await t.json();(0,p.h)(e,"social-confirmation-form:success",n.json)}catch{(0,p.h)(e,"social-confirmation-form:error")}});var n2=n(69719),n5=n(10204),n3=n(96679);let n7=[],n4=n3.XC?.hidden||!1;function n9(e){return null!=e}function n6(e){let t=document.querySelector(".js-stale-session-flash"),n=t.querySelector(".js-stale-session-flash-signed-in"),r=t.querySelector(".js-stale-session-flash-signed-out"),i=t.querySelector(".js-stale-session-flash-switched");if(t.hidden=!1,n.hidden="SIGNED_IN"!==e,r.hidden="SIGNED_OUT"!==e,i.hidden=!e?.startsWith("SWITCHED"),e?.startsWith("SWITCHED:")){let n=e.split(":");if(3===n.length){let e=n[1],r=n[2],a=i.getAttribute("data-original-user-id");a&&a===r?(t.hidden=!0,i.hidden=!0,i.removeAttribute("data-original-user-id")):a||i.setAttribute("data-original-user-id",e||"")}}window.addEventListener("popstate",function(e){e.state&&null!=e.state.container&&location.reload()}),document.addEventListener("submit",function(e){e.preventDefault()})}n3.XC?.addEventListener("visibilitychange",()=>{let e=n3.XC?.hidden||!1;void 0!==l&&clearTimeout(l),l=setTimeout(()=>{if(e!==n4)for(let t of(n4=e,l=void 0,n7))t(n4)},3e4*!!e)}),async function(){let e=await (0,n2.H)();if(!e)return;let t=(0,_.rK)(t=>e.subscribe(t.flat())),n=(0,_.rK)(t=>e.unsubscribeAll(...t)),r=(0,_.rK)(t=>e.updatePresenceMetadata(t));(0,b.lB)(".js-socket-channel[data-channel]",{subscribe:e=>{var n;let i=(e.getAttribute("data-channel")||"").trim().split(/\s+/).map(n5.KK.parse).filter(n9).map(t=>({subscriber:e,topic:t})),a=i.map(e=>e.topic.name).filter(e=>(0,n5.JR)(e)),o={unsubscribe(){}};if(a.length){let t,i,s=()=>{let n=[];for(let o of(i&&n.push(i),void 0!==t&&n.push({[n5.nH]:+!!t}),a))r({subscriber:e,channelName:o,metadata:n})};o=(0,O.Zz)((0,O.Rt)(e,"socket:set-presence-metadata",e=>{let{detail:t}=e;i=t,s()}),((n=e=>{t=e,s()})(n4),n7.push(n),new O.yU(()=>{let e=n7.indexOf(n);-1!==e&&n7.splice(e,1)})))}return t(i),o},remove:e=>n(e)})}(),(0,b.lB)("form.js-auto-replay-enforced-sso-request",{constructor:HTMLFormElement,initialize(e){(0,C.k_)(e)}});let n8=null;if("function"==typeof BroadcastChannel)try{(n8=new BroadcastChannel("stale-session")).onmessage=e=>{"string"==typeof e.data&&n6(e.data)}}catch{}if(!n8){let e=!1;n8={postMessage(t){e=!0;try{window.localStorage.setItem("logged-in",t)}finally{e=!1}},onmessage:null},window.addEventListener("storage",function(t){if(!e&&t.storageArea===window.localStorage&&"logged-in"===t.key)try{("SIGNED_IN"===t.newValue||"SIGNED_OUT"===t.newValue||t.newValue?.startsWith("SWITCHED"))&&n6(t.newValue)}finally{window.localStorage.removeItem(t.key)}})}let re=document.querySelector(".js-stale-session-flash[data-signedin]");if(re){let e=re.getAttribute("data-signedin")||"";n8?.postMessage(e)}let rt=()=>{n8?.postMessage("false")};function rn(e,t,n){let r=e.getBoundingClientRect().height,i=t.getBoundingClientRect(),a=n.getBoundingClientRect(),o=a.top;o+i.height+10>=r&&(o=Math.max(r-i.height-10,0));let s=a.right;null!=n.closest(".js-build-status-to-the-left")&&(s=Math.max(a.left-i.width-10,0)),t.style.top=`${o}px`,t.style.left=`${s}px`,t.style.right="auto"}async function rr(e){let t,n=e.querySelector(".js-dropdown-details"),r=e.querySelector(".js-status-dropdown-menu")||e.closest(".js-status-dropdown-menu");if(!(r instanceof HTMLElement))return;let i=r.querySelector(".js-status-loader");if(!i)return;let a=r.querySelector(".js-status-loading"),o=r.querySelector(".js-status-error"),s=i.getAttribute("data-contents-url");a.classList.remove("d-none"),o.classList.add("d-none");try{await (0,tH.A)(),t=await (0,to.Ts)(document,s)}catch{a.classList.add("d-none"),o.classList.remove("d-none")}t&&(i.replaceWith(t),r.querySelector(".js-details-container").classList.add("open"),n&&r.classList.contains("js-append-menu-to-body")&&rn(document.body,r,n))}function ri(e){rr(e.currentTarget)}(0,b.lB)(".js-loggout-form",function(e){e.addEventListener("submit",rt)}),(0,p.on)("toggle",".js-build-status .js-dropdown-details",function(e){let t=e.currentTarget,n=t.querySelector(".js-status-dropdown-menu");function r(){t.hasAttribute("open")||a()}function i(e){n.contains(e.target)||a()}function a(){t.removeAttribute("open"),n.classList.add("d-none"),t.appendChild(n),t.removeEventListener("toggle",r),window.removeEventListener("scroll",i)}n&&(t.addEventListener("toggle",r),n.classList.contains("js-close-menu-on-scroll")&&window.addEventListener("scroll",i,{capture:!0}),n.classList.remove("d-none"),n.querySelector(".js-details-container").classList.add("open"),n.classList.contains("js-append-menu-to-body")&&(document.body.appendChild(n),rn(document.body,n,t)))},{capture:!0}),(0,p.on)("click",".js-status-retry",({currentTarget:e})=>{rr(e)}),(0,b.lB)(".js-build-status",{add(e){e.addEventListener("mouseenter",ri,{once:!0})},remove(e){e.removeEventListener("mouseenter",ri)}}),n(23129);let ra=new IntersectionObserver(e=>{for(let t of e){let e=t.target;if(e.classList.toggle("position-stuck",t.intersectionRatio<1),t.intersectionRatio<1&&e instanceof HTMLElement){let n=e.ownerDocument.documentElement,r=t.intersectionRect.height;n.style.setProperty("--sticky-is-stuck-calculated-height",`${r}px`),requestAnimationFrame(()=>{let t=e.getBoundingClientRect().height;r!==t&&n.style.setProperty("--sticky-is-stuck-calculated-height",`${t}px`)})}}},{threshold:1});(0,b.lB)(".js-sticky-is-stuck",{constructor:HTMLElement,add(e){ra.observe(e)}});var ro=n(90903);async function rs(e){let t=e.currentTarget;if(t instanceof HTMLElement&&"false"!==t.getAttribute("data-sudo-required"))if(e.stopPropagation(),e.preventDefault(),await (0,ro.Ay)(t))t.removeAttribute("data-sudo-required"),t instanceof HTMLFormElement?(0,C.k_)(t):t.click();else{let e=t.closest("form");e&&(0,p.h)(e,"deprecatedAjaxComplete")}}function rl(e){let t=e.detail;":"===t.key&&(t.value=function(e){if(e.hasAttribute("data-use-colon-emoji"))return e.getAttribute("data-value");let t=e.firstElementChild;return t&&"G-EMOJI"===t.tagName&&!t.firstElementChild?t.textContent:e.getAttribute("data-value")}(t.item))}function rc(e){let{key:t,provide:n,text:r}=e.detail;":"===t&&n(rd(e.target.getAttribute("data-emoji-url"),r))}function ru(e){let t=e.target.querySelector(".emoji-suggestions[popover]");t&&t.showPopover()}async function rd(e,t){let[n,r]=await rf(e),i=(function(e,t){let n=` ${t.toLowerCase().replace(/_/g," ")}`;return(0,tx.d)(e,e=>{let t=e.getAttribute("data-emoji-name"),r=function(e,t){let n=e.indexOf(t);return n>-1?1e3-n:0}(function(e){let t=e.getAttribute("data-text").trim().toLowerCase().replace(/_/g," ");return` ${t}`}(e),n);return r>0?{score:r,text:t}:null},tq.UD)})(r,t).slice(0,5);for(let e of(n.textContent="",i))n.append(e);return{fragment:n,matched:i.length>0}}async function rm(e){let t=(await (0,to.Ts)(document,e)).firstElementChild;return[t,[...t.children]]}(0,p.on)("click","button[data-sudo-required], summary[data-sudo-required]",rs),(0,b.lB)("form[data-sudo-required]",{constructor:HTMLFormElement,subscribe:e=>(0,O.Rt)(e,"submit",rs)}),(0,b.lB)("text-expander[data-emoji-url]",{subscribe:e=>(0,O.Zz)((0,O.Rt)(e,"text-expander-change",rc),(0,O.Rt)(e,"text-expander-value",rl),(0,O.Rt)(e,"text-expander-activate",ru))});let rf=(0,eD.A)(rm);var rh=n(59843);function rp(e,t){return{matches:(function(e,t){if(!t)return e;let n=RegExp(`\\b${t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}`),r=/^\d+$/.test(t)?e=>(function(e,t){let n=e.search(t);return n>-1?1e3-n:0})(e,n):e=>(0,tq.dt)(e,t);return(0,tx.d)(e,e=>{let t=`${e.number} ${e.title.trim().toLowerCase()}`,n=r(t);return n>0?{score:n,text:t}:null},tq.UD)})(e.suggestions,t).slice(0,5),icons:e.icons}}function rg(e,t,n,r=""){(0,rh.XX)((0,rh.qy)`
    <ul
      role="listbox"
      class="suggester-container suggester suggestions list-style-none position-absolute"
      data-query="${r}"
    >
      ${e.map(e=>{let t=e.type in n?(0,k.B)(document,n[e.type]):"";return(0,rh.qy)`
      <li class="markdown-title" role="option" id="suggester-issue-${e.id}" data-value="${e.number}">
        <span class="d-inline-block mr-1">${t}</span>
        <small>#${e.number}</small> ${(0,rh._3)(e.title)}
      </li>
    `})}
    </ul>
  `,t)}function rb(e){let t=e.detail;if("#"!==t.key)return;let n=t.item.getAttribute("data-value");t.value=`#${n}`}function rv(e){let{key:t,provide:n,text:r}=e.detail;if("#"!==t)return;if("#"===r)return void ry(e.target);let i=e.target;n(rS(i.getAttribute("data-issue-url"),r,i))}function ry(e){if(!e)return;let t=e.closest("text-expander");t&&"dismiss"in t&&"function"==typeof t.dismiss&&t.dismiss()}function rw(e){ry(e.target)}function rE(e){let{key:t}=e;0>["ArrowRight","ArrowLeft"].indexOf(t)||ry(e.target)}async function rS(e,t,n){let r=await rL(e,t,n),i=document.createElement("div");return rg(r.matches,i,r.icons,t),{fragment:i.firstElementChild,matched:r.matches.length>0}}(0,b.lB)("text-expander[data-issue-url]",{subscribe:e=>{let t=[(0,O.Rt)(e,"text-expander-change",rv),(0,O.Rt)(e,"text-expander-value",rb),(0,O.Rt)(e,"keydown",rE),(0,O.Rt)(e,"click",rw)];return(0,O.Zz)(...t)}});let rA=new Set,rj=new Map;async function rL(e,t,n){let r=await rk(e),i=rp(r,t);if(t.length<3||r.suggestions.length<1e3)return i;let a=t.slice(0,3);(0,ei.G7)("repository_suggester_elastic_search")&&Number.isFinite(Number(t))&&(a=t);let o=rj.get(a);if(o)return rp(o,t);if(!rA.has(a)){rA.add(a);let r=rT(e,a,n);if(0===i.matches.length)return rp(await r,t)}return rp(r,t)}async function rT(e,t,n){let r=new URL(e,window.location.origin);r.searchParams.set("q",t);let i=await rC(r.toString());if(rj.set(t,i),rA.delete(t),rj.size>5){let e=rj.size-5;for(let t of Array.from(rj.keys()).slice(0,e))rj.delete(t)}let a=n?.querySelector("ul.suggestions"),o=a?.getAttribute("data-query");if(a&&o?.startsWith(t)){let e=n?.querySelector("[aria-activedescendant]")?.getAttribute("aria-activedescendant"),t=document.createElement("div"),r=rp(i,o);if(rg(r.matches,t,r.icons),e)for(let n of t.querySelectorAll(`#${e}`))n.setAttribute("aria-selected","true");let s=t.firstElementChild;a.replaceChildren(...s.children)}return i}async function rC(e){let t=await self.fetch(e,{headers:{...(0,x.kt)(),Accept:"application/json"}});if(!t.ok){let e=Error(),n=t.statusText?` ${t.statusText}`:"";throw e.message=`HTTP ${t.status}${n}`,e}return t.json()}let rk=(0,eD.A)(rC);function rq(e){let t=e.detail;if("@"!==t.key)return;let n=t.item.getAttribute("data-value");t.value=`@${n}`}function rx(e){let{key:t,provide:n,text:r}=e.detail;"@"!==t||r?.split(" ").length>1||n(rM(e.target.getAttribute("data-mention-url"),r))}async function rM(e,t){let n=await r_(e),r=document.createElement("div"),i=(function(e,t){if(!t)return e;let n=function(e){if(!e)return()=>2;let t=e.toLowerCase().split("");return(n,r)=>{if(!n)return 0;let i=function(e,t){let n,r,i,a,o=function(e,t){let n=0,r=[];for(;(n=e.indexOf(t,n))>-1;)r.push(n++);return r}(e,t[0]);if(0===o.length)return null;if(1===t.length)return[o[0],1,[]];for(r=0,a=null,i=o.length;r<i;r++){let i=o[r];if(!(n=function(e,t,n){let r=n,i=[];for(let n=1;n<t.length;n+=1){if(-1===(r=e.indexOf(t[n],r)))return;i.push(r++)}return i}(e,t,i+1)))continue;let s=n[n.length-1]-i;(!a||s<a[1])&&(a=[i,s,n])}return a}(n,t);if(!i)return 0;let a=e.length/i[1]/(i[0]/2+1);return r?a+1:a}}(t);return(0,tx.d)(e,e=>{let t=e.description?`${e.name} ${e.description}`.trim().toLowerCase():`${e.login} ${e.name}`.trim().toLowerCase(),r=n(t,e.participant);return r>0?{score:r,text:t}:null},tq.UD)})(n,t).slice(0,5);return(0,rh.XX)((0,rh.qy)`
    <ul role="listbox" class="suggester-container suggester suggestions list-style-none position-absolute">
      ${i.map(e=>{let t="user"===e.type?e.login:e.name,n="user"===e.type?e.name:e.description;return(0,rh.qy)`
      <li role="option" id="suggester-${e.id}-${e.type}-${t}" data-value="${t}">
        <span>${t}</span>
        <small>${n}</small>
      </li>
    `})}
    </ul>
  `,r),{fragment:r.firstElementChild,matched:i.length>0}}(0,b.lB)("text-expander[data-mention-url]",{subscribe:e=>(0,O.Zz)((0,O.Rt)(e,"text-expander-change",rx),(0,O.Rt)(e,"text-expander-value",rq))});let r_=(0,eD.A)(async function(e){let t=await self.fetch(e,{headers:{...(0,x.kt)(),Accept:"application/json"}});if(!t.ok){let e=Error(),n=t.statusText?` ${t.statusText}`:"";throw e.message=`HTTP ${t.status}${n}`,e}return t.json()});(0,p.on)("change","input.js-survey-contact-checkbox",function(e){let t=e.currentTarget,n=t.closest(".js-survey-question-form").querySelector(".js-survey-contact-checkbox-hidden");t.checked?n.setAttribute("disabled","true"):n.removeAttribute("disabled")}),(0,p.on)("details-menu-selected",".js-sync-select-menu-text",function(e){let t=document.querySelector(".js-sync-select-menu-button");t.textContent=e.detail.relatedTarget.querySelector("span[data-menu-button-text]").textContent,t.focus()},{capture:!0}),(0,p.on)("click",'tab-container [role="tab"]',function(e){let{currentTarget:t}=e,n=t.closest("tab-container").querySelector(".js-filterable-field, [data-filter-placeholder-input]");if(n instanceof HTMLInputElement){let e=t.getAttribute("data-filter-placeholder");e&&n.setAttribute("placeholder",e),n.focus()}}),(0,p.on)("tab-container-changed","tab-container",function(e){let t=e.detail.relatedTarget;if(!t)return;let n=t.getAttribute("data-fragment-url"),r=t.querySelector("include-fragment");n&&r&&!r.hasAttribute("src")&&(r.src=n)}),n(48696);var rH=n(95493);async function rP(e){var t;let n=e.currentTarget;if((t=n).getAttribute("data-hovercard-url")&&t.closest("[data-team-hovercards-enabled]"))return void n.classList.remove("tooltipped");let r=n.getAttribute("data-url");if(!r)return;let i=await fetch(r,{headers:{Accept:"application/json"}});if(!i.ok)return;let a=await i.json(),o=n.getAttribute("data-id"),s=document.querySelectorAll(`.js-team-mention[data-id='${o}']`);for(let e of s)e.removeAttribute("data-url");try{0===a.total?a.members.push("This team has no members"):a.total>a.members.length&&a.members.push(`${a.total-a.members.length} more`),rO(s,function(e){if("ListFormat"in Intl)return new Intl.ListFormat().format(e);if(0===e.length)return"";{if(1===e.length)return e[0];if(2===e.length)return e.join(" and ");let t=e[e.length-1];return e.slice(0,-1).concat(`and ${t}`).join(", ")}}(a.members))}catch(t){let e=t.response?t.response.status:500;rO(s,n.getAttribute(404===e?"data-permission-text":"data-error-text"))}}function rO(e,t){for(let n of e)n instanceof HTMLElement&&(n.setAttribute("aria-label",t),n.classList.add("tooltipped","tooltipped-s","tooltipped-multiline"))}function rI(e){if(function(e){let t;try{t=new URL(e.url)}catch{return!0}return t.host!==window.location.host}(e))return;let t=function(){let e=document.querySelector(".js-timeline-marker");return null!=e?e.getAttribute("data-last-modified"):null}();t&&e.headers.set("X-Timeline-Last-Modified",t)}function rR(){let e=rV();if(!e||document.querySelector(".js-pull-discussion-timeline"))return;let t=document.getElementById(e);t&&rU(t)}function r$(e=!0){let t=rV();if(!t)return;let n=document.getElementById(t);if(n)rU(n);else{var r;if(function(e){let t=rN(e,".js-comment-container");return!!t&&((0,e1.d)(t),!0)}(r=t)||rB(r,".js-thread-hidden-comment-ids")||rB(r,".js-review-hidden-comment-ids"))return;let n=document.querySelector("#js-timeline-progressive-loader");n&&e&&rz(t,n)}}function rB(e,t){let n=rN(e,t);return!!n&&(n.addEventListener("page:loaded",function(){r$()}),n.querySelector("button[type=submit]").click(),!0)}function rN(e,t){for(let n of document.querySelectorAll(t)){let t=n.getAttribute("data-hidden-comment-ids");if(t){let r=t.split(","),i=e.match(/\d+/g)?.[0];if(i&&r.includes(i))return n}}return null}async function rD(){return Promise.all(Array.from(document.querySelectorAll(".js-comment-body video")).map(e=>new Promise(t=>{if(e.readyState>=e.HAVE_METADATA)t(e);else{let n=setTimeout(()=>t(e),5e3),r=()=>{clearTimeout(n),t(e)};e.addEventListener("loadeddata",()=>{e.readyState>=e.HAVE_METADATA&&r()}),e.addEventListener("error",()=>r())}})))}async function rW(){return Promise.all(Array.from(document.querySelectorAll(".js-comment-body img")).map(e=>{new Promise(t=>{if(e.complete)t(e);else{let n=setTimeout(()=>t(e),5e3),r=()=>{clearTimeout(n),t(e)};e.addEventListener("load",()=>r()),e.addEventListener("error",()=>r())}})}))}async function rF(){return Promise.all([rD(),rW()])}async function rU(e){await rF(),function(e){let t=e.closest("details, .js-details-container");t&&("DETAILS"===t.nodeName?t.setAttribute("open","open"):(0,eQ.Z)(t)||(0,eQ.kn)(t))}(e);let t=e.querySelector(`[href='#${e.id}']`);if((0,tY.Rt)(e),t){let e=t.getAttribute("data-turbo");t.setAttribute("data-turbo","false"),setTimeout(()=>{t.click()},0),null===e?t.removeAttribute("data-turbo"):t.setAttribute("data-turbo",e)}}async function rz(e,t){let n;if(!t)return;let r=t.getAttribute("data-timeline-item-src");if(!r)return;let i=new URL(r,window.location.origin),a=new URLSearchParams(i.search.slice(1));a.append("anchor",e),i.search=a.toString();try{n=await (0,to.Ts)(document,i.toString())}catch{return}let o=n.querySelector(".js-timeline-item");if(!o)return;let s=o.getAttribute("data-gid");if(!s)return;let l=document.querySelector(`.js-timeline-item[data-gid='${s}']`);if(l)l.replaceWith(o),r$(!1);else{let e=document.getElementById("js-progressive-timeline-item-container");e&&e.replaceWith(n),r$(!1)}}function rV(){return window.location.hash.slice(1)}async function rG(){let e=[];try{e=await navigator.serviceWorker.getRegistrations()}catch(e){if("SecurityError"===e.name)return}for(let t of e)t.unregister()}document.addEventListener("keydown",e=>{if("Escape"!==e.key||e.target!==document.body)return;let t=document.querySelector(".js-targetable-element:target");t&&(0,rH._H)(t,()=>{(0,q.K3)()})}),document.addEventListener("click",e=>{let t=document.querySelector(".js-targetable-element:target");!(!t||e.target instanceof HTMLAnchorElement)&&e.target instanceof HTMLElement&&(t.contains(e.target)||(0,rH._H)(t,()=>{(0,q.K3)()}))}),n(97213),(0,b.lB)(".js-team-mention",function(e){e.addEventListener("mouseenter",rP)}),(0,g.JW)(".js-needs-timeline-marker-header",function(e,t,n){rI(n)}),(0,p.on)("deprecatedAjaxSend","[data-remote]",function(e){let{request:t}=e.detail;rI(t)}),(0,e0.A)(function(){r$()}),(0,b.lB)(".js-timeline-progressive-focus-container",rR),window.addEventListener("sticky-header-rendered",()=>{rR()}),(0,b.lB)(".js-inline-comments-container",function(e){let t=rV();if(!t)return;let n=document.getElementById(t);n&&e.contains(n)&&rU(n)}),(0,b.lB)("#js-discussions-timeline-anchor-loader",{constructor:HTMLElement,add:e=>{if(document.querySelector("#js-timeline-progressive-loader"))return;let t=rV();t&&(document.getElementById(t)||rz(t,e))}}),(0,b.lB)(".js-discussion",function(){let e=new WeakSet;function t(){e=new WeakSet(document.querySelectorAll(".js-timeline-item"))}t(),document.addEventListener("turbo:load",t),(0,b.lB)(".js-timeline-item",t=>{t instanceof HTMLElement&&(e.has(t)||(0,w.C)(t))})}),(0,p.on)("click",".js-toggler-container .js-toggler-target",function(e){if(0!==e.button)return;let t=e.currentTarget.closest(".js-toggler-container");t&&t.classList.toggle("on")}),(0,g.JW)(".js-toggler-container",async(e,t)=>{e.classList.remove("success","error"),e.classList.add("loading");try{await t.text(),e.classList.add("success")}catch{e.classList.add("error")}finally{e.classList.remove("loading")}}),async function(){if("serviceWorker"in navigator){await J.G;let e=document.querySelector('link[rel="service-worker-src"]')?.href;e?navigator.serviceWorker.register(`${e}?module=true`,{scope:"/",type:"module"}):await rG()}}();var rX=n(7332),rK=n(51606);(0,rX.Uz)(0),rX.session.isVisitable=()=>!0;let rJ=Object.getOwnPropertyDescriptor(rX.H5.prototype,"reloadReason")?.get;function*rY(e){for(let t of Object.values(e.detailsByOuterHTML))if(t.tracked)for(let e of t.elements)e instanceof HTMLMetaElement&&e.getAttribute("http-equiv")&&(yield[e.getAttribute("http-equiv")||"",e.getAttribute("content")||""])}Object.defineProperty(rX.H5.prototype,"reloadReason",{get(){let e=rJ?.call(this);if("tracked_element_mismatch"!==e.reason)return e;let t=Object.fromEntries(rY(this.currentHeadSnapshot)),n=[];for(let[e,r]of rY(this.newHeadSnapshot))t[e]!==r&&n.push((0,rK.Sf)(e));return{reason:`tracked_element_mismatch-${n.join("-")}`}}});var rZ=n(13255);rX.session.history.shouldRestore=e=>{let t=(0,rZ.cB)(),n=e?.appId;return t!==n||"rails"===n&&"rails"===t||!n};let rQ=e=>{let t=history[e];history[e]=function(n,r,i){rX.gM.history.update(function(r,i,a){let o=(0,q.JV)().turboCount||0,s="pushState"===e&&n?.turbo,l=s?"rails":n?.appId||(0,q.JV)().appId,c={...n,...r,turboCount:s?o+1:o,appId:l};t.call(this,c,i,a)},new URL(i||location.href,location.href),n?.turbo?.restorationIdentifier)}};rQ("replaceState"),rQ("pushState");let r0=rX.session.adapter,r1=null,r2=()=>{r1=setTimeout(()=>{r0.progressBar.setValue(0),r0.progressBar.show()},99)},r5=()=>{null!==r1&&(clearTimeout(r1),r1=null),r0.progressBar.setValue(1),r0.progressBar.hide()};var r3=n(72841);let r7=new Map,r4=new Map,r9=()=>r7.get(document.location.href),r6=(e,t)=>r7.set(e,t),r8=()=>r4.set(document.location.href,(0,rK.$4)()),ie=()=>r4.get(document.location.href);(async()=>{await J.G,r6(document.location.href,(0,rK.GH)(document)),r8()})();var it=n(97396),ir=n(88191),ii=n(53005),ia=n(7799);let io=!1,is=null;n3.cg&&(0,b.lB)("[data-turbo-frame]",{constructor:HTMLElement,add(e){if("A"!==e.tagName&&""!==e.getAttribute("data-turbo-frame"))for(let t of e.querySelectorAll("a:not([data-turbo-frame])"))t.setAttribute("data-turbo-frame",e.getAttribute("data-turbo-frame")||"")}}),n3.XC?.addEventListener("turbo:click",function(e){if(e.target instanceof HTMLElement){if((0,ei.G7)("disable_turbo_visit")&&!(0,nb.LM)()||(0,r3.A)(location.href,e.detail.url))return void e.preventDefault();e.defaultPrevented||(0,it.SC)("turbo")}}),n3.XC?.addEventListener("turbo:before-fetch-request",function(e){try{let t=window.onbeforeunload?.(e);t&&(confirm(t)?window.onbeforeunload=null:(e.preventDefault(),r5()))}catch(e){if(!(e instanceof Error)||"Permission denied to access object"!==e.message)throw e}}),n3.XC?.addEventListener("turbo:before-fetch-request",e=>{if(e.defaultPrevented)return;let t=e.target;(0,rK.mU)(t)&&r2(),(0,ei.G7)("client_version_header")&&(e.detail.fetchOptions.headers[ii.S]=(0,ii.O)()),e.detail.fetchOptions.headers["Turbo-Frame"]||(e.detail.fetchOptions.headers["Turbo-Visit"]="true")});let il=Object.getPrototypeOf((n3.XC?.createElement("turbo-frame")).delegate),ic=il.requestErrored;il.requestErrored=function(e,t){return this.element.dispatchEvent(new CustomEvent("turbo:fetch-error",{bubbles:!0,detail:{request:e,error:t}})),ic.apply(this,e,t)},n3.XC?.addEventListener("turbo:fetch-error",e=>{if(e.target instanceof HTMLFormElement)return;let t=e.detail.request;window.location.href=t.location.href,e.preventDefault()}),n3.XC?.addEventListener("turbo:before-fetch-response",async e=>{let t=e.detail.fetchResponse;io=t.statusCode>=500,404===t.statusCode&&((0,rK.OO)(t.statusCode.toString()),window.location.href=t.location.href,e.preventDefault());let n=t.header("X-Fetch-Nonce");if(n&&(0,ir.$r)(n),io||!n){let e=await t.responseHTML,r=new DOMParser().parseFromString(e??"","text/html");if(io){is=r;return}n||function(e){let t=e.querySelector("#pjax-head meta[name=fetch-nonce], head meta[name=fetch-nonce]")?.content;t&&(0,ir.$r)(t)}(r)}}),n3.XC?.addEventListener("turbo:frame-render",e=>{(0,rK.mU)(e.target)&&r5()}),n3.XC?.addEventListener("turbo:before-render",async e=>{e.preventDefault(),e.detail.render=id,await (0,rK.Y9)(),e.detail.resume(!0),(0,rK.Sy)(document.documentElement,e.detail.newBody.ownerDocument.documentElement),function(){if((0,ia.M3)())return;let e=(0,en.Ri)("increase_contrast_light"),t=(0,en.Ri)("increase_contrast_dark");document.documentElement.setAttribute("data-light-theme",e?.value==="enabled"?"light_high_contrast":"light"),document.documentElement.setAttribute("data-dark-theme",t?.value==="enabled"?"dark_high_contrast":"dark")}(),r8()});let iu=()=>new Promise(e=>{setTimeout(()=>e(),0)}),id=async(e,t)=>{if(await iu(),io&&is){for(let e of(document.documentElement.replaceWith(is.documentElement),document.querySelectorAll("script"))){let t=(0,rK.vV)(e);t&&e.replaceWith(t)}return}let n=e.querySelector("[data-turbo-body]"),r=t.querySelector("[data-turbo-body]");n&&r?((0,rK.Sy)(e,t),n.replaceWith(r)):((0,rK.OO)("missing_turbo_body"),window.location.reload())};n3.cg?.addEventListener("popstate",()=>{let e=document.documentElement,t=ie();if(t){for(let n of e.attributes)t.find(e=>e.nodeName===n.nodeName)||e.removeAttribute(n.nodeName);for(let n of t)e.getAttribute(n.nodeName)!==n.nodeValue&&e.setAttribute(n.nodeName,n.nodeValue)}});var im=n(39627);let ih=!1,ip=e=>{if(!(e.target instanceof HTMLElement))return;let t=e.target.closest("[data-turbo-frame]"),n=e.target.closest("#js-repo-pjax-container"),r=new URL(e.detail.url,window.location.origin),i=e.target.closest("#user-profile-frame");return n&&t&&!(0,rK.$U)(r.pathname,location.pathname)||i&&!(0,rK.e8)(r.pathname,location.pathname)};n3.XC?.addEventListener("turbo:frame-click",function(e){if(e.target instanceof HTMLElement){if((0,r3.A)(location.href,e.detail.url))return void e.preventDefault();ip(e)&&((0,rK.OO)("repo_mismatch"),e.target.removeAttribute("data-turbo-frame"),e.preventDefault()),e.defaultPrevented||(0,it.SC)("turbo.frame")}}),n3.XC?.addEventListener("turbo:before-fetch-response",e=>{c=e.detail.fetchResponse,(0,rK.mU)(e.target)&&r6(window.location.href,(0,rK.GH)(document))}),n3.XC?.addEventListener("turbo:before-frame-render",async e=>{e.preventDefault();let{resume:t,newFrame:n}=e.detail;if(ih=!0,!c)return;let r=await c.responseHTML,i=c.location,a=new DOMParser().parseFromString(r??"","text/html");c=null;let o=e.target,s=[...a.querySelectorAll("turbo-frame")].find(e=>e.id===o?.id),l=(0,rK.nZ)(a);if(!s||l.length>0){(0,rK.OO)(`tracked_element_mismatch-${l.join("-")}`),window.location.href=i.href;return}r6(i.href,(0,rK.GH)(a)),(0,rK.$Y)(a),(0,rK.Y0)(a),(0,rK.G5)(a),iw(o,s),await (0,rK.Y9)(),t(void 0),iE(n)&&window.scrollTo(0,0),iy(),ig(a)}),n3.cg?.addEventListener("popstate",()=>{document.addEventListener("turbo:load",()=>{let e=r9()?.replacedElements||[];(0,rK.G5)(document,e),(0,rK.Xm)()},{once:!0})}),n3.XC?.addEventListener(tt.z.SUCCESS,()=>{ib(),ih&&(ih=!1,iv(),iy(),(0,it.Bu)())});let ig=e=>{let t=e.querySelector("meta[name=turbo-body-classes]")?.content;t&&(document.body.setAttribute("class",t),document.querySelector("[data-turbo-body]")?.setAttribute("class",t))},ib=()=>{let e=r9()?.bodyClasses;e&&(document.body.setAttribute("class",e),document.querySelector("[data-turbo-body]")?.setAttribute("class",e))},iv=()=>{let e=r9()?.title;e&&(0,im.D)(e)},iy=()=>{let e=r9()?.transients;if(e){for(let e of document.querySelectorAll("head [data-turbo-transient]"))e.remove();for(let t of e)t.matches("title, script, link[rel=stylesheet]")||(t.setAttribute("data-turbo-transient",""),document.head.append(t))}},iw=(e,t)=>{e&&(e.className=t.className)},iE=e=>"true"!==e.getAttribute("data-turbo-skip-scroll")&&"advance"===e.getAttribute("data-turbo-action");n3.XC?.addEventListener("turbo:frame-load",e=>{(0,nb.LM)()&&(0,nb.Vy)("turbo.frame"),(0,it.rZ)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo.frame"]}),e.target instanceof HTMLElement&&"advance"!==e.target.getAttribute("data-turbo-action")&&(0,it.iS)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo.frame"]})}),n3.XC?.addEventListener("turbo:load",e=>{(0,rK.Ph)();let t=0===Object.keys(e.detail.timing??{}).length;!(0,nb.LM)()||t||(0,nb.wG)()?t&&((0,nb.wG)()||(0,nb.LM)())?(0,it.o4)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo","turbo.frame"]}):t&&(0,it.k5)():((0,it.rZ)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo"]}),(0,it.iS)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo","turbo.frame"]}))}),n3.XC?.addEventListener("beforeunload",()=>(0,it.Ti)()),n3.XC?.addEventListener("turbo:reload",function(e){(0,nb.k9)(e.detail.reason)}),n3.XC?.addEventListener(tt.z.END,r8),n3.XC?.addEventListener(tt.z.PROGRESS_BAR.START,r2),n3.XC?.addEventListener(tt.z.PROGRESS_BAR.END,r5),window.requestIdleCallback(()=>{let e=function(){if("Intl"in window)try{return new window.Intl.DateTimeFormat().resolvedOptions().timeZone}catch{}}();e&&(0,en.TV)("tz",encodeURIComponent(e))});var iS=n(39595),iA=n(16561),ij=n(52734),iL=n(74043);function iT(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function iC(e,t){var n=iT(e,t,"get");return n.get?n.get.call(e):n.value}function ik(e,t,n){var r=iT(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}function iq(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o}let ix={WebAuthn:"webauthn",Password:"password",GitHubMobile:"github_mobile",TotpApp:"app",TotpEmail:"email"};var iM=new WeakMap;let SudoCredentialOptionsElement=class SudoCredentialOptionsElement extends HTMLElement{connectedCallback(){ik(this,iM,this.initialState),this.reRenderPrompt(!0)}reRenderPrompt(e=!1){this.resetPrompt();try{switch(iC(this,iM)){case ix.WebAuthn:this.renderWebauthnOption();break;case ix.GitHubMobile:this.renderGitHubMobileOption(e);break;case ix.TotpApp:this.renderTotpAppOption();break;case ix.TotpEmail:this.renderTotpEmailOption();break;case ix.Password:default:this.renderPasswordOption()}this.reRenderNavContainer()}catch(e){this.handleUnexpectedPromptError(e)}}handleUnexpectedPromptError(e){let t="";if(t=iC(this,iM)===ix.GitHubMobile?this.githubMobileGenericErrorMessage:this.genericErrorMessage,e&&iC(this,iM)!==ix.Password)throw this.renderPasswordOptionWithError(t),e}renderPasswordOptionWithError(e){this.showPassword(),this.showErrorMessage(e)}resetPrompt(){this.hideErrorMessage(),this.isWebAuthnAvailable()&&this.hideWebAuthn(),this.isGitHubMobileAvailable()&&this.hideGitHubMobile(),this.isTotpAppAvailable()&&this.hideTotpApp(),this.isTotpEmailAvailable()&&this.hideTotpEmail(),this.hidePassword()}hideWebAuthn(){this.safeSetElementVisibility(this.webauthnContainer,!1),this.safeSetElementVisibility(this.webauthnNav,!1)}hideGitHubMobile(){this.safeSetElementVisibility(this.githubMobileContainer,!1),this.safeSetElementVisibility(this.githubMobileNav,!1),this.safeSetElementVisibility(this.githubMobileLoading,!1),this.safeSetElementVisibility(this.githubMobileLanding,!1)}hideTotpApp(){this.safeSetElementVisibility(this.totpAppContainer,!1),this.safeSetElementVisibility(this.totpAppNav,!1)}hideTotpEmail(){this.safeSetElementVisibility(this.totpEmailContainer,!1),this.safeSetElementVisibility(this.totpEmailNav,!1)}hidePassword(){this.safeSetElementVisibility(this.passwordContainer,!1),this.safeSetElementVisibility(this.passwordNav,!1)}reRenderNavContainer(){this.isWebAuthnAvailable()&&iC(this,iM)!==ix.WebAuthn&&this.safeSetElementVisibility(this.webauthnNav,!0),this.isGitHubMobileAvailable()&&iC(this,iM)!==ix.GitHubMobile&&this.safeSetElementVisibility(this.githubMobileNav,!0),this.isTotpAppAvailable()&&iC(this,iM)!==ix.TotpApp&&this.safeSetElementVisibility(this.totpAppNav,!0),this.isTotpEmailAvailable()&&iC(this,iM)!==ix.TotpEmail&&this.safeSetElementVisibility(this.totpEmailNav,!0),iC(this,iM)!==ix.Password&&this.safeSetElementVisibility(this.passwordNav,!0)}renderWebauthnOption(){this.safeSetElementVisibility(this.webauthnContainer,!0),this.webauthnGet?.setState((0,iL.$j)()?iA.U.Ready:iA.U.Unsupported)}renderGitHubMobileOption(e){try{(0,ij.s)()}catch{}e?(this.safeSetElementVisibility(this.githubMobileLoading,!1),this.safeSetElementVisibility(this.githubMobileLanding,!0),this.safeSetElementVisibility(this.githubMobileContainer,!1)):(this.safeSetElementVisibility(this.githubMobileLoading,!0),this.safeSetElementVisibility(this.githubMobileLanding,!1),this.safeSetElementVisibility(this.githubMobileContainer,!1),this.initiateGitHubMobileAuthRequest())}renderTotpAppOption(){this.safeSetElementVisibility(this.totpAppContainer,!0)}renderTotpEmailOption(){this.safeSetElementVisibility(this.totpEmailContainer,!0)}renderPasswordOption(){this.safeSetElementVisibility(this.passwordContainer,!0),this.loginField?this.loginField.focus():this.passwordField?.focus()}hasMultipleOptions(){return this.isWebAuthnAvailable()||this.isGitHubMobileAvailable()||this.isTotpAppAvailable()||this.isTotpEmailAvailable()}isWebAuthnAvailable(){return"true"===this.webauthnAvailable}isGitHubMobileAvailable(){return"true"===this.githubMobileAvailable}isTotpAppAvailable(){return"true"===this.totpAppAvailable}isTotpEmailAvailable(){return"true"===this.totpEmailAvailable}showWebauthn(){ik(this,iM,ix.WebAuthn),this.reRenderPrompt()}showGitHubMobile(){ik(this,iM,ix.GitHubMobile),this.reRenderPrompt()}showTotpApp(){ik(this,iM,ix.TotpApp),this.reRenderPrompt()}showTotpEmail(){ik(this,iM,ix.TotpEmail),this.reRenderPrompt()}showEmailConfirm(){if(iC(this,iM)!==ix.TotpEmail)return;let e=document.getElementById("email-landing-container"),t=document.getElementById("sudo-send-email");this.safeSetElementVisibility(e,!1),t?.setAttribute("disabled","true");let n=document.getElementById("email-confirm-container");this.safeSetElementVisibility(n,!0)}showPassword(){ik(this,iM,ix.Password),this.reRenderPrompt()}githubMobileRetry(e){e.preventDefault(),this.showGitHubMobile()}async initiateGitHubMobileAuthRequest(){let e=this.githubMobilePromptUrl,t=document.getElementById("sudo-credential-options-github-mobile-csrf").value,n=new FormData;n.append("authenticity_token",t);try{let t=await fetch(e,{method:"POST",headers:{...(0,x.kt)()},body:n});if(!t.ok&&iC(this,iM)===ix.GitHubMobile)return void this.mobileFailHandler(this.githubMobileGenericErrorMessage);let r=await t.json(),i=!!r.challenge;this.safeSetElementVisibility(this.githubMobileNoChallengeMessage,!i),this.safeSetElementVisibility(this.githubMobileChallengeMessage,i),this.safeSetElementVisibility(this.githubMobileChallengeValue,i),i&&(this.githubMobileChallengeValue.textContent=r.challenge);let a=document.getElementsByClassName("js-poll-github-mobile-sudo-authenticate")[0];(0,ij.R1)(a,()=>this.mobileApprovedHandler(),e=>this.mobileFailHandler(e),()=>this.mobileCancelCheck())}catch{iC(this,iM)===ix.GitHubMobile&&this.mobileFailHandler(this.githubMobileGenericErrorMessage)}finally{iC(this,iM)===ix.GitHubMobile&&(this.safeSetElementVisibility(this.githubMobileLoading,!1),this.safeSetElementVisibility(this.githubMobileContainer,!0))}}mobileApprovedHandler(){if(iC(this,iM)===ix.GitHubMobile){let e=this.githubMobileContainer.getElementsByTagName("form")[0];(0,C.k_)(e)}}mobileFailHandler(e){iC(this,iM)===ix.GitHubMobile&&(this.showErrorMessage(e),(0,ij.Cg)())}mobileCancelCheck(){return iC(this,iM)!==ix.GitHubMobile}async initiateTotpEmailRequest(){let e=this.totpEmailInitiateUrl,t=document.getElementById("sudo-credential-options-totp-email-csrf").value,n=new FormData;n.append("authenticity_token",t);try{if(!(await fetch(e,{method:"POST",headers:{...(0,x.kt)()},body:n})).ok&&iC(this,iM)===ix.TotpEmail)return}catch{return}this.showEmailConfirm()}safeSetElementVisibility(e,t){return!!e&&(e.hidden=!t,!0)}showErrorMessage(e){this.flashErrorMessageText&&(this.flashErrorMessageText.textContent=e,this.safeSetElementVisibility(this.flashErrorMessageContainer,!0))}hideErrorMessage(){this.flashErrorMessageText&&(this.flashErrorMessageText.textContent=""),this.safeSetElementVisibility(this.flashErrorMessageContainer,!1)}constructor(...e){super(...e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,iM,{writable:!0,value:void 0})}};!function(e,t,n){t in e?Object.defineProperty(e,t,{value:"",enumerable:!0,configurable:!0,writable:!0}):e[t]=""}(SudoCredentialOptionsElement,"attrPrefix",""),iq([iS.CF],SudoCredentialOptionsElement.prototype,"initialState",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"webauthnAvailable",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"githubMobileAvailable",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"totpAppAvailable",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"totpEmailAvailable",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"githubMobilePromptUrl",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"githubMobileGenericErrorMessage",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"totpEmailInitiateUrl",void 0),iq([iS.CF],SudoCredentialOptionsElement.prototype,"genericErrorMessage",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"flashErrorMessageContainer",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"flashErrorMessageText",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"webauthnContainer",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileContainer",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileLoading",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileLanding",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"totpAppContainer",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"totpEmailContainer",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"passwordContainer",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileNoChallengeMessage",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileChallengeMessage",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileChallengeValue",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"webauthnNav",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"githubMobileNav",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"totpAppNav",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"totpEmailNav",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"passwordNav",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"webauthnGet",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"loginField",void 0),iq([iS.aC],SudoCredentialOptionsElement.prototype,"passwordField",void 0),SudoCredentialOptionsElement=iq([iS.p_],SudoCredentialOptionsElement);let i_=0,iH="IntersectionObserver"in window?new IntersectionObserver(function(e){for(let t of e)t.isIntersecting&&iP(t.target)},{root:null,rootMargin:"0px",threshold:1}):null;function iP(e){e.classList.remove("js-unread-item","unread-item")}(0,b.lB)(".js-unread-item",{constructor:HTMLElement,add(e){i_++,iH&&iH.observe(e)},remove(e){i_--,iH&&iH.unobserve(e),0===i_&&function(){if(!document.hasFocus())return;let e=document.querySelector(".js-timeline-marker-form");e&&e instanceof HTMLFormElement&&(0,C.k_)(e)}()}}),(0,b.lB)(".js-discussion[data-channel-target]",{subscribe:e=>(0,O.Rt)(e,"socket:message",function(e){let t=e.target,n=e.detail.data;if(t.getAttribute("data-channel-target")===n.gid)for(let e of document.querySelectorAll(".js-unread-item"))iP(e)})});let iO=0,iI=/^\(\d+\)\s+/;function iR(){let e=iO?`(${iO}) `:"";document.title.match(iI)?document.title=document.title.replace(iI,e):document.title=`${e}${document.title}`}(0,b.lB)(".js-unread-item",{add(){iO++,iR()},remove(){iO--,iR()}});let i$=(0,n(27932).R)();async function iB(){let e=(0,q.JV)();if(e.staleRecords){for(let t in await J.G,e.staleRecords)for(let n of document.querySelectorAll(`.js-updatable-content [data-url='${t}'], .js-updatable-content[data-url='${t}']`)){let r=e.staleRecords[t];n instanceof HTMLElement&&r&&((0,ec.Hb)(r)?(0,ec.Uv)(n,r,!0):delete e.staleRecords[t])}(0,q.bj)(location.href)}}(0,b.lB)(".js-socket-channel.js-updatable-content",{subscribe:e=>(0,O.Rt)(e,"socket:message",i$)}),window.addEventListener("pagehide",ec.jH);try{iB()}catch{}(0,p.on)("upload:setup",".js-upload-avatar-image",function(e){let{form:t}=e.detail,n=e.currentTarget.getAttribute("data-alambic-organization"),r=e.currentTarget.getAttribute("data-alambic-owner-type"),i=e.currentTarget.getAttribute("data-alambic-owner-id");n&&t.append("organization_id",n),r&&t.append("owner_type",r),i&&t.append("owner_id",i)}),(0,p.on)("upload:complete",".js-upload-avatar-image",function(e){let{attachment:t}=e.detail,n=`/settings/avatars/${t.id}`;(0,ta.r)({content:(0,to.Ts)(document,n),detailsClass:"upload-avatar-details"})}),(0,p.on)("dialog:remove",".upload-avatar-details",async function(e){let t=e.currentTarget.querySelector("#avatar-crop-form").getAttribute("data-alambic-avatar-id"),n=new Request(`/settings/avatars/${t}?op=destroy`,{method:"POST",headers:{"Scoped-CSRF-Token":e.currentTarget.querySelector(".js-avatar-post-csrf").getAttribute("value"),...(0,x.kt)()}});await self.fetch(n)});var iN=n(8447);function iD(){if(!(0,iN.A)()||document.querySelector(":target"))return;let e=(0,nK.gX)(location.hash),t=e.startsWith("user-content-")?e:`user-content-${e}`,n=(0,nK.w$)(document,t)??(0,nK.w$)(document,t.toLowerCase());n&&(0,tY.Rt)(n)}async function iW(){await n.e("app_assets_modules_github_user-status-submit_ts").then(n.bind(n,30195))}window.addEventListener("hashchange",iD),document.addEventListener("turbo:load",iD),async function(){await J.G,iD()}(),(0,p.on)("click","a[href]",function(e){let{currentTarget:t}=e;t instanceof HTMLAnchorElement&&t.href===location.href&&location.hash.length>1&&setTimeout(function(){e.defaultPrevented||iD()})}),n(82624),(0,b.lB)(".js-user-status-container, .js-load-user-status-submit",{subscribe:e=>(0,O.Rt)(e,"click",iW,{once:!0})}),(0,b.lB)(".user-status-dialog-fragment",{add:iW});var iF=n(5221);function iU(e,t){for(let n of(t||e).querySelectorAll(".js-user-list-error"))n.hidden=!0;for(let n of t?[t]:e.querySelectorAll(".errored.js-user-list-input-container"))n.classList.remove("errored");let n=e.querySelector(".js-user-list-base");n&&(n.hidden=!0)}function iz(e){if(!(e.currentTarget instanceof HTMLElement))return;let t=e.currentTarget.closest(".js-user-list-form"),n=e.currentTarget.closest(".js-user-list-input-container");t&&n&&iU(t,n)}async function iV(e,t,n){let r=new FormData;for(let e of(r.set("authenticity_token",t),n))r.append("repository_ids[]",e);let i=await fetch(e,{method:"POST",body:r,headers:{Accept:"application/json",...(0,x.kt)()}}),a=new Map;if(i.ok){let e=await i.json();for(let t in e)a.set(t,(0,k.B)(document,e[t]))}return a}async function iG(){let e=document.querySelectorAll(".js-user-list-menu-content-root");if(0===e.length)return;let t=e[0].getAttribute("data-batch-update-url");if(!t)return;let n=e[0].querySelector(".js-user-list-batch-update-csrf")?.value;if(!n)return;let r=function(e){let t=new Map;for(let n of e){let e=n.querySelector(".js-user-lists-create-trigger")?.getAttribute("data-repository-id");if(e){let r=t.get(e);r?r.push(n):t.set(e,[n])}}return t}(e),i=r.keys(),a=await iV(t,n,i);a.size>0&&function(e,t){for(let[n,r]of e.entries()){let e=t.get(n)||[];for(let t of e)t.replaceWith(1===e.length?r:r.cloneNode(!0))}}(a,r)}function iX(e){let t=e.currentTarget;(0,iF.Av)(t)?function(e){let t=e.getAttribute("data-warn-unsaved-changes")||"Changes you made may not be saved.";window.onbeforeunload=function(e){return e.returnValue=t,t}}(t):iK()}function iK(){window.onbeforeunload=null}function iJ({currentTarget:e}){e.hasAttribute("open")||iK()}function iY(e){let t=e.currentTarget;if(!t.closest("details[open]"))return;let n=!0;for(let e of t.querySelectorAll("form[data-warn-unsaved-changes]"))if((0,iF.Av)(e)){n=confirm(e.getAttribute("data-warn-unsaved-changes"));break}n||e.preventDefault()}function iZ(e){e.target.classList.remove("will-transition-once")}async function iQ(e){let t=e.currentTarget,n=t.getAttribute("data-url");if(!n||function(e){switch(e.getAttribute("data-hovercard-type")){case"issue":case"pull_request":return!!e.closest("[data-issue-and-pr-hovercards-enabled]");case"discussion":return!!e.closest("[data-discussion-hovercards-enabled]");default:return!1}}(t))return;let r=t.getAttribute("data-id")||"",i=t.textContent,a=document.querySelectorAll(`.js-issue-link[data-id='${r}']`);for(let e of a)e.removeAttribute("data-url");try{let e=`${n}/title`,t=await fetch(e,{headers:{...(0,x.kt)(),Accept:"application/json"}});if(!t.ok){let e=Error(),n=t.statusText?` ${t.statusText}`:"";throw e.message=`HTTP ${t.status}${n}`,e}let r=await t.json();i0(a,`${i}, ${r.title}`)}catch(e){i0(a,(404===((null!=e.response?e.response.status:void 0)||500)?t.getAttribute("data-permission-text"):t.getAttribute("data-error-text"))||"")}}function i0(e,t){for(let n of e)n instanceof HTMLElement&&(n.classList.add("tooltipped","tooltipped-ne"),n.setAttribute("aria-label",t))}(0,g.JW)(".js-user-list-form",async function(e,t){iU(e);let n=e.querySelector("[data-submitting-message]"),r=n?.textContent;for(let t of(n&&(n.textContent=n.getAttribute("data-submitting-message"),n.disabled=!0),e.querySelectorAll(".js-user-list-input")))t.disabled=!0;try{let n=await t.html();(0,p.h)(e,"user-list-form:success",n.html)}catch(t){if(t.response?.status===422)e.replaceWith(t.response.html);else{let t=e.querySelector(".js-user-list-base");for(let i of(t&&(t.textContent=t.getAttribute("data-generic-message"),t.hidden=!1),n&&(r&&(n.textContent=r),n.disabled=!1),e.querySelectorAll(".js-user-list-input")))i.disabled=!1}}}),(0,p.on)("user-list-form:success",".js-follow-list",e=>{let t=e.detail,n=t instanceof DocumentFragment?t.querySelector(".js-target-url"):null;n?.href?location.href=n.href:location.reload()}),(0,H.eC)(".js-user-list-form input",iz),(0,H.eC)(".js-user-list-form textarea",iz),(0,p.on)("auto-check-error",".js-user-list-form input",function(e){let t=e.currentTarget.closest(".js-user-list-input-container"),n=t?.querySelector(".js-user-list-error");n&&(n.hidden=!1)}),(0,p.on)("toggle",".js-user-list-menu",function(e){let t=e.target;if(!(t instanceof HTMLDetailsElement)||t.hasAttribute("open"))return;let n=t.querySelector(".js-user-list-menu-form");n&&(0,iF.Av)(n)&&(0,C.k_)(n);let r=t.querySelector(".js-user-list-create-trigger-text");r&&(r.textContent="")},{capture:!0}),(0,H.eC)(".js-user-lists-menu-filter",e=>{let t=e.currentTarget,n=t.value.trim(),r=t.closest(".js-user-list-menu-content-root"),i=r?.querySelector(".js-user-list-create-trigger-text");i&&(i.textContent=n?`"${n}"`:"")}),(0,g.JW)(".js-user-list-menu-form",async function(e,t){let n;try{n=await t.json()}catch(t){(0,h.n)(),(0,p.h)(e,"user-list-menu-form:error",t);return}if(n.json.didStar){let t=e.closest(".js-toggler-container");t&&t.classList.add("on");let r=n.json.starCount;if(r){let t=e.closest(".js-social-container");t&&n1(t,r)}}let r=e.closest(".js-user-list-menu-content-root[data-update-after-submit]");if(r)for(let t of e.querySelectorAll(".js-user-list-menu-item"))t.checked=t.defaultChecked;n.json.didCreate?await iG():r&&await (0,ec.le)(r),(0,p.h)(e,"user-list-menu-form:success")}),(0,p.on)("click",".js-user-list-delete-confirmation-trigger",e=>{let{currentTarget:t}=e,n=t.getAttribute("data-template-id");if(!n)return;let r=document.getElementById(n);if(!r||!(r instanceof HTMLTemplateElement))return;let i=t.closest(".js-edit-user-list-dialog");i&&(i.open=!1);let a=r.content.cloneNode(!0),o=r.getAttribute("data-labelledby");(0,ta.r)({content:a,labelledBy:o})}),(0,p.on)("click",".js-user-lists-create-trigger",async function(e){let{currentTarget:t}=e,n=document.querySelector(".js-user-list-create-dialog-template"),r=e.currentTarget.getAttribute("data-repository-id"),i=t.closest(".js-user-list-menu-content-root"),a=i?.querySelector(".js-user-lists-menu-filter"),o=a?.value.trim(),s=i?.closest(".js-user-list-menu");if(!n||!(n instanceof HTMLTemplateElement)||!r){t instanceof HTMLButtonElement&&(t.disabled=!0);return}let l=n.getAttribute("data-label");if(i&&(0,iF.Av)(i)){let e=i.querySelector(".js-user-list-menu-form");e&&await function(e){let t=new Promise((t,n)=>{e.addEventListener("user-list-menu-form:success",()=>t()),e.addEventListener("user-list-menu-form:error",e=>n(e))});return(0,C.k_)(e),t}(e)}let c=new t0.i4(n,{repositoryId:r,placeholderName:o}),u=await (0,ta.r)({content:c,label:l});s&&(s.open=!1),u.addEventListener("user-list-form:success",async e=>{let n=e.detail;if(!(n instanceof DocumentFragment))return;let r=n.querySelector(".js-target-url"),i=r?.getAttribute("data-did-star")==="true",a=u.closest("details");if(!i){a&&(a.open=!1),await iG();return}let o=t.closest(".js-toggler-container");o&&o.classList.add("on");let s=r?.getAttribute("data-star-count");if(s){let e=t.closest(".js-social-container");e&&n1(e,s)}await iG(),a&&(a.open=!1)})}),(0,b.lB)("[data-warn-unsaved-changes]",{add(e){e.addEventListener("input",iX),e.addEventListener("change",iX),e.addEventListener("submit",iK);let t=e.closest("details-dialog");t&&(t.closest("details").addEventListener("toggle",iJ),t.addEventListener("details-dialog-close",iY))},remove(e){e.removeEventListener("input",iX),e.removeEventListener("change",iX),e.removeEventListener("submit",iK);let t=e.closest("details-dialog");t&&(t.closest("details").removeEventListener("toggle",iJ),t.removeEventListener("details-dialog-close",iY),iK())}}),(0,b.lB)(".will-transition-once",{constructor:HTMLElement,subscribe:e=>(0,O.Rt)(e,"transitionend",iZ)}),(0,b.lB)(".js-issue-link",{subscribe:e=>(0,O.Rt)(e,"mouseenter",iQ)});var i1=n(10734),i2=n.n(i1);function i5(){return[Math.floor(255*Math.random()+0),Math.floor(255*Math.random()+0),Math.floor(255*Math.random()+0)]}function i3(e,t){let n=i2().rgb.hsl(t);e.style.setProperty("--label-r",t[0].toString()),e.style.setProperty("--label-g",t[1].toString()),e.style.setProperty("--label-b",t[2].toString()),e.style.setProperty("--label-h",n[0].toString()),e.style.setProperty("--label-s",n[1].toString()),e.style.setProperty("--label-l",n[2].toString())}function i7(e,t){e.blur();let n=e.closest("form"),r=n.querySelector(".js-new-label-color-input");(0,C.m$)(r,`#${i2().rgb.hex(t)}`),i3(n.querySelector(".js-new-label-color"),t)}function i4(e,t,n){var r;let i=t.querySelector(e);i&&(n?(r=n[0],i.closest(".js-label-error-container").classList.add("errored"),i.textContent=r,i.hidden=!1):(i.closest(".js-label-error-container").classList.remove("errored"),i.hidden=!0))}function i9(e,t){i4(".js-label-name-error",e,t.name),i4(".js-label-description-error",e,t.description),i4(".js-label-color-error",e,t.color)}function i6(e){i4(".js-label-name-error",e,null),i4(".js-label-description-error",e,null),i4(".js-label-color-error",e,null)}async function i8(e){let t,n,r=e.closest(".js-label-preview-container");if(!r)return;let i=e.closest(".js-label-form"),a=i.querySelector(".js-new-label-error"),o=i.getAttribute("data-label-id"),s=r.querySelector(".js-label-preview"),l=((n=i.querySelector(".js-new-label-name-input").value.trim()).length<1&&(n=s.getAttribute("data-default-name")),n);if(!i.checkValidity()&&"Label preview"!==l)return;let c=function(e){let t=e.querySelector(".js-new-label-color-input");return t.checkValidity()?t.value.trim().replace(/^#/,""):"ededed"}(i),u=function(e){let t=null,n=e.querySelector(".js-new-label-description-input");return n instanceof HTMLInputElement&&n.value.trim().length>0&&(t=n.value.trim()),t}(i),d=function(e,t,n,r,i){let a=new URL(`${e}${encodeURIComponent(t)}`,window.location.origin),o=new URLSearchParams(a.search.slice(1));return o.append("color",n),r&&o.append("description",r),i&&o.append("id",i),a.search=o.toString(),a.toString()}(s.getAttribute("data-url-template"),l,c,u,o);if(!r.hasAttribute("data-last-preview-url")||d!==r.getAttribute("data-last-preview-url")){try{t=await (0,to.Ts)(document,d)}catch(t){let e=await t.response.json();i9(i,e),a&&(a.textContent=e.message,a.hidden=!1);return}a&&(a.textContent="",a.hidden=!0),i6(i),s.textContent="",s.appendChild(t),r.setAttribute("data-last-preview-url",d)}}function ae(e,t){e.closest(".js-details-container").classList.toggle("is-empty",t)}function at(e){let t=document.querySelector(".js-labels-count"),n=Number(t.textContent)+e;t.textContent=n.toString();let r=document.querySelector(".js-labels-label");return r.textContent=r.getAttribute(1===n?"data-singular-string":"data-plural-string"),n}async function an(e){let t=e.querySelector(".js-new-label-name-input");if(!t)return;let n=e.querySelector(".js-new-label-color-input"),r=i5();n.value=`#${i2().rgb.hex(r)}`;let i=e.querySelector(".js-new-label-color");i3(i,r);let a=document.querySelector(".js-new-label-name").textContent;(0,C.m$)(t,a),(0,ee.mY)(t),i8(i)}(0,H.eC)(".js-label-filter-field",function(e){let t=e.target,n=t.closest("details-menu").querySelector(".js-new-label-name");n&&(n.textContent=t.value.trim())}),(0,p.on)("filterable:change",".js-filterable-issue-labels",function(e){let t=e.currentTarget.closest("details-menu"),n=t.querySelector(".js-add-label-button");if(!n)return;let r=e.detail.inputField.value.trim().toLowerCase(),i=!1;for(let e of t.querySelectorAll("input[data-label-name]"))if((e.getAttribute("data-label-name")||"").toLowerCase()===r){i=!0;break}n.hidden=0===r.length||i}),(0,H.uE)(".js-new-label-color-input",function(e){let t=e.closest("form").querySelector(".js-new-label-swatches");t.hidden=!1,e.addEventListener("blur",function(){t.hidden=!0},{once:!0})}),(0,H.eC)(".js-new-label-color-input",function(e){let t=e.target,n=t.value.trim();n.length<1||(0!==n.indexOf("#")&&(t.value=n=`#${n}`),t.checkValidity()?(t.classList.remove("color-fg-danger"),i3(t.closest("form").querySelector(".js-new-label-color"),i2().hex.rgb(n))):t.classList.add("color-fg-danger"))}),(0,H.Ff)("keyup",".js-new-label-color-input",function(e){let t=e.target,n=t.value.trim();0!==n.indexOf("#")&&(t.value=n=`#${n}`),t.checkValidity()&&i3(t.closest("form").querySelector(".js-new-label-color"),i2().hex.rgb(n)),(0,p.h)(t,"change",!1),i6(t.closest("form"))}),(0,H.Ff)("keyup",".js-new-label-description-input",function(e){i6(e.target.form)}),(0,H.Ff)("keyup",".js-new-label-color-input",function(e){i6(e.target.form)}),(0,p.on)("click",".js-new-label-color",async function(e){let t=e.currentTarget;i7(t,i5()),i8(t)}),(0,p.on)("mousedown",".js-new-label-color-swatch",function(e){let t=e.currentTarget,n=t.getAttribute("data-color");i7(t,i2().hex.rgb(n)),i8(t),t.closest(".js-new-label-swatches").hidden=!0}),(0,p.on)("toggle",".js-new-label-modal",function(e){e.target.hasAttribute("open")&&an(e.target)},{capture:!0}),(0,g.JW)(".js-new-label-modal-form",async function(e,t){let n,r=e.querySelector(".js-new-label-error");try{n=await t.html()}catch(e){r.textContent=e.response.json.message,r.hidden=!1}if(!n)return;r.hidden=!0,document.querySelector(".js-new-label-modal").removeAttribute("open");let i=document.querySelector(".js-issue-labels-menu-content"),a=i.querySelector(".js-filterable-issue-labels"),o=n.html.querySelector("input");a.prepend(n.html),a.classList.add("filter-sort-list-refresh"),o&&o.dispatchEvent(new Event("change",{bubbles:!0}));let s=i.querySelector(".js-label-filter-field");s.value=s.defaultValue,s.focus()}),(0,p.on)("click",".js-edit-label-cancel",function(e){let t=e.target.closest("form");i6(t),t.reset();let n=t.querySelector(".js-new-label-color-input"),r=n.value;i3(t.querySelector(".js-new-label-color"),i2().hex.rgb(r)),(0,ee.ig)(t),i8(n);let i=e.currentTarget.closest(".js-labels-list-item");if(i){i.querySelector(".js-update-label").classList.add("d-none");let e=i.querySelector(".js-label-preview");for(let t of(e&&(e.classList.add("d-none"),i.querySelector(".js-label-link").classList.remove("d-none")),i.querySelectorAll(".js-hide-on-label-edit")))t.hidden=!t.hidden}}),(0,g.JW)(".js-update-label",async function(e,t){let n;try{n=await t.html()}catch(t){i9(e,t.response.json);return}i6(e),e.closest(".js-labels-list-item").replaceWith(n.html)}),(0,g.JW)(".js-create-label",async function(e,t){let n;try{n=await t.html()}catch(t){i9(e,t.response.json);return}e.reset(),i6(e),document.querySelector(".js-label-list").prepend(n.html),at(1),ae(e,!1),i7(e.querySelector(".js-new-label-color"),i5()),i8(e.querySelector(".js-new-label-name-input")),(0,ee.ig)(e);let r=e.closest(".js-details-container");r instanceof HTMLElement&&(0,eQ.kn)(r)}),(0,p.on)("click",".js-details-target-new-label",function(){document.querySelector(".js-create-label").querySelector(".js-new-label-name-input").focus()}),(0,p.on)("click",".js-edit-label",function(e){let t=e.currentTarget.closest(".js-labels-list-item"),n=t.querySelector(".js-update-label");n.classList.remove("d-none"),n.querySelector(".js-new-label-name-input").focus();let r=t.querySelector(".js-label-preview");for(let e of(r&&(r.classList.remove("d-none"),t.querySelector(".js-label-link").classList.add("d-none")),t.querySelectorAll(".js-hide-on-label-edit")))e.hidden=!e.hidden}),(0,g.JW)(".js-delete-label",async function(e,t){let n=e.closest(".js-labels-list-item");n.querySelector(".js-label-delete-spinner").hidden=!1,await t.text(),ae(e,0===at(-1)),n.remove()});let ar=(0,P.s)(function(e){i8(e.target)},500);function ai(){let e=document.querySelector(".js-reveal-custom-thread-settings").checked,t=!document.querySelector(".js-custom-thread-notification-option:checked"),n=document.querySelector(".js-custom-thread-settings"),r=document.querySelector("[data-custom-option-required-text]"),i=e&&t?r.getAttribute("data-custom-option-required-text"):"";r.setCustomValidity(i),n.hidden=!e}function aa(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o}(0,p.on)("suggester:complete",".js-new-label-name-input",ar),(0,H.eC)(".js-new-label-name-input",ar),(0,H.eC)(".js-new-label-description-input",ar),(0,H.eC)(".js-new-label-color-input",ar),(0,H.Ff)("keypress",".js-new-label-name-input",function(e){let t=e.target,n=parseInt(t.getAttribute("data-maxlength"));(0,eh.bV)(t.value)>=n&&e.preventDefault()}),(0,p.on)("click",".js-issues-label-select-menu-item",function(e){(e.altKey||e.shiftKey)&&(e.preventDefault(),e.stopPropagation(),e.altKey&&(window.location.href=new URL(e.currentTarget.getAttribute("data-excluded-url"),window.location.origin).toString()),e.shiftKey&&(window.location.href=new URL(e.currentTarget.getAttribute("data-included-url"),window.location.origin).toString()))}),(0,H.Ff)("keydown",".js-issues-label-select-menu-item",function(e){if("Enter"!==e.key||!e.altKey&&!e.shiftKey)return;let t=e.currentTarget;e.preventDefault(),e.stopPropagation(),t instanceof HTMLAnchorElement&&(e.altKey&&(window.location.href=new URL(t.getAttribute("data-excluded-url"),window.location.origin).toString()),e.shiftKey&&(window.location.href=new URL(t.getAttribute("data-included-url"),window.location.origin).toString()))}),(0,p.on)("click",".js-open-label-creation-modal",async function(e){e.stopImmediatePropagation(),an(await (0,ta.r)({content:document.querySelector(".js-label-creation-template").content.cloneNode(!0),detailsClass:"js-new-label-modal"}))},{capture:!0}),(0,p.on)("change",".js-thread-notification-setting",ai),(0,p.on)("change",".js-custom-thread-notification-option",ai),(0,p.on)("reset",".js-custom-thread-settings-form",ai);let ao=class CollapsibleSidebarWidgetElement extends HTMLElement{get activeClass(){return this.getAttribute("active-class")||"collapsible-sidebar-widget-active"}get loadingClass(){return this.getAttribute("loading-class")||"collapsible-sidebar-widget-loading"}get url(){return this.getAttribute("url")||""}get isOpen(){return this.hasAttribute("open")}set isOpen(e){e?this.setAttribute("open",""):this.removeAttribute("open")}onKeyDown(e){if("Enter"===e.code||"Space"===e.code)return e.preventDefault(),this.load()}onMouseDown(e){return e.preventDefault(),this.load()}load(){return this.pendingRequest?this.pendingRequest.abort():this.collapsible.hasAttribute("loaded")?this.isOpen?this.setClose():this.setOpen():(this.setLoading(),this.updateCollapsible())}setLoading(){this.classList.add(this.loadingClass),this.classList.remove(this.activeClass)}setOpen(){this.classList.add(this.activeClass),this.classList.remove(this.loadingClass),this.isOpen=!0}setClose(){this.classList.remove(this.activeClass),this.classList.remove(this.loadingClass),this.isOpen=!1}handleAbort(){this.pendingRequest=null,this.setClose()}async updateCollapsible(){try{this.pendingRequest=new AbortController,this.pendingRequest.signal.addEventListener("abort",()=>this.handleAbort());let e=await fetch(this.url,{signal:this.pendingRequest?.signal,headers:{Accept:"text/html",...(0,x.kt)()}});if(this.pendingRequest=null,!e.ok)return this.setClose();let t=await e.text();this.collapsible.innerHTML=t,this.collapsible.setAttribute("loaded",""),this.setOpen()}catch{return this.pendingRequest=null,this.setClose()}}};function as(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function al(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o}aa([iS.aC],ao.prototype,"collapsible",void 0),ao=aa([iS.p_],ao);let ac=class SidebarMemexInputElement extends HTMLElement{get isDisabled(){return this.read?.hasAttribute("disabled")}set hasErrored(e){e?this.setAttribute("errored",""):this.removeAttribute("errored")}set disabled(e){e?this.setAttribute("disabled",""):this.removeAttribute("disabled")}get hasExpanded(){return"true"===this.read.getAttribute("aria-expanded")}get detailsElement(){return this.querySelector("details")??null}connectedCallback(){this.disabled=this.read?.disabled??!0,this.detailsElement?.addEventListener("toggle",()=>this.handleSelectMenuToggle())}disconnectedCallback(){this.detailsElement?.removeEventListener("toggle",()=>this.handleSelectMenuToggle())}handleSelectMenuToggle(){this.detailsElement&&!this.detailsElement?.open?this.disabled=!0:this.detailsElement&&this.detailsElement?.open&&(this.disabled=!1)}handleDetailsSelect(e){let t=e.target,n=e.detail?.relatedTarget,r=t.closest("details"),i=r?.querySelector("[data-menu-button]"),a=r?.querySelector("summary");if("true"===n.getAttribute("aria-checked")){for(let t of(n.setAttribute("aria-checked","false"),e.preventDefault(),this.inputs))if(n.contains(t)){this.updateCell(t.name,""),i?.innerHTML&&(i.innerHTML=t.placeholder);break}r?.removeAttribute("open"),a?.focus()}}handleDetailsSelected(e){let t=e.detail?.relatedTarget;for(let e of this.inputs)if(t.contains(e)){this.updateCell(e.name,e.value);break}}mouseDownFocus(e){this.isDisabled&&this.onFocus(e)}keyDownFocus(e){("Enter"===e.code||"Space"===e.code)&&(this.detailsElement&&this.onSelectMenuOpen(),this.read!==document.activeElement&&this.onFocus(e))}mouseDownFocusHeader(){this.detailsElement&&this.onSelectMenuOpen()}onChange(e){"date"!==e.target.getAttribute("type")&&this.updateCell(this.read?.name,this.read?.value)}onFocus(e){e.preventDefault(),this.disabled=!1,this.read.disabled=!1,this.read.focus()}onSelectMenuOpen(){this.detailsElement&&(this.detailsElement.open=!0)}onBlur(e){if(this.hasExpanded)return void e.preventDefault();"date"===e.target.getAttribute("type")&&this.updateCell(this.read?.name,this.read?.value),this.read.disabled=!0,this.disabled=!0}onKeyDown(e){("Enter"===e.code||"Tab"===e.code)&&(e.preventDefault(),e.stopPropagation(),this.hasExpanded||this.read.blur())}async updateCell(e="",t=""){let n=new FormData;for(let r of(n.set(e,t),n.set("ui",this.instrumentType),this.parameters))n.set(r.name,r.value);try{if(this.write){let e=this.read.value,t="date"===this.read.type&&e?this.format.format(Date.parse(e)):e;this.write.textContent=e?t:this.read.placeholder}let e=await fetch(this.updateUrl,{method:"PUT",body:n,headers:{Accept:"application/json",...(0,x.kt)(),"Scoped-CSRF-Token":`${this.csrfToken}`}});if(!e.ok)throw Error("connection error");if(!this.write)return;let r=(await e.json()).memexProjectItem.memexProjectColumnValues.find(e=>e.memexProjectColumnId===Number(this.columnId)).value,i=this.parseAndFormatUpdate(r);this.write.innerHTML=t?i:this.read.placeholder}catch{this.hasErrored=!0}}parseAndFormatUpdate(e){switch(this.read.type){case"date":{let t=e.value?Date.parse(e.value):void 0;return t?this.format.format(t):""}case"number":return null==e.value?"":e.value;default:return e.html??""}}constructor(...e){super(...e),as(this,"updateUrl",""),as(this,"csrfToken",""),as(this,"instrumentType",""),as(this,"columnId",1),as(this,"format",Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric",timeZone:"UTC"}))}};as(ac,"attrPrefix",""),al([iS.CF],ac.prototype,"updateUrl",void 0),al([iS.CF],ac.prototype,"csrfToken",void 0),al([iS.CF],ac.prototype,"instrumentType",void 0),al([iS.CF],ac.prototype,"columnId",void 0),al([iS.zV],ac.prototype,"inputs",void 0),al([iS.aC],ac.prototype,"read",void 0),al([iS.aC],ac.prototype,"write",void 0),al([iS.zV],ac.prototype,"parameters",void 0),ac=al([iS.p_],ac);var au=n(95105);function ad(e,t=!1){(t||!function(e){let t=e.getAttribute("data-reviewers-team-size-check-url");if(!t)return!1;let n=[...document.querySelectorAll(".js-reviewer-team")].map(e=>e.getAttribute("data-id")),r=new URLSearchParams(e instanceof HTMLFormElement?new FormData(e):aE(e)).getAll("reviewer_team_ids[]").filter(e=>!n.includes(e));if(0===r.length)return!1;let i=new URLSearchParams(r.map(e=>["reviewer_team_ids[]",e]));return aw(e,`${t}?${i}`),!0}(e))&&(e instanceof HTMLFormElement?(0,C.k_)(e):av(e))}function am(e){let t=e.currentTarget;ad(t.closest(".js-issue-sidebar-form")||t.querySelector(".js-issue-sidebar-form"))}function af(e,t){let n=e.querySelector(`[data-menu-trigger="${t}"]`);n?.focus()}(0,p.on)("details-menu-selected",".js-discussion-sidebar-menu",function(e){let t=e.detail.relatedTarget,n=e.currentTarget,r=t.closest(".js-issue-sidebar-form"),i=n.hasAttribute("data-multiple");if(t.hasAttribute("data-clear-assignees")){for(let e of n.querySelectorAll('input[name="issue[user_assignee_ids][]"]:checked'))e.disabled=!1,e.checked=!1;ad(r)}else i?n.closest("details").addEventListener("toggle",am,{once:!0}):ad(r)},{capture:!0}),(0,g.JW)(".js-issue-sidebar-form",async function(e,t){let n;try{let n=await t.html(),r=e.closest(".js-discussion-sidebar-item"),i=r?.querySelector(".select-menu")?.getAttribute("id"),a=r?.parentElement;r.replaceWith(n.html),a&&i&&af(a,i)}catch(e){if(e instanceof Error)throw e}finally{e.dispatchEvent(new CustomEvent("submit:complete",{bubbles:!0,detail:{error:n}}))}}),(0,p.on)("click","div.js-issue-sidebar-form .js-suggested-reviewer",function(e){let t=e.currentTarget;av(t.closest(".js-issue-sidebar-form"),"post",{name:t.name,value:t.value}),e.preventDefault()}),(0,p.on)("click","div.js-issue-sidebar-form .js-issue-assign-self",function(e){let t=e.currentTarget;av(t.closest(".js-issue-sidebar-form"),"post",{name:t.name,value:t.value}),t.remove(),document.querySelector("form#new_issue .js-submit-button-value")?.remove(),e.preventDefault()}),(0,p.on)("click",".js-issue-unassign-self",function(e){av(e.currentTarget.closest(".js-issue-sidebar-form"),"delete"),e.preventDefault()});let ah=new Set;async function ap(e,t){let n=e.getAttribute("data-cache-name"),r=sessionStorage.getItem(t);if(!n||!r||ah.has(n))return;ah.add(n);let i=JSON.parse(r),a=[];for(let[t,n]of i){if("[object String]"!==Object.prototype.toString.call(n))continue;let r=document.createElement("input");r.type="hidden",r.value=n,r.name=t,e.appendChild(r),a.push(r)}try{for(let t of(await ay(e),a))t.remove()}catch{ah.delete(n)}}let ag=!1;function ab(e,t){if(ag)return;let n=aE(e);!function(e,t,n){let r=e.getAttribute("data-cache-name");if(!r)return;let i=[];for(let[e,n]of t.entries())-1!==e.indexOf(r)&&i.push([e,n]);let a=i.filter(e=>""!==e[1]);a.length>0?sessionStorage.setItem(n,JSON.stringify(a)):sessionStorage.removeItem(n)}(e,n,t),ah.clear()}async function av(e,t="post",n){await ay(e,t,n);let r=e.closest(".js-discussion-sidebar-item"),i=r?.querySelector(".select-menu")?.getAttribute("id"),a=r?.parentElement;a&&i&&af(a,i)}async function ay(e,t="post",n){var r;let i=aE(e);n&&i.append(n.name,n.value);let a=e.getAttribute("data-url");if(!a)return;let o=e.querySelector(".js-data-url-csrf"),s=await fetch(a,{method:t,body:"delete"===t?"":i,mode:"same-origin",headers:{"Scoped-CSRF-Token":o.value,...(0,x.kt)()}});if(!s.ok)return;let l=await s.text();r=e.closest(".js-discussion-sidebar-item"),r.replaceWith((0,k.B)(document,l))}async function aw(e,t){let n=await fetch(t);if(!n.ok)return;let r=await n.text();if(!r.match(/[^\w-]js-large-team[^\w-]/))return void ad(e,!0);var i=e,a=r;let o=i.querySelector(".js-large-teams-check-warning-container");for(;o.firstChild;)o.removeChild(o.firstChild);o.appendChild((0,k.B)(document,a));let s=o.querySelector("details");function l(e){if(e.target instanceof Element){if(s.open=!1,!e.target.classList.contains("js-large-teams-confirm-button"))for(let e of i.querySelectorAll("input[name='reviewer_team_ids[]']"))o.querySelector(`.js-large-team[data-id='${e.value}']`)&&(e.checked=!1);ad(i,!0),e.preventDefault()}}o.querySelector(".js-large-teams-confirm-button").addEventListener("click",l,{once:!0}),o.querySelector(".js-large-teams-cancel-button").addEventListener("click",l,{once:!0}),s.addEventListener("details-dialog-close",l,{once:!0}),s.open=!0}function aE(e){let t=e.closest("form");if(!t)return new FormData;let n=new FormData(t).entries(),r=new FormData;for(let[e,i]of n)t.contains(function(e,t,n){for(let r of e.elements)if((r instanceof HTMLInputElement||r instanceof HTMLTextAreaElement||r instanceof HTMLButtonElement)&&r.name===t&&r.value===n)return r;return null}(t,e,i.toString()))&&r.append(e,i);return r}(0,b.lB)("[data-cacher]",{add(e){let t=function(e,t){let n=e.getAttribute("data-cache-name");return`${t}:sidebar:${n}`}(e,(0,au.L)());ap(e,t),window.addEventListener("pagehide",()=>ab(e,t)),window.addEventListener("turbo:before-visit",()=>ab(e,t)),window.addEventListener("submit",e=>{e.defaultPrevented||(ag=!0,setTimeout(()=>{for(let e of Object.keys(sessionStorage))-1!==e.indexOf(t)&&(sessionStorage.removeItem(e),ah.clear())},0))},{capture:!0})}}),(0,p.on)("click",".js-prompt-dismiss",function(e){e.currentTarget.closest(".js-prompt").remove()}),(0,p.on)("click",".js-convert-to-draft",function(e){fetch(e.currentTarget.getAttribute("data-url"),{method:"POST",mode:"same-origin",headers:{"Scoped-CSRF-Token":e.currentTarget.parentElement.querySelector(".js-data-url-csrf").value,...(0,x.kt)()}})}),(0,p.on)("click",".js-dismiss-copilot-popover",function(e){fetch(e.currentTarget.getAttribute("data-url"),{method:"POST",mode:"same-origin",headers:{"Scoped-CSRF-Token":e.currentTarget.parentElement.querySelector(".js-data-url-csrf").value,...(0,x.kt)()}}),e.currentTarget.closest(".js-notice").remove(),e.preventDefault()}),(0,p.on)("click","div.js-restore-item",async function(e){let t=e.currentTarget.getAttribute("data-url"),n=e.currentTarget.getAttribute("data-column"),r=e.currentTarget.querySelector(".js-data-url-csrf"),i=new FormData;if(i.set("memexProjectItemIds[]",n),!(await fetch(t,{method:"PUT",mode:"same-origin",body:i,headers:{"Scoped-CSRF-Token":r.value,...(0,x.kt)()}})).ok)throw Error("connection error");am(e)}),(0,b.lB)("#clear-project-search-button",e=>{e?.setAttribute("type","button"),e?.addEventListener("click",()=>{let e=document.getElementById("project-search-input");e&&(e.value="",e.focus())})}),n(77153);let aS=Object.freeze({INITIAL:"soft-nav:external:initial",START:"soft-nav:external:start",SUCCESS:"soft-nav:external:success",ERROR:"soft-nav:external:error",RENDER:"soft-nav:external:render"});var aA=n(57226);function aj(){return!!document.querySelector('react-app[data-lazy="true"]')}function aL(){return!!document.querySelector('react-app[data-alternate="true"]')}function aT(){return performance.getEntriesByType("resource").some(e=>"fetch"===e.initiatorType&&e.name.includes("_graphql?"))}function aC(){return performance.getEntriesByType("resource").some(e=>"script"===e.initiatorType)}let ak={xlg:8,lg:4,md:2,sm:0};function aq(){if(!("hardwareConcurrency"in navigator))return"unknown";let e=navigator.hardwareConcurrency;for(let[t,n]of Object.entries(ak))if(e>n)return t;return"unknown"}function ax({metric:e,ssr:t,longTasks:n,longAnimationFrames:r}){let i;if(!(0,ei.G7)("report_hydro_web_vitals")){if(!i){let e=document.querySelector("react-app");(u||(u={},aM()),i=u).react=!!e,i.reactApp=e?.getAttribute("app-name"),i.reactPartials=[...new Set(Array.from(document.querySelectorAll("react-partial")).map(e=>e.getAttribute("partial-name")||""))],i.featureFlags=(0,ei.fQ)(),i.ssr=t,i.controller=document.querySelector('meta[name="route-controller"]')?.content,i.action=document.querySelector('meta[name="route-action"]')?.content,i.routePattern=document.querySelector('meta[name="route-pattern"]')?.content,i.cpu=aq()}if(e){var a,o,s;return a=i,void((o=e).value<6e4&&("HPC"===o.name?a[o.name.toLocaleLowerCase()]={name:(s=o).name,value:s.value,element:s.attribution?.element,soft:!!s.soft,mechanism:s.mechanism}:a[o.name.toLocaleLowerCase()]=function(e){let t={name:e.name,value:e.value};switch(e.name){case"LCP":case"ElementTiming":t.element=e.attribution?.element;break;case"FID":case"INP":t.element=e.attribution?.eventTarget,e.entries?.length&&(t.events=e.entries.map(e=>e.name).join(","));break;case"CLS":t.element=e.attribution?.largestShiftTarget}return t}(o)))}i.longTasks=n,i.longAnimationFrames=r}}async function aM(){await J.K,window.requestIdleCallback(a_)}function a_(){u&&((0,z.BI)("web-vital",(0,z.Ti)(u)),u=void 0)}var aH=n(7522);let aP=(0,nb.fX)()||"rails",aO=(0,n3.g5)(),aI=aj(),aR=aL();function a$(e){"soft-navigation"===e.navigationType&&aB(e,{experimentalSoftNav:!0})}function aB(e,t={}){let{name:n,value:r}=e,i={name:t.url||window.location.href,cpu:aq()};i[n.toLowerCase()]=r,(0,ei.G7)("sample_network_conn_type")&&(i.networkConnType="connection"in navigator&&navigator.connection&&"effectiveType"in navigator.connection?navigator.connection.effectiveType:"N/A"),t.experimentalSoftNav&&(i.mechanism=aH.nW[(0,nb.r7)()]),"ElementTiming"===n&&(i.identifier=e.identifier),"HPC"===n?aN(i,e):(i.ssr=aO,i.lazy=aI,i.alternate=aR,i.app=aP),document.querySelector('meta[name="synthetic-test"]')&&(i.synthetic=!0),(0,v.i)({webVitalTimings:[i]}),ax({metric:e,ssr:!!i.ssr}),function(e,t){let n=document.querySelector("#staff-bar-web-vitals"),r=n?.querySelector(`[data-metric=${e.toLowerCase()}]`);r&&(r.textContent=t.toPrecision(6))}(n,r)}n3.XC?.addEventListener(tt.z.END,()=>{aP=(0,nb.fX)()||"rails",aO=(0,n3.g5)(),aI=aj(),aR=aL()});let aN=(e,t)=>{e.soft=t.soft,e.ssr=t.ssr,e.mechanism=aH.nW[t.mechanism],e.lazy=t.lazy,e.alternate=t.alternate,e.hpcFound=t.found,e.hpcGqlFetched=t.gqlFetched,e.hpcJsFetched=t.jsFetched,e.headerRedesign=!!document.querySelector("header.AppHeader"),e.app=t.app};async function aD(){window.performance&&window.performance.timing&&window.performance.getEntriesByType&&(await J.K,await new Promise(e=>setTimeout(e)),aW(),aF())}let aW=()=>{let e=window.performance.getEntriesByType("resource").map(e=>({name:e.name,entryType:e.entryType,startTime:e.startTime,duration:e.duration,initiatorType:e.initiatorType,nextHopProtocol:e.nextHopProtocol,workerStart:e.workerStart,redirectStart:e.redirectStart,redirectEnd:e.redirectEnd,fetchStart:e.fetchStart,domainLookupStart:e.domainLookupStart,domainLookupEnd:e.domainLookupEnd,connectStart:e.connectStart,connectEnd:e.connectEnd,secureConnectionStart:e.secureConnectionStart,requestStart:e.requestStart,responseStart:e.responseStart,responseEnd:e.responseEnd,transferSize:e.transferSize,encodedBodySize:e.encodedBodySize,decodedBodySize:e.decodedBodySize}));e.length&&(0,v.i)({resourceTimings:e},!1,.05)},aF=()=>{let e=window.performance.getEntriesByType("navigation").map(e=>({activationStart:e.activationStart,name:e.name,entryType:e.entryType,startTime:e.startTime,duration:e.duration,initiatorType:e.initiatorType,nextHopProtocol:e.nextHopProtocol,workerStart:e.workerStart,redirectStart:e.redirectStart,redirectEnd:e.redirectEnd,fetchStart:e.fetchStart,domainLookupStart:e.domainLookupStart,domainLookupEnd:e.domainLookupEnd,connectStart:e.connectStart,connectEnd:e.connectEnd,secureConnectionStart:e.secureConnectionStart,requestStart:e.requestStart,responseStart:e.responseStart,responseEnd:e.responseEnd,transferSize:e.transferSize,encodedBodySize:e.encodedBodySize,decodedBodySize:e.decodedBodySize,unloadEventStart:e.unloadEventStart,unloadEventEnd:e.unloadEventEnd,domInteractive:e.domInteractive,domContentLoadedEventStart:e.domContentLoadedEventStart,domContentLoadedEventEnd:e.domContentLoadedEventEnd,domComplete:e.domComplete,loadEventStart:e.loadEventStart,loadEventEnd:e.loadEventEnd,type:e.type,redirectCount:e.redirectCount}));e.length&&(0,v.i)({navigationTimings:e},!1,"undefined"!=typeof process&&"development"===process.env.APP_NAME?1:.05)},aU=e=>{let t=e.nodeName;return 1===e.nodeType?t.toLowerCase():t.toUpperCase().replace(/^#/,"")},az=(e,t)=>{let n="";try{for(;e&&9!==e.nodeType;){let r=e,i=r.id?`#${r.id}`:aU(r)+(r.classList&&r.classList.value&&r.classList.value.trim()&&r.classList.value.trim().length?`.${r.classList.value.trim().replace(/\s+/g,".")}`:"");if(n.length+i.length>(t||100)-1)return n||i;if(n=n?`${i}>${n}`:i,r.id)break;e=r.parentNode}}catch{}return n};function aV(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let HPCTimingEvent=class HPCTimingEvent extends Event{constructor(e,t,n,r,i,a,o,s,l,c,u){super("hpc:timing"),aV(this,"name","HPC"),aV(this,"value",void 0),aV(this,"attribution",void 0),aV(this,"soft",void 0),aV(this,"ssr",void 0),aV(this,"lazy",void 0),aV(this,"alternate",void 0),aV(this,"mechanism",void 0),aV(this,"found",void 0),aV(this,"gqlFetched",void 0),aV(this,"jsFetched",void 0),aV(this,"app",void 0),this.soft=e,this.ssr=t,this.lazy=n,this.alternate=r,this.mechanism=i,this.found=a,this.gqlFetched=o,this.jsFetched=s,this.app=l,this.value=performance.now()-c,this.attribution={element:az(u)}}};let HPCDomInsertionEvent=class HPCDomInsertionEvent extends Event{constructor(e){super("hpc:dom-insertion"),aV(this,"element",void 0),this.element=e}};function aG(e,t,n){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return n}function aX(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.add(e)}function aK(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function aJ(){return(0,nb.fX)()||"rails"}function aY(e,t){(0,aA.fK)(({value:n,attribution:r})=>{window.performance.measure("HPC",{start:"navigationStart",end:n}),aB({name:"HPC",value:n,soft:e,found:t,gqlFetched:aT(),jsFetched:aC(),ssr:(0,n3.g5)(),lazy:aj(),alternate:aL(),mechanism:"hard",app:aJ(),attribution:{element:r?.element}})})}var aZ=new WeakSet,aQ=new WeakSet,a0=new WeakSet,a1=new WeakSet;let HPCObserver=class HPCObserver{connect(){if(!this.soft){let e=document.querySelector("[data-hpc]");if(e){this.hpcElement=e,aY(this.soft,!0);return}setTimeout(()=>{this.insertionFound||aY(this.soft,!1)},1e4)}aG(this,a1,a7).call(this),this.hpcDOMInsertionObserver=aG(this,aZ,a2).call(this),this.hpcDOMInsertionObserver.observe(document,{childList:!0,subtree:!0})}disconnect(){aG(this,a0,a3).call(this),this.hpcDOMInsertionObserver?.disconnect()}constructor({soft:e,mechanism:t,latestHPCElement:n}){aX(this,aZ),aX(this,aQ),aX(this,a0),aX(this,a1),aK(this,"abortController",new AbortController),aK(this,"tabHidden",!1),aK(this,"insertionFound",!1),aK(this,"hpcElement",null),aK(this,"soft",void 0),aK(this,"mechanism",void 0),aK(this,"latestHPCElement",void 0),aK(this,"hpcStart",void 0),aK(this,"hpcTarget",new EventTarget),aK(this,"animationFrame",void 0),aK(this,"dataHPCanimationFrame",void 0),aK(this,"emulatedHPCTimer",void 0),aK(this,"listenerOpts",void 0),aK(this,"hpcDOMInsertionObserver",null),aK(this,"stop",()=>{this.abortController.abort()}),aK(this,"onDOMInsertion",e=>{this.insertionFound=!0,clearTimeout(this.emulatedHPCTimer);let t=new HPCTimingEvent(this.soft,(0,n3.g5)(),aj(),aL(),this.mechanism,!1,aT(),aC(),aJ(),this.hpcStart,e.element);this.emulatedHPCTimer=setTimeout(()=>this.hpcTarget.dispatchEvent(t),1e4)}),aK(this,"onHPCTiming",e=>{!this.tabHidden&&e.value<6e4&&aB(e),this.abortController.abort()}),aK(this,"onVisibilityChange",()=>{this.tabHidden=!0,this.abortController.abort()}),aK(this,"onSoftNavRender",()=>{let e=document.querySelector("[data-hpc]");this.hpcElement=e,e&&e===this.latestHPCElement&&aG(this,aQ,a5).call(this,e)}),aK(this,"onAbort",()=>{this.dataHPCanimationFrame&&cancelAnimationFrame(this.dataHPCanimationFrame),this.animationFrame&&cancelAnimationFrame(this.animationFrame),clearTimeout(this.emulatedHPCTimer),this.disconnect()}),this.soft=e,this.mechanism=t,this.latestHPCElement=n,this.hpcStart=e?performance.now():0,this.listenerOpts={capture:!0,passive:!0,once:!0,signal:this.abortController.signal}}};function a2(){return new MutationObserver(e=>{let t=!1,n=!1,r=null,i=null;if(!e.every(e=>0===e.addedNodes.length)){for(let a of e)if("childList"===a.type){for(let e of a.addedNodes)if(e instanceof Element){if(r=e.hasAttribute("data-hpc")?e:e.querySelector("[data-hpc]")){this.hpcElement=r,this.animationFrame&&cancelAnimationFrame(this.animationFrame),t=!0;break}("function"==typeof e.checkVisibility?e.checkVisibility():!!(e.offsetParent||e.offsetWidth||e.offsetHeight))&&(i=e,this.animationFrame&&cancelAnimationFrame(this.animationFrame),n=!0)}if(t)break}if(t&&r)aG(this,aQ,a5).call(this,r);else if(n){let e=new HPCDomInsertionEvent(i);this.animationFrame=requestAnimationFrame(()=>{this.hpcTarget.dispatchEvent(e)})}}})}function a5(e){window.performance.measure("HPC","navigationStart"),this.hpcDOMInsertionObserver?.disconnect();let t=new HPCTimingEvent(this.soft,(0,n3.g5)(),aj(),aL(),this.mechanism,!0,aT(),aC(),aJ(),this.hpcStart,e);this.dataHPCanimationFrame=requestAnimationFrame(()=>{this.hpcTarget.dispatchEvent(t)})}function a3(){document.removeEventListener("touchstart",this.stop,this.listenerOpts),document.removeEventListener("mousedown",this.stop,this.listenerOpts),document.removeEventListener("keydown",this.stop,this.listenerOpts),document.removeEventListener("pointerdown",this.stop,this.listenerOpts),document.removeEventListener("visibilitychange",this.onVisibilityChange),document.removeEventListener(tt.z.RENDER,this.onSoftNavRender),this.hpcTarget.removeEventListener("hpc:dom-insertion",this.onDOMInsertion),this.hpcTarget.removeEventListener("hpc:timing",this.onHPCTiming),this.abortController.signal.removeEventListener("abort",this.onAbort)}function a7(){document.addEventListener("touchstart",this.stop,this.listenerOpts),document.addEventListener("mousedown",this.stop,this.listenerOpts),document.addEventListener("keydown",this.stop,this.listenerOpts),document.addEventListener("pointerdown",this.stop,this.listenerOpts),this.hpcTarget.addEventListener("hpc:dom-insertion",this.onDOMInsertion,{signal:this.abortController.signal}),this.hpcTarget.addEventListener("hpc:timing",this.onHPCTiming,{signal:this.abortController.signal}),document.addEventListener(tt.z.RENDER,this.onSoftNavRender),document.addEventListener("visibilitychange",this.onVisibilityChange,{signal:this.abortController.signal}),this.abortController.signal.addEventListener("abort",this.onAbort)}function a4(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let InteractionCountObserver=class InteractionCountObserver{get interactionCount(){return this.observer?this.interactionCountEstimate:performance.interactionCount||0}teardown(){this.observer&&(this.observer.takeRecords(),this.observer.disconnect(),this.observer=void 0)}observe(){"interactionCount"in performance||this.observer||(this.observer=new PerformanceObserver(async e=>{await Promise.resolve(),this.updateEstimate(e.getEntries())}),this.observer.observe({type:"event",buffered:!0,durationThreshold:0}))}constructor(){a4(this,"interactionCountEstimate",0),a4(this,"minKnownInteractionId",1/0),a4(this,"maxKnownInteractionId",0),a4(this,"observer",void 0),a4(this,"updateEstimate",e=>{for(let t of e)t.interactionId&&(this.minKnownInteractionId=Math.min(this.minKnownInteractionId,t.interactionId),this.maxKnownInteractionId=Math.max(this.maxKnownInteractionId,t.interactionId),this.interactionCountEstimate=this.maxKnownInteractionId?(this.maxKnownInteractionId-this.minKnownInteractionId)/7+1:0)})}};function a9(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let InteractionList=class InteractionList{get shortestInteraction(){return this.interactions[this.interactions.length-1]}get(e){return this.interactionsMap.get(e)}update(e,t){let n=Math.max(e.latency,t.duration);e.entries.push(t),n!==e.latency&&(e.latency=Math.max(e.latency,t.duration),this.sort())}add(e){let t=this.shortestInteraction;(this.interactions.length<=this.maxSize||!t||e.latency>t.latency)&&(this.interactionsMap.set(e.id,e),this.interactions.push(e),this.sort(),this.interactions.length>this.maxSize&&this.interactions.pop())}sort(){this.interactions.sort((e,t)=>t.latency-e.latency)}findEntry(e){return this.interactions.some(t=>t.entries.some(t=>e.duration===t.duration&&e.startTime===t.startTime))}estimateP98(e){let t=Math.min(this.interactions.length-1,Math.floor(e/50));return this.interactions[t]}constructor(e){a9(this,"interactions",[]),a9(this,"interactionsMap",new Map),a9(this,"maxSize",void 0),this.maxSize=e}};function a6(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let INPMetric=class INPMetric{constructor(e,t){a6(this,"name","INP"),a6(this,"value",void 0),a6(this,"entries",void 0),a6(this,"attribution",void 0),this.value=e,this.entries=t;let n=t.find(e=>e.target);this.attribution={eventTarget:az(n?.target)}}};function a8(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let InteractionProcessor=class InteractionProcessor{get inp(){let e=this.interactions.estimateP98(this.interactionCountObserver.interactionCount);return e?new INPMetric(e.latency,e.entries):new INPMetric(0,[])}teardown(){this.interactionCountObserver.teardown()}processEntries(e){for(let t of e){if(t.interactionId){this.processEntry(t);continue}"first-input"!==t.entryType||this.interactions.findEntry(t)||this.processEntry(t)}}processEntry(e){let t=this.interactions.get(String(e.interactionId));if(t)return this.interactions.update(t,e);let n={id:String(e.interactionId),latency:e.duration,entries:[e]};this.interactions.add(n)}constructor(){a8(this,"interactions",new InteractionList(10)),a8(this,"interactionCountObserver",void 0),this.interactionCountObserver=new InteractionCountObserver,this.interactionCountObserver.observe()}};function oe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let ot=n3.cg&&"PerformanceEventTiming"in n3.cg&&"interactionId"in PerformanceEventTiming.prototype;let INPObserver=class INPObserver{setupListeners(){if(!ot)return;let e=e=>{("pagehide"===e.type||"hidden"===document.visibilityState)&&this.report()};n3.XC?.addEventListener("visibilitychange",e,!0),n3.XC?.addEventListener("pagehide",e,!0),n3.XC?.addEventListener(tt.z.RENDER,()=>{this.report(),this.reset()})}observe(e=!0){ot&&(this.url=n3.cg?.location.href,this.observer=new PerformanceObserver(e=>{this.interactionProcessor.processEntries(e.getEntries())}),this.observer.observe({type:"first-input",buffered:e}),this.observer.observe({type:"event",durationThreshold:40,buffered:e}))}report(){this.interactionProcessor.inp.value<0||this.cb(this.interactionProcessor.inp,{url:this.url})}teardown(){this.observer?.takeRecords(),this.observer?.disconnect()}reset(){this.teardown(),this.interactionProcessor.teardown(),this.interactionProcessor=new InteractionProcessor,this.observe(!1)}constructor(e){oe(this,"cb",void 0),oe(this,"interactionProcessor",void 0),oe(this,"observer",void 0),oe(this,"url",void 0),this.cb=e,this.interactionProcessor=new InteractionProcessor,this.setupListeners()}};function on(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let ElementTimingMetric=class ElementTimingMetric{constructor(e,t,n){on(this,"name","ElementTiming"),on(this,"value",void 0),on(this,"identifier",void 0),on(this,"attribution",void 0),this.value=e,this.identifier=n,this.attribution={element:az(t)}}};function or(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let oi=n3.cg&&"PerformanceElementTiming"in n3.cg;let ElementTimingObserver=class ElementTimingObserver{setupListeners(){oi&&n3.XC?.addEventListener(tt.z.RENDER,()=>{this.reset()})}observe(e=!0){oi&&(this.observer=new PerformanceObserver(e=>{for(let{renderTime:t,element:n,identifier:r}of e.getEntries())this.report(new ElementTimingMetric(t,n,r))}),this.observer.observe({type:"element",buffered:e}))}report(e){this.cb(e,{url:this.url})}teardown(){this.observer?.takeRecords(),this.observer?.disconnect()}reset(){this.teardown(),this.observe(!1)}constructor(e){or(this,"cb",void 0),or(this,"observer",void 0),or(this,"url",void 0),this.cb=e,this.setupListeners()}};let oa=()=>{"undefined"!=typeof PerformanceObserver&&(PerformanceObserver.supportedEntryTypes||[]).includes("longtask")&&new PerformanceObserver(function(e){let t=e.getEntries(),n=t.map(({name:e,duration:t})=>({name:e,duration:t,url:window.location.href}));(0,v.i)({longTasks:n}),n.length>0&&ax({longTasks:t,ssr:(0,n3.g5)()})}).observe({type:"longtask",buffered:!0})},oo=()=>{"undefined"!=typeof PerformanceObserver&&(PerformanceObserver.supportedEntryTypes||[]).includes("long-animation-frame")&&new PerformanceObserver(function(e){let t=e.getEntries(),n=t.map(({name:e,duration:t,blockingDuration:n})=>({name:e,duration:t,blockingDuration:n,url:window.location.href}));n.length>0&&ax({longAnimationFrames:t,ssr:(0,n3.g5)()}),(0,v.i)({longAnimationFrames:n})}).observe({type:"long-animation-frame",buffered:!0})};n3.XC?.addEventListener(tt.z.SUCCESS,function(e){"turbo"===e.mechanism&&(0,w.i)(`${document.title}`)}),n3.XC?.addEventListener(aS.INITIAL,it.k5),n3.XC?.addEventListener(aS.START,e=>{(0,it.SC)(e.detail.mechanism)}),n3.XC?.addEventListener(aS.SUCCESS,()=>(0,it.iS)()),n3.XC?.addEventListener(aS.ERROR,()=>(0,it.o4)()),n3.XC?.addEventListener(aS.RENDER,()=>(0,it.rZ)()),function(){aD(),(0,aA.IN)(aB),(0,aA.zB)(aB),(0,aA.lt)(aB),(0,aA.fK)(aB),(0,aA.Ck)(aB),oa(),oo(),(0,aA.fK)(a$,{reportSoftNavs:!0}),(0,aA.IN)(a$,{reportSoftNavs:!0}),new INPObserver(aB).observe(),new ElementTimingObserver(aB).observe();let e=new HPCObserver({soft:!1,mechanism:"hard",latestHPCElement:null});e.connect(),n3.XC?.addEventListener(tt.z.START,({mechanism:t})=>{e.disconnect(),(e=new HPCObserver({soft:!0,mechanism:t,latestHPCElement:document.querySelector("[data-hpc]")})).connect()}),n3.XC?.addEventListener(tt.z.REPLACE_MECHANISM,({mechanism:t})=>{e.mechanism=t})}(),(0,en.TV)("cpu_bucket",aq())},57233:(e,t,n)=>{"use strict";n.d(t,{_:()=>BaseBatchDeferredContentElement});var r=n(39595),i=n(1739),a=n(26559);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let s=class AutoFlushingQueue{push(e){let t=`item-${this.index++}`;return this.timer&&(window.clearTimeout(this.timer),this.timer=null),this.elements.length>=this.limit&&this.flush(),this.timer=window.setTimeout(()=>{this.timer=null,this.flush()},this.timeout),this.elements.push([e,t]),t}onFlush(e){this.callbacks.push(e)}async flush(){let e=this.elements.splice(0,this.limit);0!==e.length&&await Promise.all(this.callbacks.map(t=>t(e)))}constructor(e=50,t=30){o(this,"timeout",void 0),o(this,"limit",void 0),o(this,"elements",[]),o(this,"timer",null),o(this,"callbacks",[]),o(this,"index",void 0),this.timeout=e,this.limit=t,this.index=0}};let BatchLoader=class BatchLoader{loadInBatch(e){let t=this.autoFlushingQueue.push(e);return new Promise(e=>this.callbacks.set(t,e))}async load(e){let t=new Map;for(let[n,r]of e)t.set(r,n);let n=new FormData;for(let[e,r]of t.entries())for(let t of r.inputs)n.append(`items[${e}][${t.name}]`,t.value);if(0===Array.from(n.values()).length)return;n.set("_method","GET");let r=await fetch(this.url,{method:"POST",body:n,headers:{Accept:"application/json",...(0,a.kt)()}});if(r.ok){let e=await r.json();if(!e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed batch response");for(let t in e){let n=this.callbacks.get(t);if(n){let r=e[t];this.validate(r),n(r)}}}}constructor(e,t){o(this,"autoFlushingQueue",void 0),o(this,"url",void 0),o(this,"callbacks",void 0),o(this,"validate",void 0),this.url=e,this.callbacks=new Map,this.autoFlushingQueue=new s,this.autoFlushingQueue.onFlush(async e=>{this.load(e)}),this.validate=t}};function l(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o}let BaseBatchDeferredContentElement=class BaseBatchDeferredContentElement extends HTMLElement{async connectedCallback(){let e=await this.batchLoader.loadInBatch(this);this.update(e)}get batchLoader(){let e=this.getAttribute("data-url");if(!e)throw Error(`${this.tagName} element requires a data-url attribute`);let t=this.batchLoaders.get(e);return t||(t=new BatchLoader(e,e=>this.validate(e)),this.batchLoaders.set(e,t)),t}};let c=new Map,u=class BatchDeferredContentElement extends BaseBatchDeferredContentElement{validate(e){if("string"!=typeof e)throw Error("Batch deferred content was not a string")}update(e){let t=(0,i.B)(document,e);this.replaceWith(t)}constructor(...e){super(...e),function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"batchLoaders",c)}};l([r.zV],u.prototype,"inputs",void 0),u=l([r.p_],u)},55760:(e,t,n)=>{"use strict";n.d(t,{Cw:()=>a,ig:()=>s,mY:()=>o});var r=n(35707);function i(e){return e.hasAttribute("data-maxlength")?parseInt(e.getAttribute("data-maxlength")||""):e.maxLength}function a(e){let t=i(e);return t-(0,r.bV)(e.value)<0}function o(e){let t=i(e);!function(e,t,n){let i=n.closest(".js-characters-remaining-container");if(!i)return;let a=i.querySelector(".js-characters-remaining"),o=String(a.getAttribute("data-suffix")),s=t-(0,r.bV)(e);s<=20?(a.textContent=`${s} ${o}`,a.classList.toggle("color-fg-danger",s<=5),a.setAttribute("role","status"),a.hidden=!1):(a.setAttribute("role","none"),a.hidden=!0)}(e.value,t,e)}function s(e){for(let t of e.querySelectorAll(".js-characters-remaining-container"))o(t.querySelector(".js-characters-remaining-field"))}(0,n(36175).uE)(".js-characters-remaining-field",function(e){function t(){(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&o(e)}t(),e.addEventListener("input",t),e.addEventListener("blur",()=>{e.removeEventListener("input",t)},{once:!0})})},77153:()=>{document.addEventListener("DOMContentLoaded",()=>{for(let t of document.querySelectorAll("[data-clear-btn]")){let n=t.getAttribute("data-clear-btn");if(!n)return;let r=document.getElementById(n);if(!r)return;function e(){r&&(r.style.display=t.value?"flex":"none")}t.addEventListener("input",e),r.addEventListener("click",()=>{t.value="",t.focus(),e()}),e()}})},54765:()=>{document.addEventListener("click",function(e){if(!(e.target instanceof Element))return;let t=e.target.closest("a[data-confirm], input[type=submit][data-confirm], input[type=checkbox][data-confirm], button[data-confirm]");if(!t)return;let n=t.getAttribute("data-confirm");n&&(confirm(n)||(e.stopImmediatePropagation(),e.preventDefault()))},!0)},5012:(e,t,n)=>{"use strict";n.d(t,{D4:()=>o,lF:()=>s,w:()=>a});var r=n(78134),i=n(97797);async function a(e,t){let r=new TextEncoder().encode(t),{seal:i}=await Promise.all([n.e("vendors-node_modules_buffer_index_js"),n.e("vendors-node_modules_blakejs_index_js-node_modules_tweetnacl_nacl-fast_js"),n.e("_empty-file_js-app_assets_modules_github_tweetsodium_ts")]).then(n.bind(n,82447));return i(r,e)}function o(e){let t=atob(e).split("").map(e=>e.charCodeAt(0));return Uint8Array.from(t)}function s(e){let t="";for(let n of e)t+=String.fromCharCode(n);return btoa(t)}function l(e){return async function(t){let n=t.currentTarget;if(t.defaultPrevented||!n.checkValidity())return;let i=o(n.getAttribute("data-public-key"));for(let o of(t.preventDefault(),n.elements))if(o.id.endsWith("secret")){if(o.disabled=!0,o.required&&!o.value){let e=`${o.name} is invalid!`,t=document.querySelector("template.js-flash-template");t.after(new r.i4(t,{className:"flash-error",message:e}));return}let t=`${o.name}_encrypted_value`;if(!o.value){n.elements.namedItem(t).disabled=e;continue}n.elements.namedItem(t).value=s(await a(i,o.value))}n.submit()}}(0,i.on)("submit","form.js-encrypt-submit",async function(e){let t=e.currentTarget;if(e.defaultPrevented||!t.checkValidity())return;let n=t.elements.namedItem("secret_value");if(n.disabled=!0,!n.value)return;e.preventDefault();let r=o(t.getAttribute("data-public-key"));t.elements.namedItem("encrypted_value").value=s(await a(r,n.value)),t.submit()}),(0,i.on)("submit","form.js-encrypt-bulk-submit",l(!0)),(0,i.on)("submit","form.js-encrypt-bulk-submit-enable-empty",l(!1))},889:(e,t,n)=>{"use strict";n.d(t,{n:()=>a});var r=n(22247),i=n(32475);async function a(e){await i.K,o(e)}function o(e){let t=e.querySelectorAll(".js-responsive-underlinenav-item"),n=e.querySelector(".js-responsive-underlinenav-overflow"),r=s(n,e);if(!r)return;let i=!1;for(let n of t){let t=s(n,e);if(t){let e=t.left+n.offsetWidth>=r.left;n.style.visibility=e?"hidden":"";let a=n.getAttribute("data-tab-item");if(a){let t=document.querySelector(`[data-menu-item=${a}]`);t instanceof HTMLElement&&(t.hidden=!e)}i=i||e}}n.style.visibility=i?"":"hidden"}function s(e,t){let n=e,r=n.ownerDocument;if(!r||!r.documentElement)return;let i=r.defaultView.HTMLElement,a=0,o=0;for(;n!==r.body&&n!==t;){if(a+=n.offsetTop||0,o+=n.offsetLeft||0,!(n.offsetParent instanceof i))return;n=n.offsetParent}return{top:a,left:o}}(0,n(21403).lB)(".js-responsive-underlinenav",{constructor:HTMLElement,subscribe:e=>(a(e),(0,r.Rt)(window,"resize",()=>o(e)))})},31901:(e,t,n)=>{"use strict";n.d(t,{Q:()=>l});var r=n(55150),i=n(70170),a=n(95105),o=n(21403);let s=(0,n(85351).A)("localStorage",{ttl:3e5,throwQuotaErrorsOnSet:!1,sendCacheStats:!0}),l=()=>{(0,r.o)((0,a.L)()),(0,r.o)((0,a.L)(),{storage:s})},c=()=>{(0,r.YV)((0,a.L)(),{selector:".js-session-resumable"}),(0,r.YV)((0,a.L)(),{selector:".js-local-storage-resumable",storage:s})},u=(0,i.s)(function(){l()},50);window.addEventListener("submit",r.Bu,{capture:!0}),window.addEventListener("pageshow",function(){l()}),(0,o.lB)(".js-session-resumable",function(){document.querySelector("html")?.hasAttribute("data-turbo-preview")||u()}),window.addEventListener("pagehide",function(){c()}),window.addEventListener("turbo:before-fetch-response",function(){c()}),window.addEventListener("turbo:load",function(){l()})},93885:()=>{function e(e){let t=e&&e.getAttribute("value");if(t)for(let e of document.querySelectorAll(".js-sidenav-container-pjax .js-selected-navigation-item")){let n=(e.getAttribute("data-selected-links")||"").split(" ").indexOf(t)>=0;n?e.setAttribute("aria-current","page"):e.removeAttribute("aria-current"),e.classList.toggle("selected",n)}}new MutationObserver(t=>{for(let n of t)for(let t of n.addedNodes)t instanceof HTMLMetaElement&&"selected-link"===t.getAttribute("name")&&e(t)}).observe(document.head,{childList:!0}),document.addEventListener("turbo:load",()=>{let t=document.head.querySelector('meta[name="selected-link"]');t&&e(t)})},48696:(e,t,n)=>{"use strict";var r=n(65461),i=n(97797),a=n(21403);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let s=class TagInput{setup(){this.container.addEventListener("click",e=>{e.target.closest(".js-remove")?this.removeTag(e):this.onFocus()}),this.container.addEventListener("keydown",e=>{"Enter"===e.key&&!(e.defaultPrevented||!this.input.value)&&(e.preventDefault(),this.selectTag(this.input.value),this.autoComplete&&(this.autoComplete.open=!1))}),this.input.addEventListener("focus",this.onFocus.bind(this)),this.input.addEventListener("blur",this.onBlur.bind(this)),this.input.addEventListener("keydown",this.onKeyDown.bind(this)),this.form.addEventListener("submit",this.onSubmit.bind(this)),this.autoComplete?.addEventListener("auto-complete-change",()=>{this.selectTag(this.autoComplete.value)})}onFocus(){this.inputWrap.classList.add("focus"),this.input!==document.activeElement&&this.input.focus()}onBlur(){this.inputWrap.classList.remove("focus"),this.autoComplete&&(!this.autoComplete||this.autoComplete.open)||this.onSubmit()}onSubmit(){this.input.value&&(this.selectTag(this.input.value),this.autoComplete&&(this.autoComplete.open=!1))}onKeyDown(e){switch((0,r.Vy)(e)){case"Backspace":this.onBackspace();break;case"Enter":case"Tab":this.taggifyValueWhenSuggesterHidden(e);break;case",":case"Space":this.taggifyValue(e)}}taggifyValueWhenSuggesterHidden(e){this.autoComplete&&!this.autoComplete.open&&this.input.value&&(e.preventDefault(),this.selectTag(this.input.value))}taggifyValue(e){this.input.value&&(e.preventDefault(),this.selectTag(this.input.value),this.autoComplete&&(this.autoComplete.open=!1))}selectTag(e){let t=this.normalizeTag(e),n=this.selectedTags(),r=!1;for(let e=0;e<t.length;e++){let i=t[e];0>n.indexOf(i)&&(this.selections.appendChild(this.templateTag(i)),r=!0)}r&&(this.input.value="",(0,i.h)(this.form,"tags:changed"))}removeTag(e){let t=e.target;e.preventDefault(),t.closest(".js-tag-input-tag").remove(),(0,i.h)(this.form,"tags:changed")}templateTag(e){let t=this.tagTemplate.cloneNode(!0);return t.querySelector("input").value=e,t.querySelector(".js-placeholder-tag-name").replaceWith(e),t.classList.remove("d-none","js-template"),t}normalizeTag(e){let t=e.toLowerCase().trim();return t?this.multiTagInput?t.split(/[\s,']+/):[t.replace(/[\s,']+/g,"-")]:[]}onBackspace(){if(!this.input.value){let e=this.selections.querySelector("li:last-child .js-remove");e instanceof HTMLElement&&e.click()}}selectedTags(){return Array.from(this.selections.querySelectorAll("input")).map(e=>e.value).filter(e=>e.length>0)}constructor(e){o(this,"container",void 0),o(this,"selections",void 0),o(this,"inputWrap",void 0),o(this,"input",void 0),o(this,"form",void 0),o(this,"tagTemplate",void 0),o(this,"autoComplete",void 0),o(this,"multiTagInput",void 0),this.container=e.container,this.selections=e.selections,this.inputWrap=e.inputWrap,this.input=e.input,this.tagTemplate=e.tagTemplate,this.form=this.input.form,this.autoComplete=e.autoComplete,this.multiTagInput=e.multiTagInput}};(0,a.lB)(".js-tag-input-container",{constructor:HTMLElement,initialize(e){new s({container:e,inputWrap:e.querySelector(".js-tag-input-wrapper"),input:e.querySelector('input[type="text"], input:not([type])'),selections:e.querySelector(".js-tag-input-selected-tags"),tagTemplate:e.querySelector(".js-template"),autoComplete:e.querySelector("auto-complete"),multiTagInput:!1}).setup()}}),(0,a.lB)(".js-multi-tag-input-container",{constructor:HTMLElement,initialize(e){new s({container:e,inputWrap:e.querySelector(".js-tag-input-wrapper"),input:e.querySelector('input[type="text"], input:not([type])'),selections:e.querySelector(".js-tag-input-selected-tags"),tagTemplate:e.querySelector(".js-template"),autoComplete:e.querySelector("auto-complete"),multiTagInput:!0}).setup()}})},82624:()=>{!function(){let e=document.createElement("div");return e.style.cssText="-ms-user-select: element; user-select: contain;","element"===e.style.getPropertyValue("-ms-user-select")||"contain"===e.style.getPropertyValue("-ms-user-select")||"contain"===e.style.getPropertyValue("user-select")}()&&document.addEventListener("click",function(e){if(!(e.target instanceof Element))return;let t=e.target.closest(".user-select-contain");if(!t)return;let n=window.getSelection();if(!n||!n.rangeCount||!n.rangeCount||"Range"!==n.type)return;let r=n.getRangeAt(0).commonAncestorContainer;t.contains(r)||n.selectAllChildren(t)})},48234:(e,t,n)=>{"use strict";function r(e){let t=e.match(/#?(?:L)(\d+)((?:C)(\d+))?/g);if(t){if(1===t.length){let e=s(t[0]);if(!e)return;return Object.freeze({start:e,end:e})}if(2!==t.length)return;{let e=s(t[0]),n=s(t[1]);if(!e||!n)return;return u(Object.freeze({start:e,end:n}))}}}function i(e){let{start:t,end:n}=u(e);return null!=t.column&&null!=n.column?`L${t.line}C${t.column}-L${n.line}C${n.column}`:null!=t.column?`L${t.line}C${t.column}-L${n.line}`:null!=n.column?`L${t.line}-L${n.line}C${n.column}`:t.line===n.line?`L${t.line}`:`L${t.line}-L${n.line}`}function a(e){return{blobRange:r(e),anchorPrefix:function(e){let t=e.length<5e3&&e.match(/(file-.+?-)L\d+?/i);return t?t[1]:""}(e)}}function o({anchorPrefix:e,blobRange:t}){return t?`#${e}${i(t)}`:"#"}function s(e){let t=e.match(/L(\d+)/),n=e.match(/C(\d+)/);return t?Object.freeze({line:parseInt(t[1]),column:n?parseInt(n[1]):null}):null}function l(e,t){let[n,r]=c(e.start,!0,t),[i,a]=c(e.end,!1,t);if(!n||!i)return;let o=r,s=a;if(-1===o&&(o=0),-1===s&&(s=i.childNodes.length),!n.ownerDocument)throw Error("DOMRange needs to be inside document");let l=n.ownerDocument.createRange();return l.setStart(n,o),l.setEnd(i,s),l}function c(e,t,n){let r=[null,0],i=n(e.line);if(!i)return r;if(null==e.column)return[i,-1];let a=e.column-1,o=function e(t){if(t.nodeType===Node.TEXT_NODE)return[t];if(!t.childNodes||!t.childNodes.length)return[];let n=[];for(let r of t.childNodes)n=n.concat(e(r));return n}(i);for(let e=0;e<o.length;e++){let n=o[e],r=a-(n.textContent||"").length;if(0===r){let r=o[e+1];if(t&&r)return[r,0];return[n,a]}if(r<0)return[n,a];a=r}return r}function u(e){let t=[e.start,e.end];return(t.sort(d),t[0]===e.start&&t[1]===e.end)?e:Object.freeze({start:t[0],end:t[1]})}function d(e,t){return e.line===t.line&&e.column===t.column?0:e.line===t.line&&"number"==typeof e.column&&"number"==typeof t.column?e.column-t.column:e.line-t.line}n.d(t,{$c:()=>a,JB:()=>o,Kn:()=>i,Py:()=>l,eC:()=>r})},20784:(e,t,n)=>{"use strict";n.d(t,{B:()=>o,j:()=>a});var r=n(97797);let i=new WeakMap;function a(e){return i.get(e)}async function o(e){var t,n;return i.get(e)||s(await (t=e,n="codeEditor:ready",new Promise(e=>{t.addEventListener(n,e,{once:!0})})))}function s(e){if(!(e instanceof CustomEvent))throw Error("assert: event is not a CustomEvent");let t=e.detail.editor;if(!e.target)throw Error("assert: event.target is null");return i.set(e.target,t),t}(0,r.on)("codeEditor:ready",".js-code-editor",s)},62643:(e,t,n)=>{"use strict";n.d(t,{A:()=>d});var r=n(91385);let i=(e,t,n)=>{if(!(0,r.qA)(e,t))return-1/0;let i=(0,r.fN)(e,t);return i<n?-1/0:i},a=(e,t,n)=>{e.textContent="";let i=0;for(let a of(0,r.Xq)(t,n)){""!==n.slice(i,a)&&e.appendChild(document.createTextNode(n.slice(i,a))),i=a+1;let t=document.createElement("mark");t.textContent=n[a],e.appendChild(t)}e.appendChild(document.createTextNode(n.slice(i)))},o=new WeakMap,s=new WeakMap,l=new WeakMap,c=e=>{if(!l.has(e)&&e instanceof HTMLElement){let t=(e.getAttribute("data-value")||e.textContent||"").trim();return l.set(e,t),t}return l.get(e)||""},u=class FuzzyListElement extends HTMLElement{connectedCallback(){let e=this.querySelector("ul");if(!e)return;let t=new Set(e.querySelectorAll("li")),n=this.querySelector("input");n instanceof HTMLInputElement&&n.addEventListener("input",()=>{this.value=n.value});let i=new MutationObserver(e=>{let n=!1;for(let i of e)if("childList"===i.type&&i.addedNodes.length){for(let e of i.addedNodes)if(e instanceof HTMLLIElement&&!t.has(e)){let i=c(e);n=n||(0,r.qA)(this.value,i),t.add(e)}}n&&this.sort()});i.observe(e,{childList:!0});let a={handler:i,items:t,lazyItems:new Map,timer:null};s.set(this,a)}disconnectedCallback(){let e=s.get(this);e&&(e.handler.disconnect(),s.delete(this))}addLazyItems(e,t){let n=s.get(this);if(!n)return;let{lazyItems:i}=n,{value:a}=this,o=!1;for(let n of e)i.set(n,t),o=o||!!a&&(0,r.qA)(a,n);o&&this.sort()}sort(){let e=o.get(this);e&&(e.aborted=!0);let t={aborted:!1};o.set(this,t);let{minScore:n,markSelector:r,maxMatches:u,value:d}=this,m=s.get(this);if(!m||!this.dispatchEvent(new CustomEvent("fuzzy-list-will-sort",{cancelable:!0,detail:d})))return;let{items:f,lazyItems:h}=m,p=this.hasAttribute("mark-selector"),g=this.querySelector("ul");if(!g)return;let b=[];if(d){for(let e of f){let t=i(d,c(e),n);t!==-1/0&&b.push({item:e,score:t})}for(let[e,t]of h){let r=i(d,e,n);r!==-1/0&&b.push({text:e,render:t,score:r})}b.sort((e,t)=>t.score-e.score).splice(u)}else{let e=b.length;for(let t of f){if(e>=u)break;b.push({item:t,score:1}),e+=1}for(let[t,n]of h){if(e>=u)break;b.push({text:t,render:n,score:1}),e+=1}}requestAnimationFrame(()=>{if(t.aborted)return;let e=g.querySelector('input[type="radio"]:checked');g.textContent="";let n=0,i=()=>{if(t.aborted)return;let o=Math.min(b.length,n+100),s=document.createDocumentFragment();for(let e=n;e<o;e+=1){let t=b[e],n=null;if("render"in t&&"text"in t){let{render:e,text:r}=t;n=e(r),f.add(n),l.set(n,r),h.delete(r)}else"item"in t&&(n=t.item);n instanceof HTMLElement&&(p&&a(r&&n.querySelector(r)||n,p?d:"",c(n)),s.appendChild(n))}n=o;let u=!1;if(e instanceof HTMLInputElement)for(let t of s.querySelectorAll('input[type="radio"]:checked'))t instanceof HTMLInputElement&&t.value!==e.value&&(t.checked=!1,u=!0);if(this.getAttribute("data-tab-only-first")){let e=this.querySelectorAll("button.js-emoji-button");for(let t of e)t.setAttribute("tabindex","-1");e.item(0)?.setAttribute("tabindex","0")}else for(let e of s.querySelectorAll('button[tabindex="-1"]'))e.setAttribute("tabindex","0");if(g.appendChild(s),e&&u&&e.dispatchEvent(new Event("change",{bubbles:!0})),o<b.length)requestAnimationFrame(i);else{g.hidden=0===b.length;let e=this.querySelector("[data-fuzzy-list-show-on-empty]");e&&(e.hidden=b.length>0),this.dispatchEvent(new CustomEvent("fuzzy-list-sorted",{detail:b.length}))}};i()})}get value(){return this.getAttribute("value")||""}set value(e){this.setAttribute("value",e)}get markSelector(){return this.getAttribute("mark-selector")||""}set markSelector(e){e?this.setAttribute("mark-selector",e):this.removeAttribute("mark-selector")}get minScore(){return Number(this.getAttribute("min-score")||0)}set minScore(e){Number.isNaN(e)||this.setAttribute("min-score",String(e))}get maxMatches(){return Number(this.getAttribute("max-matches")||1/0)}set maxMatches(e){Number.isNaN(e)||this.setAttribute("max-matches",String(e))}get ariaLiveElement(){let e=this.getAttribute("data-aria-live-element");if(!e)return;let t=document.getElementById(e);if(t)return t}static get observedAttributes(){return["value","mark-selector","min-score","max-matches"]}attributeChangedCallback(e,t,n){if(t===n)return;let r=s.get(this);r&&(r.timer&&window.clearTimeout(r.timer),r.timer=window.setTimeout(()=>this.sort(),100))}},d=u;window.customElements.get("fuzzy-list")||(window.FuzzyListElement=u,window.customElements.define("fuzzy-list",u))},67105:(e,t,n)=>{"use strict";n.d(t,{$3:()=>a,HV:()=>o,Vb:()=>i});var r=n(7479);function i(e,t,n){let i={hydroEventPayload:e,hydroEventHmac:t,visitorPayload:"",visitorHmac:"",hydroClientContext:n},a=document.querySelector("meta[name=visitor-payload]");a instanceof HTMLMetaElement&&(i.visitorPayload=a.content);let o=document.querySelector("meta[name=visitor-hmac]")||"";o instanceof HTMLMetaElement&&(i.visitorHmac=o.content),(0,r.i)(i,!0)}function a(e){let t=e.getAttribute("data-hydro-view")||"";i(t,e.getAttribute("data-hydro-view-hmac")||"",e.getAttribute("data-hydro-client-context")||"")}function o(e){let t=e.getAttribute("data-hydro-click-payload")||"";i(t,e.getAttribute("data-hydro-click-hmac")||"",e.getAttribute("data-hydro-client-context")||"")}},21244:(e,t,n)=>{"use strict";n.d(t,{d:()=>s,t:()=>o});var r=n(24852);let i=["notification_referrer_id","notifications_before","notifications_after","notifications_query"],a="notification_shelf";function o(e,t=null){return e.has("notification_referrer_id")?(function(e,t){let n=l(t);if(!n)return;let o={pathname:n};for(let t of i){let n=e.get(t);n&&(o[t]=n)}(0,r.SO)(a,JSON.stringify(o))}(e,t),function(e){for(let t of i)e.delete(t);return e}(e)):null}function s(e=null){let t=l(e);if(!t)return(0,r.Ai)(a),null;try{let e=(0,r.Gq)(a);if(!e)return null;let n=JSON.parse(e);if(!n||!n.pathname)throw Error("Must have a pathname");if(n.pathname!==t)throw Error("Stored pathname does not match current pathname.");let o={};for(let e of i)o[e]=n[e];return o}catch{return(0,r.Ai)(a),null}}function l(e){let t=(e=e||window.location.pathname).match(/^(\/[^/]+\/[^/]+\/pull\/[^/]+)/);return t?t[0]:null}},92811:(e,t,n)=>{"use strict";function r(e,t){var n,r,i,a,o,s;let l=e.closest("[data-notification-id]");t.hasAttribute("data-status")&&(n=l,r=t.getAttribute("data-status"),n.classList.toggle("notification-archived","archived"===r),n.classList.toggle("notification-unread","unread"===r),n.classList.toggle("notification-read","read"===r)),t.hasAttribute("data-subscription-status")&&(i=l,a=t.getAttribute("data-subscription-status"),i.classList.toggle("notification-unsubscribed","unsubscribed"===a)),t.hasAttribute("data-starred-status")&&(o=l,s=t.getAttribute("data-starred-status"),o.classList.toggle("notification-starred","starred"===s))}n.d(t,{T:()=>r})},62660:(e,t,n)=>{"use strict";function r(e,t){t.appendChild(e.extractContents()),e.insertNode(t)}n.d(t,{t:()=>r})},95105:(e,t,n)=>{"use strict";function r(e){let t=e||window.location,n=document.head&&document.head.querySelector("meta[name=session-resume-id]");return n instanceof HTMLMetaElement&&n.content||t.pathname}n.d(t,{L:()=>r})},52734:(e,t,n)=>{"use strict";n.d(t,{Cg:()=>l,R1:()=>m,s:()=>c});var r=n(78134),i=n(21403),a=n(26559);let o="github-mobile-auth-flash";function s(){let e=document.querySelector("#js-flash-container");if(e)for(let t of e.children)!t.classList.contains("js-flash-template")&&t.classList.contains(o)&&e.removeChild(t)}function l(){let e=document.getElementById("github-mobile-authenticate-prompt");e&&(e.hidden=!0);let t=document.getElementById("github-mobile-authenticate-error-and-retry");t&&(t.hidden=!1)}function c(){s();let e=document.getElementById("github-mobile-authenticate-prompt");e&&(e.hidden=!1);let t=document.getElementById("github-mobile-authenticate-error-and-retry");t&&(t.hidden=!0)}function u(e){e&&function(e){let t=new r.i4(document.querySelector("template.js-flash-template"),{className:`flash-error ${o}`,message:e}),n=document.importNode(t,!0),i=document.querySelector("#js-flash-container");i&&(s(),i.appendChild(n))}(e),l()}function d(e){return document.getElementById("github-mobile-authenticate-error-and-retry").getAttribute(e)}async function m(e,t,n,r){try{var i;await (i=e.getAttribute("data-poll-url"),async function e(o){let s,l,c;if(r&&r())return;let m="STATUS_UNKNOWN";try{let e=document.getElementById("github-mobile-authenticate-form"),t=e.querySelector(".js-data-url-csrf"),n=await self.fetch(new Request(i,{method:"POST",body:new FormData(e),mode:"same-origin",headers:{Accept:"application/json","Scoped-CSRF-Token":t.value,...(0,a.kt)()}}));if(n.ok){let e=await n.json();m=e.status,s=e.token}else m="STATUS_ERROR"}catch{m="STATUS_ERROR"}switch(m){case"STATUS_APPROVED":var f;return t?t():void((c=(f=s)?new URL(`password_reset/${encodeURIComponent(f)}`,window.location.origin):new URL("",window.location.href)).searchParams.set("redirect","true"),window.location.assign(c));case"STATUS_EXPIRED":return l=d("timeout-flash"),n?n(l):u(l);case"STATUS_ACTIVE":case"STATUS_ERROR":case"STATUS_UNKNOWN":break;case"STATUS_REJECTED":return l=d("error-flash"),n?n(l):void document.getElementById("github-mobile-rejected-redirect").click();default:return l=d("error-flash"),n?n(l):u(l)}await new Promise(e=>setTimeout(e,3e3)),e(o)}(0))}catch{return u(d("error-flash"))}}(0,i.lB)(".js-poll-github-mobile-two-factor-authenticate",function(e){m(e)}),(0,i.lB)(".js-poll-github-mobile-verified-device-authenticate",function(e){m(e)}),(0,i.lB)(".js-poll-github-mobile-two-factor-password-reset-authenticate",function(e){m(e)})},62004:(e,t,n)=>{"use strict";n.d(t,{NB:()=>c,Up:()=>l,pk:()=>u});let r=new WeakMap;function i(e){let t=r.get(e);t&&(null!=t.timer&&clearTimeout(t.timer),t.timer=window.setTimeout(()=>{null!=t.timer&&(t.timer=null),t.inputed=!1,t.listener.call(null,e)},t.wait))}function a(e){let t=e.currentTarget,n=r.get(t);n&&(n.keypressed=!0,null!=n.timer&&clearTimeout(n.timer))}function o(e){let t=e.currentTarget,n=r.get(t);n&&(n.keypressed=!1,n.inputed&&i(t))}function s(e){let t=e.currentTarget,n=r.get(t);n&&(n.inputed=!0,n.keypressed||i(t))}function l(e,t,n={wait:null}){r.set(e,{keypressed:!1,inputed:!1,timer:void 0,listener:t,wait:null!=n.wait?n.wait:100}),e.addEventListener("keydown",a),e.addEventListener("keyup",o),e.addEventListener("input",s)}function c(e,t){e.removeEventListener("keydown",a),e.removeEventListener("keyup",o),e.removeEventListener("input",s);let n=r.get(e);n&&(null!=n.timer&&n.listener===t&&clearTimeout(n.timer),r.delete(e))}function u(e){let t=r.get(e);t&&t.listener.call(null,e)}},63166:(e,t,n)=>{"use strict";var r=n(97797),i=n(21403),a=n(22353),o=n(38007);let s="data-analytics-event",l="data-analytics-visible",c=`a:not([${s}]), button:not([${s}]), [${s}]`,u=`[${l}]`;function d(e,t){return e.tagName.toLowerCase()===t}function m(e){return{text:e.textContent||e.getAttribute("aria-label")||"",target:e.href}}function f(e){let t=e.closest("form");return{text:e.textContent||e.getAttribute("aria-label")||"",role:e.getAttribute("type")||e.getAttribute("role")||"button",...e.value&&{value:e.value},...t&&{formAction:t.getAttribute("action")||""}}}function h(e){if(!e)return{};let t=JSON.parse(e),{label:n}=t;return{...function(e){if(!e)return{};let t={};for(let n of e.split(";").map(e=>e.trim())){let[e,r]=n.split(":");e&&(t[e.trim()]=r?.trim()||e.trim())}return t}(n),...t}}(0,r.on)("click",c,e=>{if(e&&e.currentTarget)try{(0,o.BI)("analytics.click",function(e){return{...d(e,"a")&&m(e),...d(e,"button")&&f(e),...h(e.getAttribute(s))}}(e.currentTarget)),d(e.currentTarget,"details")&&e.currentTarget.removeAttribute(s)}catch(e){(0,a.N7)(e)}});let p=new IntersectionObserver(function(e){for(let n of e)if(n.isIntersecting)try{var t;(0,o.BI)("analytics.visible",(t=n.target,{...d(t,"a")&&m(t),...d(t,"button")&&f(t),...h(t.getAttribute(l))})),p.unobserve(n.target)}catch(e){(0,a.N7)(e)}},{rootMargin:"0% 0% -30% 0%",threshold:0});(0,i.lB)(u,e=>{p.observe(e)})},27932:(e,t,n)=>{"use strict";n.d(t,{R:()=>l});var r=n(82939),i=n(96679),a=n(21715);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let BatchedUpdatableContent=class BatchedUpdatableContent{onEvent(e,t,n){let r=this.intervals[t];r||(r=this.intervals[t]={interval:null,targets:new Set}),r.targets.add(e),r.interval||(r.interval=setInterval(()=>this.onInterval(t,n),t))}constructor(){o(this,"intervals",{}),o(this,"onInterval",(e,t)=>{let n=this.intervals[e];if(!n)return;let r=n.targets;if(0!==r.size){for(let e of(n.targets=new Set,r))document.body.contains(e)&&t(e);r.clear()}}),o(this,"clear",()=>{for(let e in this.intervals)if(Object.prototype.hasOwnProperty.call(this.intervals,e)){let t=this.intervals[e];t&&(t.interval&&(clearInterval(t.interval),t.interval=null),t.targets.clear())}})}};let s=new BatchedUpdatableContent;function l(e=r.le){return function(t){let{gid:n,wait:r,event_updates:i}=t.detail.data,a=t.target,o=n?function(e,t){if(e.getAttribute("data-gid")===t)return e;for(let n of e.querySelectorAll("[data-url][data-gid]"))if(n.getAttribute("data-gid")===t)return n;return null}(a,n):a;if(o){let t=o.getAttribute("data-batched"),n=o.getAttribute("data-channel-event-name");if(n&&(void 0===i||!i.hasOwnProperty(n)))return;if(t){let n=Math.max(parseInt(t)||0,1e3);s.onEvent(o,n,e)}else setTimeout(e,r||0,o)}}}i.cg?.addEventListener(a.z.END,s.clear)},69719:(e,t,n)=>{"use strict";let r;n.d(t,{H:()=>P});var i=n(10204);function a(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function o(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}var s=new WeakMap,l=new WeakSet;let AliveSession=class AliveSession extends i.ib{constructor(e,t,n,r,i){super(e,()=>(function(e,t,n){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return n})(this,l,c).call(this),n,r,void 0,i),function(e,t){a(e,t),t.add(e)}(this,l),function(e,t,n){a(e,t),t.set(e,n)}(this,s,{writable:!0,value:void 0}),function(e,t,n){var r=o(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}}(this,s,t)}};function c(){var e;return u((e=o(this,s,"get"),e.get?e.get.call(this):e.value))}async function u(e){let t=await d(e);return t&&t.url&&t.token?m(t.url,t.token):null}async function d(e){let t=await fetch(e,{headers:{Accept:"application/json"}});if(t.ok)return t.json();if(404===t.status)return null;throw Error("fetch error")}async function m(e,t){let n=await fetch(e,{method:"POST",mode:"same-origin",headers:{"Scoped-CSRF-Token":t}});if(n.ok)return n.text();throw Error("fetch error")}var f=n(70170),h=n(32475),p=n(85351),g=n(23683),b=n(27851);function v(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function y(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function w(e,t){var n=y(e,t,"get");return n.get?n.get.call(e):n.value}function E(e,t,n){v(e,t),t.set(e,n)}function S(e,t,n){var r=y(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}function A(e,{channel:t,type:n,data:r}){for(let i of e)i.dispatchEvent(new CustomEvent(`socket:${n}`,{bubbles:!1,cancelable:!1,detail:{name:t,data:r}}))}var j=new WeakMap,L=new WeakMap,T=new WeakMap,C=new WeakMap,k=new WeakMap,q=new WeakSet;let x=class AliveSessionProxy{subscribe(e){let t=w(this,L).add(...e);t.length&&w(this,j).port.postMessage({subscribe:t});let n=new Set(t.map(e=>e.name)),r=e.reduce((e,t)=>{let r=t.topic.name;return(0,i.JR)(r)&&!n.has(r)&&e.add(r),e},new Set);r.size&&w(this,j).port.postMessage({requestPresence:Array.from(r)})}unsubscribeAll(...e){let t=w(this,L).drain(...e);t.length&&w(this,j).port.postMessage({unsubscribe:t});let n=w(this,T).removeSubscribers(e);this.sendPresenceMetadataUpdate(n)}updatePresenceMetadata(e){let t=new Set;for(let n of e)w(this,T).setMetadata(n),t.add(n.channelName);this.sendPresenceMetadataUpdate(t)}sendPresenceMetadataUpdate(e){if(!e.size)return;let t=[];for(let n of e)t.push({channelName:n,metadata:w(this,T).getChannelMetadata(n)});w(this,j).port.postMessage({updatePresenceMetadata:t})}online(){w(this,j).port.postMessage({online:!0})}offline(){w(this,j).port.postMessage({online:!1})}hangup(){w(this,j).port.postMessage({hangup:!0})}constructor(e,t,n,r,a,o){!function(e,t){v(e,t),t.add(e)}(this,q),E(this,j,{writable:!0,value:void 0}),E(this,L,{writable:!0,value:new i.m0}),E(this,T,{writable:!0,value:new i.VH}),E(this,C,{writable:!0,value:void 0}),E(this,k,{writable:!0,value:new Map}),S(this,C,a),S(this,j,new SharedWorker(`${e}?module=true`,{name:`github-socket-worker-v3-${r}`,type:"module"})),w(this,j).port.onmessage=({data:e})=>(function(e,t,n){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return n})(this,q,M).call(this,e),w(this,j).port.postMessage({connect:{url:t,refreshUrl:n,options:o}})}};function M(e){let{channel:t}=e;if("presence"===e.type){let n=w(this,k).get(t);n||(n=(0,f.s)((e,n)=>{w(this,C).call(this,e,n),w(this,k).delete(t)},100),w(this,k).set(t,n)),n(w(this,L).subscribers(t),e);return}w(this,C).call(this,w(this,L).subscribers(t),e)}async function _(){let e=function(){let e=document.head.querySelector("link[rel=shared-web-socket-src]")?.getAttribute("href");return e&&e.startsWith("/")?e:null}();if(!e)return;let t=document.head.querySelector("link[rel=shared-web-socket]")?.href??null;if(!t)return;let n=document.head.querySelector("link[rel=shared-web-socket]")?.getAttribute("data-refresh-url")??null;if(!n)return;let r=document.head.querySelector("link[rel=shared-web-socket]")?.getAttribute("data-session-id")??null;if(!r)return;let i=(()=>{let i=(0,b.G7)("alive_legacy_retries")?{socketPolicy:{timeout:4e3,attempts:7}}:{};if(!(0,g.nr)()&&"SharedWorker"in window&&"true"!==(0,p.A)("localStorage").getItem("bypassSharedWorker"))try{return new x(e,t,n,r,A,i)}catch{}return new AliveSession(t,n,!1,A,i)})();return window.addEventListener("online",()=>i.online()),window.addEventListener("offline",()=>i.offline()),window.addEventListener("pagehide",()=>{"hangup"in i&&i.hangup()}),i}async function H(){return await h.G,_()}function P(){return r||(r=H())}},8447:(e,t,n)=>{"use strict";n.d(t,{A:()=>a,s:()=>i});let r=!0;function i(e){r=e}function a(){return r}},13255:(e,t,n)=>{"use strict";n.d(t,{_S:()=>a,cB:()=>s,cW:()=>o});var r=n(66871),i=n(96679);let a=()=>{let e=(0,r.JV)().appId;return e&&"rails"!==e?e:crypto.randomUUID()},o=e=>{(0,r.C3)({appId:e})},s=()=>{let e=document.querySelector("react-app")||document.querySelector("projects-v2");return e?.uuid||"rails"};i.cg?.addEventListener("hashchange",()=>{(0,r.C3)({appId:s()})},!0)},25794:(e,t,n)=>{"use strict";function r(e,t,n){if(!t)return;let r=t.className.includes("cm-content")?t:t.querySelector(".cm-content");r&&r.dispatchEvent(new CustomEvent(e,{detail:n}))}n.d(t,{R:()=>r})},39627:(e,t,n)=>{"use strict";n.d(t,{D:()=>a,Y:()=>o});var r=n(52811),i=n(96679);function a(e){if(!i.XC)return;let t=i.XC.querySelector("title"),n=i.XC.createElement("title");n.textContent=e,t?t.textContent!==e&&(t.replaceWith(n),(0,r.i)(e)):(i.XC.head.appendChild(n),(0,r.i)(e))}function o(e){return document.body.classList.contains("logged-out")?`${e} \xb7 GitHub`:e}},76899:(e,t,n)=>{"use strict";function r(){return Promise.resolve()}function i(){return new Promise(window.requestAnimationFrame)}async function a(e,t){let n,r=new Promise(t=>{n=self.setTimeout(t,e)});if(!t)return r;try{await Promise.race([r,function(e){return new Promise((t,n)=>{let r=Error("aborted");r.name="AbortError",e.aborted?n(r):e.addEventListener("abort",()=>n(r))})}(t)])}catch(e){throw self.clearTimeout(n),e}}n.d(t,{G$:()=>i,k2:()=>r,rK:()=>o,uk:()=>a});function o(e){let t=[];return function(n){t.push(n),1===t.length&&queueMicrotask(()=>{let n=t.slice(0);t.length=0,e(n)})}}},98831:(e,t,n)=>{"use strict";function r(){return{favicon:document.querySelector('.js-site-favicon[type="image/svg+xml"]'),faviconFallback:document.querySelector('.js-site-favicon[type="image/png"]')}}function i(e){let{favicon:t,faviconFallback:n}=r();if(!t||!n)return;let i=l();e=e.substr(0,e.lastIndexOf(".")),t.href=e=`${e}${i}.svg`;let a=t.href.substr(0,t.href.lastIndexOf("."));n.href=`${a}.png`}function a(){let{favicon:e,faviconFallback:t}=r();if(!e||!t)return;let n=l(),i=e.href.indexOf("-dark.svg"),a=e.href.substr(0,-1!==i?i:e.href.lastIndexOf("."));e.href=`${a}${n}.svg`,t.href=`${a}${n}.png`}function o(e){let{favicon:t,faviconFallback:n}=r();if(!t||!n)return;let i=t.getAttribute("data-base-href"),a="default"===e?"":`-${e}`,o=l();i&&(t.href=`${i}${a}${o}.svg`,n.href=`${i}${a}${o}.png`)}function s(){o("default")}function l(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"-dark":""}n.d(t,{Ow:()=>i,gd:()=>s,iD:()=>o,uQ:()=>a})},13617:(e,t,n)=>{"use strict";function r(e,t,n){return[...function*(e,t){for(let n of e){let e=t(n);null!=e&&(yield e)}}(e,e=>{let n=t(e);return null!=n?[e,n]:null})].sort((e,t)=>n(e[1],t[1])).map(([e])=>e)}n.d(t,{d:()=>r})},30695:(e,t,n)=>{"use strict";function r(e,t,n=.1){let i=o(e,t,n);return i&&-1===t.indexOf("/")&&(i+=o(e.substring(e.lastIndexOf("/")+1),t,n)),i}function i(e,t,n){if(t){let r=e.innerHTML.trim().match(n||function(e){let t=e.toLowerCase().split(""),n="",r=!0;for(let e of t){let t=e.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&");r?(n+=`(.*)(${t})`,r=!1):n+=`([^${t}]*?)(${t})`}return RegExp(`${n}(.*?)$`,"i")}(t));if(!r)return;let i=!1,a=[];for(let e=1;e<r.length;++e){let t=r[e];t&&(e%2==0?i||(a.push("<mark>"),i=!0):i&&(a.push("</mark>"),i=!1),a.push(t))}e.innerHTML=a.join("")}else{let t=e.innerHTML.trim(),n=t.replace(/<\/?mark>/g,"");t!==n&&(e.innerHTML=n)}}n.d(t,{He:()=>i,UD:()=>s,dt:()=>r});let a=new Set([" ","-","_"]);function o(e,t,n=.1){let r=e;if(r===t)return 1;let i=r.length,s=0,l=0,c=!0;for(let e of t){let t=r.indexOf(e.toLowerCase()),o=r.indexOf(e.toUpperCase()),u=Math.min(t,o),d=u>-1?u:Math.max(t,o);if(-1===d)return 0;s+=.1,r[d]===e&&(s+=.1),0===d&&(s+=.9-n,c&&(l=1)),a.has(r.charAt(d-1))&&(s+=.9-n),r=r.substring(d+1,i),c=!1}let u=t.length,d=s/u,m=(u/i*d+d)/2;return l&&m+n<1&&(m+=n),m}function s(e,t){return e.score>t.score?-1:e.score<t.score?1:e.text<t.text?-1:1*!!(e.text>t.text)}},55463:(e,t,n)=>{"use strict";n.d(t,{Fr:()=>o,U0:()=>l,xl:()=>s});var r=n(96679);let i={Android:"Android",iOS:"iOS",macOS:"macOS",Windows:"Windows",Linux:"Linux",Unknown:"Unknown"};function a(){let e=i.Unknown,t=!1;if(r.cg){let n=r.cg.navigator,a="";try{a=n.userAgent}catch{}let o="";try{o=n?.userAgentData?.platform||n.platform}catch{}-1!==["Macintosh","MacIntel","MacPPC","Mac68K","macOS"].indexOf(o)?e=i.macOS:-1!==["iPhone","iPad","iPod"].indexOf(o)?e=i.iOS:-1!==["Win32","Win64","Windows","WinCE"].indexOf(o)?e=i.Windows:/Android/.test(a)?e=i.Android:/Linux/.test(o)&&(e=i.Linux),t=n?.userAgentData?.mobile??(e===i.Android||e===i.iOS)}return{os:e,isAndroid:e===i.Android,isIOS:e===i.iOS,isMacOS:e===i.macOS,isWindows:e===i.Windows,isLinux:e===i.Linux,isDesktop:e===i.macOS||e===i.Windows||e===i.Linux,isMobile:t}}function o(){return a().isMobile}function s(){return a().isDesktop}function l(){return a().isMacOS}},57909:(e,t,n)=>{"use strict";n.d(t,{$$:()=>l,GI:()=>o,zw:()=>a});var r=n(55463),i=n(65461);let a=()=>{if("undefined"==typeof document)return!1;let e=document.querySelector("meta[name=keyboard-shortcuts-preference]");return!e||"all"===e.content},o=e=>/Enter|Arrow|Escape|Meta|Control|Mod|Esc|Tab/.test(e)||!(0,r.U0)()&&e.includes("Alt")&&e.includes("Shift"),s=new Set(["button","checkbox","color","file","hidden","image","radio","range","reset","submit"]),l=e=>{let t=(0,i.Vy)(e),n=a()&&!function(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=e.getAttribute("type")?.toLowerCase()??"text",r="true"===e.ariaReadOnly||"true"===e.getAttribute("aria-readonly")||null!==e.getAttribute("readonly");return("select"===t||"textarea"===t||"input"===t&&!s.has(n)||e.isContentEditable)&&!r}(e.target);return o(t)||n}},38007:(e,t,n)=>{"use strict";let r;n.d(t,{BI:()=>h,Ti:()=>p,lA:()=>m,sX:()=>f});var i=n(70837),a=n(18679),o=n(85351),s=n(7479);let{getItem:l}=(0,o.A)("localStorage"),c="dimension_",u=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","scid"];try{let e=(0,i.O)("octolytics");delete e.baseContext,r=new a.s(e)}catch{}function d(e){let t=(0,i.O)("octolytics").baseContext||{};if(t)for(let[e,n]of(delete t.app_id,delete t.event_url,delete t.host,Object.entries(t)))e.startsWith(c)&&(t[e.replace(c,"")]=n,delete t[e]);let n=document.querySelector("meta[name=visitor-payload]");for(let[e,r]of(n&&Object.assign(t,JSON.parse(atob(n.content))),new URLSearchParams(window.location.search)))u.includes(e.toLowerCase())&&(t[e]=r);return t.staff=(0,s.X)().toString(),Object.assign(t,e)}function m(e){r?.sendPageView(d(e))}function f(){return document.head?.querySelector('meta[name="current-catalog-service"]')?.content}function h(e,t={}){let n=f(),i=n?{service:n}:{};for(let[e,n]of Object.entries(t))null!=n&&(i[e]=`${n}`);r&&(d(i),r.sendEvent(e||"unknown",d(i)))}function p(e){return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,JSON.stringify(t)]))}},72841:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(e,t)=>{let n=new URL(e,window.location.origin),r=new URL(t,window.location.origin);return r.href.includes("#")&&n.host===r.host&&n.pathname===r.pathname&&n.search===r.search}},59843:(e,t,n)=>{"use strict";n.d(t,{XX:()=>r.XX,_3:()=>r._3,qy:()=>r.qy});var r=n(31143)},12153:(e,t,n)=>{"use strict";n.d(t,{pF:()=>u,ty:()=>a,VL:()=>l,nf:()=>o,TG:()=>d,iQ:()=>s});var r=n(55463);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let PNGScanner=class PNGScanner{advance(e){this.pos+=e}readInt(e){let t=this,n=function(){switch(e){case 1:return t.dataview.getUint8(t.pos);case 2:return t.dataview.getUint16(t.pos);case 4:return t.dataview.getUint32(t.pos);default:throw Error("bytes parameter must be 1, 2 or 4")}}();return this.advance(e),n}readChar(){return this.readInt(1)}readShort(){return this.readInt(2)}readLong(){return this.readInt(4)}readString(e){let t=[];for(let n=0;n<e;n++)t.push(String.fromCharCode(this.readChar()));return t.join("")}scan(e){if(0x89504e47!==this.readLong())throw Error("invalid PNG");for(this.advance(4);;){let t=this.readLong(),n=this.readString(4),r=this.pos+t+4;if(!1===e.call(this,n,t)||"IEND"===n)break;this.pos=r}}constructor(e,t){i(this,"dataview",void 0),i(this,"pos",void 0),i(this,"callback",void 0),this.dataview=new DataView(e),this.pos=0,this.callback=t}};let a=e=>{let t=e.value.lastIndexOf(`
`,e.selectionStart-1)+1,n=e.value.indexOf(`
`,e.selectionEnd);return -1===n&&(n=e.value.length),[t,n]},o=e=>`<!-- ${e.replaceAll("--","\\-\\-")} -->`,s=(e,t)=>`[${e.replaceAll("[","\\[").replaceAll("]","\\]")}](${t.replaceAll("(","\\(").replaceAll(")","\\)")})`,l=e=>(0,r.U0)()?e.metaKey:e.ctrlKey,c=e=>{let t=new PNGScanner(e),n={width:0,height:0,ppi:1};return t.scan(function(e){switch(e){case"IHDR":n.width=this.readLong(),n.height=this.readLong();break;case"pHYs":{let e,t=this.readLong(),r=this.readLong();return 1===this.readChar()&&(e=.0254),e&&(n.ppi=Math.round((t+r)/2*e)),!1}case"IDAT":return!1}return!0}),n},u=async e=>{let t=await new Promise((t,n)=>{let r=new FileReader;r.onload=()=>t(r.result),r.onerror=()=>n(r.error),r.readAsArrayBuffer(e)});return t?c(t):null},d=(e,t,n="Image",r=!1)=>{if(144===e.ppi){let i=Math.round(e.width/2),a=Math.round(e.height/2);return r?`<img width="${i}" height="${a}" alt="${n}" src="${t}" />`:`<img width="${i}" alt="${n}" src="${t}" />`}return r&&e&&e.width>0&&e.height>0?`<img width="${e.width}" height="${e.height}" alt="${n}" src="${t}" />`:`![${n}](${t})`}},90903:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>f,YU:()=>d,nA:()=>h});var r=n(24791),i=n(20451),a=n(12559),o=n(91903),s=n(26559);let l=!1;async function c(){let e=document.querySelector("link[rel=sudo-modal]"),t=document.querySelector(".js-sudo-prompt");if(t instanceof HTMLTemplateElement)return t;if(e){let t=await (0,i.Ts)(document,function(e){let t=new URL(e,window.location.origin),n=new URLSearchParams(t.search.slice(1));return n.set("webauthn-support",(0,o.K)()),t.search=n.toString(),t.toString()}(e.href));return document.body.appendChild(t),document.querySelector(".js-sudo-prompt")}throw Error("couldn't load sudo prompt")}let u=!1;async function d(e){if(l)return!1;l=!0,u=!1;let t=(await c()).content.cloneNode(!0),n=await (0,r.r)({content:t}),i=e?.closest("details[open]");return i&&i.removeAttribute("open"),await new Promise(e=>{n.addEventListener("dialog:remove",function(){i&&i.setAttribute("open","open"),l=!1,e()},{once:!0})}),u}async function m(e,t,n="Sudo authentication failed.",r="Too many authentication attempts. Please try again later.",i=".js-sudo-error",a){try{await t.text()}catch(o){let t;if(!o.response)throw o;switch(o.response.status){case 401:t=n;break;case 429:t=r;break;default:t="An unknown error occurred. Please try again later."}if(e.querySelector(i).textContent=t,e.querySelector(i).hidden=!1,a&&(e.querySelector(a).value=""),401!==o.response.status&&429!==o.response.status)throw o;return}u=!0,e.closest("details").removeAttribute("open")}async function f(e){let t=await fetch("/sessions/in_sudo",{headers:{accept:"application/json",...(0,s.kt)()}});return!!t.ok&&"true"===await t.text()||d(e)}async function h(){let e=await fetch("/sessions/in_sudo",{headers:{accept:"application/json",...(0,s.kt)()}});return!!e.ok&&"true"===await e.text()||!1}(0,a.JW)(".js-sudo-webauthn-form",async function(e,t){await m(e,t)}),(0,a.JW)(".js-sudo-github-mobile-form",async function(e,t){await m(e,t)}),(0,a.JW)(".js-sudo-totp-form",async function(e,t){await m(e,t,void 0,void 0,".flash-error","#totp")}),(0,a.JW)(".js-sudo-email-form",async function(e,t){await m(e,t,void 0,void 0,".flash-error","#email")}),(0,a.JW)(".js-sudo-password-form",async function(e,t){await m(e,t,"Incorrect password.","Too many password attempts. Please wait and try again.",void 0,".js-sudo-password")})},35707:(e,t,n)=>{"use strict";function r(e){let t=e.split("\u200D"),n=0;for(let e of t)n+=Array.from(e.split(/[\ufe00-\ufe0f]/).join("")).length;return n/t.length}function i(e,t,n,r=!0){let a=e.value.substring(0,e.selectionEnd||0),o=e.value.substring(e.selectionEnd||0);return s(e,(a=a.replace(t,n))+(o=o.replace(t,n)),a.length,r),n}function a(e,t,n){if(null===e.selectionStart||null===e.selectionEnd)return i(e,t,n);let r=e.value.substring(0,e.selectionStart),a=e.value.substring(e.selectionEnd);return s(e,r+n+a,r.length),n}function o(e,t,n={}){let r=e.selectionEnd||0,i=e.value.substring(0,r),a=e.value.substring(r),s=(""===e.value||i.match(/\n$/)?"":`
`)+t+(n.appendNewline?`
`:"");e.value=i+s+a;let l=r+s.length;return e.selectionStart=l,e.selectionEnd=l,e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1})),e.focus(),s}function s(e,t,n,r=!0){e.value=t,r&&(e.selectionStart=n,e.selectionEnd=n),e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1}))}function l(e,t){let n=[...e],r=new TextEncoder,i=new Uint8Array(4);for(let e=0;e<n.length;e++){let a=n[e],{written:o,read:s}=r.encodeInto(a,i);if(!o||!s)return -1;let l=o-s;if(0!==l&&(e<t&&(t-=l),e>=t))break}return t}n.d(t,{bV:()=>r,bc:()=>o,ee:()=>i,kN:()=>l,tJ:()=>a})},51606:(e,t,n)=>{"use strict";n.d(t,{$4:()=>v,$U:()=>s,$Y:()=>m,G5:()=>d,GH:()=>b,OO:()=>w,Ph:()=>i,Sf:()=>y,Sy:()=>S,Xm:()=>E,Y0:()=>f,Y9:()=>c,e8:()=>l,mU:()=>o,nZ:()=>g,uW:()=>a,vV:()=>h});let r="data-turbo-loaded";function i(){document.documentElement.setAttribute(r,"")}function a(){return document.documentElement.hasAttribute(r)}let o=e=>e?.tagName==="TURBO-FRAME";function s(e,t){return e.split("/",3).join("/")===t.split("/",3).join("/")}function l(e,t){return e.split("/",2).join("/")===t.split("/",2).join("/")}async function c(){let e=document.head.querySelectorAll("link[rel=stylesheet]"),t=new Set([...document.styleSheets].map(e=>e.href)),n=[];for(let r of e)""===r.href||t.has(r.href)||n.push(u(r));await Promise.all(n)}let u=(e,t=2e3)=>new Promise(n=>{let r=()=>{e.removeEventListener("error",r),e.removeEventListener("load",r),n()};e.addEventListener("load",r,{once:!0}),e.addEventListener("error",r,{once:!0}),setTimeout(r,t)}),d=(e,t)=>{let n=t||e.querySelectorAll("[data-turbo-replace]"),r=[...document.querySelectorAll("[data-turbo-replace]")];for(let e of n){let t=r.find(t=>t.id===e.id);t&&t.replaceWith(e.cloneNode(!0))}},m=e=>{for(let t of e.querySelectorAll("link[rel=stylesheet]"))document.head.querySelector(`link[href="${t.getAttribute("href")}"],
           link[data-href="${t.getAttribute("data-href")}"]`)||document.head.append(t)},f=e=>{for(let t of e.querySelectorAll("script"))document.head.querySelector(`script[src="${t.getAttribute("src")}"]`)||p(t)},h=e=>{let{src:t}=e;if(!t)return;let n=document.createElement("script"),r=e.getAttribute("type");return r&&(n.type=r),n.src=t,n},p=e=>{let t=h(e);document.head&&t&&document.head.appendChild(t)},g=e=>{let t=[];for(let n of e.querySelectorAll('meta[data-turbo-track="reload"]'))document.querySelector(`meta[http-equiv="${n.getAttribute("http-equiv")}"]`)?.content!==n.content&&t.push(y(n.getAttribute("http-equiv")||""));return t},b=e=>{let t=e.querySelector("[data-turbo-head]")||e.head;return{title:t.querySelector("title")?.textContent,transients:[...t.querySelectorAll("[data-turbo-transient]")].map(e=>e.cloneNode(!0)),bodyClasses:e.querySelector("meta[name=turbo-body-classes]")?.content,replacedElements:[...e.querySelectorAll("[data-turbo-replace]")].map(e=>e.cloneNode(!0))}},v=()=>[...document.documentElement.attributes],y=e=>e.replace(/^x-/,"").replaceAll("-","_"),w=e=>document.dispatchEvent(new CustomEvent("turbo:reload",{detail:{reason:e}})),E=()=>document.dispatchEvent(new CustomEvent("turbo:restored")),S=(e,t)=>{for(let n of e.attributes)t.hasAttribute(n.nodeName)||"aria-busy"===n.nodeName||e.removeAttribute(n.nodeName);for(let n of t.attributes)e.getAttribute(n.nodeName)!==n.nodeValue&&e.setAttribute(n.nodeName,n.nodeValue)}},16561:(e,t,n)=>{"use strict";n.d(t,{U:()=>l});var r=n(39595),i=n(74043),a=n(97325);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o}let l={Initializing:"initializing",Unsupported:"unsupported",Ready:"ready",Waiting:"waiting",Error:"error",Submitting:"submitting"};let WebauthnGetElement=class WebauthnGetElement extends HTMLElement{async connectedCallback(){this.originalButtonText=this.getCurrentButtonText(),this.originalErrorText=this.errorText.textContent,this.setState((0,i.$j)()?l.Ready:l.Unsupported),this.passkeySupport=await window.PublicKeyCredential?.isUserVerifyingPlatformAuthenticatorAvailable(),this.state!==l.Unsupported&&!this.passkeySupport&&this.passkeysUnsupportedMessage&&(this.passkeysUnsupportedMessage.hidden=!1),this.subtleLogin?this.handleWebauthnSubtle():this.showWebauthnLoginFragment()}handleWebauthnSubtle(){let e=document.querySelector(".js-webauthn-subtle");e&&(e.hidden=!1,this.updateWebauthnSubtleParentBoxVisibility(!1),e.addEventListener("webauthn-subtle-submit",()=>{this.showWebauthnLoginFragment(),this.state!==l.Unsupported&&this.prompt()}))}showWebauthnLoginFragment(){let e=document.querySelector(".js-webauthn-login-section");e&&(e.hidden=!1,this.updateWebauthnSubtleParentBoxVisibility(!0))}updateWebauthnSubtleParentBoxVisibility(e){let t=document.querySelector(".js-webauthn-hint");t&&(t.hidden=e)}getCurrentButtonText(){return this.buttonText.textContent||""}setCurrentButtonText(e){this.buttonText.textContent=e}setState(e){let t=this.button.getAttribute("data-retry-message")||this.originalButtonText,n=this.hasErrored?t:this.originalButtonText;for(let e of(this.setCurrentButtonText(n),this.button.disabled=!1,this.button.hidden=!1,this.errorText.textContent="",this.messages))e.hidden=!0;switch(e){case l.Initializing:this.button.disabled=!0;break;case l.Unsupported:this.button.disabled=!0,this.unsupportedMessage.hidden=!1,this.passkeysUnsupportedMessage&&(this.passkeysUnsupportedMessage.hidden=!0);break;case l.Ready:break;case l.Waiting:this.waitingMessage.hidden=!1,this.button.hidden=!0;break;case l.Error:this.errorMessage.hidden=!1,this.errorText.textContent=this.originalErrorText;break;case l.Submitting:this.setCurrentButtonText("Verifying\u2026"),this.button.disabled=!0;break;default:throw Error("invalid state")}this.state=e}async prompt(e,t){e?.preventDefault(),this.dispatchEvent(new CustomEvent("webauthn-get-prompt"));try{t||this.setState(l.Waiting);let e=JSON.parse(this.dataJson),n=await (0,i.Jt)((0,i.d5)(e));this.setState(l.Submitting);let r=this.closest(".js-webauthn-form");r.querySelector(".js-webauthn-response").value=JSON.stringify(n),(0,a.k_)(r)}catch(e){if(!t)throw this.hasErrored=!0,this.setState(l.Error),e}}constructor(...e){super(...e),o(this,"state",l.Initializing),o(this,"dataJson",""),o(this,"subtleLogin",!1),o(this,"hasErrored",!1)}};o(WebauthnGetElement,"attrPrefix",""),s([r.aC],WebauthnGetElement.prototype,"button",void 0),s([r.aC],WebauthnGetElement.prototype,"buttonText",void 0),s([r.zV],WebauthnGetElement.prototype,"messages",void 0),s([r.aC],WebauthnGetElement.prototype,"capitalizedDescription",void 0),s([r.aC],WebauthnGetElement.prototype,"unsupportedMessage",void 0),s([r.aC],WebauthnGetElement.prototype,"passkeysUnsupportedMessage",void 0),s([r.aC],WebauthnGetElement.prototype,"waitingMessage",void 0),s([r.aC],WebauthnGetElement.prototype,"errorMessage",void 0),s([r.aC],WebauthnGetElement.prototype,"errorText",void 0),s([r.CF],WebauthnGetElement.prototype,"dataJson",void 0),s([r.CF],WebauthnGetElement.prototype,"subtleLogin",void 0),WebauthnGetElement=s([r.p_],WebauthnGetElement)},91903:(e,t,n)=>{"use strict";n.d(t,{K:()=>i,e:()=>a});var r=n(74043);function i(){return(0,r.$j)()?"supported":"unsupported"}async function a(){return await window.PublicKeyCredential?.isUserVerifyingPlatformAuthenticatorAvailable()?"supported":"unsupported"}}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52","vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_github_selector-observer_dist_index_esm_js","vendors-node_modules_braintree_browser-detection_dist_browser-detection_js-node_modules_githu-bb80ec","vendors-node_modules_lit-html_lit-html_js","vendors-node_modules_morphdom_dist_morphdom-esm_js","vendors-node_modules_fzy_js_index_js-node_modules_github_paste-markdown_dist_index_js","vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f","vendors-node_modules_github_auto-complete-element_dist_index_js-node_modules_github_catalyst_-8e9f78","vendors-node_modules_color-convert_index_js","vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-d8c643","vendors-node_modules_github_quote-selection_dist_index_js-node_modules_github_session-resume_-c1aa61","ui_packages_failbot_failbot_ts","ui_packages_updatable-content_updatable-content_ts","app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-900dde","app_assets_modules_github_sticky-scroll-into-view_ts","app_assets_modules_github_behaviors_ajax-error_ts-app_assets_modules_github_behaviors_include-d0d0a6","app_assets_modules_github_behaviors_commenting_edit_ts-app_assets_modules_github_behaviors_ht-83c235"],()=>t(38962)),e.O()}]);
//# sourceMappingURL=behaviors-7979902c23ed.js.map