"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["notifications-subscriptions-menu"],{78924:(e,t,n)=>{n.d(t,{I:()=>a});let a=(0,n(96540).createContext)(null)},52811:(e,t,n)=>{n.d(t,{C:()=>o,i:()=>i});var a=n(96679),s=n(27851),r=n(46493);function o(e,t){(0,s.G7)("arianotify_comprehensive_migration")?i(l(e),{...t,element:t?.element??e}):(0,s.G7)("primer_live_region_element")&&t?.element===void 0?(0,r.Cj)(e,{politeness:t?.assertive?"assertive":"polite"}):i(l(e),t)}function i(e,t){let{assertive:n,element:o}=t??{};(0,s.G7)("arianotify_comprehensive_migration")&&"ariaNotify"in Element.prototype?(o||document.body).ariaNotify(e):(0,s.G7)("primer_live_region_element")&&void 0===o?(0,r.iP)(e,{politeness:n?"assertive":"polite"}):function(e,t,n){let s=n??a.XC?.querySelector(t?"#js-global-screen-reader-notice-assertive":"#js-global-screen-reader-notice");s&&(s.textContent===e?s.textContent=`${e}\u00A0`:s.textContent=e)}(e,n,o)}function l(e){return(e.getAttribute("aria-label")||e.innerText||"").trim()}},53005:(e,t,n)=>{n.d(t,{O:()=>o,S:()=>r});var a=n(96679);let s=a.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",r="X-GitHub-Client-Version";function o(){return s}},39627:(e,t,n)=>{n.d(t,{D:()=>r,Y:()=>o});var a=n(52811),s=n(96679);function r(e){if(!s.XC)return;let t=s.XC.querySelector("title"),n=s.XC.createElement("title");n.textContent=e,t?t.textContent!==e&&(t.replaceWith(n),(0,a.i)(e)):(s.XC.head.appendChild(n),(0,a.i)(e))}function o(e){return document.body.classList.contains("logged-out")?`${e} \xb7 GitHub`:e}},26559:(e,t,n)=>{n.d(t,{jC:()=>l,kt:()=>o,tV:()=>i});var a=n(53005),s=n(27851),r=n(88191);function o(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,r.wE)(e)};return(0,s.G7)("client_version_header")&&(t={...t,[a.S]:(0,a.O)()}),t}function i(e,t){for(let[n,a]of Object.entries(o(t)))e.set(n,a)}function l(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,n)=>{n.d(t,{$r:()=>o,M1:()=>i,li:()=>s,pS:()=>c,wE:()=>l});var a=n(96679);let s="X-Fetch-Nonce",r=new Set;function o(e){r.add(e)}function i(){return r.values().next().value||""}function l(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[s]=i():r.has(e)?t[s]=e:t[s]=Array.from(r).join(","),t}function c(){let e=a.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&o(e)}},13233:(e,t,n)=>{n.d(t,{l:()=>a});let a=()=>void 0},39784:(e,t,n)=>{let a;var s,r=n(52497),o=n(74848),i=n(96540),l=n(10569),c=n(95776),d=n(2724),u=n(38621),h=n(60039);let p=(a="/notifications/subscribe",async e=>{try{let t=await (0,h.DI)(a,{method:"POST",body:e});if(t.ok)return t;return Error("Failed to update")}catch(e){return e}});var m=n(15385);let f={NONE:"none",WATCHING:"watching",IGNORING:"ignoring",CUSTOM:"custom"},b={[f.NONE]:"Participating and @mentions",[f.WATCHING]:"All Activity",[f.IGNORING]:"Ignore",[f.CUSTOM]:"Custom"},g={...b,[f.NONE]:"Participating"},y={[f.NONE]:"Watch",[f.WATCHING]:"Unwatch",[f.IGNORING]:"Stop ignoring",[f.CUSTOM]:"Unwatch"},C=[{name:b[f.NONE],description:"Only receive notifications from this repository when participating or @mentioned.",subscriptionType:f.NONE},{name:b[f.WATCHING],description:"Notified of all notifications on this repository.",subscriptionType:f.WATCHING},{name:b[f.IGNORING],description:"Never be notified.",subscriptionType:f.IGNORING},{name:b[f.CUSTOM],description:"Select events you want to be notified of in addition to participating and @mentions.",trailingIcon:(0,i.createElement)(u.ArrowRightIcon),subscriptionType:f.CUSTOM}],x=e=>e in y?y[e]:"",v=(e,t)=>{let n=x(e),a=g[e];return e===f.IGNORING?`${n} in ${t}`:`${n}: ${a} in ${t}`},N={subscriptionTypeName:"SubscriptionList-module__subscriptionTypeName--mWUOD"};function w(e){return(0,o.jsx)(m.l,{selectionVariant:"single",children:C.map((t,n)=>(0,o.jsxs)(i.Fragment,{children:[(0,o.jsxs)(m.l.Item,{selected:t.subscriptionType===e.selected,onSelect:()=>e.onSelect(t.subscriptionType),children:[(0,o.jsx)("span",{className:N.subscriptionTypeName,children:t.name}),(0,o.jsx)(m.l.Description,{variant:"block",children:t.description}),t.trailingIcon?(0,o.jsx)(m.l.TrailingVisual,{children:t.trailingIcon}):null]}),n!==C.length-1?(0,o.jsx)(m.l.Divider,{}):""]},n))})}try{w.displayName||(w.displayName="SubscriptionList")}catch{}var S=n(73451),_=n(65607),j=n(45286),T=n(55847),A=n(94977);function k(e){let[t,n]=(0,i.useState)(!1),[a,s]=(0,i.useState)(""),r=e.items.filter(e=>e?.text?.toLowerCase().startsWith(a.toLowerCase())),l=i.memo(I);return(0,o.jsx)(j.X,{title:"Select labels",renderAnchor:t=>0===e.items.length?(0,o.jsx)(A.A,{text:"Add labels to this repository to filter on them.",direction:"s",children:(0,o.jsx)(l,{anchorProps:t,itemsLength:e.items.length,labelsText:e.labelsText})}):(0,o.jsx)(l,{anchorProps:t,itemsLength:e.items.length,labelsText:e.labelsText}),placeholderText:"Filter labels",open:t,onCancel:e.resetLabels,onOpenChange:(t,a)=>{n(t),"click-outside"===a&&e.applyLabels()},items:r,selected:e.selectedLabels,onSelectedChange:e.onChangeLabels,onFilterChange:s,showItemDividers:!0,overlayProps:{width:"small",height:"medium",maxHeight:"medium"}})}let I=({anchorProps:e,itemsLength:t,labelsText:n})=>(0,o.jsx)(T.Q,{leadingVisual:u.TagIcon,trailingAction:u.TriangleDownIcon,...e,"aria-label":"Filter labels","aria-describedby":"select-labels","aria-haspopup":"dialog",size:"small",disabled:0===t,children:0===t?"No labels available":(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:"color-fg-muted",children:"Labels: "}),(0,o.jsx)("span",{id:"select-labels",children:n})]})});try{k.displayName||(k.displayName="FilterLabels")}catch{}try{(s=ButtonFilter).displayName||(s.displayName="ButtonFilter")}catch{}var O=n(63867);let E={footerContainer:"FooterActions-module__footerContainer--Z9ixI",buttonsContainer:"FooterActions-module__buttonsContainer--lkkwg",errorMessageText:"FooterActions-module__errorMessageText--UsWpj",FooterActionsSpinner:"FooterActions-module__FooterActionsSpinner--ElpdD",applyButton:"FooterActions-module__applyButton--cgiu4"};function L(e){let[t,n]=(0,i.useState)(!1),a=(0,i.useCallback)(()=>(e.nextFocusRef?.current?.focus(),!0),[e.nextFocusRef]),s=(0,i.useCallback)(()=>{n(!0)},[]),r=(0,i.useCallback)(()=>{e.onApply(),setTimeout(()=>{e?.checkStatus&&e.checkStatus(s)},600)},[e,s]);return(0,o.jsxs)("div",{className:E.footerContainer,children:[e.showError?(0,o.jsx)("span",{className:E.errorMessageText,children:"Error. Please try again."}):null,(0,o.jsxs)("div",{className:E.buttonsContainer,style:e.overrideButtonStyles??{padding:"var(--base-size-16)"},children:[!e.showError&&t?(0,o.jsx)(O.A,{size:"small",className:E.FooterActionsSpinner}):null,(0,o.jsx)(T.Q,{size:"small",onClick:()=>e.onCancel(),onBlur:t=>{e.disabled&&a(t)},children:"Cancel"}),(0,o.jsx)(T.Q,{disabled:e.disabled,variant:"primary",size:"small",onClick:()=>r(),onBlur:a,className:E.applyButton,children:"Apply"})]})]})}try{L.displayName||(L.displayName="FooterActions")}catch{}let R=e=>{let t=$(e,2);if(e.length>=2){if(2===e.length)return P(e);let n=$(e,3);if(n.length>30)return`${t.slice(0,30)}... +${e.length-2} more`;{let t=e.length>3?` +${e.length-3} more`:"";return`${n}${t}`}}if(1!==e.length)return"All";{let t=e[0]?.text||"";return t.length>30?`${t.slice(0,30)}...`:t}},P=e=>{let t=e[0]?.text||"",n=$(e,2);return n.length>30?t.length>25?`${t.slice(0,25)}... +1 more`:`${n.slice(0,30)}...`:n},$=(e,t)=>e.slice(0,t).map(e=>e.text).join(", "),F=e=>{switch(e){case"PullRequest":return"Pull requests";case"SecurityAlert":return"Security alerts";default:return`${e}s`}},M={filterContainer:"ThreadList-module__filterContainer--eNebD",threadContent:"ThreadList-module__threadContent--Ry8II",threadRow:"ThreadList-module__threadRow--lx6FW",threadDisabledMessage:"ThreadList-module__threadDisabledMessage--Wwkul"};function G(e){let[t,n]=(0,i.useState)(e.appliedThreads),[a,s]=(0,i.useState)(e.appliedLabels),[r,l]=(0,i.useState)(e.appliedLabels),[c,d]=(0,i.useState)(()=>R(e.appliedLabels));(0,i.useEffect)(()=>{e.appliedLabels.length>0&&!t.includes("Issue")&&n([...t,"Issue"])},[]);let u=(0,i.useCallback)(e=>{t&&t.includes(e)?n(t.filter(t=>t!==e)):n([...t,e])},[t]),h=(0,i.useCallback)(()=>{e.applyThreads(t)},[e,t]),p=(0,i.useCallback)(e=>{l(e),d(R(e))},[]),m=(0,i.useCallback)(()=>{s(r)},[r]),f=(0,i.useCallback)(()=>{l(a),d(R(a))},[a]);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:M.threadContent,children:e.subscribableThreadTypes.map((n,a)=>(0,o.jsxs)("div",{className:M.threadRow,style:a===e.subscribableThreadTypes.length-1?{}:{borderBottom:"1px solid var(--borderColor-default, var(--color-border-default))"},children:[(0,o.jsxs)(S.A,{children:[(0,o.jsx)(_.A,{checked:t.includes(n.name),onChange:()=>u(n.name)}),(0,o.jsx)(S.A.Label,{children:F(n.name)})]}),n.enabled?null:(0,o.jsxs)("p",{className:M.threadDisabledMessage,children:[F(n.name)," are not enabled for this repository"]}),(0,o.jsx)("div",{"aria-live":"polite",children:"Issue"===n.name&&e.showLabelSubscriptions&&t.includes("Issue")?(0,o.jsx)("div",{className:M.filterContainer,children:(0,o.jsx)(k,{filterAction:h,items:e.repoLabels,labelsText:c,onChangeLabels:p,selectedLabels:r,applyLabels:m,resetLabels:f})}):null})]},a))}),(0,o.jsx)(L,{onCancel:e.cancelMenuCallback,onApply:()=>e.saveThreads(t,r),showError:e.showError,disabled:0===t.length||e.isSavingThreads})]})}try{G.displayName||(G.displayName="ThreadList")}catch{}let W={watchCounter:"NotificationsSubscriptionsMenu-module__watchCounter--nAbhU",watchButton:"NotificationsSubscriptionsMenu-module__watchButton--ifxlS"};function D({repositoryId:e,repositoryName:t,watchersCount:n,subscriptionType:a,subscribableThreadTypes:s,repositoryLabels:r,showLabelSubscriptions:h}){let m=(0,i.useMemo)(()=>r.map(e=>({id:e.id,text:e.name,selected:e.subscribed})),[r]),b=m.filter(e=>e.selected),g=(0,i.useMemo)(()=>s.map(e=>e.subscribed||"Issue"===e.name&&h&&b.length>0?e.name:null).filter(e=>null!==e),[s,h,b]),[y,C]=(0,i.useState)(!1),[N,S]=(0,i.useState)(!1),[_,j]=(0,i.useState)(!1),T=(0,i.useCallback)(()=>j(!1),[]),[A,k]=(0,i.useState)(g.length>0?f.CUSTOM:a),[I,O]=(0,i.useState)(A),[E,L]=(0,i.useState)(g),[R,P]=(0,i.useState)(b),[$,F]=(0,i.useState)(!1),M=(0,i.useRef)(null),D=(0,i.useCallback)(()=>{S(!1),k(I)},[I]),B=(0,i.useCallback)(async(t,n)=>{F(!0),L(t),P(n),O(f.CUSTOM);let a=new FormData;a.set("do","custom"),a.set("repository_id",e),t.map(e=>{a.append("thread_types[]",e)}),n.map(e=>{e.id&&a.append("labels[]",e.id.toString())}),(await p(a)).ok?(S(!1),F(!1)):C(!0)},[e]),U=(0,i.useCallback)(async t=>{let n=new FormData;return t===f.IGNORING?n.set("do","ignore"):t===f.WATCHING?n.set("do","subscribed"):(t===f.NONE||t===f.CUSTOM&&0===E.length)&&n.set("do","included"),n.append("thread_types[]",""),n.set("repository_id",e),!(await p(n) instanceof Error)},[e,E]),q=(0,i.useCallback)(async e=>{e===f.CUSTOM?(S(!0),k(f.CUSTOM)):await U(e)?(k(e),O(e),L([])):j(!0)},[k]),H=(0,i.useCallback)(e=>{L(e)},[L]),X=(0,i.useMemo)(()=>v(A,t),[A,t]);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("div",{className:"d-md-none",children:(0,o.jsxs)(l.W,{children:[(0,o.jsx)(l.W.Button,{"data-testid":"notifications-subscriptions-menu-button-desktop",leadingVisual:A===f.IGNORING?u.BellSlashIcon:u.EyeIcon,trailingAction:null,className:W.watchButton,"aria-label":X,children:(0,o.jsx)(o.Fragment,{})}),(0,o.jsx)(l.W.Overlay,{width:"medium",children:(0,o.jsx)(w,{selected:A,onSelect:q})})]})}),(0,o.jsx)("div",{className:"d-none d-md-block",children:(0,o.jsxs)(l.W,{children:[(0,o.jsxs)(l.W.Button,{"data-testid":"notifications-subscriptions-menu-button-mobile",size:"small",leadingVisual:A===f.IGNORING?u.BellSlashIcon:u.EyeIcon,sx:{'&& [data-component="leadingVisual"]':{color:"var(--fgColor-muted, var(--color-fg-muted))"}},"aria-label":X,children:[x(A),(0,o.jsx)("span",{className:`ml-2 Counter rounded-3 ${W.watchCounter}`,children:n})]}),(0,o.jsx)(l.W.Overlay,{width:"medium",children:(0,o.jsx)(w,{selected:A,onSelect:q})})]})}),(0,o.jsx)(c.A,{returnFocusRef:M,isOpen:N,onDismiss:()=>D(),"aria-labelledby":"header",children:(0,o.jsxs)("div",{"data-testid":"inner",children:[(0,o.jsxs)(c.A.Header,{id:"header",children:["Subscribe to events for ",t]}),(0,o.jsx)(G,{subscribableThreadTypes:s,showLabelSubscriptions:h,cancelMenuCallback:D,appliedThreads:E,repoLabels:m,subscribedThreads:g,applyThreads:H,appliedLabels:R,saveThreads:B,showError:y,isSavingThreads:$})]})}),_&&(0,o.jsx)(d.l,{title:"Cannot watch this repository",onClose:T,children:(0,o.jsxs)(d.l.Body,{children:["You have reached the limit of 10,000 watched repositories. Update your"," ",(0,o.jsx)("a",{href:"/watching",children:"watch settings"})," to continue."]})})]})}try{D.displayName||(D.displayName="NotificationsSubscriptionsMenu")}catch{}(0,r.k)("notifications-subscriptions-menu",{Component:D})},7531:(e,t,n)=>{n.d(t,{Y:()=>a});function a(){let e={};return e.promise=new Promise((t,n)=>{e.resolve=t,e.reject=n}),e}},41764:(e,t,n)=>{n.d(t,{A:()=>i});let{getItem:a,setItem:s,removeItem:r}=(0,n(85351).A)("localStorage"),o="REACT_PROFILING_ENABLED",i={enable:()=>s(o,"true"),disable:()=>r(o),isEnabled:()=>!!a(o)}},64899:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(17515),s=n(96540);function r(){let e=(0,s.useRef)(!1),t=(0,s.useCallback)(()=>e.current,[]);return(0,a.N)(()=>(e.current=!0,()=>{e.current=!1}),[]),t}},17515:(e,t,n)=>{n.d(t,{N:()=>r});var a=n(96679),s=n(96540);let r=void 0!==a.cg?.document?.createElement?s.useLayoutEffect:s.useEffect},47019:(e,t,n)=>{n.d(t,{A:()=>r});var a=n(64899),s=n(96540);let r=function(e){let t=(0,a.A)(),[n,r]=(0,s.useState)(e);return[n,(0,s.useCallback)(e=>{t()&&r(e)},[t])]}},60039:(e,t,n)=>{n.d(t,{DI:()=>s,QJ:()=>o,Sr:()=>i,lS:()=>r});var a=n(26559);function s(e,t={}){var n=e;if(new URL(n,window.location.origin).origin!==window.location.origin)throw Error("Can not make cross-origin requests from verifiedFetch");let r=function(e){let t=new URL(e,window.location.href),n=new URL(window.location.href,window.location.origin),a=n.searchParams.get("_features");a&&!t.searchParams.has("_features")&&t.searchParams.set("_features",a);let s=n.searchParams.get("_tracing");return s&&!t.searchParams.has("_tracing")&&t.searchParams.set("_tracing",s),e.startsWith(window.location.origin)?t.href:`${t.pathname}${t.search}`}(e),o={...t.headers,"GitHub-Verified-Fetch":"true",...(0,a.kt)()};return fetch(r,{...t,headers:o})}function r(e,t){let n={...t?.headers??{},Accept:"application/json","Content-Type":"application/json"},a=t?.body?JSON.stringify(t.body):void 0;return s(e,{...t,body:a,headers:n})}function o(e,t={}){let n={...t.headers,"GitHub-Is-React":"true"};return s(e,{...t,headers:n})}function i(e,t){let n={...t?.headers??{},"GitHub-Is-React":"true"};return r(e,{...t,headers:n})}},26033:(e,t,n)=>{n.d(t,{y:()=>o});var a=n(74848),s=n(21728),r=n(78924);function o(e){let t,n,o,i=(0,s.c)(7),{children:l,appName:c,category:d,metadata:u}=e;return i[0]!==c||i[1]!==d||i[2]!==u?(n={appName:c,category:d,metadata:u},i[0]=c,i[1]=d,i[2]=u,i[3]=n):n=i[3],t=n,i[4]!==l||i[5]!==t?(o=(0,a.jsx)(r.I.Provider,{value:t,children:l}),i[4]=l,i[5]=t,i[6]=o):o=i[6],o}try{o.displayName||(o.displayName="AnalyticsProvider")}catch{}},60674:(e,t,n)=>{n.d(t,{BP:()=>u,D3:()=>d,O8:()=>l});var a=n(74848),s=n(21728),r=n(96540),o=n(96679),i=n(17515);let l={ServerRender:"ServerRender",ClientHydrate:"ClientHydrate",ClientRender:"ClientRender"},c=(0,r.createContext)(l.ClientRender);function d(e){let t,n,d,u,h=(0,s.c)(8),{wasServerRendered:p,children:m}=e;h[0]!==p?(t=()=>o.X3?l.ServerRender:p?l.ClientHydrate:l.ClientRender,h[0]=p,h[1]=t):t=h[1];let[f,b]=(0,r.useState)(t);return h[2]!==f?(n=()=>{f!==l.ClientRender&&b(l.ClientRender)},d=[f],h[2]=f,h[3]=n,h[4]=d):(n=h[3],d=h[4]),(0,i.N)(n,d),h[5]!==m||h[6]!==f?(u=(0,a.jsx)(c.Provider,{value:f,children:m}),h[5]=m,h[6]=f,h[7]=u):u=h[7],u}function u(){return(0,r.useContext)(c)}try{c.displayName||(c.displayName="RenderPhaseContext")}catch{}try{d.displayName||(d.displayName="RenderPhaseProvider")}catch{}},99543:(e,t,n)=>{n.d(t,{Qn:()=>l,T8:()=>d,Y6:()=>h,k6:()=>u});var a=n(74848),s=n(65556),r=n(96540),o=n(13233),i=n(47019);let l=5e3,c=(0,r.createContext)({addToast:o.l,addPersistedToast:o.l,clearPersistedToast:o.l}),d=(0,r.createContext)({toasts:[],persistedToast:null});function u({children:e}){let[t,n]=(0,i.A)([]),[o,u]=(0,r.useState)(null),{safeSetTimeout:h}=(0,s.A)(),p=(0,r.useCallback)(function(e){n([...t,e]),h(()=>n(t.slice(1)),l)},[t,h,n]),m=(0,r.useCallback)(function(e){u(e)},[u]),f=(0,r.useCallback)(function(){u(null)},[u]),b=(0,r.useMemo)(()=>({addToast:p,addPersistedToast:m,clearPersistedToast:f}),[m,p,f]),g=(0,r.useMemo)(()=>({toasts:t,persistedToast:o}),[t,o]);return(0,a.jsx)(c.Provider,{value:b,children:(0,a.jsx)(d.Provider,{value:g,children:e})})}function h(){return(0,r.useContext)(c)}try{c.displayName||(c.displayName="ToastContext")}catch{}try{d.displayName||(d.displayName="InternalToastsContext")}catch{}try{u.displayName||(u.displayName="ToastContextProvider")}catch{}},42218:(e,t,n)=>{n.d(t,{V:()=>h});var a=n(74848),s=n(96540),r=n(99543),o=n(38621),i=n(65556),l=n(16255);let c={info:"",success:"Toast--success",error:"Toast--error"},d={info:(0,a.jsx)(o.InfoIcon,{}),success:(0,a.jsx)(o.CheckIcon,{}),error:(0,a.jsx)(o.StopIcon,{})},u=({message:e,timeToLive:t,icon:n,type:r="info",role:o="log"})=>{let[u,h]=s.useState(!0),{safeSetTimeout:p}=(0,i.A)();return(0,s.useEffect)(()=>{t&&p(()=>h(!1),t-300)},[p,t]),(0,a.jsx)(l.Z,{children:(0,a.jsx)("div",{className:"p-1 position-fixed bottom-0 left-0 mb-3 ml-3",children:(0,a.jsxs)("div",{className:`Toast ${c[r]} ${u?"Toast--animateIn":"Toast--animateOut"}`,id:"ui-app-toast","data-testid":`ui-app-toast-${r}`,role:o,children:[(0,a.jsx)("span",{className:"Toast-icon",children:n||d[r]}),(0,a.jsx)("span",{className:"Toast-content",children:e})]})})})};try{u.displayName||(u.displayName="Toast")}catch{}function h(){let{toasts:e,persistedToast:t}=(0,s.useContext)(r.T8);return(0,a.jsxs)(a.Fragment,{children:[e.map((e,t)=>(0,a.jsx)(u,{message:e.message,icon:e.icon,timeToLive:r.Qn,type:e.type,role:e.role},t)),t&&(0,a.jsx)(u,{message:t.message,icon:t.icon,type:t.type,role:t.role})]})}try{h.displayName||(h.displayName="Toasts")}catch{}},39595:(e,t,n)=>{let a;n.d(t,{CF:()=>f,p_:()=>I,FB:()=>u,Se:()=>S,aC:()=>A,zV:()=>k});let s=new WeakSet,r=new WeakMap;function o(e=document){if(r.has(e))return r.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)d(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&i(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let a={get closed(){return t},unsubscribe(){t=!0,r.delete(e),n.disconnect()}};return r.set(e,a),a}function i(e){for(let t of e.querySelectorAll("[data-action]"))d(t);e instanceof Element&&e.hasAttribute("data-action")&&d(e)}function l(e){let t=e.currentTarget;for(let n of c(t))if(e.type===n.type){let a=t.closest(n.tag);s.has(a)&&"function"==typeof a[n.method]&&a[n.method](e);let r=t.getRootNode();if(r instanceof ShadowRoot&&s.has(r.host)&&r.host.matches(n.tag)){let t=r.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function d(e){for(let t of c(e))e.addEventListener(t.type,l)}function u(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let a of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!a.closest(n))return a}for(let a of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(a.closest(n)===e)return a}let h=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),p=(e,t="property")=>{let n=h(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},m="attr";function f(e,t){T(e,m).add(t)}let b=new WeakSet;function g(e,t){if(b.has(e))return;b.add(e);let n=Object.getPrototypeOf(e),a=n?.constructor?.attrPrefix??"data-";for(let s of(t||(t=T(n,m)),t)){let t=e[s],n=p(`${a}${s}`),r={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?r={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(r={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,s,r),s in e&&!e.hasAttribute(n)&&r.set.call(e,t)}}let y=new Map,C=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),x=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},a=()=>t.abort();document.addEventListener("mousedown",a,n),document.addEventListener("touchstart",a,n),document.addEventListener("keydown",a,n),document.addEventListener("pointerdown",a,n)}),v={ready:()=>C,firstInteraction:()=>x,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let a of e)if(a.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},N=new WeakMap;function w(e){cancelAnimationFrame(N.get(e)||0),N.set(e,requestAnimationFrame(()=>{for(let t of y.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let a=n?.getAttribute("data-load-on")||"ready",s=a in v?v[a]:v.ready;for(let e of y.get(t)||[])s(t).then(e);y.delete(t),N.delete(e)}}}))}function S(e,t){for(let[n,a]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))y.has(n)||y.set(n,new Set),y.get(n).add(a);_(document)}function _(e){a||(a=new MutationObserver(e=>{if(y.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&w(e)})),w(e),a.observe(e,{subtree:!0,childList:!0})}let j=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let a=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,a)};let s=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,a){t.attributeChangedCallback(this,e,n,a,s)};let r=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,r)},set(e){r=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",a=e=>p(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...T(e.prototype,m)].map(a).concat(t),set(e){t=e}})}(e),function(e){let t=h(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,a;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(g(e),s.add(e),e.shadowRoot&&(i(a=e.shadowRoot),o(a)),i(e),o(e.ownerDocument),t?.call(e),e.shadowRoot)&&(i(n=e.shadowRoot),o(n),_(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,a,s){g(e),"data-catalyst"!==t&&s&&s.call(e,t,n,a)}};function T(e,t){if(!Object.prototype.hasOwnProperty.call(e,j)){let t=e[j],n=e[j]=new Map;if(t)for(let[e,a]of t)n.set(e,new Set(a))}let n=e[j];return n.has(t)||n.set(t,new Set),n.get(t)}function A(e,t){T(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return u(this,t)}})}function k(e,t){T(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let a of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))a.closest(e)||n.push(a);for(let a of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))a.closest(e)===this&&n.push(a);return n}})}function I(e){new CatalystDelegate(e)}}},e=>{var t=t=>e(e.s=t);e.O(0,["primer-react","react-core","react-lib","octicons-react","vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52","vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483","vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6","ui_packages_failbot_failbot_ts"],()=>t(39784)),e.O()}]);
//# sourceMappingURL=notifications-subscriptions-menu-50f6a9ba8e34.js.map