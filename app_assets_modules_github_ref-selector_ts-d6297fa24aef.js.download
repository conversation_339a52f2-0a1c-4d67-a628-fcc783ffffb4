"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_github_ref-selector_ts"],{33284:(e,t,i)=>{var r=i(99707),n=i(78134),s=i(39595),o=i(80590);function a(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function h(e,t,i,r){var n,s=arguments.length,o=s<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,i,r);else for(var a=e.length-1;a>=0;a--)(n=e[a])&&(o=(s<3?n(o):s>3?n(t,i,o):n(t,i))||o);return s>3&&o&&Object.defineProperty(t,i,o),o}let l=class RefSelectorElement extends HTMLElement{connectedCallback(){window.addEventListener("resize",this.windowResized),this.refType="branch"===this.getRequiredAttr("type")?r._.Branch:r._.Tag;let e=this.getAttribute("current-committish");this.currentCommittish=e?atob(e):null,this.input=this.hasAttribute("initial-filter")&&this.currentCommittish||"",this.defaultBranch=atob(this.getRequiredAttr("default-branch")),this.nameWithOwner=atob(this.getRequiredAttr("name-with-owner")),this.canCreate=this.hasAttribute("can-create"),this.prefetchOnMouseover=this.hasAttribute("prefetch-on-mouseover");let t=this.getRequiredAttr("query-endpoint"),i=this.getRequiredAttr("cache-key");this.index=new r.d(this.refType,this,t,i,this.nameWithOwner),this.updateViewportSize(),this.setupFetchListeners()}disconnectedCallback(){this.resizeAnimationRequest&&cancelAnimationFrame(this.resizeAnimationRequest),window.removeEventListener("resize",this.windowResized)}updateViewportSize(){this.isMobileViewport=window.innerWidth<544,this.windowHeight=window.innerHeight}inputEntered(e){this.input=e.detail,this.render()}tabSelected(){this.index.fetchData()}renderTemplate(e,t){return new n.i4(e,t,n.xr)}renderRow(e){let t=this.index.currentSearchResult[e];if(!t&&e>=this.listLength)return document.createElement("span");if(this.index.fetchFailed)return this.renderTemplate(this.fetchFailedTemplate,{index:e,refName:this.input});if(!t){let t=this.input===this.currentCommittish;return this.isCurrentVisible||(this.isCurrentVisible=t),this.renderTemplate(this.noMatchTemplate,{index:e,isCurrent:t,refName:this.input})}let i=this.input.length>0,r=t===this.currentCommittish;this.isCurrentVisible||(this.isCurrentVisible=r);let n=this.renderTemplate(this.itemTemplate,{refName:t,index:e,isFilteringClass:i?"is-filtering":"",urlEncodedRefName:this.urlEncodeRef(t),isCurrent:r,isNotDefault:t!==this.defaultBranch});if(i){let e=n.querySelector("span");e.textContent="";let i=t.split(this.input),r=i.length-1;for(let t=0;t<i.length;t++){let n=i[t];if(e.appendChild(document.createTextNode(n)),t<r){let t=document.createElement("b");t.textContent=this.input,e.appendChild(t)}}}return n}urlEncodeRef(e){return encodeURIComponent(e).replaceAll("%2F","/").replaceAll("%3A",":").replaceAll("%2B","+")}render(){if(this.currentSelectionIndex=null,!this.index.isLoading){if(!this.virtualizedList){this.index.search(this.input),this.setupVirtualizedList();return}this.listContainer.scrollTop=0,this.index.search(this.input),this.virtualizedList.setRowCount(this.listLength)}}get listHeight(){return this.isMobileViewport?.3*this.windowHeight:330}get listLength(){let e=this.index.currentSearchResult.length;return this.showCreateRow?e+1:e||1}get showCreateRow(){return!this.index.fetchFailed&&!this.index.exactMatchFound&&""!==this.input&&this.canCreate}getRequiredAttr(e,t=this){let i=t.getAttribute(e);if(!i)throw Error(`Missing attribute for ${t}: ${e}`);return i}setupFetchListeners(){let e=this.closest("details"),t=!1,i=()=>{t||(this.index.fetchData(),t=!0)};if(!e||e.open)return void i();e.addEventListener("toggle",i,{once:!0}),this.prefetchOnMouseover&&e.addEventListener("mouseover",i,{once:!0}),this.addEventListener("keydown",this.keydown),this.addEventListener("change",this.updateCurrent);let r=e.querySelector("input[data-ref-filter]");r&&(r.addEventListener("input",()=>{this.input=r.value,this.render()}),r.addEventListener("keydown",t=>{if("ArrowDown"!==t.key&&("Tab"!==t.key||t.shiftKey)){if("Enter"===t.key){let i=this.index.currentSearchResult.indexOf(this.input);if(-1===i)if(!this.showCreateRow)return;else i=this.listLength-1;e.querySelector(`[data-index="${i}"]`).click(),t.preventDefault()}}else t.preventDefault(),t.stopPropagation(),this.focusFirstListMember()}))}focusFirstListMember(){this.virtualizedList&&(this.currentSelectionIndex=0,this.focusItemAtIndex(this.currentSelectionIndex))}updateCurrent(e){e.target instanceof HTMLInputElement&&e.target.checked&&e.target.value&&(this.currentCommittish=e.target.value)}keydown(e){if(null!==this.currentSelectionIndex){if("Enter"===e.key){let t=document.activeElement;if(!t)return;t.click(),e.preventDefault();return}if("Tab"!==e.key&&"Escape"!==e.key)switch(e.preventDefault(),e.stopPropagation(),e.key){case"ArrowUp":this.currentSelectionIndex--,this.currentSelectionIndex<0&&(this.currentSelectionIndex=this.listLength-1),this.focusItemAtIndex(this.currentSelectionIndex);break;case"Home":this.currentSelectionIndex=0,this.focusItemAtIndex(this.currentSelectionIndex);break;case"End":this.currentSelectionIndex=this.listLength-1,this.focusItemAtIndex(this.currentSelectionIndex);break;case"ArrowDown":this.currentSelectionIndex++,this.currentSelectionIndex>this.listLength-1&&(this.currentSelectionIndex=0),this.focusItemAtIndex(this.currentSelectionIndex)}}}focusItemAtIndex(e){this.virtualizedList.scrollToIndex(e,"center"),setTimeout(()=>{let t=this.listContainer.querySelector(`[data-index="${e}"]`);t&&t.focus()},20)}setupVirtualizedList(){this.listContainer.textContent="",this.listContainer.style.maxHeight=`${this.listHeight}px`,this.virtualizedList=new o.A(this.listContainer,{height:this.listHeight,rowCount:this.listLength,renderRow:this.renderRow.bind(this),rowHeight:e=>{let t=this.isMobileViewport?54:33;return this.showCreateRow&&e===this.listLength-1?51:t},onRowsRendered:()=>{this.hiddenCurrentElement&&(this.listContainer.removeChild(this.hiddenCurrentElement),delete this.hiddenCurrentElement),this.isCurrentVisible?this.isCurrentVisible=!1:this.hiddenCurrentItemTemplate&&(this.hiddenCurrentElement=document.createElement("div"),this.hiddenCurrentElement?.appendChild(this.renderTemplate(this.hiddenCurrentItemTemplate,{refName:this.currentCommittish})),this.listContainer.appendChild(this.hiddenCurrentElement))},initialIndex:0,overscanCount:6}),this.virtualizedList.resize.bind(this.virtualizedList)}constructor(...e){super(...e),a(this,"isCurrentVisible",!1),a(this,"hiddenCurrentElement",void 0),a(this,"currentSelectionIndex",null),a(this,"resizeAnimationRequest",void 0),a(this,"handleWindowResize",()=>{if(!this.virtualizedList)return;let e=this.isMobileViewport,t=this.windowHeight;this.updateViewportSize();let i=e!==this.isMobileViewport,r=t!==this.windowHeight;if(i){this.virtualizedList.destroy(),this.setupVirtualizedList();return}this.isMobileViewport&&r&&(this.listContainer.style.maxHeight=`${this.listHeight}px`,this.virtualizedList.resize(this.listHeight))}),a(this,"windowResized",()=>{this.resizeAnimationRequest&&cancelAnimationFrame(this.resizeAnimationRequest),this.resizeAnimationRequest=requestAnimationFrame(this.handleWindowResize)})}};h([s.aC],l.prototype,"listContainer",void 0),h([s.aC],l.prototype,"itemTemplate",void 0),h([s.aC],l.prototype,"noMatchTemplate",void 0),h([s.aC],l.prototype,"fetchFailedTemplate",void 0),h([s.aC],l.prototype,"hiddenCurrentItemTemplate",void 0),l=h([s.p_],l)},7799:(e,t,i)=>{let r;function n(){if(!r)throw Error("Client env was requested before it was loaded. This likely means you are attempting to use client env at the module level in SSR, which is not supported. Please move your client env usage into a function.");return r}function s(){return r?.locale??"en-US"}function o(){return!!n().login}i.d(t,{JK:()=>s,M3:()=>o,_$:()=>n});!function(){if("undefined"!=typeof document){let e=document.getElementById("client-env");if(e)try{r=JSON.parse(e.textContent||"")}catch(e){console.error("Error parsing client-env",e)}}}()},32475:(e,t,i)=>{i.d(t,{G:()=>n,K:()=>s});var r=i(96679);let n=r.XC?.readyState==="interactive"||r.XC?.readyState==="complete"?Promise.resolve():new Promise(e=>{r.XC?.addEventListener("DOMContentLoaded",()=>{e()})}),s=r.XC?.readyState==="complete"?Promise.resolve():new Promise(e=>{r.cg?.addEventListener("load",e)})},27851:(e,t,i)=>{i.d(t,{G7:()=>h,XY:()=>l,fQ:()=>a});var r=i(5225),n=i(7799);function s(){return new Set((0,n._$)().featureFlags)}let o=i(96679).X3||function(){try{return process?.env?.STORYBOOK==="true"}catch{return!1}}()?s:(0,r.A)(s);function a(){return Array.from(o())}function h(e){return o().has(e)}let l={isFeatureEnabled:h}},99707:(e,t,i)=>{i.d(t,{_:()=>l,d:()=>SearchIndex});var r=i(85351),n=i(7479);function s(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}let{getItem:o,setItem:a,removeItem:h}=(0,r.A)("localStorage",{throwQuotaErrorsOnSet:!0}),l={Branch:"branch",Tag:"tag"};let SearchIndex=class SearchIndex{render(){this.selector.render()}async fetchData(){try{if(!this.isLoading||this.fetchInProgress)return;if(!this.bootstrapFromLocalStorage()){this.fetchInProgress=!0,this.fetchFailed=!1;let e=await fetch(`${this.refEndpoint}?type=${this.refType}`,{headers:{Accept:"application/json"}});await this.processResponse(e)}this.isLoading=!1,this.fetchInProgress=!1,this.render()}catch{this.fetchInProgress=!1,this.fetchFailed=!0}}async processResponse(e){if(this.emitStats(e),!e.ok){this.fetchFailed=!0;return}let t=e.clone(),i=await e.json();this.knownItems=i.refs,this.cacheKey=i.cacheKey,this.flushToLocalStorage(await t.text())}emitStats(e){if(!e.ok)return void(0,n.i)({incrementKey:"REF_SELECTOR_BOOT_FAILED"},!0);switch(e.status){case 200:(0,n.i)({incrementKey:"REF_SELECTOR_BOOTED_FROM_UNCACHED_HTTP"});break;case 304:(0,n.i)({incrementKey:"REF_SELECTOR_BOOTED_FROM_HTTP_CACHE"});break;default:(0,n.i)({incrementKey:"REF_SELECTOR_UNEXPECTED_RESPONSE"})}}search(e){let t;if(this.searchTerm=e,""===e){this.currentSearchResult=this.knownItems;return}let i=[],r=[];for(let n of(this.exactMatchFound=!1,this.knownItems))if(!((t=n.indexOf(e))<0)){if(0===t){e===n?(r.unshift(n),this.exactMatchFound=!0):r.push(n);continue}i.push(n)}this.currentSearchResult=[...r,...i]}bootstrapFromLocalStorage(){let e=o(this.localStorageKey);if(!e)return!1;let t=JSON.parse(e);return t.cacheKey===this.cacheKey&&"refs"in t?(this.knownItems=t.refs,this.isLoading=!1,(0,n.i)({incrementKey:"REF_SELECTOR_BOOTED_FROM_LOCALSTORAGE"}),!0):(h(this.localStorageKey),!1)}async flushToLocalStorage(e){try{a(this.localStorageKey,e)}catch(t){if(t.message.toLowerCase().includes("quota")){this.clearSiblingLocalStorage(),(0,n.i)({incrementKey:"REF_SELECTOR_LOCALSTORAGE_OVERFLOWED"});try{a(this.localStorageKey,e)}catch(e){e.message.toLowerCase().includes("quota")&&(0,n.i)({incrementKey:"REF_SELECTOR_LOCALSTORAGE_GAVE_UP"})}}else throw t}}clearSiblingLocalStorage(){for(let e of Object.keys(localStorage))e.startsWith(SearchIndex.LocalStoragePrefix)&&h(e)}clearLocalStorage(){h(this.localStorageKey)}get localStorageKey(){return`${SearchIndex.LocalStoragePrefix}:${this.nameWithOwner}:${this.refType}`}constructor(e,t,i,r,n){s(this,"refType",void 0),s(this,"selector",void 0),s(this,"knownItems",[]),s(this,"currentSearchResult",[]),s(this,"exactMatchFound",!1),s(this,"searchTerm",""),s(this,"refEndpoint",void 0),s(this,"cacheKey",void 0),s(this,"nameWithOwner",void 0),s(this,"isLoading",!0),s(this,"fetchInProgress",!1),s(this,"fetchFailed",!1),this.refType=e,this.selector=t,this.refEndpoint=i,this.cacheKey=r,this.nameWithOwner=n}};s(SearchIndex,"LocalStoragePrefix","ref-selector")},43827:(e,t,i)=>{i.d(t,{k:()=>o,v:()=>a});var r=i(5225),n=i(96679);let s=(0,r.A)(function(){return n.XC?.head?.querySelector('meta[name="runtime-environment"]')?.content||""}),o=(0,r.A)(function(){return"enterprise"===s()}),a="webpack"},85351:(e,t,i)=>{i.d(t,{A:()=>o,D:()=>a});var r=i(96679),n=i(7479);let s=class NoOpStorage{getItem(){return null}setItem(){}removeItem(){}clear(){}key(){return null}get length(){return 0}};function o(e,t={throwQuotaErrorsOnSet:!1},i=r.cg,a=e=>e,h=e=>e){let l;try{if(!i)throw Error();l=i[e]||new s}catch{l=new s}let{throwQuotaErrorsOnSet:c}=t;function u(e){t.sendCacheStats&&(0,n.i)({incrementKey:e})}function d(e){try{if(l.removeItem(e),t.ttl){let t=`${e}:expiry`;l.removeItem(t)}}catch{}}return{getItem:function(e,t=Date.now()){try{let i=l.getItem(e);if(!i)return null;let r=`${e}:expiry`,n=Number(l.getItem(r));if(n&&t>n)return d(e),d(r),u("SAFE_STORAGE_VALUE_EXPIRED"),null;return u("SAFE_STORAGE_VALUE_WITHIN_TTL"),a(i)}catch{return null}},setItem:function(e,i,r=Date.now()){try{if(l.setItem(e,h(i)),t.ttl){let i=`${e}:expiry`,n=r+t.ttl;l.setItem(i,n.toString())}}catch(e){if(c&&e instanceof Error&&e.message.toLowerCase().includes("quota"))throw e}},removeItem:d,clear:l.clear,getKeys:function(){return Object.keys(l)},get length(){return l.length}}}function a(e){return o(e,{throwQuotaErrorsOnSet:!1},r.cg,JSON.parse,JSON.stringify)}},96679:(e,t,i)=>{i.d(t,{KJ:()=>r.KJ,Kn:()=>n.Kn,X3:()=>r.X3,XC:()=>n.XC,cg:()=>n.cg,fV:()=>n.fV,g5:()=>r.g5});var r=i(28583),n=i(46570)},46570:(e,t,i)=>{i.d(t,{Kn:()=>o,XC:()=>n,cg:()=>s,fV:()=>a});let r="undefined"!=typeof FORCE_SERVER_ENV&&FORCE_SERVER_ENV,n="undefined"==typeof document||r?void 0:document,s="undefined"==typeof window||r?void 0:window,o="undefined"==typeof history||r?void 0:history,a="undefined"==typeof location||r?{pathname:"",origin:"",search:"",hash:"",href:""}:location},28583:(e,t,i)=>{i.d(t,{KJ:()=>s,X3:()=>n,g5:()=>o});var r=i(46570);let n=void 0===r.XC,s=!n;function o(){return!!n||!r.XC||!!(r.XC.querySelector('react-app[data-ssr="true"]')||r.XC.querySelector('react-partial[data-ssr="true"][partial-name="repos-overview"]'))}},7479:(e,t,i)=>{i.d(t,{X:()=>m,i:()=>c});var r=i(96679),n=i(32475),s=i(43827),o=i(27851),a=i(7799),h=i(70170);let l=[];function c(e,t=!1,i=.5){if(!r.X3&&!0!==(0,o.G7)("browser_stats_disabled")){if(i<0||i>1)throw RangeError("Sampling probability must be between 0 and 1");void 0===e.timestamp&&(e.timestamp=Date.now()),e.loggedIn=(0,a.M3)(),e.staff=m(),e.bundler=s.v,Math.random()<i&&l.push(e),t?f():d()}}let u=null,d=(0,h.n)(async function(){await n.K,null==u&&(u=window.requestIdleCallback(f))},5e3);function f(){if(u=null,!l.length)return;let e=r.XC?.head?.querySelector('meta[name="browser-stats-url"]')?.content;if(e){for(let n of function(e){let t=[],i=e.map(e=>JSON.stringify(e));for(;i.length>0;)t.push(function(e){let t=e.shift(),i=[t],r=t.length;for(;e.length>0&&r<=65536;){let t=e[0].length;if(r+t<=65536){let n=e.shift();i.push(n),r+=t}else break}return i}(i));return t}(l)){var t=e,i=`{"stats": [${n.join(",")}], "target": "${r.XC?.head?.querySelector('meta[name="ui-target"]')?.content||"full"}"}`;try{navigator.sendBeacon&&navigator.sendBeacon(t,i)}catch{}}l=[]}}function m(){return!!r.XC?.head?.querySelector('meta[name="user-staff"]')?.content}r.XC?.addEventListener("pagehide",f),r.XC?.addEventListener("visibilitychange",f)}}]);
//# sourceMappingURL=app_assets_modules_github_ref-selector_ts-f2c25710fcd2.js.map