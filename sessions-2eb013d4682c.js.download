"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["sessions"],{52734:(e,t,n)=>{n.d(t,{Cg:()=>l,R1:()=>m,s:()=>c});var o=n(78134),r=n(21403),i=n(26559);let a="github-mobile-auth-flash";function u(){let e=document.querySelector("#js-flash-container");if(e)for(let t of e.children)!t.classList.contains("js-flash-template")&&t.classList.contains(a)&&e.removeChild(t)}function l(){let e=document.getElementById("github-mobile-authenticate-prompt");e&&(e.hidden=!0);let t=document.getElementById("github-mobile-authenticate-error-and-retry");t&&(t.hidden=!1)}function c(){u();let e=document.getElementById("github-mobile-authenticate-prompt");e&&(e.hidden=!1);let t=document.getElementById("github-mobile-authenticate-error-and-retry");t&&(t.hidden=!0)}function s(e){e&&function(e){let t=new o.i4(document.querySelector("template.js-flash-template"),{className:`flash-error ${a}`,message:e}),n=document.importNode(t,!0),r=document.querySelector("#js-flash-container");r&&(u(),r.appendChild(n))}(e),l()}function d(e){return document.getElementById("github-mobile-authenticate-error-and-retry").getAttribute(e)}async function m(e,t,n,o){try{var r;await (r=e.getAttribute("data-poll-url"),async function e(a){let u,l,c;if(o&&o())return;let m="STATUS_UNKNOWN";try{let e=document.getElementById("github-mobile-authenticate-form"),t=e.querySelector(".js-data-url-csrf"),n=await self.fetch(new Request(r,{method:"POST",body:new FormData(e),mode:"same-origin",headers:{Accept:"application/json","Scoped-CSRF-Token":t.value,...(0,i.kt)()}}));if(n.ok){let e=await n.json();m=e.status,u=e.token}else m="STATUS_ERROR"}catch{m="STATUS_ERROR"}switch(m){case"STATUS_APPROVED":var f;return t?t():void((c=(f=u)?new URL(`password_reset/${encodeURIComponent(f)}`,window.location.origin):new URL("",window.location.href)).searchParams.set("redirect","true"),window.location.assign(c));case"STATUS_EXPIRED":return l=d("timeout-flash"),n?n(l):s(l);case"STATUS_ACTIVE":case"STATUS_ERROR":case"STATUS_UNKNOWN":break;case"STATUS_REJECTED":return l=d("error-flash"),n?n(l):void document.getElementById("github-mobile-rejected-redirect").click();default:return l=d("error-flash"),n?n(l):s(l)}await new Promise(e=>setTimeout(e,3e3)),e(a)}(0))}catch{return s(d("error-flash"))}}(0,r.lB)(".js-poll-github-mobile-two-factor-authenticate",function(e){m(e)}),(0,r.lB)(".js-poll-github-mobile-verified-device-authenticate",function(e){m(e)}),(0,r.lB)(".js-poll-github-mobile-two-factor-password-reset-authenticate",function(e){m(e)})},68551:(e,t,n)=>{var o=n(8367),r=n(21403);(0,r.lB)(".js-transform-notice",{constructor:HTMLElement,add(e){for(let t of(0,o.OR)("org_transform_notice")){let n=document.createElement("span");try{n.textContent=atob(decodeURIComponent(t.value)),(0,o.Yj)(t.key),e.appendChild(n),e.hidden=!1}catch{}return}}});var i=n(97797),a=n(36175),u=n(12559),l=n(97325),c=n(43862);(0,u.JW)(".js-send-auth-code",async(e,t)=>{let n;document.body.classList.add("is-sending"),document.body.classList.remove("is-sent","is-not-sent");try{n=await t.text()}catch(e){!function(e){e&&(document.querySelector(".js-sms-error").textContent=e),document.body.classList.add("is-not-sent"),document.body.classList.remove("is-sending")}(e.response.text)}n&&(document.body.classList.add("is-sent"),document.body.classList.remove("is-sending"))}),(0,u.JW)(".js-two-factor-set-sms-fallback",async(e,t)=>{let n;try{n=await t.text()}catch(r){let t=e.querySelector(".js-configure-sms-fallback"),n=e.querySelector(".js-verify-sms-fallback"),o=(t.hidden?n:t).querySelector(".flash");switch(r.response.status){case 404:case 422:case 429:o.textContent=JSON.parse(r.response.text).error,o.hidden=!1}}if(n)switch(n.status){case 200:case 201:window.location.reload();break;case 202:e.querySelector(".js-configure-sms-fallback").hidden=!0,e.querySelector(".js-verify-sms-fallback").hidden=!1,e.querySelector(".js-fallback-otp").focus()}}),(0,a.eC)(".js-verification-code-input-auto-submit",function(e){let t=e.currentTarget,n=t.pattern||"[0-9]{6}";RegExp(`^(${n})$`).test(t.value)&&(0,l.k_)(t.form)}),(0,i.on)("click",".js-toggle-redacted-note-content",async e=>{let t=e.currentTarget,n=t.closest(".note");if(n){let e=n.getElementsByClassName("js-note")[0];e&&(e.innerHTML=t.getAttribute("data-content").replace(/</g,"&lt;").replace(/>/g,"&gt;"))}for(let e of n.getElementsByClassName("js-toggle-redacted-note-content"))e.hidden=!e.hidden});let s=new WeakMap;document.addEventListener("turbo:load",function(){for(let e of document.querySelectorAll(".more-options-two-factor")){let t=s.get(e);t?.timeline&&t.timeline.kill(),s.delete(e)}}),(0,i.on)("click",".more-options-two-factor",e=>{let t=e.currentTarget,n=s.get(t);n||(n={isOpen:!1,timeline:null},s.set(t,n));let o=document.querySelector(".two-factor-alternatives-body"),r=document.querySelectorAll(".two-factor-alternatives-item"),i=document.querySelectorAll(".two-factor-alternatives-item-text"),a=document.querySelectorAll(".more-options-two-factor .octicon-triangle-down"),u=document.querySelector(".two-factor-alternatives-divider");o&&r.length&&(n.timeline?.kill(),n.timeline=c.os.timeline(),n.isOpen?n.timeline.to(u,{opacity:0,y:-5,duration:.25},0).to(a,{rotation:180,duration:.25},0).to(i,{opacity:0,duration:.1},0).to(r,{opacity:0,y:-5,duration:.15,filter:"blur(5px)"},0):(c.os.set(r,{opacity:0,y:-5}),c.os.set(i,{opacity:0}),c.os.set(u,{opacity:0,y:-5}),n.timeline.to(o,{opacity:1,duration:.01}).to(r,{opacity:1,y:0,duration:.25,filter:"none"}).to(u,{opacity:1,y:0,duration:.25},0).to(i,{opacity:1,duration:.08},.1).to(a,{rotation:0,duration:.15},0)),n.isOpen=!n.isOpen)}),n(52734);var d=n(91903),m=n(74043);let f=new AbortController;async function h(){return await globalThis.PublicKeyCredential?.isConditionalMediationAvailable?.()}async function p(){let e=await (0,d.e)(),t=document.querySelector(".js-conditional-webauthn-placeholder"),n=document.querySelector("webauthn-get");if(n&&null!==n.getAttribute("subtle-login"))return;let o=await h();if(t&&o&&"supported"===e){document.querySelector("#login_field")?.setAttribute("autocomplete","username webauthn");let e=t.getAttribute("data-webauthn-sign-request");if(!e)return;n&&n.addEventListener("webauthn-get-prompt",()=>{f.abort()});let o=JSON.parse(e),r=(0,m.d5)(o);r.signal=f.signal;let i=await (0,m.Jt)(r);t.querySelector(".js-conditional-webauthn-response").value=JSON.stringify(i),(0,l.k_)(t)}}(0,r.lB)(".js-webauthn-support",{constructor:HTMLInputElement,add(e){(0,l.m$)(e,(0,d.K)())}}),(0,r.lB)(".js-webauthn-iuvpaa-support",{constructor:HTMLInputElement,async add(e){(0,l.m$)(e,await (0,d.e)())}}),(0,r.lB)(".js-support",{constructor:HTMLInputElement,async add(e){(0,l.m$)(e,"true")}}),(0,r.lB)(".js-conditional-webauthn-placeholder",function(){p()});var b=n(43827);function y(e){let t=e.closest("form");if(!t)return;let n=t.querySelector(".js-password-field"),o=t.querySelector(".js-sign-in-button");if(!n||!o)return;let r=e.value,i=document.querySelector(".js-webauthn-login-emu-control"),a=document.querySelector(".js-webauthn-subtle-emu-control"),u=document.querySelector(".js-webauthn-hint-emu-control"),l=document.querySelector("#forgot-password");!(!(0,b.k)()&&"true"!==o.getAttribute("disable-emu-sso")&&r.includes("_"))||r.includes("@")||["pj_nitin","up_the_irons"].includes(r)||r.endsWith("_admin")||o.getAttribute("development")&&r.endsWith("_fab")?(n.removeAttribute("disabled"),o.value=o.getAttribute("data-signin-label")||" ",i?.removeAttribute("hidden"),a?.removeAttribute("hidden"),u?.removeAttribute("hidden"),l?.removeAttribute("hidden")):(n.setAttribute("disabled","true"),o.value=o.getAttribute("data-sso-label")||" ",i?.setAttribute("hidden","true"),a?.setAttribute("hidden","true"),u?.setAttribute("hidden","true"),l?.setAttribute("hidden","true"))}(0,r.lB)(".js-login-field",{constructor:HTMLInputElement,add(e){y(e),e.addEventListener("input",function(){y(e)})}})},7799:(e,t,n)=>{let o;function r(){if(!o)throw Error("Client env was requested before it was loaded. This likely means you are attempting to use client env at the module level in SSR, which is not supported. Please move your client env usage into a function.");return o}function i(){return o?.locale??"en-US"}function a(){return!!r().login}n.d(t,{JK:()=>i,M3:()=>a,_$:()=>r});!function(){if("undefined"!=typeof document){let e=document.getElementById("client-env");if(e)try{o=JSON.parse(e.textContent||"")}catch(e){console.error("Error parsing client-env",e)}}}()},53005:(e,t,n)=>{n.d(t,{O:()=>a,S:()=>i});var o=n(96679);let r=o.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",i="X-GitHub-Client-Version";function a(){return r}},8367:(e,t,n)=>{function o(e){return r(e)[0]}function r(e){let t=[];for(let n of function(){try{return document.cookie.split(";")}catch{return[]}}()){let[o,r]=n.trim().split("=");e===o&&void 0!==r&&t.push({key:o,value:r})}return t}function i(e,t,n=null,o=!1,r="lax"){let a=document.domain;if(null==a)throw Error("Unable to get document domain");a.endsWith(".github.com")&&(a="github.com");let u="https:"===location.protocol?"; secure":"",l=n?`; expires=${n}`:"";!1===o&&(a=`.${a}`);try{document.cookie=`${e}=${t}; path=/; domain=${a}${l}${u}; samesite=${r}`}catch{}}function a(e,t=!1){let n=document.domain;if(null==n)throw Error("Unable to get document domain");n.endsWith(".github.com")&&(n="github.com");let o=new Date(Date.now()-1).toUTCString(),r="https:"===location.protocol?"; secure":"",i=`; expires=${o}`;!1===t&&(n=`.${n}`);try{document.cookie=`${e}=''; path=/; domain=${n}${i}${r}`}catch{}}n.d(t,{OR:()=>r,Ri:()=>o,TV:()=>i,Yj:()=>a})},27851:(e,t,n)=>{n.d(t,{G7:()=>l,XY:()=>c,fQ:()=>u});var o=n(5225),r=n(7799);function i(){return new Set((0,r._$)().featureFlags)}let a=n(96679).X3||function(){try{return process?.env?.STORYBOOK==="true"}catch{return!1}}()?i:(0,o.A)(i);function u(){return Array.from(a())}function l(e){return a().has(e)}let c={isFeatureEnabled:l}},26559:(e,t,n)=>{n.d(t,{jC:()=>l,kt:()=>a,tV:()=>u});var o=n(53005),r=n(27851),i=n(88191);function a(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,i.wE)(e)};return(0,r.G7)("client_version_header")&&(t={...t,[o.S]:(0,o.O)()}),t}function u(e,t){for(let[n,o]of Object.entries(a(t)))e.set(n,o)}function l(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,n)=>{n.d(t,{$r:()=>a,M1:()=>u,li:()=>r,pS:()=>c,wE:()=>l});var o=n(96679);let r="X-Fetch-Nonce",i=new Set;function a(e){i.add(e)}function u(){return i.values().next().value||""}function l(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[r]=u():i.has(e)?t[r]=e:t[r]=Array.from(i).join(","),t}function c(){let e=o.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&a(e)}},97325:(e,t,n)=>{n.d(t,{Cy:()=>u,K3:()=>s,Z8:()=>l,k_:()=>i,lK:()=>d,m$:()=>a});var o=n(94982);function r(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:n}))}function i(e,t){t&&(function(e,t){if(!(e instanceof HTMLFormElement))throw TypeError("The specified element is not of type HTMLFormElement.");if(!(t instanceof HTMLElement))throw TypeError("The specified element is not of type HTMLElement.");if("submit"!==t.type)throw TypeError("The specified element is not a submit button.");if(!e||e!==t.form)throw Error("The specified element is not owned by the form element.")}(e,t),(0,o.A)(t)),r(e,"submit",!0)&&e.submit()}function a(e,t){if("boolean"==typeof t)if(e instanceof HTMLInputElement)e.checked=t;else throw TypeError("only checkboxes can be set to boolean value");else if("checkbox"===e.type)throw TypeError("checkbox can't be set to string value");else e.value=t;r(e,"change",!1)}function u(e,t){for(let n in t){let o=t[n],r=e.elements.namedItem(n);r instanceof HTMLInputElement?r.value=o:r instanceof HTMLTextAreaElement&&(r.value=o)}}function l(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==n&&"reset"!==n||e.isContentEditable}function c(e){return new URLSearchParams(e)}function s(e,t){let n=new URLSearchParams(e.search);for(let[e,o]of c(t))n.append(e,o);return n.toString()}function d(e){return c(new FormData(e)).toString()}},94982:(e,t,n)=>{function o(e){let t=e.closest("form");if(!(t instanceof HTMLFormElement))return;let n=r(t);if(e.name){let o=e.matches("input[type=submit]")?"Submit":"",r=e.value||o;n||((n=document.createElement("input")).type="hidden",n.classList.add("js-submit-button-value"),t.prepend(n)),n.name=e.name,n.value=r}else n&&n.remove()}function r(e){let t=e.querySelector("input.js-submit-button-value");return t instanceof HTMLInputElement?t:null}n.d(t,{A:()=>o,C:()=>r})},36175:(e,t,n)=>{n.d(t,{Ff:()=>l,eC:()=>c,uE:()=>u});var o=n(6986);let r=!1,i=new o.A;function a(e){let t=e.target;if(t instanceof HTMLElement&&t.nodeType!==Node.DOCUMENT_NODE)for(let e of i.matches(t))e.data.call(null,t)}function u(e,t){r||(r=!0,document.addEventListener("focus",a,!0)),i.add(e,t),document.activeElement instanceof HTMLElement&&document.activeElement.matches(e)&&t(document.activeElement)}function l(e,t,n){function o(t){let r=t.currentTarget;r&&(r.removeEventListener(e,n),r.removeEventListener("blur",o))}u(t,function(t){t.addEventListener(e,n),t.addEventListener("blur",o)})}function c(e,t){function n(e){let{currentTarget:o}=e;o&&(o.removeEventListener("input",t),o.removeEventListener("blur",n))}u(e,function(e){e.addEventListener("input",t),e.addEventListener("blur",n)})}},12559:(e,t,n)=>{n.d(t,{Ax:()=>r.Ax,JW:()=>i,ZV:()=>r.ZV});var o=n(26559),r=n(13937);function i(e,t){(0,r.JW)(e,async(e,n,r)=>((0,o.tV)(r.headers),t(e,n,r)))}},43827:(e,t,n)=>{n.d(t,{k:()=>a,v:()=>u});var o=n(5225),r=n(96679);let i=(0,o.A)(function(){return r.XC?.head?.querySelector('meta[name="runtime-environment"]')?.content||""}),a=(0,o.A)(function(){return"enterprise"===i()}),u="webpack"},96679:(e,t,n)=>{n.d(t,{KJ:()=>o.KJ,Kn:()=>r.Kn,X3:()=>o.X3,XC:()=>r.XC,cg:()=>r.cg,fV:()=>r.fV,g5:()=>o.g5});var o=n(28583),r=n(46570)},46570:(e,t,n)=>{n.d(t,{Kn:()=>a,XC:()=>r,cg:()=>i,fV:()=>u});let o="undefined"!=typeof FORCE_SERVER_ENV&&FORCE_SERVER_ENV,r="undefined"==typeof document||o?void 0:document,i="undefined"==typeof window||o?void 0:window,a="undefined"==typeof history||o?void 0:history,u="undefined"==typeof location||o?{pathname:"",origin:"",search:"",hash:"",href:""}:location},28583:(e,t,n)=>{n.d(t,{KJ:()=>i,X3:()=>r,g5:()=>a});var o=n(46570);let r=void 0===o.XC,i=!r;function a(){return!!r||!o.XC||!!(o.XC.querySelector('react-app[data-ssr="true"]')||o.XC.querySelector('react-partial[data-ssr="true"][partial-name="repos-overview"]'))}},91903:(e,t,n)=>{n.d(t,{K:()=>r,e:()=>i});var o=n(74043);function r(){return(0,o.$j)()?"supported":"unsupported"}async function i(){return await window.PublicKeyCredential?.isUserVerifyingPlatformAuthenticatorAvailable()?"supported":"unsupported"}}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_github_selector-observer_dist_index_esm_js","vendors-node_modules_gsap_index_js","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67"],()=>t(68551)),e.O()}]);
//# sourceMappingURL=sessions-e8b164f06b99.js.map