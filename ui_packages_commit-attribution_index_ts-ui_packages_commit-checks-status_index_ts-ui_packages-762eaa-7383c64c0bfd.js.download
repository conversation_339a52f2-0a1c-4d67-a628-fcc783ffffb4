"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_commit-attribution_index_ts-ui_packages_commit-checks-status_index_ts-ui_packages-762eaa"],{37804:(e,t,a)=>{a.d(t,{h1:()=>A,jF:()=>G,Cf:()=>E});var r=a(74848),l=a(21728),o=a(39459),i=a(96235),s=a(34614),n=a(75177);function c(e){return e.path?.startsWith("/apps/")??!1}var d=a(89985);function u(e){let t,a=(0,l.c)(5),{renderTooltip:o,author:i,children:s}=e;if(!1===o){let e;return a[0]!==s?(e=(0,r.jsx)(r.<PERSON>,{children:s}),a[0]=s,a[1]=e):e=a[1],e}let n=`commits by ${i.login}`;return a[2]!==s||a[3]!==n?(t=(0,r.jsx)(d.m,{text:n,direction:"se",children:s}),a[2]=s,a[3]=n,a[4]=t):t=a[4],t}try{u.displayName||(u.displayName="AuthorTooltip")}catch{}var m=a(14744),h=a.n(m),x=a(96540);let p={fontWeight:"bold",fontColor:"fg.default",includeTooltip:!1,avatarSize:void 0},f=(0,x.createContext)(p);function g(e){let t,a,o,i=(0,l.c)(7),{authorSettings:s,children:n}=e;i[0]!==s?(t=s??{},i[0]=s,i[1]=t):t=i[1],i[2]!==t?(a=h()(p,t),i[2]=t,i[3]=a):a=i[3];let c=a;return i[4]!==c||i[5]!==n?(o=(0,r.jsx)(f.Provider,{value:c,children:n}),i[4]=c,i[5]=n,i[6]=o):o=i[6],o}function y(){return(0,x.useContext)(f)||p}try{f.displayName||(f.displayName="AuthorSettingsContext")}catch{}try{g.displayName||(g.displayName="AuthorSettingsProvider")}catch{}var N=a(52464),v=a(30631);let j={Text:"AuthorDisplayName-module__Text--dSylB"},_=["150px","150px","200px"];function C(e){let t,a,o,i=(0,l.c)(9),{displayName:s,authorSettings:n}=e;return i[0]!==n.fontColor||i[1]!==n.fontWeight?(t={fontWeight:n.fontWeight,color:n.fontColor},i[0]=n.fontColor,i[1]=n.fontWeight,i[2]=t):t=i[2],i[3]!==s||i[4]!==t?(a=(0,r.jsx)(N.A,{sx:t,className:j.Text,children:s}),i[3]=s,i[4]=t,i[5]=a):a=i[5],i[6]!==s||i[7]!==a?(o=(0,r.jsx)(v.A,{title:s,maxWidth:_,inline:!0,children:a}),i[6]=s,i[7]=a,i[8]=o):o=i[8],o}try{C.displayName||(C.displayName="AuthorDisplayName")}catch{}let k={GitHubAvatar:"AuthorAvatar-module__GitHubAvatar--Bx7dK",AuthorAvatarContainer:"AuthorAvatar-module__AuthorAvatarContainer--NQwZ2",authorHoverableLink:"AuthorAvatar-module__authorHoverableLink--ED3Do"};function A(e){let t,a,d,m,h,x,p=(0,l.c)(22),{author:f,repo:g,sx:N}=e,v=y();if(!f)return null;let j=`${f.login||"author"}`,_=f.avatarUrl,A=`${f.login||"author"}`,b=v.avatarSize;p[0]!==f?(t=c(f),p[0]=f,p[1]=t):t=p[1],p[2]!==f.avatarUrl||p[3]!==v.avatarSize||p[4]!==j||p[5]!==A||p[6]!==t?(a=(0,r.jsx)(o.r,{"aria-label":j,src:_,alt:A,size:b,square:t,className:k.GitHubAvatar}),p[2]=f.avatarUrl,p[3]=v.avatarSize,p[4]=j,p[5]=A,p[6]=t,p[7]=a):a=p[7];let S=a;return p[8]!==N?(d={...N},p[8]=N,p[9]=d):d=p[9],p[10]!==f.login||p[11]!==f.path||p[12]!==S?(m=f.path?(0,r.jsx)(s.A,{href:f.path,"data-testid":"avatar-icon-link","data-hovercard-url":f.login?(0,i.dCN)({owner:f.login}):void 0,children:S}):S,p[10]=f.login,p[11]=f.path,p[12]=S,p[13]=m):m=p[13],p[14]!==f||p[15]!==v||p[16]!==g?(h=f.login?(0,r.jsx)(u,{author:f,renderTooltip:v.includeTooltip,children:(0,r.jsx)(s.A,{muted:!0,href:(0,i.jQC)({repo:g,author:f.login}),"aria-label":`commits by ${f.login}`,"data-hovercard-url":(0,i.dCN)({owner:f.login}),sx:{fontWeight:v.fontWeight,color:v.fontColor,"&:hover":{color:v.fontColor}},className:k.authorHoverableLink,children:f.login})}):(0,r.jsx)(C,{displayName:f.displayName,authorSettings:v}),p[14]=f,p[15]=v,p[16]=g,p[17]=h):h=p[17],p[18]!==d||p[19]!==m||p[20]!==h?(x=(0,r.jsxs)(n.A,{sx:d,"data-testid":"author-avatar",className:k.AuthorAvatarContainer,children:[m,h]}),p[18]=d,p[19]=m,p[20]=h,p[21]=x):x=p[21],x}try{A.displayName||(A.displayName="AuthorAvatar")}catch{}var b=a(15385),S=a(2724);let B={PrimerLink:"AuthorsDialog-module__PrimerLink--G20Sq",ActionList:"AuthorsDialog-module__ActionList--TYWvB",ActionList_LinkItem:"AuthorsDialog-module__ActionList_LinkItem--zhT2L",GitHubAvatar:"AuthorsDialog-module__GitHubAvatar--Nlvyq"};function L(e){let t,a,o,i,n=(0,l.c)(12),{authors:c,repo:d}=e,u=c.length,[m,h]=(0,x.useState)(!1),p=(0,x.useRef)(null),f=`Show ${u} authors`;return n[0]===Symbol.for("react.memo_cache_sentinel")?(t=()=>{h(!0)},n[0]=t):t=n[0],n[1]!==u||n[2]!==f?(a=(0,r.jsxs)(s.A,{as:"button","aria-label":f,"data-testid":"authors-dialog-anchor",onClick:t,ref:p,muted:!0,className:B.PrimerLink,children:[u," ","people"]}),n[1]=u,n[2]=f,n[3]=a):a=n[3],n[4]!==u||n[5]!==c||n[6]!==m||n[7]!==d?(o=m&&(0,r.jsx)(S.l,{title:`${u} authors`,onClose:()=>{h(!1),setTimeout(()=>p.current?.focus(),25)},width:"medium",height:u>=12?"small":"auto",renderBody:()=>(0,r.jsx)(b.l,{"data-testid":"contributor-dialog-list",className:B.ActionList,children:c.map((e,t)=>(0,r.jsx)(T,{author:e,repo:d},`${e.login}_${t}`))})}),n[4]=u,n[5]=c,n[6]=m,n[7]=d,n[8]=o):o=n[8],n[9]!==a||n[10]!==o?(i=(0,r.jsxs)(r.Fragment,{children:[a,o]}),n[9]=a,n[10]=o,n[11]=i):i=n[11],i}function T(e){let t,a,s,n,d,u=(0,l.c)(16),{author:m,repo:h}=e,x=m.login??"";u[0]!==h||u[1]!==x?(t=(0,i.jQC)({repo:h,author:x}),u[0]=h,u[1]=x,u[2]=t):t=u[2];let p=m.avatarUrl,f=m.login??m.displayName;u[3]!==m?(a=c(m),u[3]=m,u[4]=a):a=u[4],u[5]!==m.avatarUrl||u[6]!==f||u[7]!==a?(s=(0,r.jsx)(o.r,{src:p,alt:f,"aria-hidden":"true",square:a,className:B.GitHubAvatar}),u[5]=m.avatarUrl,u[6]=f,u[7]=a,u[8]=s):s=u[8];let g=m.login??m.displayName??"",y=m.login??m.displayName;return u[9]!==g||u[10]!==y?(n=(0,r.jsx)(v.A,{inline:!0,title:g,children:y}),u[9]=g,u[10]=y,u[11]=n):n=u[11],u[12]!==t||u[13]!==s||u[14]!==n?(d=(0,r.jsxs)(b.l.LinkItem,{"data-testid":"contributor-dialog-row",href:t,className:B.ActionList_LinkItem,children:[s,n]}),u[12]=t,u[13]=s,u[14]=n,u[15]=d):d=u[15],d}try{L.displayName||(L.displayName="AuthorsDialog")}catch{}try{T.displayName||(T.displayName="AuthorRow")}catch{}var I=a(70892);function D(e){let t,a,s,n=(0,l.c)(11),{authors:d,onBehalfOf:u}=e,m=y();if(n[0]!==m||n[1]!==d){let e;n[3]!==m?(e=(e,t)=>(0,r.jsx)(o.r,{"data-testid":"commit-stack-avatar",src:e.avatarUrl,alt:e.login??e.displayName,"data-hovercard-url":(0,i.dCN)({owner:e.login??""}),square:c(e),size:m.avatarSize},`${e.login}_${t}`),n[3]=m,n[4]=e):e=n[4],t=d.slice(0,5).map(e),n[0]=m,n[1]=d,n[2]=t}else t=n[2];return n[5]!==m||n[6]!==u?(a=u&&(0,r.jsx)(o.r,{"data-testid":"commit-stack-avatar",src:u.avatarUrl,alt:u.login??u.displayName,"data-hovercard-url":(0,i.qsO)({owner:u.login??""}),square:!0,size:m.avatarSize},`${u.login}_on_behalf_of`),n[5]=m,n[6]=u,n[7]=a):a=n[7],n[8]!==t||n[9]!==a?(s=(0,r.jsxs)(I.A,{children:[t,a]}),n[8]=t,n[9]=a,n[10]=s):s=n[10],s}try{D.displayName||(D.displayName="CommitAuthorStack")}catch{}var w=a(34164);let z={Box:"AuthorLink-module__Box--GHJfU",Link:"AuthorLink-module__Link--wFBg2"};function F(e){let t,a,o,n=(0,l.c)(9),{author:c,repo:d,className:m}=e,h=y();return c?(n[0]!==m?(t=(0,w.$)(z.Box,m),n[0]=m,n[1]=t):t=n[1],n[2]!==c||n[3]!==h||n[4]!==d?(a=c.login?(0,r.jsx)(u,{author:c,renderTooltip:h.includeTooltip,children:(0,r.jsx)(s.A,{muted:!0,href:(0,i.jQC)({repo:d,author:c.login}),"aria-label":`commits by ${c.login}`,"data-hovercard-url":(0,i.dCN)({owner:c.login}),sx:{fontWeight:h.fontWeight,color:h.fontColor,"&:hover":{color:h.fontColor}},className:z.Link,children:c.login})}):(0,r.jsx)(C,{displayName:c.displayName,authorSettings:h}),n[2]=c,n[3]=h,n[4]=d,n[5]=a):a=n[5],n[6]!==t||n[7]!==a?(o=(0,r.jsx)("div",{"data-testid":"author-link",className:t,children:a}),n[6]=t,n[7]=a,n[8]=o):o=n[8],o):null}try{F.displayName||(F.displayName="AuthorLink")}catch{}let H={orgLink:"OrgLink-module__orgLink--pOGLv"};function R(e){let t,a,o,n,c=(0,l.c)(12),{org:d,className:u}=e;if(!d)return null;c[0]!==u?(t=(0,w.$)("d-flex flex-row flex-items-center",u),c[0]=u,c[1]=t):t=c[1];let m=d.path,h=`${d.login}'s org home page`;return c[2]!==d.login?(a=(0,i.qsO)({owner:d.login}),c[2]=d.login,c[3]=a):a=c[3],c[4]!==d.login||c[5]!==d.path||c[6]!==h||c[7]!==a?(o=(0,r.jsx)(s.A,{muted:!0,href:m,"aria-label":h,"data-hovercard-url":a,className:H.orgLink,children:d.login}),c[4]=d.login,c[5]=d.path,c[6]=h,c[7]=a,c[8]=o):o=c[8],c[9]!==t||c[10]!==o?(n=(0,r.jsx)("div",{className:t,children:o}),c[9]=t,c[10]=o,c[11]=n):n=c[11],n}try{R.displayName||(R.displayName="OrgLink")}catch{}var $=a(38621);let U={AuthorLink:"CommitAttribution-module__AuthorLink--S0Z7k",CommitAttributionContainer:"CommitAttribution-module__CommitAttributionContainer--dZiJ_"};function W(e){let t,a=(0,l.c)(3),{author:o,repo:i}=e;return a[0]!==o||a[1]!==i?(t=(0,r.jsx)(A,{author:o,repo:i}),a[0]=o,a[1]=i,a[2]=t):t=a[2],t}function O(e){let t,a,o,i,s=(0,l.c)(13),{author:n,committer:c,committerAttribution:d,onBehalfOf:u,repo:m}=e;return s[0]!==n||s[1]!==c||s[2]!==d?(t=[n],c&&d&&t.push(c),s[0]=n,s[1]=c,s[2]=d,s[3]=t):t=s[3],s[4]!==t||s[5]!==u?(a=(0,r.jsx)(D,{authors:t,onBehalfOf:u}),s[4]=t,s[5]=u,s[6]=a):a=s[6],s[7]!==n||s[8]!==m?(o=(0,r.jsx)(F,{author:n,repo:m,className:U.AuthorLink}),s[7]=n,s[8]=m,s[9]=o):o=s[9],s[10]!==a||s[11]!==o?(i=(0,r.jsxs)(r.Fragment,{children:[a,o]}),s[10]=a,s[11]=o,s[12]=i):i=s[12],i}function P(e){let t,a,o,i=(0,l.c)(12),{authors:s,onBehalfOf:n,repo:c}=e;if(i[0]!==s||i[1]!==n?(t=(0,r.jsx)(D,{authors:s,onBehalfOf:n}),i[0]=s,i[1]=n,i[2]=t):t=i[2],i[3]!==s||i[4]!==c){let e;i[6]!==s.length||i[7]!==c?(e=(e,t)=>(0,r.jsxs)(x.Fragment,{children:[(0,r.jsx)(F,{author:e,repo:c,className:U.AuthorLink}),t!==s.length-1&&(0,r.jsx)("span",{className:"pl-1",children:"and"})]},`${e.login}_${t}`),i[6]=s.length,i[7]=c,i[8]=e):e=i[8],a=s.map(e),i[3]=s,i[4]=c,i[5]=a}else a=i[5];return i[9]!==t||i[10]!==a?(o=(0,r.jsxs)(r.Fragment,{children:[t,a]}),i[9]=t,i[10]=a,i[11]=o):o=i[11],o}function q(e){let t,a,o,i=(0,l.c)(9),{authors:s,onBehalfOf:n,repo:c}=e;return i[0]!==s||i[1]!==n?(t=(0,r.jsx)(D,{authors:s,onBehalfOf:n}),i[0]=s,i[1]=n,i[2]=t):t=i[2],i[3]!==s||i[4]!==c?(a=(0,r.jsx)(L,{authors:s,repo:c}),i[3]=s,i[4]=c,i[5]=a):a=i[5],i[6]!==t||i[7]!==a?(o=(0,r.jsxs)(r.Fragment,{children:[t,a]}),i[6]=t,i[7]=a,i[8]=o):o=i[8],o}function G(e){let t,a,o,i,s,n,c,d,u,m=(0,l.c)(43),{authors:h,committer:x,committerAttribution:p,onBehalfOf:f,repo:y,children:N,includeVerbs:v,authorSettings:j,textVariant:_}=e,C=void 0===v||v,k=1===h.length&&!p&&!f,A=1===h.length&&(p||f),b=2===h.length&&!p,S=!k&&!A&&!b,B=h[0],L=C?"pl-1":"",T="muted"===(void 0===_?"default":_)?"color-fg-muted":"";m[0]!==T?(t=(0,w.$)(T,U.CommitAttributionContainer),m[0]=T,m[1]=t):t=m[1],m[2]!==B||m[3]!==y||m[4]!==k?(a=k&&B&&(0,r.jsx)(W,{author:B,repo:y}),m[2]=B,m[3]=y,m[4]=k,m[5]=a):a=m[5],m[6]!==A||m[7]!==x||m[8]!==p||m[9]!==B||m[10]!==f||m[11]!==y?(o=A&&B&&(0,r.jsx)(O,{author:B,committer:x,committerAttribution:p,onBehalfOf:f,repo:y}),m[6]=A,m[7]=x,m[8]=p,m[9]=B,m[10]=f,m[11]=y,m[12]=o):o=m[12],m[13]!==h||m[14]!==b||m[15]!==f||m[16]!==y?(i=b&&(0,r.jsx)(P,{authors:h,onBehalfOf:f,repo:y}),m[13]=h,m[14]=b,m[15]=f,m[16]=y,m[17]=i):i=m[17],m[18]!==h||m[19]!==S||m[20]!==f||m[21]!==y?(s=S&&(0,r.jsx)(q,{authors:h,onBehalfOf:f,repo:y}),m[18]=h,m[19]=S,m[20]=f,m[21]=y,m[22]=s):s=m[22];let I=p||!1;return m[23]!==x||m[24]!==C||m[25]!==y||m[26]!==I||m[27]!==L?(n=(0,r.jsx)(Q,{committer:x,committerAttribution:I,includeVerbs:C,repo:y,verbClass:L}),m[23]=x,m[24]=C,m[25]=y,m[26]=I,m[27]=L,m[28]=n):n=m[28],m[29]!==f?(c=f&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"pl-1",children:"on behalf of"}),(0,r.jsx)(R,{org:f,className:"pl-1"})]}),m[29]=f,m[30]=c):c=m[30],m[31]!==j||m[32]!==N||m[33]!==n||m[34]!==c||m[35]!==a||m[36]!==o||m[37]!==i||m[38]!==s?(d=(0,r.jsxs)(g,{authorSettings:j,children:[a,o,i,s,n,c,N]}),m[31]=j,m[32]=N,m[33]=n,m[34]=c,m[35]=a,m[36]=o,m[37]=i,m[38]=s,m[39]=d):d=m[39],m[40]!==d||m[41]!==t?(u=(0,r.jsx)("div",{className:t,children:d}),m[40]=d,m[41]=t,m[42]=u):u=m[42],u}function Q(e){let t=(0,l.c)(18),{committer:a,committerAttribution:o,includeVerbs:i,repo:s,verbClass:n}=e;if(a&&a.isGitHub){let e,a=i&&"authored";return t[0]!==a||t[1]!==n?(e=(0,r.jsx)("span",{className:n,children:a}),t[0]=a,t[1]=n,t[2]=e):e=t[2],e}if(o){let e,l,o,c,d=i?"authored and":"and";t[6]!==d?(e=(0,r.jsx)("span",{className:"pl-1",children:d}),t[6]=d,t[7]=e):e=t[7],t[8]!==a||t[9]!==s?(l=(0,r.jsx)(F,{author:a,repo:s,className:U.AuthorLink}),t[8]=a,t[9]=s,t[10]=l):l=t[10];let u=i&&"committed";return t[11]!==u||t[12]!==n?(o=(0,r.jsx)("span",{className:n,children:u}),t[11]=u,t[12]=n,t[13]=o):o=t[13],t[14]!==e||t[15]!==l||t[16]!==o?(c=(0,r.jsxs)(r.Fragment,{children:[e,l,o]}),t[14]=e,t[15]=l,t[16]=o,t[17]=c):c=t[17],c}{let e,a=i&&"committed";return t[3]!==a||t[4]!==n?(e=(0,r.jsx)("span",{className:n,children:a}),t[3]=a,t[4]=n,t[5]=e):e=t[5],e}}function E(e){let t,a,o,i,s,n=(0,l.c)(11),{pusher:c,repo:d,children:u,textVariant:m}=e;if(!c)return null;let h="muted"===(void 0===m?"default":m)?"color-fg-muted":"";return n[0]!==h?(t=(0,w.$)(h,U.CommitAttributionContainer),n[0]=h,n[1]=t):t=n[1],n[2]===Symbol.for("react.memo_cache_sentinel")?(a=(0,r.jsx)("span",{className:"pl-1 pr-1",children:(0,r.jsx)($.RepoPushIcon,{className:"mr-1",size:"small"})}),n[2]=a):a=n[2],n[3]!==c||n[4]!==d?(o=(0,r.jsx)(W,{author:c,repo:d}),n[3]=c,n[4]=d,n[5]=o):o=n[5],n[6]===Symbol.for("react.memo_cache_sentinel")?(i=(0,r.jsx)("span",{className:"pl-1",children:"pushed"}),n[6]=i):i=n[6],n[7]!==u||n[8]!==t||n[9]!==o?(s=(0,r.jsxs)("div",{className:t,children:[a,o,i,u]}),n[7]=u,n[8]=t,n[9]=o,n[10]=s):s=n[10],s}try{W.displayName||(W.displayName="SingleAuthor")}catch{}try{O.displayName||(O.displayName="AuthorByline")}catch{}try{P.displayName||(P.displayName="TwoAuthors")}catch{}try{q.displayName||(q.displayName="MultipleAuthors")}catch{}try{G.displayName||(G.displayName="CommitAttribution")}catch{}try{Q.displayName||(Q.displayName="AuthoredOrCommitted")}catch{}try{E.displayName||(E.displayName="PushAttribution")}catch{}},981:(e,t,a)=>{a.d(t,{xC:()=>A,B6:()=>B,ym:()=>I});var r=a(74848),l=a(21728),o=a(38621),i=a(55847),s=a(87330),n=a(53110),c=a(94977),d=a(96540),u=a(34164),m=a(63867),h=a(2724),x=a(39459),p=a(34614),f=a(89985);let g={Box:"CheckRunItem-module__Box--vkNX2",Box_1:"CheckRunItem-module__Box_1--NFRJY",Link:"CheckRunItem-module__Link--lCbe1",Text:"CheckRunItem-module__Text--b7ASk",Text_1:"CheckRunItem-module__Text_1--ixwzU",Text_2:"CheckRunItem-module__Text_2--bhoH3",Link_1:"CheckRunItem-module__Link_1--YV819",Box_2:"CheckRunItem-module__Box_2--If0BN"};function y(e){let t,a,i,s,n,c,d,u,m,h,y,N,v,j=(0,l.c)(37),{checkRun:_}=e;j[0]!==_.icon?(t=function(e){switch(e){case"check":return(0,r.jsx)(o.CheckIcon,{className:"fgColor-success my-0 mx-2 flex-self-center"});case"dot-fill":return(0,r.jsx)(o.DotFillIcon,{className:"fgColor-attention my-0 mx-2 flex-self-center"});case"stop":return(0,r.jsx)(o.StopIcon,{className:"fgColor-muted my-0 mx-2 flex-self-center"});case"issue-reopened":return(0,r.jsx)(o.IssueReopenedIcon,{className:"fgColor-muted my-0 mx-2 flex-self-center"});case"clock":return(0,r.jsx)(o.ClockIcon,{className:"fgColor-attention my-0 mx-2 flex-self-center"});case"square-fill":return(0,r.jsx)(o.SquareFillIcon,{className:"fgColor-default my-0 mx-2 flex-self-center"});case"skip":return(0,r.jsx)(o.SkipIcon,{className:"fgColor-muted my-0 mx-2 flex-self-center"});case"alert":return(0,r.jsx)(o.AlertIcon,{className:"fgColor-danger my-0 mx-2 flex-self-center"});default:return(0,r.jsx)(o.XIcon,{className:"fgColor-danger my-0 mx-2 flex-self-center"})}}(_.icon),j[0]=_.icon,j[1]=t):t=j[1];let C=t,k="in_progress"===_.state;return j[2]!==C||j[3]!==k?(a=k?(0,r.jsx)("div",{className:g.Box_2,children:(0,r.jsxs)("svg",{fill:"none",viewBox:"0 0 16 16",className:"anim-rotate","aria-hidden":"true",role:"img",children:[(0,r.jsx)("path",{opacity:".5",d:"M8 15A7 7 0 108 1a7 7 0 000 14v0z",stroke:"#dbab0a",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M15 8a7 7 0 01-7 7",stroke:"#dbab0a",strokeWidth:"2"}),(0,r.jsx)("path",{d:"M8 12a4 4 0 100-8 4 4 0 000 8z",fill:"#dbab0a"})]})}):(0,r.jsx)(r.Fragment,{children:C}),j[2]=C,j[3]=k,j[4]=a):a=j[4],j[5]!==_.avatarBackgroundColor?(i={backgroundColor:_.avatarBackgroundColor},j[5]=_.avatarBackgroundColor,j[6]=i):i=j[6],j[7]!==_.avatarLogo||j[8]!==i?(s=(0,r.jsx)(x.r,{square:!0,src:_.avatarLogo,sx:i}),j[7]=_.avatarLogo,j[8]=i,j[9]=s):s=j[9],j[10]!==_.avatarUrl||j[11]!==s?(n=(0,r.jsx)(p.A,{href:_.avatarUrl,"aria-label":"Avatar",className:g.Link,children:s}),j[10]=_.avatarUrl,j[11]=s,j[12]=n):n=j[12],j[13]!==_.avatarDescription||j[14]!==n?(c=(0,r.jsx)(f.m,{text:_.avatarDescription,direction:"e",children:n}),j[13]=_.avatarDescription,j[14]=n,j[15]=c):c=j[15],j[16]!==a||j[17]!==c?(d=(0,r.jsxs)("div",{className:g.Box_1,children:[a,c]}),j[16]=a,j[17]=c,j[18]=d):d=j[18],j[19]!==_.name?(u=(0,r.jsxs)("span",{className:g.Text_1,children:[_.name," "]}),j[19]=_.name,j[20]=u):u=j[20],j[21]!==_.additionalContext||j[22]!==_.pending?(m=_.pending?(0,r.jsx)("span",{className:g.Text_2,children:_.additionalContext}):_.additionalContext,j[21]=_.additionalContext,j[22]=_.pending,j[23]=m):m=j[23],j[24]!==_.description||j[25]!==_.pending?(h=_.description&&(0,r.jsxs)("span",{children:[" ","- ",_.pending?(0,r.jsx)("span",{className:g.Text_2,children:_.description}):_.description]}),j[24]=_.description,j[25]=_.pending,j[26]=h):h=j[26],j[27]!==h||j[28]!==u||j[29]!==m?(y=(0,r.jsxs)("span",{className:g.Text,children:[u,m,h]}),j[27]=h,j[28]=u,j[29]=m,j[30]=y):y=j[30],j[31]!==_.targetUrl?(N=(0,r.jsx)(p.A,{href:_.targetUrl,className:g.Link_1,children:"Details"}),j[31]=_.targetUrl,j[32]=N):N=j[32],j[33]!==y||j[34]!==N||j[35]!==d?(v=(0,r.jsxs)("li",{"data-testid":"check-run-item",className:g.Box,children:[d,y,N]}),j[33]=y,j[34]=N,j[35]=d,j[36]=v):v=j[36],v}try{y.displayName||(y.displayName="CheckRunItem")}catch{}let N={Box:"ChecksStatusBadgeFooter-module__Box--H_aiQ"};function v(e){let t,a,o=(0,l.c)(4),{checkRuns:i}=e;return o[0]!==i?(t=i.map(j),o[0]=i,o[1]=t):t=o[1],o[2]!==t?(a=(0,r.jsx)("ul",{className:N.Box,children:t}),o[2]=t,o[3]=a):a=o[3],a}function j(e,t){return(0,r.jsx)(y,{checkRun:e},t)}try{v.displayName||(v.displayName="ChecksStatusBadgeFooter")}catch{}let _={Text:"ChecksStatusBadgeHeader-module__Text--IBN_O",Text_1:"ChecksStatusBadgeHeader-module__Text_1--nuXs5",Text_2:"ChecksStatusBadgeHeader-module__Text_2--nXTHk"};function C(e){let t=(0,l.c)(4),{checksHeaderState:a}=e;switch(a){case"SUCCEEDED":{let e;return t[0]===Symbol.for("react.memo_cache_sentinel")?(e=(0,r.jsx)("span",{className:_.Text,children:"All checks have passed"}),t[0]=e):e=t[0],e}case"FAILED":{let e;return t[1]===Symbol.for("react.memo_cache_sentinel")?(e=(0,r.jsx)("span",{className:_.Text_1,children:"All checks have failed"}),t[1]=e):e=t[1],e}case"PENDING":{let e;return t[2]===Symbol.for("react.memo_cache_sentinel")?(e=(0,r.jsx)("span",{className:_.Text_2,children:"Some checks haven\u2019t completed yet"}),t[2]=e):e=t[2],e}default:{let e;return t[3]===Symbol.for("react.memo_cache_sentinel")?(e=(0,r.jsx)("span",{className:_.Text_1,children:"Some checks were not successful"}),t[3]=e):e=t[3],e}}}try{C.displayName||(C.displayName="HeaderState")}catch{}let k={Dialog:"CheckStatusDialog-module__Dialog--TK4Dm",Dialog_Body:"CheckStatusDialog-module__Dialog_Body--WjYUz",Box:"CheckStatusDialog-module__Box--KtBxw"};function A(e){let t,a,o=(0,l.c)(7),{combinedStatus:i,isOpen:s,onDismiss:n}=e;o[0]!==i?(t=i?(0,r.jsx)(C,{checksHeaderState:i.checksHeaderState}):"Loading...",o[0]=i,o[1]=t):t=o[1];let c=t;return o[2]!==i||o[3]!==s||o[4]!==n||o[5]!==c?(a=s?(0,r.jsx)(h.l,{onClose:n,title:c,subtitle:i?i.checksStatusSummary:void 0,width:"xlarge",renderBody:()=>(0,r.jsx)(h.l.Body,{className:k.Dialog_Body,children:i?(0,r.jsx)(v,{checkRuns:i.checkRuns}):(0,r.jsx)("div",{className:k.Box,children:(0,r.jsx)(m.A,{size:"medium"})})}),className:k.Dialog}):null,o[2]=i,o[3]=s,o[4]=n,o[5]=c,o[6]=a):a=o[6],a}try{A.displayName||(A.displayName="CheckStatusDialog")}catch{}let b={success:{circled:o.CheckCircleIcon,filled:o.CheckCircleFillIcon,default:o.CheckIcon,color:"var(--bgColor-success-emphasis, var(--color-success-emphasis))"},pending:{circled:o.CircleIcon,filled:o.DotFillIcon,default:o.DotFillIcon,color:"var(--bgColor-attention-emphasis, var(--color-scale-yellow-4))"},failure:{circled:o.XCircleIcon,filled:o.XCircleFillIcon,default:o.XIcon,color:"var(--bgColor-danger-emphasis, var(--color-scale-red-4))"},error:{circled:o.QuestionIcon,filled:o.QuestionIcon,default:o.QuestionIcon,color:"var(--fgColor-muted, var(--color-canvas-subtle))"}};function S(e){let t,a,o,i,s,m=(0,l.c)(19),{className:h,descriptionText:x,icon:p,iconColor:f,tooltipText:g}=e,y=(0,d.useId)(),N=g?y:void 0,v=g?void 0:x||"See all checks";m[0]!==f?(t={color:f},m[0]=f,m[1]=t):t=m[1],m[2]!==p||m[3]!==N||m[4]!==v||m[5]!==t?(a=(0,r.jsx)(n.A,{"aria-labelledby":N,icon:p,"aria-label":v,sx:t}),m[2]=p,m[3]=N,m[4]=v,m[5]=t,m[6]=a):a=m[6];let j=a;if(g){let e;m[7]!==j||m[8]!==y||m[9]!==g?(e=(0,r.jsx)(c.A,{id:y,"aria-label":g,direction:"e",children:j}),m[7]=j,m[8]=y,m[9]=g,m[10]=e):e=m[10],j=e}return m[11]!==h?(o=(0,u.$)(h,"d-flex flex-items-center gap-1"),m[11]=h,m[12]=o):o=m[12],m[13]!==x?(i=x&&(0,r.jsxs)("span",{children:[" ",x]}),m[13]=x,m[14]=i):i=m[14],m[15]!==j||m[16]!==o||m[17]!==i?(s=(0,r.jsxs)("span",{className:o,"data-testid":"checks-status-badge-icon-only",children:[j,i]}),m[15]=j,m[16]=o,m[17]=i,m[18]=s):s=m[18],s}function B(e){let t,a,o,n,c=(0,l.c)(26),{statusRollup:u,combinedStatus:m,variant:h,disablePopover:x,buttonSx:p,size:f,descriptionText:g,onWillOpenPopup:y}=e,N=void 0===h?"default":h,v=void 0===f?"medium":f,j=void 0===g?"":g,[_,C]=(0,d.useState)(!1),k=(0,d.useRef)(null),B=b[u],L=B?.[N]||b.error[N],T=B?.color||b.error.color;c[0]!==L||c[1]!==T?(t={icon:L,iconColor:T},c[0]=L,c[1]=T,c[2]=t):t=c[2];let{icon:I,iconColor:D}=t;if("error"===u){let e,t=x?void 0:"p-1";return c[3]!==I||c[4]!==D||c[5]!==t?(e=(0,r.jsx)(S,{className:t,descriptionText:"?/?",icon:I,iconColor:D,tooltipText:"There was an error retrieving checks status"}),c[3]=I,c[4]=D,c[5]=t,c[6]=e):e=c[6],e}if(x){let e;return c[7]!==j||c[8]!==I||c[9]!==D?(e=(0,r.jsx)(S,{descriptionText:j,icon:I,iconColor:D}),c[7]=j,c[8]=I,c[9]=D,c[10]=e):e=c[10],e}return c[11]!==p||c[12]!==m?.checksStatusSummary||c[13]!==j||c[14]!==y||c[15]!==I||c[16]!==D||c[17]!==v||c[18]!==u?(a=j?(0,r.jsx)(i.Q,{"data-testid":"checks-status-badge-button",leadingVisual:I,variant:"invisible",size:v,"aria-label":m?.checksStatusSummary??`Status checks: ${u}`,sx:{p:1,color:"fg.default",fontWeight:"normal",svg:{color:D},...p},ref:k,onClick:()=>{y?.(),C(!0)},children:j}):(0,r.jsx)(s.K,{"data-testid":"checks-status-badge-icon",tooltipDirection:"s",icon:I,variant:"invisible",size:v,"aria-label":m?.checksStatusSummary??u,sx:{mr:2,py:0,px:0,svg:{color:D},":hover:not([disabled])":{bg:"pageHeaderBg"},...p},ref:k,onClick:()=>{y?.(),C(!0)}}),c[11]=p,c[12]=m?.checksStatusSummary,c[13]=j,c[14]=y,c[15]=I,c[16]=D,c[17]=v,c[18]=u,c[19]=a):a=c[19],c[20]!==m||c[21]!==_?(o=_&&(0,r.jsx)(A,{combinedStatus:m,isOpen:_,onDismiss:()=>{C(!1),setTimeout(()=>{k.current?.focus()},0)}}),c[20]=m,c[21]=_,c[22]=o):o=c[22],c[23]!==a||c[24]!==o?(n=(0,r.jsxs)(r.Fragment,{children:[a,o]}),c[23]=a,c[24]=o,c[25]=n):n=c[25],n}try{S.displayName||(S.displayName="IconOnlyStatus")}catch{}try{B.displayName||(B.displayName="ChecksStatusBadge")}catch{}var L=a(96235),T=a(60039);function I(e,t){let a,r,o=(0,l.c)(7),[i,s]=(0,d.useState)(),[n,c]=(0,d.useState)();o[0]!==e||o[1]!==n||o[2]!==t?(a=async()=>{if(n!==e&&(c(e),s(void 0),e)){let a=(0,L.vki)(t,e),r=await (0,T.lS)(a);s(await r.json())}},o[0]=e,o[1]=n,o[2]=t,o[3]=a):a=o[3];let u=a;return o[4]!==i||o[5]!==u?(r=[i,u],o[4]=i,o[5]=u,o[6]=r):r=o[6],r}},88795:(e,t,a)=>{a.d(t,{d:()=>s,t:()=>n});var r=a(74848),l=a(21728),o=a(96540);let i=o.createContext({});function s(e){let t,a=(0,l.c)(3),{repository:o,children:s}=e;return a[0]!==s||a[1]!==o?(t=(0,r.jsxs)(i.Provider,{value:o,children:[" ",s," "]}),a[0]=s,a[1]=o,a[2]=t):t=a[2],t}function n(){return o.useContext(i)}try{i.displayName||(i.displayName="CurrentRepositoryContext")}catch{}try{s.displayName||(s.displayName="CurrentRepositoryProvider")}catch{}},93955:(e,t,a)=>{a.d(t,{Q:()=>s,i:()=>n});var r=a(74848),l=a(21728),o=a(96540);let i=o.createContext(void 0);function s(e){let t,a=(0,l.c)(3),{user:o,children:s}=e;return a[0]!==s||a[1]!==o?(t=(0,r.jsxs)(i.Provider,{value:o,children:[" ",s," "]}),a[0]=s,a[1]=o,a[2]=t):t=a[2],t}function n(){return o.useContext(i)}try{i.displayName||(i.displayName="CurrentUserContext")}catch{}try{s.displayName||(s.displayName="CurrentUserProvider")}catch{}},68415:(e,t,a)=>{a.d(t,{Gy:()=>c,LB:()=>x,nn:()=>p,lm:()=>m});var r=a(74848),l=a(96540);let o=null,i=null,s=new Set;function n(e){return s.add(e),o||(o=new ResizeObserver(()=>{i||(i=requestAnimationFrame(()=>{for(let e of(i=null,s))e()}))})).observe(document.documentElement),()=>{s.delete(e),0===s.size&&o&&(o.disconnect(),o=null)}}let c={small:1,medium:544,large:768,xlarge:1012,xxlarge:1280,xxxlarge:1350,xxxxlarge:1440},d=[c.xxxxlarge,c.xxxlarge,c.xxlarge,c.xlarge,c.large,c.medium,c.small],u=l.createContext({screenSize:c.small});function m(){return l.useContext(u)}function h(){return p(window.innerWidth)}function x({children:e,initialValue:t=c.small}){let a=(0,l.useSyncExternalStore)(n,h,(0,l.useCallback)(()=>t,[t])),o=(0,l.useMemo)(()=>({screenSize:a}),[a]);return(0,r.jsx)(u.Provider,{value:o,children:e})}function p(e){for(let t of d)if(e>=t)return t;return c.small}try{u.displayName||(u.displayName="ScreenContext")}catch{}try{x.displayName||(x.displayName="ScreenSizeProvider")}catch{}}}]);
//# sourceMappingURL=ui_packages_commit-attribution_index_ts-ui_packages_commit-checks-status_index_ts-ui_packages-762eaa-533ca772c757.js.map