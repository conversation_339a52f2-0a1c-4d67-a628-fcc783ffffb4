.BorderGrid{display:table;width:100%;margin-top:calc(var(--base-size-16)*-1);margin-bottom:calc(var(--base-size-16)*-1);table-layout:fixed;border-collapse:collapse;border-style:hidden}.BorderGrid .BorderGrid-cell{padding-top:var(--base-size-16);padding-bottom:var(--base-size-16)}.BorderGrid--spacious{margin-top:calc(var(--base-size-24)*-1);margin-bottom:calc(var(--base-size-24)*-1)}.BorderGrid--spacious .BorderGrid-cell{padding-top:var(--base-size-24);padding-bottom:var(--base-size-24)}.BorderGrid-row{display:table-row}.BorderGrid-cell{display:table-cell;border:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted))}.hx_details-with-rotating-caret[open]>.btn-link .hx_dropdown-caret-rotatable{border-width:0 var(--base-size-4) var(--base-size-4) var(--base-size-4);border-top-color:rgba(0,0,0,0);border-bottom-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis))}.branches-tag-list{display:inline;margin-right:var(--base-size-8);margin-left:2px;vertical-align:middle;list-style:none}.branches-tag-list .more-commit-details,.branches-tag-list.open .hidden-text-expander{display:none}.branches-tag-list.open .more-commit-details{display:inline-block}.branches-tag-list li{display:inline-block;padding-left:var(--base-size-4)}.branches-tag-list li:first-child{padding-left:0;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.branches-tag-list li.loading{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.branches-tag-list li.abbrev-tags{cursor:pointer}.branches-tag-list li a{color:inherit}.create-branch-source-branch .SelectMenu-modal{max-height:100%;overflow:visible}.branch-a-b-count .count-value{position:relative;top:-1px;display:block;padding:0 var(--base-size-4);font-size:12px}.branch-a-b-count .bar{position:absolute;min-width:3px;height:4px}.branch-a-b-count .meter{position:absolute;height:4px;background-color:var(--bgColor-neutral-muted, var(--color-neutral-muted))}.branch-a-b-count .meter.zero{background-color:rgba(0,0,0,0)}.branches .clear-search{display:none}.branches .loading-overlay{position:absolute;top:0;z-index:20;display:none;width:100%;height:100%;padding-top:50px;text-align:center}.branches .loading-overlay::before{position:absolute;top:0;right:0;bottom:0;left:0;content:"";background-color:var(--bgColor-default, var(--color-canvas-default));opacity:.7}.branches .loading-overlay .spinner{display:inline-block}.branches.is-loading .loading-overlay{display:block}.branches.is-search-mode .clear-search{display:inline-block}.commit-loader .loader-error{display:none;margin:0;font-size:12px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-danger, var(--color-danger-fg))}.commit-loader.is-error .loader-error{display:block}@media screen and (max-width: 768px){.truncate-for-mobile{display:none}}.show-for-mobile{display:none}@media screen and (max-width: 768px){.show-for-mobile{display:table-row}}.editor-abort{display:inline;font-size:14px}.file-commit-form{padding-left:var(--base-size-64)}.file-commit-form--full{position:absolute;bottom:0;left:0;z-index:10;width:100%;padding-top:var(--base-size-16);padding-left:0;margin-top:var(--base-size-16);margin-bottom:var(--base-size-16);background:var(--bgColor-default, var(--color-canvas-default))}@media(min-width: 1012px){.file-commit-form--full{top:0;right:0;bottom:auto;left:auto;width:auto;margin-top:0;margin-bottom:0}}.file-commit-form--full .commit-form{padding:0;margin-bottom:var(--base-size-24);border:0}.file-commit-form--full .commit-form::before{display:none}.file-commit-form-dropdown{position:fixed;top:0;left:0;width:100%;height:100%}.file-commit-form-dropdown::after{display:none}@media(min-width: 1012px){.file-commit-form-dropdown{position:absolute;top:auto;left:auto;width:420px;height:auto}.file-commit-form-dropdown::after{display:inline-block}}.react-code-view-edit .cm-editor{border-bottom-right-radius:var(--borderRadius-medium);border-bottom-left-radius:var(--borderRadius-medium)}.react-code-view-edit .cm-editor .cm-panels-bottom{contain:paint;border-bottom-right-radius:var(--borderRadius-medium);border-bottom-left-radius:var(--borderRadius-medium)}.react-code-view-edit .cm-editor .cm-gutters{border-bottom-left-radius:var(--borderRadius-medium)}.page-blob.height-full .blob-wrapper{overflow-y:auto}.file-info-divider{display:inline-block;width:1px;height:18px;margin-right:var(--base-size-4);margin-left:var(--base-size-4);vertical-align:middle;border-left:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.file-mode{text-transform:capitalize}.html-blob{margin-bottom:var(--base-size-16)}.file-sidebar-container .file{border-top-right-radius:0;border-bottom-right-radius:0}.file-navigation::before{display:table;content:""}.file-navigation::after{display:table;clear:both;content:""}.file-navigation .select-menu-button .css-truncate-target{max-width:200px}.file-navigation .breadcrumb{float:left;margin-top:0;margin-left:var(--base-size-4);font-size:16px;line-height:26px}.file-navigation+.breadcrumb{margin-bottom:var(--base-size-8)}.file-blankslate{border:0;border-radius:0 0 var(--borderRadius-medium) var(--borderRadius-medium)}.about-margin{margin-top:var(--base-size-12)}.focusable-grid-cell{caret-color:rgba(0,0,0,0);scroll-margin-top:200px}.focusable-grid-cell:is(:focus-visible){outline:none}.focusable-grid-cell textarea{caret-color:auto}.diff-line-row{height:var(--diff-line-minimum-height);line-height:var(--diff-line-height)}.diff-line-row:has(.diff-line-number[data-selected=true]){background-color:var(--bgColor-attention-muted)}.diff-line-row:has(.diff-text-cell[data-selected=true]){background-color:var(--bgColor-attention-muted)}.diff-line-row:last-child .diff-line-number.left-side:first-of-type{border-bottom-left-radius:5px}.diff-line-row:last-child .diff-text-cell.right-side-diff-cell{border-bottom-right-radius:5px}.diff-line-row:last-child .empty-diff-line.left-side:first-of-type{border-bottom-left-radius:5px}.diff-line-row:last-child .empty-diff-line:not(.left-side):last-of-type{border-bottom-right-radius:5px}.diff-line-row:last-child .diff-hunk-cell{border-bottom-left-radius:5px;border-bottom-right-radius:5px}.diff-line-row:last-child .diff-hunk-cell button:first-of-type{border-bottom-left-radius:5px}.diff-hunk-cell{position:relative;padding-right:var(--diff-line-height);padding-left:var(--diff-line-height)}.diff-hunk-cell.hunk{display:flex;flex-direction:row;align-items:center}.diff-text-cell{position:relative;padding-right:var(--diff-line-height);padding-left:var(--diff-line-height)}.diff-text-cell.hunk{display:flex;flex-direction:row;align-items:center}.diff-text-cell[data-selected=true]{background-color:var(--bgColor-attention-muted)}.diff-text-cell .diff-text .diff-text-marker{position:absolute;top:0;left:var(--base-size-8);padding-right:var(--base-size-8);-webkit-user-select:none;user-select:none}.diff-text-cell .diff-text .diff-text-inner{overflow:hidden;color:var(--fgColor-default, var(--color-fg-default));word-wrap:break-word;white-space:pre-wrap}.diff-text-cell .syntax-highlighted-line.addition .x{color:var(--diffBlob-additionWord-fgColor, var(--diffBlob-addition-fgColor-text));background-color:var(--diffBlob-additionWord-bgColor, var(--diffBlob-addition-bgColor-word))}.diff-text-cell .syntax-highlighted-line.deletion .x{color:var(--diffBlob-deletionWord-fgColor, var(--diffBlob-deletion-fgColor-text));background-color:var(--diffBlob-deletionWord-bgColor, var(--diffBlob-deletion-bgColor-word))}.diff-text-cell .syntax-highlighted-line .x-first{border-top-left-radius:var(--borderRadius-small);border-bottom-left-radius:var(--borderRadius-small)}.diff-text-cell .syntax-highlighted-line .x-last{border-top-right-radius:var(--borderRadius-small);border-bottom-right-radius:var(--borderRadius-small)}.diff-text-cell[data-selected=true]::before{content:" ";border-left:solid var(--borderWidth-thick) var(--borderColor-accent-emphasis);position:absolute;top:0;display:inline-block;left:0;height:100%}.diff-text-cell:is(:focus)::before{z-index:1;content:" ";position:absolute;left:0;right:0;top:0;bottom:0;border:var(--borderWidth-thick) solid var(--focus-outlineColor);pointer-events:none}.empty-diff-line{background-color:var(--diffBlob-emptyLine-bgColor, var(--bgColor-muted))}.diff-line-number{width:1%;min-width:50px;line-height:100%;text-align:right;cursor:pointer;-webkit-user-select:none;user-select:none}.diff-line-number code{line-height:var(--diff-line-height)}.diff-line-number .diff-line-number-button{all:unset;width:100%}.diff-line-number .diff-line-number-button:hover{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.diff-line-number.has-expander{padding-right:0;padding-left:0}.diff-line-number[data-selected=true]{background-color:var(--bgColor-attention-muted);mix-blend-mode:multiply}.diff-line-number[data-selected=true][data-first-unified-line-number-cell=false]:is(:focus)::before{z-index:1;content:" ";position:absolute;left:0;right:0;top:0;bottom:0;border-left:var(--borderWidth-thick) solid var(--focus-outlineColor) !important;border-top:var(--borderWidth-thick) solid var(--focus-outlineColor) !important;border-bottom:var(--borderWidth-thick) solid var(--focus-outlineColor) !important}.diff-line-number[data-selected=false]:is(:focus)::before,.diff-line-number[data-selected=true][data-first-unified-line-number-cell=true]:is(:focus)::before{z-index:1;content:" ";position:absolute;left:0;right:0;top:0;bottom:0;border:var(--borderWidth-thick) solid var(--focus-outlineColor)}.diff-line-number-neutral{color:var(--fgColor-muted, var(--color-fg-muted))}:root{--line-number-cell-width: 44px;--line-number-cell-width-unified: 88px;--diff-line-minimum-height: 24px;--diff-line-height: 24px;--diff-action-bar-position: 0}.hunk-kebab-icon{display:flex;width:var(--line-number-cell-width);padding-top:var(--base-size-4);margin-left:calc(var(--diff-line-height)*-1);background-color:var(--diffBlob-hunkNum-bgColor-rest, var(--diffBlob-hunk-bgColor-num));justify-content:right}.hunk-kebab-icon-unified{width:var(--line-number-cell-width-unified)}table[data-block-diff-cell-selection=left] .left-side-diff-cell{-webkit-user-select:none;user-select:none}table[data-block-diff-cell-selection=right] .right-side-diff-cell{-webkit-user-select:none;user-select:none}.react-code-file-contents{display:flex}.react-line-code-pairs{width:100%}.react-code-line-container:focus{outline:2px solid var(--focus-outlineColor, var(--color-accent-emphasis)) !important}.react-line-numbers{position:relative;z-index:2;display:flex;width:72px;min-width:72px;pointer-events:auto;flex-direction:column;align-items:flex-end}.react-line-numbers-no-virtualization{position:relative;z-index:2;display:flex;width:82px;min-width:82px;pointer-events:auto;flex-direction:column;align-items:flex-end}.react-code-lines,.react-code-line{position:relative;width:100%}.react-line-number{position:relative;padding-right:10px;padding-left:var(--base-size-16);color:var(--fgColor-muted, var(--color-fg-muted));text-align:right;white-space:nowrap;border:0}.react-line-number.highlighted-line{z-index:1}.react-line-number:not(.prevent-click){cursor:pointer;-webkit-user-select:none;user-select:none}.react-line-number:not(.prevent-click):hover{color:var(--fgColor-default, var(--color-fg-default))}.react-code-line-contents-no-virtualization{position:relative;width:100%;padding-right:10px;padding-left:10px;overflow:visible;color:var(--fgColor-default, var(--color-fg-default));vertical-align:middle;scroll-margin-top:20vh}.expand-row-ellipsis{z-index:3;max-height:20px;color:var(--fgColor-muted, var(--color-fg-subtle));pointer-events:auto;background-color:rgba(0,0,0,0)}.expand-row-ellipsis:hover{color:var(--fgColor-accent, var(--color-accent-fg))}.react-code-line-contents{position:relative;display:flex;width:100%;padding-right:10px;padding-left:10px;overflow:visible;color:var(--fgColor-default, var(--color-fg-default));vertical-align:middle;scroll-margin-top:20vh}.react-code-line-contents .expand-row-ellipsis{max-height:20px;color:var(--fgColor-muted, var(--color-fg-subtle));pointer-events:auto;background-color:rgba(0,0,0,0)}.react-code-line-contents .expand-row-ellipsis:hover{color:var(--fgColor-accent, var(--color-accent-fg))}.react-code-text{font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace);font-size:12px;line-height:20px;word-wrap:normal;white-space:pre}.react-code-text span{display:inline-block}.react-code-text-cell{word-wrap:anywhere;white-space:pre-wrap}.react-code-text-cell span{display:inline}.react-code-text-cell span:empty{display:inline-block}.react-no-virtualization-wrapper{content-visibility:auto;contain-intrinsic-height:auto 1200px}.react-no-virtualization-wrapper-lines{content-visibility:auto;contain-intrinsic-height:auto 1200px;padding-right:10px}.react-no-virtualization-wrapper-lines-ssr{padding-right:10px}.react-line-number.virtual,.react-code-line-contents.virtual{position:absolute;top:0}.react-csv-row{background:var(--bgColor-default, var(--color-canvas-default))}.react-csv-line-number{position:relative;padding-left:var(--base-size-4)}.react-csv-line-number .react-line-number{padding-top:9px;padding-bottom:var(--base-size-8);padding-left:var(--base-size-12);line-height:unset}.react-csv-row--highlighted .react-csv-cell{background:var(--bgColor-attention-muted, var(--color-attention-subtle))}.react-csv-row--highlighted .react-csv-cell:nth-of-type(2){box-shadow:inset 2px 0 0 var(--borderColor-attention-emphasis, var(--color-attention-fg))}.react-csv-cell{padding:var(--base-size-8);font-size:12px;white-space:nowrap;border-top:solid var(--borderWidth-thin) var(--borderColor-default);border-left:solid var(--borderWidth-thin) var(--borderColor-default)}.react-csv-cell--header{font-weight:var(--base-text-weight-semibold, 600);text-align:left;background:var(--bgColor-muted, var(--color-canvas-subtle));border-top:0}.react-file-line.html-div{padding-left:10px}.react-file-line [data-code-text]::before{content:attr(data-code-text)}.hidden-unicode-replacement{-webkit-user-select:none;user-select:none;border:var(--borderWidth-thin) solid var(--borderColor-danger-emphasis, var(--color-danger-emphasis));border-radius:var(--borderRadius-medium)}.hidden-unicode-replacement.padded{padding:var(--base-size-4);margin-right:var(--base-size-4);margin-left:var(--base-size-4)}.react-code-size-details-banner{display:none}@media(max-width: 1012px){.react-code-size-details-banner{display:flex !important}}.react-code-size-details-in-header{display:flex;align-items:center}@media(max-width: 1012px){.react-code-size-details-in-header{display:none}}@media(max-width: 544px){.react-blob-view-header-sticky{position:relative !important}}.react-blob-header-edit-and-raw-actions{display:inherit !important}@media(max-width: 544px){.react-blob-header-edit-and-raw-actions{display:none !important}}.react-blob-header-edit-and-raw-actions-combined{display:none !important}@media(max-width: 544px){.react-blob-header-edit-and-raw-actions-combined{display:inherit !important}}@media(max-width: 430px){.react-contributors-title{display:none}}.react-blame-segment-wrapper{display:flex;width:100%;flex-direction:column;border-bottom:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted))}@media(min-width: 768px){.react-blame-segment-wrapper{flex-direction:row}}.react-blame-no-line-data{padding-top:10px;padding-bottom:10px}@media(min-width: 768px){.react-blame-no-line-data{padding-top:5px;padding-bottom:5px}}:root{--blame-segments-count: 1;--blame-virt-total-size: unset;--blame-single-blame-height-narrow: 41px}.virtual-blame-wrapper{display:flex;flex-direction:column;isolation:isolate;position:relative;height:calc(var(--blame-virt-total-size) + var(--blame-segments-count)*var(--blame-single-blame-height-narrow))}@media screen and (min-width: 768px){.virtual-blame-wrapper{height:var(--blame-virt-total-size)}}.react-blame-for-range{min-width:auto}@media(max-width: 768px){.react-blame-for-range{background:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:var(--borderWidth-thin) solid var(--borderColor-muted, var(--color-border-muted))}}@media(min-width: 768px){.react-blame-for-range{min-width:350px;max-width:350px}}.react-blame-for-range .age-indicator{width:var(--base-size-4);padding:2px}.react-blame-for-range .age-indicator .blame-age-indicator{width:var(--base-size-4);height:100%;border-radius:2px}.react-blame-for-range .author-avatar-wrapper{width:25px;padding-top:6px;padding-left:var(--base-size-4);vertical-align:top}@media(min-width: 768px){.react-blame-for-range .author-avatar-wrapper{padding-top:3px}}.react-blame-for-range .timestamp-ago{width:100px;padding-left:10px;vertical-align:top}.react-blame-for-range .timestamp-wrapper-desktop{display:none}@media(min-width: 768px){.react-blame-for-range .timestamp-wrapper-desktop{display:inherit}}.react-blame-for-range .timestamp-wrapper-mobile{display:flex}@media(min-width: 768px){.react-blame-for-range .timestamp-wrapper-mobile{display:none}}.react-file-upload{display:flex;min-height:0;flex-direction:column}.react-file-upload .file-input-focused{padding:5px var(--base-size-8);border:var(--borderWidth-thick) solid var(--borderColor-accent-emphasis, var(--color-accent-emphasis)) !important}.react-blob-print-hide{font-family:var(--fontStack-monospace, ui-monospace, SFMono-Regular, SF Mono, Menlo, Consolas, Liberation Mono, monospace) !important}@media print{.react-blob-print-hide{display:none}}@media(forced-colors: active){.code-navigation-cursor{forced-color-adjust:none;background-color:#fff !important}}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight{display:flex;justify-content:space-between;margin-bottom:var(--base-size-16);background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content pre,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight pre{margin-bottom:0}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content .zeroclipboard-container,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight .zeroclipboard-container{display:block;animation:none}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content .zeroclipboard-container clipboard-copy,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight .zeroclipboard-container clipboard-copy{width:var(--control-small-size, 28px);height:var(--control-small-size, 28px)}.react-blob-print-hide::selection{background-color:var(--selection-bgColor, var(--color-accent-muted))}.react-button-with-indicator::after{position:absolute;top:0;right:0;display:inline-block;width:var(--base-size-8, 8px);height:var(--base-size-8, 8px);content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border:var(--borderWidth-thick) solid var(--bgColor-default, var(--color-canvas-default));border-radius:50%}@media(max-width: 544px){.react-blob-sticky-header{display:none !important;content-visibility:hidden}.react-blob-scroll-marks{display:none}.AvatarShowLarge{display:none !important}.AvatarShowMedium{display:none !important}.popover-container-width{width:320px}}@media(max-width: 768px)and (min-width: 544px){.AvatarShowLarge{display:none !important}.AvatarShowMedium{display:inherit !important}}@media(max-width: 768px){.react-code-view-bottom-padding{margin-bottom:var(--base-size-8)}.react-code-view-header-mb--narrow{margin-bottom:var(--base-size-8)}.react-tree-show-tree-items-on-large-screen{display:none}.inner-panel-content-not-narrow{display:none !important}.find-text-help-tooltip{display:none !important}.blob-license-banner-outer{flex-direction:column}.code-nav-file-information{max-height:40vh;overflow-y:auto}.find-in-file-popover{position:absolute;right:0;bottom:0;left:0;z-index:11;width:100%;background:var(--bgColor-default, var(--color-canvas-default));border:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-large)}}@media(min-width: 768px){.AvatarShowLarge{display:inherit !important}.react-tree-show-tree-items-on-large-screen{display:block}.AvatarShowMedium{display:inherit !important}.react-code-view-bottom-padding{margin-bottom:var(--base-size-16)}.panel-content-narrow-styles{width:33%;min-width:320px;max-width:460px}}@media(min-width: 768px)and (max-width: 1012px){.panel-content-narrow-styles{margin-top:var(--base-size-40)}}@media(min-width: 768px){.blob-license-banner-outer{flex-direction:row}.find-in-file-popover-stickied{position:absolute;top:98px;right:var(--base-size-8);z-index:11;background:var(--bgColor-default, var(--color-canvas-default));border-bottom:none;border-radius:var(--borderRadius-large);box-shadow:var(--shadow-floating-large, var(--color-shadow-large))}.find-in-file-popover-not-stickied{position:absolute;top:52px;right:var(--base-size-8);z-index:11;background:var(--bgColor-default, var(--color-canvas-default));border-bottom:none;border-radius:var(--borderRadius-large);box-shadow:var(--shadow-floating-large, var(--color-shadow-large))}}.react-blob-textarea{scrollbar-width:0}.react-blob-textarea::-webkit-scrollbar{display:none}.react-blob-textarea.select-contrast::selection{color:initial}.react-blob-scroll-marks{display:"block"}.react-tree-show-tree-items{display:block !important}@media(max-width: 1012px){.org-onboarding-tip-media{display:none}}.react-tree-pane-contents-3-panel{display:block}@media(min-width: 768px)and (max-width: 1350px){.react-tree-pane-contents-3-panel{display:none !important}}.react-tree-pane-contents{display:block}@media(min-width: 768px)and (max-width: 1012px){.react-tree-pane-contents{display:none !important}}.react-tree-pane-overlay-3-panel{display:none}@media(min-width: 768px)and (max-width: 1350px){.react-tree-pane-overlay-3-panel{display:block}}.react-tree-pane-overlay{display:none}@media(min-width: 768px)and (max-width: 1012px){.react-tree-pane-overlay{display:block}}.container{container-type:inline-size}@container (max-width: 768px){.react-code-view-header-element--wide{display:none !important}.react-code-view-header-element--narrow{display:flex !important}.react-code-view-header-wrap--narrow{flex-wrap:wrap}}@container (min-width: 768px){.react-code-view-header-element--wide{display:flex !important}.react-code-view-header-element--narrow{display:none !important}}@supports not (container-type: inline-size){@media(max-width: 768px){.react-code-view-header-element--wide{display:none !important}.react-code-view-header-element--narrow{display:flex !important;width:100%}}@media(min-width: 768px){.react-code-view-header-element--wide{display:flex !important;width:100%}.react-code-view-header-element--narrow{display:none !important}}}.react-directory-row{height:40px;font-size:14px}.react-directory-row td{padding-left:var(--base-size-16);text-align:left;border-top:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default))}.react-directory-row:hover{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.react-directory-filename-column{display:flex;height:40px;padding-right:var(--base-size-16);align-items:center;row-gap:4px;column-gap:10px}.react-directory-filename-column .react-directory-filename-cell{margin:0;font-size:14px;font-weight:var(--base-text-weight-normal, 400)}.react-directory-filename-column .icon-directory{color:var(--treeViewItem-leadingVisual-iconColor-rest, var(--color-icon-directory))}.react-directory-truncate{display:inline-block;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;vertical-align:top}.react-directory-commit-message{max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.react-directory-commit-age{padding-right:var(--base-size-16);color:var(--fgColor-muted, var(--color-fg-muted));text-align:right}.react-tree-toggle-button-with-indicator::after{position:absolute;top:3px;right:2px;display:inline-block;width:var(--base-size-8, 8px);height:var(--base-size-8, 8px);content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border:var(--borderWidth-thick) solid var(--bgColor-default, var(--color-canvas-default));border-radius:50%}.react-repos-overview-margin{--Layout-gutter: 24px}@media screen and (min-width: 1280px){.react-repos-overview-margin{margin-right:calc((100% - 1356px)/2)}}@media screen and (max-width: 1012px){.react-repos-overview-margin{--Layout-sidebar-width: 240px}}.react-repos-tree-pane-ref-selector span{justify-content:normal}.react-directory-row-name-cell-small-screen{display:none}@media screen and (max-width: 544px){.react-directory-row-name-cell-small-screen{display:table-cell}}.react-directory-row-name-cell-large-screen{display:table-cell}@media screen and (max-width: 544px){.react-directory-row-name-cell-large-screen{display:none}}.react-directory-row-commit-cell{display:table-cell}@media screen and (max-width: 544px){.react-directory-row-commit-cell{display:none}}.react-directory-add-file-icon{display:block}@media screen and (min-width: 1279px){.react-directory-add-file-icon{display:none}}.react-overview-code-button-action-list{width:400px}@media screen and (max-width: 544px){.react-overview-code-button-action-list{width:250px}}.react-directory-remove-file-icon{display:block}@media screen and (max-width: 1278px){.react-directory-remove-file-icon{display:none}}.manifest-commit-form{margin-top:var(--base-size-16)}.repo-file-upload-outline{width:100%;height:100%}.repo-file-upload-target{position:relative}.repo-file-upload-target.is-uploading .repo-file-upload-text.initial-text,.repo-file-upload-target.is-failed .repo-file-upload-text.initial-text,.repo-file-upload-target.is-default .repo-file-upload-text.initial-text{display:none}.repo-file-upload-target.is-uploading .repo-file-upload-text.alternate-text,.repo-file-upload-target.is-failed .repo-file-upload-text.alternate-text,.repo-file-upload-target.is-default .repo-file-upload-text.alternate-text{display:block}.repo-file-upload-target.is-uploading.dragover .repo-file-upload-text,.repo-file-upload-target.is-failed.dragover .repo-file-upload-text,.repo-file-upload-target.is-default.dragover .repo-file-upload-text{display:none}.repo-file-upload-target .repo-file-upload-text.initial-text{display:block}.repo-file-upload-target .repo-file-upload-text.alternate-text{display:none}.repo-file-upload-target .repo-file-upload-text,.repo-file-upload-target .repo-file-upload-drop-text{margin-bottom:var(--base-size-4)}.repo-file-upload-target .repo-file-upload-choose{display:inline-block;margin-top:0;font-size:16px}.repo-file-upload-target .manual-file-chooser{margin-left:0}.repo-file-upload-target .manual-file-chooser:hover+.manual-file-chooser-text{text-decoration:underline}.repo-file-upload-target .manual-file-chooser:focus+.manual-file-chooser-text{text-decoration:underline;outline:var(--focus-outlineColor, var(--color-accent-fg)) solid 2px}.repo-file-upload-target .repo-file-upload-outline{position:absolute;top:3%;left:1%;width:98%;height:94%}.repo-file-upload-target.is-failed .repo-file-upload-outline,.repo-file-upload-target.is-bad-file .repo-file-upload-outline,.repo-file-upload-target.is-too-big .repo-file-upload-outline,.repo-file-upload-target.is-too-many .repo-file-upload-outline,.repo-file-upload-target.is-empty .repo-file-upload-outline{height:85%}.repo-file-upload-target.dragover .repo-file-upload-text{display:none}.repo-file-upload-target.dragover .repo-file-upload-choose{visibility:hidden}.repo-file-upload-target.dragover .repo-file-upload-drop-text{display:block}.repo-file-upload-target.dragover .repo-file-upload-outline{border:6px dashed var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-medium)}.repo-file-upload-target .repo-file-upload-drop-text{display:none}.repo-file-upload-errors{display:none}.repo-file-upload-errors .error{display:none}.is-failed .repo-file-upload-errors,.is-bad-file .repo-file-upload-errors,.is-too-big .repo-file-upload-errors,.is-too-many .repo-file-upload-errors,.is-hidden-file .repo-file-upload-errors,.is-empty .repo-file-upload-errors{position:absolute;right:0;bottom:0;left:0;display:block;padding:var(--base-size-4) var(--base-size-8);line-height:1.5;text-align:left;background-color:var(--bgColor-default, var(--color-canvas-default));border-top:var(--borderWidth-thin) solid var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:var(--borderRadius-medium);border-bottom-left-radius:var(--borderRadius-medium)}.is-file-list .repo-file-upload-errors{border-bottom-right-radius:0;border-bottom-left-radius:0}.is-failed .repo-file-upload-errors .failed-request,.is-bad-file .repo-file-upload-errors .failed-request{display:inline-block}.is-too-big .repo-file-upload-errors .too-big{display:inline-block}.is-hidden-file .repo-file-upload-errors .hidden-file{display:inline-block}.is-too-many .repo-file-upload-errors .too-many{display:inline-block}.is-empty .repo-file-upload-errors .empty{display:inline-block}.repo-file-upload-tree-target{position:fixed;top:0;left:0;z-index:1000;width:100%;height:100%;padding:var(--base-size-16);color:var(--fgColor-default, var(--color-fg-default));visibility:hidden;background:var(--bgColor-default, var(--color-canvas-default));opacity:0}.repo-file-upload-tree-target .repo-file-upload-outline{border:6px dashed var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-medium)}.dragover .repo-file-upload-tree-target{visibility:visible;opacity:1;transition:visibility .2s,opacity .2s}.dragover .repo-file-upload-tree-target .repo-file-upload-slate{top:50%;opacity:1}.repo-file-upload-slate{position:absolute;top:50%;width:100%;text-align:center;transform:translateY(-50%)}.repo-file-upload-slate h2{margin-top:var(--base-size-4)}.repo-upload-breadcrumb{margin-bottom:var(--base-size-16)}.tree-finder-input{min-height:32px;box-sizing:border-box;border-color:rgba(0,0,0,0)}.tree-finder-input,.tree-finder-input:focus{font-size:inherit;box-shadow:none;appearance:none}.tree-browser .octicon-chevron-right{color:rgba(0,0,0,0)}.tree-browser-result .octicon-file{color:var(--fgColor-muted, var(--color-fg-muted))}.tree-browser-result:hover,.tree-browser-result[aria-selected=true]{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.tree-browser-result:hover .octicon-file,.tree-browser-result[aria-selected=true] .octicon-file{color:inherit}.tree-browser-result[aria-selected=true] .octicon-chevron-right{color:inherit}.tree-browser-result .css-truncate-target{max-width:870px}.tree-browser-result mark{font-weight:var(--base-text-weight-semibold, 600);color:inherit;background:none}
/*# sourceMappingURL=index.scss.map */

/*# sourceMappingURL=code-7d1db14b47d7.css.map*/