"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_code-view-shared_hooks_use-canonical-object_ts-ui_packages_code-view-shared_hooks-6097ef"],{7991:(e,t,a)=>{a.d(t,{A:()=>n});let n={"code-view-link-button":"LinkButton-module__code-view-link-button--xvCGA"}},30903:(e,t,a)=>{a.d(t,{p:()=>r});var n=a(96540);function r(e){let t=(0,n.useRef)([]);for(let a of t.current)if(e===a||function e(t,a){if(t===a)return!0;if("object"!=typeof t||typeof t!=typeof a||!t||!a)return!1;if(Array.isArray(t)){if(!Array.isArray(a)||t.length!==a.length)return!1;for(let n=0;n<t.length;n++)if(!e(t[n],a[n]))return!1;return!0}let n=Object.keys(t),r=Object.keys(a);if(n.length!==r.length)return!1;for(let r of n)if(!e(t[r],a[r]))return!1;return!0}(a,e))return a;return t.current.unshift(e),t.current.length>5&&t.current.pop(),e}},28408:(e,t,a)=>{a.d(t,{P:()=>l});var n=a(96235),r=a(29769),i=a(25772),s=a(96540),o=a(74474);function l(e){let t=(0,i.B)(),a=e||t,l=(0,s.useRef)(a),c=(0,r.q)();if(a)l.current=a;else{let e=(0,n.n7E)(location.pathname,l.current.refInfo.name,l.current.path);a=(0,o.Bl)(l.current,c,e)}return a}},74474:(e,t,a)=>{a.d(t,{Bl:()=>i,aO:()=>r});var n=a(18138);function r(e){return(0,n.Hf)(e)?{...e.fileTree,[e.path]:{items:e.tree.items,totalCount:e.tree.totalCount}}:e.fileTree}function i(e,t,a){return{path:a,repo:e.repo,refInfo:e.refInfo,currentUser:e.currentUser,fileTree:r(e),fileTreeProcessingTime:e.fileTreeProcessingTime,foldersToFetch:e.foldersToFetch,allShortcutsEnabled:e.allShortcutsEnabled,treeExpanded:e.treeExpanded,symbolsExpanded:e.symbolsExpanded,codeLineWrapEnabled:e.codeLineWrapEnabled,error:t||void 0}}},73081:(e,t,a)=>{a.d(t,{E:()=>n});let n={"&:hover:not([disabled])":{textDecoration:"none"},"&:focus:not([disabled])":{textDecoration:"none"},"&:active:not([disabled])":{textDecoration:"none"}}},38007:(e,t,a)=>{let n;a.d(t,{BI:()=>f,Ti:()=>x,lA:()=>u,sX:()=>h});var r=a(70837),i=a(18679),s=a(85351),o=a(7479);let{getItem:l}=(0,s.A)("localStorage"),c="dimension_",d=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","scid"];try{let e=(0,r.O)("octolytics");delete e.baseContext,n=new i.s(e)}catch{}function m(e){let t=(0,r.O)("octolytics").baseContext||{};if(t)for(let[e,a]of(delete t.app_id,delete t.event_url,delete t.host,Object.entries(t)))e.startsWith(c)&&(t[e.replace(c,"")]=a,delete t[e]);let a=document.querySelector("meta[name=visitor-payload]");for(let[e,n]of(a&&Object.assign(t,JSON.parse(atob(a.content))),new URLSearchParams(window.location.search)))d.includes(e.toLowerCase())&&(t[e]=n);return t.staff=(0,o.X)().toString(),Object.assign(t,e)}function u(e){n?.sendPageView(m(e))}function h(){return document.head?.querySelector('meta[name="current-catalog-service"]')?.content}function f(e,t={}){let a=h(),r=a?{service:a}:{};for(let[e,a]of Object.entries(t))null!=a&&(r[e]=`${a}`);n&&(m(r),n.sendEvent(e||"unknown",m(r)))}function x(e){return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,JSON.stringify(t)]))}},80663:(e,t,a)=>{a.d(t,{I:()=>s});var n=a(96540),r=a(17515),i=a(60674);function s(e,t,a=[]){let o=(0,n.useCallback)(e,a),l=(0,i.BP)(),c=(0,n.useRef)(l===i.O8.ClientRender),[d,m]=(0,n.useState)(()=>l===i.O8.ClientRender?o():t),u=(0,n.useCallback)(()=>{m(o)},[o]);return(0,r.N)(()=>{c.current||m(o),c.current=!1},[o,...a]),[d,u]}},22279:(e,t,a)=>{a.d(t,{E:()=>u,q:()=>m});var n=a(5225),r=a(96235),i=a(21715),s=a(60039),o=a(96540);async function l(e){let t=await (0,s.lS)(e);return t.ok?await t.json():void 0}let c=new Map,d=(0,n.A)(l,{cache:c});function m(){c.clear()}function u(e,t,a,n){let[s,l]=(0,o.useState)(),[c,u]=(0,o.useState)(!1),[h,f]=(0,o.useState)(!0),x=t&&e&&a&&n?(0,r.IO9)({repo:{name:t,ownerLogin:e},commitish:a,action:"latest-commit",path:n}):null;return(0,o.useEffect)(()=>{let e=new AbortController;return document.addEventListener(i.z.START,m,{signal:e.signal}),()=>{e.abort()}},[]),(0,o.useEffect)(()=>{let e=!1;return(async()=>{if(!x)return;u(!1),f(!0),l(void 0);let t=await d(x);if(!e){try{t?l(t):u(!0)}catch{u(!0)}f(!1)}})(),function(){e=!0}},[x,a]),[s,h,c]}},60039:(e,t,a)=>{a.d(t,{DI:()=>r,QJ:()=>s,Sr:()=>o,lS:()=>i});var n=a(26559);function r(e,t={}){var a=e;if(new URL(a,window.location.origin).origin!==window.location.origin)throw Error("Can not make cross-origin requests from verifiedFetch");let i=function(e){let t=new URL(e,window.location.href),a=new URL(window.location.href,window.location.origin),n=a.searchParams.get("_features");n&&!t.searchParams.has("_features")&&t.searchParams.set("_features",n);let r=a.searchParams.get("_tracing");return r&&!t.searchParams.has("_tracing")&&t.searchParams.set("_tracing",r),e.startsWith(window.location.origin)?t.href:`${t.pathname}${t.search}`}(e),s={...t.headers,"GitHub-Verified-Fetch":"true",...(0,n.kt)()};return fetch(i,{...t,headers:s})}function i(e,t){let a={...t?.headers??{},Accept:"application/json","Content-Type":"application/json"},n=t?.body?JSON.stringify(t.body):void 0;return r(e,{...t,body:n,headers:a})}function s(e,t={}){let a={...t.headers,"GitHub-Is-React":"true"};return r(e,{...t,headers:a})}function o(e,t){let a={...t?.headers??{},"GitHub-Is-React":"true"};return i(e,{...t,headers:a})}},72334:(e,t,a)=>{a.d(t,{L:()=>x});var n=a(74848),r=a(88795),i=a(96235),s=a(28391),o=a(24208),l=a(38621),c=a(10569),d=a(87330),m=a(15385),u=a(141),h=a(98637);let f={ActionMenu_Overlay:"AddFileDropdownButton-module__ActionMenu_Overlay--YbWNH"};function x({useIcon:e}){let{refInfo:t,path:a}=(0,u.eu)(),x=(0,r.t)(),{sendRepoClickEvent:p}=(0,h.T)();return t.canEdit?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.W,{as:"h2",text:"Add file"}),(0,n.jsxs)(c.W,{children:[e?(0,n.jsx)(c.W.Anchor,{children:(0,n.jsx)(d.K,{icon:l.PlusIcon,"aria-label":"Add file"})}):(0,n.jsx)(c.W.Button,{children:"Add file"}),(0,n.jsx)(c.W.Overlay,{className:f.ActionMenu_Overlay,children:(0,n.jsxs)(m.l,{children:[(0,n.jsxs)(m.l.LinkItem,{as:s.N,onClick:()=>p("NEW_FILE_BUTTON"),to:(0,i.IO9)({repo:x,path:a,commitish:t.name,action:"new"}),children:[(0,n.jsx)(m.l.LeadingVisual,{children:(0,n.jsx)(l.PlusIcon,{})}),"Create new file"]}),(0,n.jsxs)(m.l.LinkItem,{onClick:()=>p("UPLOAD_FILES_BUTTON"),href:(0,i.IO9)({repo:x,path:a,commitish:t.name,action:"upload"}),children:[(0,n.jsx)(m.l.LeadingVisual,{children:(0,n.jsx)(l.UploadIcon,{})}),"Upload files"]})]})})]})]}):null}try{x.displayName||(x.displayName="AddFileDropdownButton")}catch{}},95782:(e,t,a)=>{a.d(t,{D:()=>x});var n=a(74848),r=a(141),i=a(88795),s=a(96235),o=a(38621),l=a(10871),c=a(2676),d=a(34614),m=a(53110),u=a(7991),h=a(34164);let f={Box:"CodeViewError-module__Box--sYHgp"};function x(e){let t=(0,i.t)(),{refInfo:a,path:c}=(0,r.eu)();return(0,n.jsx)("div",{className:f.Box,children:(0,n.jsxs)("div",{className:"d-flex flex-column flex-justify-center flex-items-center border rounded-2 px-6 py-7",children:[(0,n.jsx)(m.A,{icon:o.AlertIcon,className:"fgColor-muted mb-2",size:20}),(0,n.jsx)(p,{...e}),(c||!a.currentOid)&&(0,n.jsx)(l.z,{type:"button",className:(0,h.$)("mt-4",u.A["code-view-link-button"]),variant:"primary","aria-label":a.currentOid?"go to Overview":"go to default branch",href:a.currentOid?(0,s.Do2)(t):(0,s.xlN)(t),children:a.currentOid?"Return to the repository overview":"Go to default branch"})]})})}function p({httpStatus:e,type:t}){return(0,n.jsxs)("div",{className:"d-flex flex-column flex-items-center gap-1 text-center",children:[(0,n.jsx)("div",{className:"f2 fgColor-default text-bold",children:404===e?"404 - page not found":"Error loading page"}),404===e?(0,n.jsx)(y,{}):(0,n.jsx)(N,{httpStatus:e,type:t})]})}function y(){let e=(0,i.t)(),{path:t,refInfo:a}=(0,r.eu)();return a.currentOid?(0,n.jsxs)("div",{className:"d-flex flex-wrap flex-justify-center fgColor-muted","data-testid":"eror-404-description",children:["The\xa0",(0,n.jsx)(c.A,{as:"p",className:"mb-0",children:a.name}),"\xa0branch of\xa0",(0,n.jsx)("p",{className:"text-bold mb-0",children:e.name}),"\xa0does not contain the path\xa0",(0,n.jsxs)("p",{className:"text-bold mb-0",children:[t,"."]})]}):(0,n.jsxs)("div",{className:"d-flex flex-wrap flex-justify-center fgColor-muted","data-testid":"error-404-description",children:["Cannot find a valid ref in\xa0",(0,n.jsx)(c.A,{as:"p",className:"mb-0",children:a.name})]})}function N({httpStatus:e,type:t}){let a=e?` ${e} error`:"error";return"fetchError"===t?(0,n.jsx)("div",{className:"f5 fgColor-muted","data-testid":"fetch-error-description",children:"It looks like your internet connection is down. Please check it."}):(0,n.jsxs)("div",{className:"f5 fgColor-muted","data-testid":"default-error-description",children:["An unexpected ",a," occured. Try",(0,n.jsx)(d.A,{inline:!0,onClick:()=>window.location.reload(),children:"\xa0reloading the page."},"reload-page")]})}try{x.displayName||(x.displayName="CodeViewError")}catch{}try{p.displayName||(p.displayName="ErrorText")}catch{}try{y.displayName||(y.displayName="DescriptionText404")}catch{}try{N.displayName||(N.displayName="DefaultDescriptionText")}catch{}},50144:(e,t,a)=>{a.d(t,{l:()=>l});var n=a(74848),r=a(26807),i=a(38621),s=a(87330),o=a(50104);function l({editPath:e,editTooltip:t,customSx:a}){let{editFileShortcut:l}=(0,o.wk)(),c=(0,r.Z)();return e?(0,n.jsx)(s.K,{icon:i.PencilIcon,sx:{...a},"aria-label":t,onClick:()=>{c(e)},"data-hotkey":l.hotkey,size:"small",title:t,variant:"invisible"}):null}try{l.displayName||(l.displayName="EditButton")}catch{}},37902:(e,t,a)=>{a.d(t,{S:()=>S,D:()=>T});var n=a(74848),r=a(99921),i=a(141),s=a(98637),o=a(37804),l=a(88795),c=a(96235),d=a(28391),m=a(45968),u=a(30422),h=a(24208),f=a(22279),x=a(38621),p=a(89169),y=a(34614),N=a(10871),j=a(87330),b=a(96540),g=a(981);function v({status:e,oid:t}){let a=(0,l.t)(),[r,i]=(0,g.ym)(t,a);return e?(0,n.jsx)(g.B6,{statusRollup:e,combinedStatus:r,onWillOpenPopup:i,size:"small"}):null}try{v.displayName||(v.displayName="ReposChecksStatusBadge")}catch{}var w=a(7991);let C={Box:"LatestCommit-module__Box--En0AE",Box_1:"LatestCommit-module__Box_1--Kkzat",Box_2:"LatestCommit-module__Box_2--gAJMD",IconButton:"LatestCommit-module__IconButton--jJLCx",Box_3:"LatestCommit-module__Box_3--rfvgc",VerifiedHTMLText:"LatestCommit-module__VerifiedHTMLText--Otdmz",VerifiedHTMLText_1:"LatestCommit-module__VerifiedHTMLText_1--OpJGr"};var _=a(34164),k=a(53110),B=a(94977);function T({commitCount:e}){return(0,n.jsx)("div",{className:"d-flex flex-column border rounded-2 mb-3 pl-1",children:(0,n.jsx)(S,{commitCount:e})})}function S({commitCount:e}){let t=(0,l.t)(),{refInfo:a,path:s}=(0,i.eu)(),[o,c,d]=(0,f.E)(t.ownerLogin,t.name,a.name,s),[m,u]=(0,b.useState)(!1);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:C.Box,children:[(0,n.jsx)(h.W,{as:"h2",text:"Latest commit"}),d?(0,n.jsx)(A,{}):c?(0,n.jsx)(r.r,{width:120,"data-testid":"loading"}):o?(0,n.jsx)(I,{commit:o,detailsOpen:m,setDetailsOpen:u,repo:t}):null,(0,n.jsxs)("div",{className:"d-flex flex-shrink-0 gap-2",children:[(0,n.jsx)(L,{commit:o,repo:t}),(0,n.jsx)(O,{commit:o,commitCount:e,detailsOpen:m,setDetailsOpen:u})]})]}),m&&o&&(0,n.jsx)("div",{className:o.bodyMessageHtml?"d-flex":"d-flex d-sm-none",children:(0,n.jsx)(P,{commit:o,repo:t})})]})}function A(){return(0,n.jsxs)("span",{className:"fgColor-attention","data-testid":"latest-commit-error-message",children:[(0,n.jsx)(k.A,{icon:x.AlertFillIcon}),"\xa0Cannot retrieve latest commit at this time."]})}function I({commit:e,detailsOpen:t,setDetailsOpen:a,repo:r}){let i=`data-hovercard-url=${(0,c.fQd)({owner:r.ownerLogin,repo:r.name,commitish:e.oid})} `,s=function(e,t){let a="";if(e){for(let n of e.split("<a "))if(""!==n){if(n.includes("data-hovercard-url")){a=a.concat("<a ",n);continue}a=a.concat("<a ",t,n)}}return a}(e.shortMessageHtmlLink,i);return(0,n.jsxs)("div",{"data-testid":"latest-commit",className:C.Box_1,children:[e.authors&&e.authors.length>0?(0,n.jsx)(o.jF,{authors:e.authors,repo:r,includeVerbs:!1,committer:e.committer,committerAttribution:e.committerAttribution}):(0,n.jsx)(o.h1,{author:e.author,repo:r}),(0,n.jsxs)("div",{className:(0,_.$)("d-none d-sm-flex",C.Box_2),children:[(0,n.jsx)("div",{className:"Truncate flex-items-center f5",children:e.shortMessageHtmlLink&&(0,n.jsx)(u.BC,{className:"Truncate-text","data-testid":"latest-commit-html",html:s})}),e.bodyMessageHtml&&(0,n.jsx)(F,{detailsOpen:t,setDetailsOpen:a}),(0,n.jsx)(v,{oid:e.oid,status:e.status})]}),Number.isNaN(Date.parse(e.date))?null:(0,n.jsx)("span",{className:"d-flex d-sm-none fgColor-muted f6",children:(0,n.jsx)(p.A,{datetime:e.date,tense:"past"})})]})}function L({commit:e,repo:t}){let a=e?.oid.slice(0,7);return(0,n.jsx)("div",{"data-testid":"latest-commit-details",className:"d-none d-sm-flex flex-items-center",children:e&&(0,n.jsxs)("span",{className:"d-flex flex-nowrap fgColor-muted f6",children:[(0,n.jsx)(y.A,{as:d.N,to:e.url,className:"Link--secondary","aria-label":`Commit ${a}`,"data-hovercard-url":(0,c.fQd)({owner:t.ownerLogin,repo:t.name,commitish:e.oid}),children:a}),"\xa0\xb7\xa0",Number.isNaN(Date.parse(e.date))?null:(0,n.jsx)(p.A,{datetime:e.date,tense:"past"})]})})}function O({commit:e,commitCount:t,detailsOpen:a,setDetailsOpen:r}){return(0,n.jsxs)("div",{className:"d-flex gap-2",children:[(0,n.jsx)(h.W,{as:"h2",text:"History"}),(0,n.jsx)(D,{className:"d-none d-lg-flex",leadingVisual:x.HistoryIcon,children:(0,n.jsx)("span",{className:"fgColor-default",children:R(t)})}),(0,n.jsx)("div",{className:"d-sm-none",children:(e?.shortMessageHtmlLink||e?.bodyMessageHtml)&&(0,n.jsx)(F,{detailsOpen:a,setDetailsOpen:r})}),(0,n.jsx)("div",{className:"d-flex d-lg-none",children:(0,n.jsx)(B.A,{text:R(t),id:"history-icon-button-tooltip",children:(0,n.jsx)(D,{leadingVisual:x.HistoryIcon,"aria-describedby":"history-icon-button-tooltip","aria-label":"View commit history for this file."})})})]})}function R(e){return e?"1"===e?"1 Commit":`${e} Commits`:"History"}function D({children:e,className:t,leadingVisual:a,...r}){let{sendRepoClickEvent:o}=(0,s.T)(),{refInfo:d,path:m}=(0,i.eu)(),u=(0,l.t)();return(0,n.jsx)(N.z,{"aria-describedby":r["aria-describedby"],"aria-label":r["aria-label"],className:(0,_.$)(t,w.A["code-view-link-button"],"flex-items-center fgColor-default"),onClick:()=>o("HISTORY_BUTTON"),href:(0,c.GpY)({owner:u.ownerLogin,repo:u.name,ref:d.name,path:m}),variant:"invisible",size:"small",leadingVisual:a,children:e})}function F({detailsOpen:e,setDetailsOpen:t}){return(0,n.jsx)(j.K,{"aria-label":"Open commit details",icon:x.EllipsisIcon,onClick:()=>t(!e),variant:"invisible","aria-pressed":e,"aria-expanded":e,"data-testid":"latest-commit-details-toggle",size:"small",className:C.IconButton})}function P({commit:e,repo:t}){let a=e?.oid.slice(0,7);return(0,n.jsxs)("div",{className:"bgColor-muted border-top rounded-bottom-2 px-3 py-2 flex-1",children:[(0,n.jsxs)("div",{className:"d-flex d-sm-none flex-column",children:[(0,n.jsxs)("div",{className:C.Box_3,children:[e.shortMessageHtmlLink&&(0,n.jsx)(m.JR,{className:(0,_.$)("Truncate-text",C.VerifiedHTMLText),"data-testid":"latest-commit-html",html:e.shortMessageHtmlLink}),(0,n.jsx)(v,{oid:e.oid,status:e.status})]}),(0,n.jsx)(y.A,{as:d.N,to:e.url,className:"Link--secondary","aria-label":`Commit ${a}`,"data-hovercard-url":(0,c.fQd)({owner:t.ownerLogin,repo:t.name,commitish:e.oid}),children:a}),e.bodyMessageHtml&&(0,n.jsx)("br",{})]}),e.bodyMessageHtml&&(0,n.jsx)("div",{className:"mt-2 mt-sm-0 fgColor-muted",children:(0,n.jsx)(m.JR,{className:(0,_.$)("Truncate-text",C.VerifiedHTMLText_1),"data-testid":"latest-commit-html",html:e.bodyMessageHtml})})]})}try{T.displayName||(T.displayName="LatestCommitSingleLine")}catch{}try{S.displayName||(S.displayName="LatestCommitContent")}catch{}try{A.displayName||(A.displayName="CommitErrorMessage")}catch{}try{I.displayName||(I.displayName="CommitSummary")}catch{}try{L.displayName||(L.displayName="LastCommitTimestamp")}catch{}try{O.displayName||(O.displayName="HistoryLink")}catch{}try{D.displayName||(D.displayName="HistoryLinkButton")}catch{}try{F.displayName||(F.displayName="CommitDetailsButton")}catch{}try{P.displayName||(P.displayName="CommitDetails")}catch{}},56236:(e,t,a)=>{a.d(t,{A:()=>h});var n=a(74848),r=a(60039),i=a(38621),s=a(6869),o=a(10871),l=a(87330),c=a(96540),d=a(7991);let m={Flash:"PublishBanners-module__Flash--RM5Rx"};var u=a(34164);function h({showPublishActionBanner:e,releasePath:t,dismissActionNoticePath:a,className:h}){let[f,x]=(0,c.useState)(!1);return e?(0,n.jsxs)(s.A,{hidden:f,className:(0,u.$)(h,m.Flash),children:[e&&(0,n.jsx)("div",{className:"flex-1",children:"You can publish this Action to the GitHub Marketplace"}),(0,n.jsx)(o.z,{href:t,className:(0,u.$)(d.A["code-view-link-button"],"f6 mr-2"),children:"Draft a release"}),(0,n.jsx)(l.K,{icon:i.XIcon,tooltipDirection:"s","aria-label":"Dismiss",className:"bgColor-transparent border-0 pr-0",onClick:e?()=>{(0,r.DI)(a,{method:"POST"}),x(!0)}:()=>{}})]}):null}try{h.displayName||(h.displayName="PublishBanners")}catch{}},18338:(e,t,a)=>{a.d(t,{s:()=>m,e:()=>u});var n=a(74848),r=a(88795),i=a(72841),s=a(45968),o=a(17515),l=a(26807),c=a(96540),d=a(85647);let m=(0,c.forwardRef)(function({onAnchorClick:e,richText:t,stickyHeaderHeight:a,sx:m,suppressHydrationWarning:h,className:f},x){let{hash:p}=(0,d.zy)(),y=(0,r.t)(),N=(0,l.Z)(),j=(0,c.useRef)(null);return(0,c.useImperativeHandle)(x,()=>j.current),(0,c.useEffect)(()=>{let e=()=>{u(window.location.hash,a)};return window.addEventListener("load",e),window.addEventListener("hashchange",e),()=>{window.removeEventListener("load",e),window.removeEventListener("hashchange",e)}},[]),(0,o.N)(()=>{j?.current&&u(window.location.hash,a)},[p]),(0,n.jsx)(s.vb,{ref:j,className:`js-snippet-clipboard-copy-unpositioned ${f}`,html:t,sx:m,suppressHydrationWarning:h,"data-hpc":!0,onClick:t=>{let n=t.metaKey||t.ctrlKey,r=t.target.closest("a");if(r&&r.href){if(!n){let e=r.href,n=new URL(e,window.location.origin);(0,i.A)(window.location.href,e)?(u(n.hash,a),window.location.hash===n.hash&&t.preventDefault()):e.startsWith(`${window.location.origin}/${y.ownerLogin}/${y.name}/`)&&(N(n.pathname+n.search+n.hash),t.preventDefault())}e?.(t)}}})});function u(e,t=125){var a,n;if(!e)return;let r=(function(e){try{return decodeURIComponent(e.slice(1))}catch{return""}})(e).toLowerCase(),i=(a=document,""===(n=r.startsWith("user-content-")?r:`user-content-${r}`)?null:a.getElementById(n)||a.getElementsByName(n)[0]||null);i&&document&&document.defaultView&&setTimeout(()=>{window.requestAnimationFrame(()=>{let e=i.getBoundingClientRect().top-document.body.getBoundingClientRect().top-t;window.scrollTo({top:e});let a=i.closest("h1,h2,h3,h4,h5,h6,li,span");a&&(a.focus(),a.setAttribute("data-react-autofocus","true"))})},1)}try{m.displayName||(m.displayName="SharedMarkdownContent")}catch{}try{m.displayName||(m.displayName="SharedMarkdownContent")}catch{}},99921:(e,t,a)=>{a.d(t,{r:()=>r});var n=a(74848);function r({width:e,...t}){return(0,n.jsx)("div",{style:{width:e},className:"Skeleton Skeleton--text",...t,children:"\xa0"})}try{r.displayName||(r.displayName="SkeletonText")}catch{}},6709:(e,t,a)=>{a.d(t,{I6:()=>m,IL:()=>u,QU:()=>h});var n=a(74848),r=a(141),i=a(88795),s=a(22279),o=a(38621),l=a(6869),c=a(53110);let d={SpoofedCommitWarningBanner:"SpoofedCommitWarning-module__SpoofedCommitWarningBanner--EICDj"};function m(){return h()?(0,n.jsx)(u,{className:d.SpoofedCommitWarningBanner}):null}function u({className:e}){return(0,n.jsxs)(l.A,{variant:"warning",className:e,"data-testid":"spoofed-commit-warning-banner",children:[(0,n.jsx)(c.A,{icon:o.AlertIcon}),(0,n.jsx)("span",{children:"This commit does not belong to any branch on this repository, and may belong to a fork outside of the repository."})]})}function h(){let e=(0,i.t)(),{refInfo:t,path:a}=(0,r.eu)(),[n]=(0,s.E)(e.ownerLogin,e.name,t.name,a);return n?.isSpoofed??!1}try{m.displayName||(m.displayName="SpoofedCommitWarning")}catch{}try{u.displayName||(u.displayName="SpoofedCommitWarningBanner")}catch{}},87183:(e,t,a)=>{a.d(t,{A:()=>u});var n=a(74848),r=a(45968),i=a(38621),s=a(87330),o=a(9591),l=a(19509),c=a(96540),d=a(18338);let m={Box:"TableOfContentsPanel-module__Box--fnjeX",NavList:"TableOfContentsPanel-module__NavList--bSFug"};function u({onClose:e,toc:t}){let[a,u]=(0,c.useState)(""),[h,f]=(0,c.useState)(""),x=(0,c.useRef)(null);return((0,c.useEffect)(()=>{x.current?.focus()},[]),(0,c.useEffect)(()=>{let e=()=>{window.location.hash&&f(window.location.hash)};return e(),window.addEventListener("hashchange",e),()=>{window.removeEventListener("hashchange",e)}},[]),t)?(0,n.jsxs)("section",{"aria-labelledby":"outline-id",className:m.Box,children:[e?(0,n.jsxs)("div",{className:"d-flex flex-justify-between flex-items-center",children:[(0,n.jsx)("h3",{id:"outline-id",ref:x,className:"d-flex flex-justify-between flex-items-center f5 text-bold px-2",tabIndex:-1,children:"Outline"}),(0,n.jsx)(s.K,{"aria-label":"Close outline",tooltipDirection:"sw",className:"fgColor-muted",icon:i.XIcon,onClick:e,variant:"invisible"})]}):null,t.length>=8?(0,n.jsx)("div",{className:"pt-3 px-2",children:(0,n.jsx)(o.A,{leadingVisual:i.FilterIcon,placeholder:"Filter headings","aria-label":"Filter headings",className:"width-full",onChange:e=>{u(e.target.value)}})}):null,(0,n.jsx)(l.c,{className:m.NavList,children:t.map(({level:e,htmlText:t,anchor:i},s)=>{let o;if(!t||a&&!t.toLowerCase().includes(a.toLowerCase()))return null;o=1===e?{fontWeight:"bold"}:{paddingLeft:`${(e-1)*16}px`};let c=`#${i}`;return(0,n.jsx)(l.c.Item,{"aria-current":h===c?"page":void 0,href:c,onClick:e=>{1===e.button||e.metaKey||e.ctrlKey||(h!==c&&(location.href=c),(0,d.e)(c),e.preventDefault())},children:(0,n.jsx)(r.vb,{sx:{...o},html:t})},`outline-${i}-${s}`)})})]}):null}try{u.displayName||(u.displayName="TableOfContentsPanel")}catch{}},50436:(e,t,a)=>{a.d(t,{k:()=>z,o:()=>K});var n=a(74848),r=a(88795),i=a(75177),s=a(6869),o=a(99921),l=a(141),c=a(96235),d=a(60039),m=a(96540),u=a(98637),h=a(38621),f=a(10569),x=a(55847),p=a(2676),y=a(10871),N=a(92265);let j={Box:"InfobarPopover-module__Box--TeHD6",CircleOcticon:"InfobarPopover-module__CircleOcticon--LHncx"};var b=a(34164);function g({children:e}){return(0,n.jsx)("div",{className:(0,b.$)("popover-container-width",j.Box),children:e})}function v({icon:e,header:t,content:a,headerRef:r}){return(0,n.jsxs)("div",{className:"d-flex p-3",children:[(0,n.jsx)("div",{className:"mr-2",children:e}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{ref:r,tabIndex:-1,className:"f5 mb-1",children:t}),(0,n.jsx)("span",{className:"fgColor-muted f6",children:a})]})]})}function w({icon:e,bg:t}){return(0,n.jsx)(N.A,{sx:{bg:t},size:30,icon:()=>(0,n.jsx)(e,{size:16}),className:j.CircleOcticon})}function C({children:e}){return(0,n.jsx)("div",{className:"d-flex flex-wrap flex-justify-between p-3 gap-3 border-top borderColor-muted",children:e})}try{g.displayName||(g.displayName="PopoverContainer")}catch{}try{v.displayName||(v.displayName="PopoverContent")}catch{}try{w.displayName||(w.displayName="PopoverIcon")}catch{}try{C.displayName||(C.displayName="PopoverActions")}catch{}var _=a(34614);function k(e){return`${e} ${1===e?"commit":"commits"}`}function B({comparison:e,repo:t,linkify:a=!1}){let{sendRepoClickEvent:r}=(0,u.T)(),{ahead:i,behind:s,baseBranch:o,baseBranchRange:l,currentRef:d}=e,m=(0,c.bSP)({repo:t,base:l,head:d}),h=(0,c.bSP)({repo:t,base:d,head:l}),f=()=>r("AHEAD_BEHIND_LINK",{category:"Branch Infobar",action:"Ahead Compare",label:`ref_loc:bar;is_fork:${t.isFork}`}),x=()=>r("AHEAD_BEHIND_LINK",{category:"Branch Infobar",action:"Behind Compare",label:`ref_loc:bar;is_fork:${t.isFork}`});return 0===i&&0===s?(0,n.jsxs)("span",{children:["This branch is up to date with ",(0,n.jsx)(p.A,{as:"span",children:o}),"."]}):i>0&&s>0?(0,n.jsxs)("span",{children:["This branch is"," ",(0,n.jsxs)(T,{linkify:a,href:m,onClick:f,children:[k(i)," ahead of"]}),","," ",(0,n.jsxs)(T,{linkify:a,href:h,onClick:x,children:[k(s)," behind"]})," ",(0,n.jsx)(p.A,{as:"span",children:o}),"."]}):i>0?(0,n.jsxs)("span",{children:["This branch is"," ",(0,n.jsxs)(T,{linkify:a,href:m,onClick:f,children:[k(i)," ahead of"]})," ",(0,n.jsx)(p.A,{as:"span",children:o}),"."]}):(0,n.jsxs)("span",{children:["This branch is"," ",(0,n.jsxs)(T,{linkify:a,href:h,onClick:x,children:[k(s)," behind"]})," ",(0,n.jsx)(p.A,{as:"span",children:o}),"."]})}function T({sx:e,href:t,linkify:a,children:r,...i}){return a?(0,n.jsx)(_.A,{sx:e,href:t,...i,children:r}):(0,n.jsx)("span",{children:r})}try{B.displayName||(B.displayName="RefComparisonText")}catch{}try{T.displayName||(T.displayName="LinkOrText")}catch{}var S=a(7991);function A({comparison:e}){let t=(0,r.t)(),a=e.ahead>0,i=(0,c.bSP)({repo:t,base:e.baseBranchRange,head:e.currentRef});return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v,{icon:(0,n.jsx)(w,{bg:"neutral.emphasis",icon:h.GitPullRequestIcon}),header:a?(0,n.jsx)(B,{repo:t,comparison:{...e,behind:0}}):(0,n.jsxs)("span",{children:["This branch is not ahead of the upstream ",(0,n.jsx)(p.A,{as:"span",children:e.baseBranch}),"."]}),content:(0,n.jsx)("p",{children:a?"Open a pull request to contribute your changes upstream.":"No new commits yet. Enjoy your day!"})}),a&&(0,n.jsxs)(C,{children:[!t.isFork&&(0,n.jsx)(y.z,{className:(0,b.$)(S.A["code-view-link-button"],"flex-1"),href:i,"data-testid":"compare-button",children:"Compare"}),(0,n.jsx)(y.z,{className:(0,b.$)(S.A["code-view-link-button"],"flex-1"),href:`${i}?expand=1`,variant:"primary","data-testid":"open-pr-button",children:"Open pull request"})]})]})}try{A.displayName||(A.displayName="ContributePopoverContent")}catch{}function I({comparison:e}){let{sendRepoClickEvent:t}=(0,u.T)();return(0,n.jsxs)(f.W,{onOpenChange:e=>e&&t("CONTRIBUTE_BUTTON",{category:"Branch Infobar",action:"Open Contribute dropdown",label:"ref_loc:contribute_dropdown"}),children:[(0,n.jsx)(f.W.Anchor,{children:(0,n.jsx)(x.Q,{leadingVisual:h.GitPullRequestIcon,trailingVisual:h.TriangleDownIcon,children:"Contribute"})}),(0,n.jsx)(f.W.Overlay,{align:"end",className:"mt-2",children:(0,n.jsx)(g,{children:(0,n.jsx)(A,{comparison:e})})})]})}try{I.displayName||(I.displayName="ContributeButton")}catch{}var L=a(63867);function O(e,t,a){let[n,r]=(0,m.useState)(!1);return{disabled:n,label:n?t:e,action:async()=>{r(!0),await a(),r(!1)}}}let R={category:"Branch Infobar",label:"ref_loc:fetch_upstream_dropdown"};function D({comparison:e,discard:t,update:a}){let i=(0,r.t)(),{helpUrl:s}=(0,l.sq)(),{sendRepoClickEvent:o}=(0,u.T)(),d=`${s}/github/collaborating-with-issues-and-pull-requests/syncing-a-fork`,f=(0,c.bSP)({repo:i,base:e.baseBranchRange,head:e.currentRef}),p=F(e),y=e.behind>0,N=O(`Discard ${k(e.ahead)}`,"Discarding changes...",t),j=O("Update branch","Updating...",a),g={compare:"behind"===p,discard:"behind-and-ahead"===p&&e.isTrackingBranch,update:["behind","behind-and-ahead"].includes(p)},B=Object.values(g).some(Boolean),T=(0,m.useRef)(null);return(0,m.useEffect)(()=>{let e=window.setTimeout(()=>T.current?.focus());return()=>{window.clearTimeout(e)}}),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v,{icon:(0,n.jsx)(w,{icon:y?h.GitMergeIcon:h.CheckIcon,bg:y?"neutral.emphasis":"success.emphasis"}),headerRef:T,header:(0,n.jsx)(P,{comparison:e}),content:(0,n.jsx)(E,{comparison:e,helpUrl:d})}),B&&(0,n.jsxs)(C,{children:[g.compare&&(0,n.jsx)(x.Q,{as:_.A,className:(0,b.$)(S.A["code-view-link-button"],"flex-1"),href:f,onClick:()=>o("SYNC_FORK.COMPARE",{...R,action:"Compare"}),"data-testid":"compare-button",children:"Compare"}),g.discard&&(0,n.jsx)(x.Q,{onClick:N.action,className:"flex-1","data-testid":"discard-button",variant:"danger",disabled:N.disabled,children:N.label}),g.update&&(0,n.jsx)(x.Q,{onClick:j.action,disabled:j.disabled,className:"flex-1",variant:"primary","data-testid":"update-branch-button",children:j.label})]})]})}function F({behind:e,ahead:t}){return 0===e&&0===t?"sync":e>0&&t>0?"behind-and-ahead":e>0?"behind":"ahead"}function P({comparison:e}){switch(F(e)){case"behind":case"behind-and-ahead":return(0,n.jsx)("span",{children:"This branch is out-of-date"});default:return(0,n.jsxs)("span",{children:["This branch is not behind the upstream ",(0,n.jsx)(p.A,{as:"span",children:e.baseBranch}),"."]})}}function E({comparison:e,helpUrl:t}){switch(F(e)){case"sync":case"ahead":return(0,n.jsx)("p",{children:"No new commits to fetch. Enjoy your day!"});case"behind":return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{children:["Update branch to keep this branch up-to-date by syncing ",k(e.behind)," from the upstream repository."]}),(0,n.jsx)("p",{children:(0,n.jsx)(_.A,{href:t,target:"_blank",rel:"noopener noreferrer",children:"Learn more about syncing a fork"})})]});case"behind-and-ahead":return e.isTrackingBranch?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("p",{children:"Update branch to merge the latest changes from the upstream repository into this branch."}),(0,n.jsxs)("p",{children:["Discard ",k(e.ahead)," to make this branch match the upstream repository."," ",k(e.ahead)," will be removed from this branch."]}),(0,n.jsx)("p",{children:(0,n.jsx)(_.A,{href:t,target:"_blank",rel:"noopener noreferrer",children:"Learn more about syncing a fork"})})]}):(0,n.jsx)("p",{children:"Update branch to merge the latest changes from the upstream repository into this branch."})}}try{D.displayName||(D.displayName="FetchUpstreamPopoverContent")}catch{}try{P.displayName||(P.displayName="HeaderText")}catch{}try{E.displayName||(E.displayName="ContentText")}catch{}function H({comparison:e,discard:t}){let a=(0,r.t)(),{sendRepoClickEvent:i}=(0,u.T)(),s=(0,c.w7M)({repo:a,refName:e.currentRef}),o=k(e.ahead),l=O(`Discard ${o}`,"Discarding changes...",t);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(v,{icon:(0,n.jsx)(w,{icon:h.AlertIcon,bg:"neutral.emphasis"}),header:"This branch has conflicts that must be resolved",content:(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("p",{children:["Discard ",o," to make this branch match the upstream repository. ",o," will be removed from this branch."]}),(0,n.jsx)("p",{children:"You can resolve merge conflicts using the command line and a text editor."})]})}),(0,n.jsxs)(C,{children:[(0,n.jsx)(x.Q,{className:"flex-1",onClick:l.action,disabled:l.disabled,"data-testid":"discard-button",variant:"danger",children:l.label}),(0,n.jsx)(x.Q,{as:_.A,className:(0,b.$)(S.A["code-view-link-button"],"flex-1"),href:s,variant:"primary","data-testid":"open-pr-button",onClick:()=>i("SYNC_FORK.OPEN_PR",{...R,action:"Open pull request"}),children:"Open pull request"})]})]})}try{H.displayName||(H.displayName="FetchUpstreamWithConflictsPopoverContent")}catch{}function $({comparison:e}){let[t,a,i]=function({head:e,base:t}){let[a,n]=(0,m.useState)(),[i,s]=(0,m.useState)(!0),[o,l]=(0,m.useState)(),u=(0,r.t)(),h=(0,c.a3t)({repo:u,head:e,base:t});return(0,m.useEffect)(()=>{(async()=>{s(!0),n(void 0);let e=await (0,d.lS)(h);try{e.ok?n((await e.json()).state):l(e.statusText)}catch(e){l(e?.message||e?.toString())}s(!1)})()},[h]),[a,i,o]}({base:e.currentRef,head:e.baseBranchRange}),{sendRepoClickEvent:s}=(0,u.T)(),{discardChanges:o,updateBranch:h}=function(){let e=(0,r.t)(),{refInfo:{name:t}}=(0,l.eu)(),a=(0,c.X6K)({repo:e,refName:t,discard:!0}),n=(0,c.X6K)({repo:e,refName:t,discard:!1});return{updateBranch:(0,m.useCallback)(()=>(0,d.DI)(n,{method:"POST"}),[n]),discardChanges:(0,m.useCallback)(()=>(0,d.DI)(a,{method:"POST"}),[a])}}(),f=async()=>{s("SYNC_FORK.DISCARD",{...R,action:"Discard Conflicts"});let e=await o();e.ok&&e.url&&(window.location.href=e.url)},x=async()=>{s("SYNC_FORK.UPDATE",{...R,action:"Fetch and merge"});let e=await h();e.ok&&e.url&&(window.location.href=e.url)};return 0===e.behind?(0,n.jsx)(D,{update:x,discard:f,comparison:e}):a||i?(0,n.jsx)("div",{className:"p-4 d-flex flex-justify-center",children:(0,n.jsx)(L.A,{})}):"clean"===t?(0,n.jsx)(D,{update:x,discard:f,comparison:e}):(0,n.jsx)(H,{discard:f,comparison:e})}try{$.displayName||($.displayName="FetchPopoverContainer")}catch{}function M({comparison:e}){let{sendRepoClickEvent:t}=(0,u.T)();return(0,n.jsxs)(f.W,{onOpenChange:a=>a&&t("SYNC_FORK_BUTTON",{category:"Branch Infobar",action:"Open Fetch upstream dropdown",label:"ref_loc:fetch_upstream_dropdown",ahead:e.ahead,behind:e.behind}),children:[(0,n.jsx)(f.W.Anchor,{children:(0,n.jsx)(x.Q,{leadingVisual:h.SyncIcon,trailingAction:h.TriangleDownIcon,children:"Sync fork"})}),(0,n.jsx)(f.W.Overlay,{align:"end",className:"mt-2",children:(0,n.jsx)(g,{children:(0,n.jsx)($,{comparison:e})})})]})}try{M.displayName||(M.displayName="FetchUpstreamButton")}catch{}let U={Link:"PullRequestLink-module__Link--pLSjF"};function W({repo:e,pullRequestNumber:t}){return(0,n.jsxs)(_.A,{href:(0,c.oDn)({repo:e,number:t}),className:U.Link,children:[(0,n.jsx)(h.GitPullRequestIcon,{size:16}),"#",t]})}try{W.displayName||(W.displayName="PullRequestLink")}catch{}let V={BranchInfoBarContainer:"BranchInfoBar-module__BranchInfoBarContainer--Bo51w",Box:"BranchInfoBar-module__Box--xWlKp"};function z({sx:e}){let t,[a,i]=function(){let[e,t]=(0,m.useState)(),[a,n]=(0,m.useState)(),{refInfo:i,path:s}=(0,l.eu)(),o=(0,r.t)(),u=(0,c.IO9)({repo:o,action:"branch-infobar",commitish:i.name,path:s});return(0,m.useEffect)(()=>{(async()=>{t(void 0);let e=await (0,d.lS)(u);try{e.ok?t(await e.json()):n(422===e.status?"timeout":e.statusText)}catch(e){n(e?.message||e?.toString())}})()},[u]),[e,a]}(),s=(0,r.t)();return t="timeout"===i?(0,n.jsx)(n.Fragment,{children:"Sorry, getting ahead/behind information for this branch is taking too long."}):a?a.refComparison?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(B,{linkify:!0,repo:s,comparison:a.refComparison}),(0,n.jsx)("div",{className:"d-flex gap-2",children:a.pullRequestNumber?(0,n.jsx)(W,{repo:s,pullRequestNumber:a.pullRequestNumber}):(0,n.jsxs)(n.Fragment,{children:[s.currentUserCanPush&&(0,n.jsx)(I,{comparison:a.refComparison}),s.isFork&&s.currentUserCanPush&&(0,n.jsx)(M,{comparison:a.refComparison})]})})]}):(0,n.jsx)(n.Fragment,{children:"Cannot retrieve ahead/behind information for this branch."}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.r,{width:"40%"}),(0,n.jsx)(o.r,{width:"30%"})]}),(0,n.jsx)(q,{sx:e,className:V.BranchInfoBarContainer,children:t})}function q({children:e,sx:t,className:a}){return(0,n.jsx)(i.A,{"data-testid":"branch-info-bar","aria-live":"polite",sx:t,className:(0,b.$)(V.Box,a),children:e})}function K(){return(0,n.jsx)(s.A,{variant:"warning",className:"my-3",children:(0,n.jsx)("span",{children:"Cannot retrieve comparison with upstream repository."})})}try{z.displayName||(z.displayName="BranchInfoBar")}catch{}try{q.displayName||(q.displayName="BranchInfoBarContainer")}catch{}try{K.displayName||(K.displayName="BranchInfoBarErrorBanner")}catch{}},53861:(e,t,a)=>{a.d(t,{t:()=>z});var n=a(74848),r=a(93224),i=a(141),s=a(88795),o=a(96235),l=a(24208),c=a(68415),d=a(26807),m=a(38621),u=a(30631),h=a(6869),f=a(34614),x=a(53110),p=a(96540),y=a(53614),N=a(51314),j=a(60039),b=a(21113),g=a(37902);function v({uploadUrl:e}){return(0,n.jsx)("document-dropzone",{children:(0,n.jsx)("div",{className:"repo-file-upload-tree-target js-upload-manifest-tree-view","data-testid":"dragzone","data-drop-url":e,"data-target":"document-dropzone.dropContainer",children:(0,n.jsx)("div",{className:"repo-file-upload-outline",children:(0,n.jsxs)("div",{className:"repo-file-upload-slate",children:[(0,n.jsx)("div",{className:"fgColor-muted",children:(0,n.jsx)(m.FileIcon,{size:32})}),(0,n.jsx)("h2",{"aria-hidden":"true",children:"Drop to upload your files"})]})})})})}try{v.displayName||(v.displayName="Dropzone")}catch{}var w=a(28391),C=a(99921),_=a(45968),k=a(89169);let B={directory:"Directory",submodule:"Submodule",symlink_directory:"Symlink to directory",symlink_file:"Symlink to file"};function T({item:e}){switch(e.contentType){case"directory":return(0,n.jsx)(m.FileDirectoryFillIcon,{className:"icon-directory"});case"submodule":case"symlink_directory":return(0,n.jsx)(m.FileSubmoduleIcon,{className:"icon-directory"});case"symlink_file":return(0,n.jsx)(m.FileSymlinkFileIcon,{className:"icon-directory"});default:return(0,n.jsx)(m.FileIcon,{className:"color-fg-muted"})}}function S({initialFocus:e,item:t,getItemUrl:a,onNavigate:r}){let i=a(t),s=t.hasSimplifiedPath?"This path skips through empty directories":t.name;return"submodule"===t.contentType&&(i=t.submoduleUrl??i,r=e=>{e.preventDefault(),t.submoduleUrl&&(window.location.href=t.submoduleUrl)}),(0,n.jsx)("div",{className:"overflow-hidden",children:(0,n.jsx)("div",{className:"react-directory-filename-cell",children:(0,n.jsx)("div",{className:"react-directory-truncate",children:(0,n.jsx)(w.N,{title:s,"aria-label":`${t.name}, ${function({contentType:e}){return`(${B[e]||"File"})`}(t)}`,className:"symlink_directory"!==t.contentType&&"symlink_file"!==t.contentType?"Link--primary":void 0,"data-react-autofocus":!!e||null,onClick:r,to:i,children:(0,n.jsx)(A,{item:t})})})})})}function A({item:e}){return e.hasSimplifiedPath?(0,n.jsx)(n.Fragment,{children:e.name.split("/").map((e,t,a)=>{let r=t===a.length-1;return(0,n.jsx)("span",{className:r?"":"react-directory-default-color","data-testid":"path-name-segment",children:`${e}${r?"":"/"}`},t)})}):e.submoduleDisplayName?(0,n.jsx)("span",{style:{color:"var(--fgColor-accent, var(--color-accent-fg))"},children:e.submoduleDisplayName}):(0,n.jsx)(n.Fragment,{children:e.name})}function I({commit:e}){return e?e.shortMessageHtmlLink?(0,n.jsx)("div",{children:(0,n.jsx)(_.$6,{className:"react-directory-commit-message",html:e.shortMessageHtmlLink})}):(0,n.jsx)(f.A,{className:"Link--secondary",href:e.url,children:"No commit message"}):(0,n.jsx)(C.r,{})}function L({commit:e}){return e?.date?(0,n.jsx)("div",{className:"react-directory-commit-age",children:Number.isNaN(Date.parse(e.date))?"Invalid date":(0,n.jsx)(k.A,{datetime:e.date,tense:"past"})}):(0,n.jsx)(C.r,{})}try{T.displayName||(T.displayName="IconCell")}catch{}try{S.displayName||(S.displayName="NameCell")}catch{}try{A.displayName||(A.displayName="ItemPathName")}catch{}try{I.displayName||(I.displayName="CommitMessageCell")}catch{}try{L.displayName||(L.displayName="CommitAgeCell")}catch{}let O={Box:"Table-module__Box--h4W6R",Box_1:"Table-module__Box_1--JrPYF",Box_2:"Table-module__Box_2--kJgvd",Box_3:"Table-module__Box_3--SP5mx",Box_4:"Table-module__Box_4--sB2q5"};var R=a(34164);function D({children:e,className:t,...a}){return(0,n.jsx)("table",{className:(0,R.$)(O.Box,t),...a,children:e})}let F=({children:e,className:t})=>(0,n.jsx)("thead",{className:(0,R.$)(t,O.Box_1),children:(0,n.jsx)("tr",{className:O.Box_2,children:e})}),P=({children:e,onClick:t,index:a,id:r})=>(0,n.jsx)("tr",{onClick:t,"data-index":a,id:r,className:O.Box_3,children:e}),E=({children:e})=>(0,n.jsx)("tfoot",{className:O.Box_4,children:e});try{D.displayName||(D.displayName="Table")}catch{}try{F.displayName||(F.displayName="HeaderRow")}catch{}try{P.displayName||(P.displayName="Row")}catch{}try{E.displayName||(E.displayName="TableFooter")}catch{}let H={PrimerLink:"DirectoryRow-module__PrimerLink--Pt1d6",Box:"DirectoryRow-module__Box--NOziH",Octicon:"DirectoryRow-module__Octicon--S5rCK"},$=({className:e,onClickHandler:t,index:a,item:r,initialFocus:i,getItemUrl:s,onNavigate:o,commit:l})=>(0,n.jsxs)("tr",{className:`react-directory-row ${e}`,onClick:t,id:`folder-row-${a}`,children:[(0,n.jsx)("td",{className:"react-directory-row-name-cell-small-screen",colSpan:2,children:(0,n.jsxs)("div",{className:"react-directory-filename-column",children:[(0,n.jsx)(T,{item:r}),(0,n.jsx)(S,{initialFocus:i,item:r,getItemUrl:s,onNavigate:o})]})}),(0,n.jsx)("td",{className:"react-directory-row-name-cell-large-screen",colSpan:1,children:(0,n.jsxs)("div",{className:"react-directory-filename-column",children:[(0,n.jsx)(T,{item:r}),(0,n.jsx)(S,{initialFocus:i,item:r,getItemUrl:s,onNavigate:o})]})}),(0,n.jsx)("td",{className:"react-directory-row-commit-cell",children:(0,n.jsx)(I,{commit:l})}),(0,n.jsx)("td",{children:(0,n.jsx)(L,{commit:l})})]});function M({initialFocus:e,item:t,commit:a,onNavigate:r,getItemUrl:i,navigate:s,index:o,className:l}){let d=p.useCallback(e=>{window.innerWidth<c.Gy.small&&!e.defaultPrevented&&("submodule"===t.contentType?t.submoduleUrl&&s(t.submoduleUrl):s(i(t)))},[t,i,s]);return(0,n.jsx)($,{className:l,onClickHandler:d,index:o,item:t,initialFocus:e,getItemUrl:i,onNavigate:r,commit:a})}let U=p.memo(M);function W({initialFocus:e,linkTo:t,linkRef:a,navigate:r}){let{setFocusHint:s}=(0,y.e)(),{path:o}=(0,i.eu)(),d=p.useCallback(()=>{window.innerWidth<c.Gy.medium&&r(t)},[t,r]);return(0,n.jsx)(P,{onClick:d,id:"folder-row-0",children:(0,n.jsxs)("td",{colSpan:3,className:"f5 text-normal px-3",children:[(0,n.jsx)(l.W,{as:"h3",text:"parent directory"}),(0,n.jsx)(f.A,{"aria-label":"Parent directory","data-react-autofocus":!!e||null,"data-testid":"up-tree",as:w.N,muted:!0,onClick:()=>{s(o)},ref:a,rel:"nofollow",to:t,className:H.PrimerLink,children:(0,n.jsxs)("div",{className:(0,R.$)("width-full",H.Box),children:[(0,n.jsx)(x.A,{icon:m.FileDirectoryFillIcon,size:"small",className:H.Octicon}),".."]})})]})})}try{$.displayName||($.displayName="RowContent")}catch{}try{M.displayName||(M.displayName="WrappedDirectoryRow")}catch{}try{U.displayName||(U.displayName="DirectoryRow")}catch{}try{W.displayName||(W.displayName="GoDirectoryUpRow")}catch{}let V={Table:"DirectoryContent-module__Table--DNJx9",Box:"DirectoryContent-module__Box--J2MQZ",Box_1:"DirectoryContent-module__Box_1--mB8B7",Box_2:"DirectoryContent-module__Box_2--LsXd4",Box_3:"DirectoryContent-module__Box_3--BoinM",Box_4:"DirectoryContent-module__Box_4--EzUVO",Box_5:"DirectoryContent-module__Box_5--r9c8e",OverviewHeaderRow:"DirectoryContent-module__OverviewHeaderRow--W8yGl"};function z({overview:e}){let t=(0,s.t)(),{refInfo:a,path:w}=(0,i.eu)(),{items:C,templateDirectorySuggestionUrl:_,totalCount:k}=(0,r.d)(),{items:B}=function(e,t){let a=e.length>100,[n,r]=(0,p.useState)(a);!function(e,t){let a=(0,p.useCallback)(e,t);(0,p.useEffect)(()=>{let e=null,t=null;return t=requestAnimationFrame(()=>{e=setTimeout(()=>{a(),e=null},0),t=null}),()=>{e&&clearTimeout(e),t&&cancelAnimationFrame(t)}},[a])}(()=>{n&&r(!1)},[n]);let i=n?e.slice(0,100):e;return{truncated:n,items:i}}(C,100),[T,S]=p.useState(!!e),A=k-C.length,{commitInfo:I}=function(){let{refInfo:e,path:t}=(0,i.eu)(),a=(0,s.t)(),[n,r]=(0,p.useState)({loading:!0}),l=(0,N.Xl)(),c=(0,o.IO9)({repo:a,action:"tree-commit-info",commitish:e.name,path:t});return(0,p.useEffect)(()=>{let e=!1;return(async()=>{r({loading:!0});let t=await (0,j.lS)(c);if(!e)try{t.ok?r({commitInfo:await t.json()}):(l({variant:"warning",message:"Failed to load latest commit information."}),r({error:!0}))}catch{r({error:!0})}})(),function(){e=!0}},[l,c]),n}(),L=w.length>1,O=(0,o.ylB)(w),H=(0,o.IO9)({repo:t,action:"tree",commitish:a.name,path:O}),$=(0,o.IO9)({repo:t,commitish:a.name,path:w,action:"upload"}),M=p.useRef(null),{getItemUrl:z}=(0,b.Z)(),q=(0,d.Z)(),{focusHint:K}=(0,y.e)(),[G,J]=p.useState(-1),Q=p.useCallback(e=>{0===e.screenX&&0===e.screenY&&M.current?.focus()},[]),Y=p.useCallback(()=>{S(!1)},[]),X=p.useCallback(e=>{let t;J(e);let a=document.getElementById(`folder-row-${e}`);(t=window.innerWidth<=c.Gy.medium?a?.querySelector(".react-directory-row-name-cell-small-screen"):a?.querySelector(".react-directory-row-name-cell-large-screen"))||(t=a),t?.getElementsByTagName("a")[0]?.focus()},[]);return(0,n.jsxs)("div",{"data-hpc":!0,children:[(0,n.jsx)("button",{hidden:!0,"data-testid":"focus-next-element-button","data-hotkey":"j",onClick:()=>{X(Math.min(G+1,L?B.length:B.length-1))}}),(0,n.jsx)("button",{hidden:!0,"data-testid":"focus-previous-element-button","data-hotkey":"k",onClick:()=>{X(Math.max(G-1,0))}}),(0,n.jsx)(l.W,{as:"h2",text:"Folders and files",id:"folders-and-files"}),(0,n.jsxs)(D,{"aria-labelledby":"folders-and-files",className:V.Table,children:[(0,n.jsxs)(F,{className:(0,R.$)(e&&V.OverviewHeaderRow),children:[(0,n.jsx)("th",{colSpan:2,className:V.Box,children:(0,n.jsx)("span",{className:"text-bold",children:"Name"})}),(0,n.jsx)("th",{colSpan:1,className:V.Box_1,children:(0,n.jsx)("span",{className:"text-bold",children:"Name"})}),(0,n.jsx)("th",{className:"hide-sm",children:(0,n.jsx)(u.A,{inline:!0,title:"Last commit message",className:"width-fit",children:(0,n.jsx)("span",{className:"text-bold",children:"Last commit message"})})}),(0,n.jsx)("th",{colSpan:1,className:V.Box_2,children:(0,n.jsx)(u.A,{inline:!0,title:"Last commit date",className:"width-fit",children:(0,n.jsx)("span",{className:"text-bold",children:"Last commit date"})})})]}),(0,n.jsxs)("tbody",{children:[!!e&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("tr",{className:V.Box_3,children:(0,n.jsx)("td",{colSpan:3,className:"bgColor-muted p-1 rounded-top-2",children:(0,n.jsx)(g.S,{commitCount:e?.commitCount})})}),A>0?(0,n.jsx)("tr",{children:(0,n.jsx)("td",{colSpan:3,children:(0,n.jsxs)(h.A,{variant:"warning",className:"rounded-0",children:[(0,n.jsx)(x.A,{icon:m.AlertIcon}),"Sorry, we had to truncate this directory to 1,000 files. ",A," entries were omitted from the list."]})})}):null]}),L&&(0,n.jsx)(W,{initialFocus:!K||!B.some(e=>e.path===K),linkTo:H,linkRef:M,navigate:q}),B.map((e,t)=>(0,n.jsx)(U,{initialFocus:e.path===K,item:e,commit:(I||{})[e.name],onNavigate:Q,getItemUrl:z,navigate:q,className:T&&t>=10?"truncate-for-mobile":void 0,index:L?t+1:t},e.name)),(0,n.jsx)("tr",{className:(0,R.$)(T&&B.length>10?"show-for-mobile":"d-none",V.Box_4),"data-testid":"view-all-files-row",children:(0,n.jsx)("td",{colSpan:3,onClick:Y,className:V.Box_5,children:(0,n.jsx)("div",{children:(0,n.jsx)(f.A,{as:"button",onClick:Y,children:"View all files"})})})})]}),_&&(0,n.jsx)(E,{children:(0,n.jsx)(P,{children:(0,n.jsxs)("td",{colSpan:3,children:["Customize the issue creation experience with a ",(0,n.jsx)("code",{children:"config.yml"})," file."," ",(0,n.jsx)(f.A,{inline:!0,href:_,children:"Learn more about configuring a template chooser."})]})})})]}),t.currentUserCanPush&&(0,n.jsx)(v,{uploadUrl:$})]})}try{z.displayName||(z.displayName="DirectoryContent")}catch{}},71748:(e,t,a)=>{a.d(t,{e:()=>u});var n=a(74848),r=a(141),i=a(88795),s=a(96235),o=a(38621),l=a(34614),c=a(53110),d=a(18338);let m={SharedMarkdownContent:"DirectoryRichtextContent-module__SharedMarkdownContent--YORdJ"};function u({errorMessage:e,onAnchorClick:t,path:a,richText:u,stickyHeaderHeight:h,timedOut:f}){let x=(0,i.t)(),{refInfo:p}=(0,r.eu)();return e?(0,n.jsxs)("div",{className:"py-6 px-3 text-center",children:[f&&(0,n.jsx)(c.A,{icon:o.HourglassIcon,size:32}),(0,n.jsx)("div",{"data-testid":"directory-richtext-error-message",children:e}),f&&(0,n.jsxs)("div",{children:["But you can view the"," ",(0,n.jsx)(l.A,{inline:!0,href:(0,s.IO9)({repo:x,commitish:p.name,action:"raw",path:a}),"data-testid":"directory-richtext-timeout-raw-link",children:"raw file"}),"."]})]}):u?(0,n.jsx)(d.s,{onAnchorClick:t,richText:u,stickyHeaderHeight:h,className:m.SharedMarkdownContent}):null}try{u.displayName||(u.displayName="DirectoryRichtextContent")}catch{}},51314:(e,t,a)=>{a.d(t,{Xl:()=>l,lG:()=>d,x7:()=>o,yY:()=>c});var n=a(74848),r=a(96540),i=a(85647);let s=(0,r.createContext)({banners:[],addBanner:()=>void 0,addQueuedBanner:()=>void 0});function o(){return(0,r.useContext)(s).banners}function l(){return(0,r.useContext)(s).addBanner}function c(){return(0,r.useContext)(s).addQueuedBanner}function d({children:e}){let t=(0,i.zy)(),[a,o]=(0,r.useState)([]),[l,c]=(0,r.useState)([]),d=(0,r.useCallback)(e=>o(t=>[...t,e]),[]),m=(0,r.useCallback)(e=>c(t=>[...t,e]),[]),u=(0,r.useMemo)(()=>({banners:a,addBanner:d,addQueuedBanner:m}),[d,m,a]);return(0,r.useEffect)(()=>{o(l),c([])},[t.key]),(0,n.jsx)(s.Provider,{value:u,children:e})}try{s.displayName||(s.displayName="CodeViewBannersContext")}catch{}try{d.displayName||(d.displayName="CodeViewBannersProvider")}catch{}},93224:(e,t,a)=>{a.d(t,{X:()=>o,d:()=>s});var n=a(74848),r=a(96540);let i=r.createContext({});function s(){return r.useContext(i)}function o({payload:e,children:t}){return(0,n.jsx)(i.Provider,{value:e,children:t})}try{i.displayName||(i.displayName="TreeContext")}catch{}try{o.displayName||(o.displayName="CurrentTreeProvider")}catch{}},53614:(e,t,a)=>{a.d(t,{e:()=>l,i:()=>o});var n=a(74848),r=a(96679),i=a(96540);let s=i.createContext({focusHint:null,setFocusHint:()=>void 0});function o({children:e}){let t={key:r.fV.pathname+r.fV.search},a=(0,i.useRef)(t.key),o=(0,i.useRef)(t.key),l=(0,i.useRef)({hint:null,location:null}),c=(0,i.useCallback)((e,a)=>{l.current={hint:e,context:a,location:t.key}},[t.key]);o.current!==t.key&&(a.current=o.current,o.current=t.key);let d=l.current.location===a.current,m=d?l.current.hint:null,u=d?l.current.context:null,h=(0,i.useMemo)(()=>({focusHint:m,context:u,setFocusHint:c}),[m,u,c]);return(0,n.jsx)(s.Provider,{value:h,children:e})}function l(){return(0,i.useContext)(s)}try{s.displayName||(s.displayName="FocusHintContext")}catch{}try{o.displayName||(o.displayName="FocusHintContextProvider")}catch{}},39459:(e,t,a)=>{a.d(t,{r:()=>o});var n=a(74848),r=a(96679),i=a(47258),s=a(96540);let o=(0,s.forwardRef)(function({src:e,size:t=20,...a},o){let l=(0,s.useMemo)(()=>{let a=new URL(e,r.fV.origin);return a.searchParams.has("size")||a.searchParams.has("s")||a.searchParams.set("size",String(2*Number(t))),a.toString()},[e,t]);return(0,n.jsx)(i.A,{ref:o,src:l,size:t,"data-testid":"github-avatar",...a})});try{o.displayName||(o.displayName="GitHubAvatar")}catch{}},45968:(e,t,a)=>{a.d(t,{$6:()=>d,JR:()=>l,vb:()=>o});var n=a(74848),r=a(75177),i=a(52464),s=a(96540);let o=m(r.A),l=m(i.A),c=(0,s.forwardRef)((e,t)=>(0,n.jsx)("div",{ref:t,...e}));c.displayName="Div";let d=m(c);function m(e){let t=(0,s.forwardRef)((t,a)=>{let{html:r,...i}=t;return(0,n.jsx)(e,{ref:a,...i,dangerouslySetInnerHTML:r?{__html:r}:void 0})});return t.displayName=`SafeHTML${e.displayName||e.name}`,t}try{o.displayName||(o.displayName="VerifiedHTMLBox")}catch{}try{l.displayName||(l.displayName="VerifiedHTMLText")}catch{}try{d.displayName||(d.displayName="VerifiedHTMLDiv")}catch{}},24208:(e,t,a)=>{a.d(t,{W:()=>s});var n=a(74848),r=a(84217);let i={userSelectNone:"ScreenReaderHeading-module__userSelectNone--vW4Cq"};function s({as:e,text:t,...a}){return(0,n.jsx)(r.A,{as:e,className:`sr-only ${i.userSelectNone}`,"data-testid":"screen-reader-heading",...a,children:t})}try{s.displayName||(s.displayName="ScreenReaderHeading")}catch{}}}]);
//# sourceMappingURL=ui_packages_code-view-shared_hooks_use-canonical-object_ts-ui_packages_code-view-shared_hooks-6097ef-72189442d8b3.js.map