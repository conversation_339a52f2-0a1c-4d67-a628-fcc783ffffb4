"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["notifications-global"],{70396:(e,t,o)=>{o.d(t,{a:()=>s,n:()=>n});var i=o(97797);function n(){let e=document.getElementById("ajax-error-message");e&&(e.hidden=!1)}function s(){let e=document.getElementById("ajax-error-message");e&&(e.hidden=!0)}(0,i.on)("deprecatedAjaxError","[data-remote]",function(e){let{error:t,text:o}=e.detail;e.currentTarget===e.target&&"abort"!==t&&"canceled"!==t&&(/<html/.test(o)?(n(),e.stopImmediatePropagation()):setTimeout(function(){e.defaultPrevented||n()},0))}),(0,i.on)("deprecatedAjaxSend","[data-remote]",function(){s()}),(0,i.on)("click",".js-ajax-error-dismiss",function(){s()})},35111:(e,t,o)=>{var i=o(39595),n=o(70396),s=o(97325),a=o(26559);function l(e,t,o,i){var n,s=arguments.length,a=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var l=e.length-1;l>=0;l--)(n=e[l])&&(a=(s<3?n(a):s>3?n(t,o,a):n(t,o))||a);return s>3&&a&&Object.defineProperty(t,o,a),a}let r=class NotificationsListSubscriptionFormElement extends HTMLElement{async submitCustomForm(e){await this.submitForm(e),this.closeMenu()}async submitForm(e){e.preventDefault(),(0,n.a)();let t=e.currentTarget,o=new FormData(t),i=await self.fetch(t.action,{method:t.method,body:o,headers:{...(0,a.kt)(),Accept:"application/json"}});if(!i.ok)return void(0,n.n)();let s=await i.json(),l=o.get("do");"string"==typeof l&&this.updateCheckedState(l),"string"==typeof l&&this.updateMenuButtonCopy(l),this.updateSocialCount(s.count),this.applyInputsCheckedPropertiesToAttributesForNextFormReset(),this.closeMenu()}updateMenuButtonCopy(e){this.unwatchButtonCopy.hidden="subscribed"!==e&&"custom"!==e,this.stopIgnoringButtonCopy.hidden="ignore"!==e,this.watchButtonCopy.hidden="subscribed"===e||"custom"===e||"ignore"===e}applyInputsCheckedPropertiesToAttributesForNextFormReset(){for(let e of[...this.threadTypeCheckboxes])e.toggleAttribute("checked",e.checked)}updateCheckedState(e){for(let t of this.subscriptionButtons)t.setAttribute("aria-checked",t.value===e?"true":"false");if("custom"===e)this.customButton.setAttribute("aria-checked","true");else{for(let e of(this.customButton.setAttribute("aria-checked","false"),[...this.threadTypeCheckboxes]))(0,s.m$)(e,!1);if(void 0!==this.subscriptionsContainer){for(let e=0;e<this.subscriptionsLabels.length;e++)this.subscriptionsLabels[e].remove();void 0!==this.subscriptionsSubtitle&&this.subscriptionsSubtitle.toggleAttribute("hidden",!1),this.subscriptionsContainer.textContent=""}}}updateSocialCount(e){this.socialCount&&(this.socialCount.textContent=e,this.socialCount.setAttribute("aria-label",`${this.pluralizeUsers(e)} watching this repository`))}pluralizeUsers(e){return 1===parseInt(e)?"1 user is":`${e} users are`}handleDialogLabelToggle(e){let t=e.detail.wasChecked,o=e.detail.toggledLabelId,i=e.detail.templateLabelElementClone;if(t){for(let e=0;e<this.subscriptionsLabels.length;e++)if(this.subscriptionsLabels[e].getAttribute("data-label-id")===o){this.subscriptionsLabels[e].remove();break}}else i.removeAttribute("hidden"),i.setAttribute("data-targets","notifications-list-subscription-form.subscriptionsLabels"),this.subscriptionsContainer.appendChild(i)}openCustomDialog(e){e.preventDefault(),e.stopPropagation(),this.menu.toggleAttribute("hidden",!0),this.enableApplyButtonAndCheckbox(),this.saveCurrentLabelsState(),this.customDialog.toggleAttribute("hidden",!1),setTimeout(()=>{this.customDialog.querySelector("input[type=checkbox][autofocus]")?.focus()},0)}enableApplyButtonAndCheckbox(){this.customDialog.querySelectorAll('[data-type="label"]:not([hidden])').length>0&&(this.customSubmit.removeAttribute("disabled"),this.threadTypeCheckboxes[0].checked=!0)}closeCustomDialog(e){e.preventDefault(),e.stopPropagation(),this.menu.toggleAttribute("hidden",!1),this.customDialog.toggleAttribute("hidden",!0),setTimeout(()=>{this.customButton.focus()},0)}resetFilterLabelsDialog(e){e.preventDefault(),e.stopPropagation();for(let e=0;e<this.subscriptionsLabels.length;e++){let t=this.subscriptionsLabels[e].getAttribute("data-label-id");for(let e=0;e<this.dialogLabelItems.length;e++)if(this.dialogLabelItems[e].labelId===t){this.dialogLabelItems[e].setCheckedForDropdownLabel(!1);break}}for(let e=0;e<Object.keys(this.lastAppliedLabels).length;e++){let t=Object.keys(this.lastAppliedLabels)[e];for(let e=0;e<this.dialogLabelItems.length;e++)if(this.dialogLabelItems[e].labelId===t){this.dialogLabelItems[e].setCheckedForDropdownLabel(!0);break}}this.subscriptionsContainer.replaceChildren(...Object.values(this.lastAppliedLabels)),this.closeFilterLabelsDialog(e)}openFilterLabelsDialog(e){e.preventDefault(),e.stopPropagation(),this.saveCurrentLabelsState(),this.customDialog.toggleAttribute("hidden",!0),this.filterLabelsDialog.toggleAttribute("hidden",!1),setTimeout(()=>{this.filterLabelsDialog.querySelector("input[type=checkbox][autofocus]")?.focus()},0)}closeFilterLabelsDialog(e){e.preventDefault(),e.stopPropagation(),this.menu.toggleAttribute("hidden",!0),this.customDialog.toggleAttribute("hidden",!1),this.filterLabelsDialog.toggleAttribute("hidden",!0)}applyFilterLabelsDialog(e){e.preventDefault(),e.stopPropagation(),this.saveCurrentLabelsState(),this.hideFilterSubtitle(),this.enableIssuesCheckbox(),this.closeFilterLabelsDialog(e)}enableIssuesCheckbox(){let e=Object.keys(this.lastAppliedLabels).length>0;e&&this.threadTypeCheckboxes.length>0&&(this.threadTypeCheckboxes[0].checked=e),this.threadTypeCheckboxesUpdated()}hideFilterSubtitle(){let e=Object.keys(this.lastAppliedLabels).length>0;this.subscriptionsSubtitle.toggleAttribute("hidden",e)}saveCurrentLabelsState(){this.lastAppliedLabels={},this.labelInputs.textContent="";for(let e=0;e<this.subscriptionsLabels.length;e++){let t=this.subscriptionsLabels[e].getAttribute("data-label-id");t&&(this.lastAppliedLabels[t]=this.subscriptionsLabels[e].cloneNode(!0),this.appendLabelToFormInput(t))}}appendLabelToFormInput(e){let t=document.createElement("input");t.setAttribute("type","hidden"),t.setAttribute("name","labels[]"),t.setAttribute("value",e),this.labelInputs.appendChild(t)}detailsToggled(){this.menu.toggleAttribute("hidden",!1),this.customDialog.toggleAttribute("hidden",!0)}submitCustom(e){e.preventDefault(),this.details.toggleAttribute("open",!1)}threadTypeCheckboxesUpdated(){let e=!this.threadTypeCheckboxes.some(e=>e.checked);this.customSubmit.disabled=e}closeMenu(){this.details.toggleAttribute("open",!1)}constructor(...e){super(...e),function(e,t,o){t in e?Object.defineProperty(e,t,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[t]=o}(this,"lastAppliedLabels",{})}};function u(e,t,o,i){var n,s=arguments.length,a=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var l=e.length-1;l>=0;l--)(n=e[l])&&(a=(s<3?n(a):s>3?n(t,o,a):n(t,o))||a);return s>3&&a&&Object.defineProperty(t,o,a),a}l([i.aC],r.prototype,"details",void 0),l([i.aC],r.prototype,"menu",void 0),l([i.aC],r.prototype,"customButton",void 0),l([i.aC],r.prototype,"customDialog",void 0),l([i.aC],r.prototype,"filterLabelsDialog",void 0),l([i.zV],r.prototype,"subscriptionButtons",void 0),l([i.zV],r.prototype,"subscriptionsLabels",void 0),l([i.aC],r.prototype,"labelInputs",void 0),l([i.aC],r.prototype,"subscriptionsSubtitle",void 0),l([i.aC],r.prototype,"socialCount",void 0),l([i.aC],r.prototype,"unwatchButtonCopy",void 0),l([i.aC],r.prototype,"stopIgnoringButtonCopy",void 0),l([i.aC],r.prototype,"watchButtonCopy",void 0),l([i.zV],r.prototype,"threadTypeCheckboxes",void 0),l([i.aC],r.prototype,"customSubmit",void 0),l([i.aC],r.prototype,"subscriptionsContainer",void 0),l([i.zV],r.prototype,"dialogLabelItems",void 0),r=l([i.p_],r);let c=class NotificationsTeamSubscriptionFormElement extends HTMLElement{closeMenu(){this.details.toggleAttribute("open",!1)}};function d(e,t,o,i){var n,s=arguments.length,a=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,o):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(e,t,o,i);else for(var l=e.length-1;l>=0;l--)(n=e[l])&&(a=(s<3?n(a):s>3?n(t,o,a):n(t,o))||a);return s>3&&a&&Object.defineProperty(t,o,a),a}u([i.aC],c.prototype,"details",void 0),c=u([i.p_],c);let NotificationsDialogLabelItemElement=class NotificationsDialogLabelItemElement extends HTMLElement{toggleDropdownLabel(e){if(e.preventDefault(),e.stopPropagation(),this.label){let e="true"===this.label.getAttribute("aria-checked");this.setCheckedForDropdownLabel(!e),this.dispatchEvent(new CustomEvent("notifications-dialog-label-toggled",{detail:{wasChecked:e,toggledLabelId:this.labelId,templateLabelElementClone:this.hiddenLabelTemplate.cloneNode(!0)},bubbles:!0}))}}setCheckedForDropdownLabel(e){this.label.setAttribute("aria-checked",e.toString())}};!function(e,t,o){t in e?Object.defineProperty(e,t,{value:"",enumerable:!0,configurable:!0,writable:!0}):e[t]=""}(NotificationsDialogLabelItemElement,"attrPrefix",""),d([i.aC],NotificationsDialogLabelItemElement.prototype,"label",void 0),d([i.aC],NotificationsDialogLabelItemElement.prototype,"hiddenLabelTemplate",void 0),d([i.aC],NotificationsDialogLabelItemElement.prototype,"hiddenCheckboxInput",void 0),d([i.CF],NotificationsDialogLabelItemElement.prototype,"labelId",void 0),NotificationsDialogLabelItemElement=d([i.p_],NotificationsDialogLabelItemElement)},7799:(e,t,o)=>{let i;function n(){if(!i)throw Error("Client env was requested before it was loaded. This likely means you are attempting to use client env at the module level in SSR, which is not supported. Please move your client env usage into a function.");return i}function s(){return i?.locale??"en-US"}function a(){return!!n().login}o.d(t,{JK:()=>s,M3:()=>a,_$:()=>n});!function(){if("undefined"!=typeof document){let e=document.getElementById("client-env");if(e)try{i=JSON.parse(e.textContent||"")}catch(e){console.error("Error parsing client-env",e)}}}()},53005:(e,t,o)=>{o.d(t,{O:()=>a,S:()=>s});var i=o(96679);let n=i.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",s="X-GitHub-Client-Version";function a(){return n}},27851:(e,t,o)=>{o.d(t,{G7:()=>r,XY:()=>u,fQ:()=>l});var i=o(5225),n=o(7799);function s(){return new Set((0,n._$)().featureFlags)}let a=o(96679).X3||function(){try{return process?.env?.STORYBOOK==="true"}catch{return!1}}()?s:(0,i.A)(s);function l(){return Array.from(a())}function r(e){return a().has(e)}let u={isFeatureEnabled:r}},26559:(e,t,o)=>{o.d(t,{jC:()=>r,kt:()=>a,tV:()=>l});var i=o(53005),n=o(27851),s=o(88191);function a(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,s.wE)(e)};return(0,n.G7)("client_version_header")&&(t={...t,[i.S]:(0,i.O)()}),t}function l(e,t){for(let[o,i]of Object.entries(a(t)))e.set(o,i)}function r(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,o)=>{o.d(t,{$r:()=>a,M1:()=>l,li:()=>n,pS:()=>u,wE:()=>r});var i=o(96679);let n="X-Fetch-Nonce",s=new Set;function a(e){s.add(e)}function l(){return s.values().next().value||""}function r(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[n]=l():s.has(e)?t[n]=e:t[n]=Array.from(s).join(","),t}function u(){let e=i.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&a(e)}},97325:(e,t,o)=>{o.d(t,{Cy:()=>l,K3:()=>c,Z8:()=>r,k_:()=>s,lK:()=>d,m$:()=>a});var i=o(94982);function n(e,t,o){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:o}))}function s(e,t){t&&(function(e,t){if(!(e instanceof HTMLFormElement))throw TypeError("The specified element is not of type HTMLFormElement.");if(!(t instanceof HTMLElement))throw TypeError("The specified element is not of type HTMLElement.");if("submit"!==t.type)throw TypeError("The specified element is not a submit button.");if(!e||e!==t.form)throw Error("The specified element is not owned by the form element.")}(e,t),(0,i.A)(t)),n(e,"submit",!0)&&e.submit()}function a(e,t){if("boolean"==typeof t)if(e instanceof HTMLInputElement)e.checked=t;else throw TypeError("only checkboxes can be set to boolean value");else if("checkbox"===e.type)throw TypeError("checkbox can't be set to string value");else e.value=t;n(e,"change",!1)}function l(e,t){for(let o in t){let i=t[o],n=e.elements.namedItem(o);n instanceof HTMLInputElement?n.value=i:n instanceof HTMLTextAreaElement&&(n.value=i)}}function r(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),o=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==o&&"reset"!==o||e.isContentEditable}function u(e){return new URLSearchParams(e)}function c(e,t){let o=new URLSearchParams(e.search);for(let[e,i]of u(t))o.append(e,i);return o.toString()}function d(e){return u(new FormData(e)).toString()}},94982:(e,t,o)=>{function i(e){let t=e.closest("form");if(!(t instanceof HTMLFormElement))return;let o=n(t);if(e.name){let i=e.matches("input[type=submit]")?"Submit":"",n=e.value||i;o||((o=document.createElement("input")).type="hidden",o.classList.add("js-submit-button-value"),t.prepend(o)),o.name=e.name,o.value=n}else o&&o.remove()}function n(e){let t=e.querySelector("input.js-submit-button-value");return t instanceof HTMLInputElement?t:null}o.d(t,{A:()=>i,C:()=>n})},96679:(e,t,o)=>{o.d(t,{KJ:()=>i.KJ,Kn:()=>n.Kn,X3:()=>i.X3,XC:()=>n.XC,cg:()=>n.cg,fV:()=>n.fV,g5:()=>i.g5});var i=o(28583),n=o(46570)},46570:(e,t,o)=>{o.d(t,{Kn:()=>a,XC:()=>n,cg:()=>s,fV:()=>l});let i="undefined"!=typeof FORCE_SERVER_ENV&&FORCE_SERVER_ENV,n="undefined"==typeof document||i?void 0:document,s="undefined"==typeof window||i?void 0:window,a="undefined"==typeof history||i?void 0:history,l="undefined"==typeof location||i?{pathname:"",origin:"",search:"",hash:"",href:""}:location},28583:(e,t,o)=>{o.d(t,{KJ:()=>s,X3:()=>n,g5:()=>a});var i=o(46570);let n=void 0===i.XC,s=!n;function a(){return!!n||!i.XC||!!(i.XC.querySelector('react-app[data-ssr="true"]')||i.XC.querySelector('react-partial[data-ssr="true"][partial-name="repos-overview"]'))}}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-ea8eaa"],()=>t(35111)),e.O()}]);
//# sourceMappingURL=notifications-global-ac2d8805d636.js.map