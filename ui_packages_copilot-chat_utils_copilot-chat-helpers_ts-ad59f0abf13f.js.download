"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_copilot-chat_utils_copilot-chat-helpers_ts"],{3032:(e,t,o)=>{o.d(t,{L:()=>n,N:()=>a});let i=[{type:"task",context:"pull-request",persona:"author",iconName:"NoteIcon",name:"Proof read this pull request",prompt:`Use the following info about this pull request:
- File changes
- Pull request description

And then provide answers to the following questions in separate paragraphs:
- A concise representation and reasoning of the file changes.
- Whether the pull request description reflects the purpose of the file changes. Please suggest possible improvements and present them with examples.
- Whether there is any complexities or notable efforts, such as handling edge cases or reducing technical debt.

Additional guidance:
- Provide a heading for each paragraph, formatted in bold.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"pull-request",persona:"reviewer",iconName:"NoteIcon",name:"Highlight focus areas for review in this pull request",prompt:`Use the following info about this pull request:
- File changes
- Pull request description
- Pull request reviews
- Pull request code comments
- Pull request issue comments

And then provide answers to the following questions in separate paragraphs:
- Provide insights on the scope and purpose of this pull request.
- Analyze the file changes and suggest risk areas that reviewers should focus on.
- Analyze the existing reviews and comments. Please highlight conversations that reviewers should pay attention to along with their links.

Additional Guidance:
- Provide a heading for each paragraph, formatted in bold.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"pull-request",persona:"other",iconName:"NoteIcon",name:"Explain this pull request",prompt:`Use the following info about this pull request:
- File changes
- Pull request description
- Pull request reviews
- Pull request code comments
- Pull request issue comments

And then provide answers to the following questions in separate paragraphs:
- Provide a high-level overview of the purpose of this pull request.
- Summarize the impact and potential risks for the file changes of this pull request.
- Provide a high-leverl overview on all coversations in this pull request.
- Indicate whether this pull request has been approved.

Additional Guidance:
- Provide a heading for each paragraph, formatted in bold.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"pull-request",persona:"author",iconName:"NoteIcon",name:"Catch me up on the reviews",prompt:`Use the following info about this pull request:
- Pull request description
- Pull request reviews
- Pull request code comments
- Pull request issue comments
- Pull request commits

Context:
- My latest commit refers to the most recent commit from me in this pull request.

1. First state my latest commit in this pull request use this format: "Your last commit was pushed at [timestamp]."
2. Ignore reviews and comments made earlier than the timestamp of my latest commit. Based on the remaining reviews and comments from other people in this pull request, provide answers to the following questions (use separate paragraphs with bold headings):
  - **Pull Request Status**: indicate if this pull request is approved or merged. If not merged, state whether anyone has reviewed or commented in this pull request after my latest commit.
  - **New Comments**: summarize the comments made by other people after my latest commit. Please highlight the ones that require further code changes or follow-ups.

Additional guidance:
- Output timestamp in a human-readable format.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"pull-request",persona:"reviewer",iconName:"NoteIcon",name:"Catch me up on the changes",prompt:`Use the following info about this pull request:
- File changes
- Pull request description
- Pull request reviews
- Pull request code comments
- Pull request issue comments
- Pull request commits

Context:
- My latest activity refers to the most recent review or comment from me in this pull request.

If I have not left any reviews or comments in this pull request:
1. First state that I have not reviewed this pull request.
2. Based on the commits, reviews, or comments in this pull request, provide answers to the following questions (use separate paragraphs with bold headings):
  - **Pull Request Status**: indicate if this pull request is approved or merged. If not merged, state whether anyone has reviewed or commented in this pull request.
  - **Code Changes**: Analyze the file changes and suggest risk areas that reviewers should focus on.
  - **Key Comments**: Analyze the existing reviews and comments. Please highlight conversations that reviewers should pay attention to along with their links.

If I have left reviews or comments in this pull request:
1. First state my latest activity in this pull request use this format: "Your last [review or comment] was posted at [timestamp]."
2. Ignore commits, reviews, and comments made earlier than the timestamp of my latest activity. Based on the remaining commits, reviews, and comments from other people in this pull request, provide answers to the following questions (use separate paragraphs with bold headings):
  - **Pull Request Status**: indicate if this pull request is approved or merged. If not merged, state whether anyone has committed, reviewed or commented in this pull request after my latest activity.
  - **New Code Changes**: summarize the new code changes from the commits made after my latest activity.
  - **New Comments**: summarize the new comments made by other people after my latest activity and highlight the ones I should pay attention to (with their links).

Additional guidance:
- Output timestamp in a human-readable format.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"pull-request",persona:"author",iconName:"FileCodeIcon",name:"Analyze build failures",prompt:`Use the following info about this pull request:
- File changes
- Pull request actions job logs

If all jobs passed successfully, stop there and report that there are no job errors.

Otherwise, provide answers to the following questions in separate paragraphs:
- Analyze the job failures with snippet of error logs. Please call out the type of failures (e.g. test failures, linting errors, compiler errors etc.).
- Suggest possible fixes for the job failures with examples. Please call out whether the failure is due to file changes from this pull request.

Additional guidance:
- Provide a heading for each paragraph, formatted in bold.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`,intent:o(99377).wh.actionsAgent}],r=[{type:"task",context:"repository",iconName:"RepoIcon",name:"Tell me about this repository",prompt:`Please provide answers to the following questions in separate paragraphs:
- Provide insights on the purpose of this repository. Please also provide a summary of its README if it exists.
- Provide detailed analysis on features and technologies used in this repository if it contains implementation of software system of any kind. Otherwise, provide an analysis on contents of this repository instead.

Additional guidance:
- Provide a heading for each paragraph, formatted in bold.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"repository",iconName:"RepoIcon",name:"How to get started with this repository",prompt:`Please provide answers to the following questions in separate paragraphs:
- Provide the step by step snippet of installation and usage sections from this repository if they are mentioned in its README. Otherwise, skip this question.
- Provide summary of contribution guidelines of this repository to help new contributors to get started. Make sure to include links to related articles.
- Provide a link to issues good for new contributors.
- Provide list of contributors and maintainers for new contributors to reach out to.

Additional guidance:
- Provide a heading for each paragraph, formatted in bold.
- Do not output the paragrah in final response if the question is skipped or ignored.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"repository",iconName:"RepoIcon",name:"Summarize activity for this repository in the last day",prompt:`Summarize all recent activity in this repository. "Recent activity" refers to the timeframe immediately preceding today's date. Use the GitHub global search API, filtered by 'repo:owner/repository' to return the following lists

- "Shipped" - PRs merged on or after yesterday's date
- "Pushed" - PRs pushed on or after yesterday's date, which are still open
- "Tracked" - Issues updated on or after yesterday's date

For each of these categories, produce a simple bulleted list, with a one-line description that includes the item number, along with the item title as a link. Each category header should include the total number of items, and a link to the complete results in the GitHub UI.`}],s=[{type:"task",context:"global",iconName:"GlobeIcon",name:"Help me get started with Copilot",prompt:`What can I do with GitHub Copilot Chat Assistant in github.com? To help me get started, please provide prompt suggestions and their expected output.

Additional guidance:
- Provide a heading for each paragraph, formatted in bold.
- Keep your analysis succinct and conversational.
- Do not repeat or list the context details in your final response.
- Provide a concise synthesis of the information, avoiding unnecessary repetition.`},{type:"task",context:"global",iconName:"GlobeIcon",name:"Summarize my activity in the last week",prompt:`Summarize my recent GitHub activity across all repositories. "Recent activity" refers to the timeframe immediately preceding today's date. Use the GitHub API to return the following lists

- "Pushed" - PRs created in the last 7 days, where I am the author, which are still open
- "Shipped" - PRs merged in the last 7 days, where I am the author
- "Tracked" - Issues created in the last 7 days, where I am the author
- "Contributed" - Issues where I have commented in the last 7 days
- "Reviewed" - PRs where I have commented or reviewed in the last 7 days

For each of these categories, produce a simple bulleted list, with a one-line description that includes the owner, repository, and item number in the format 'owner/repo#1234', along with the item title as a link. Each category header should include the total number of items, and a link to the complete results in the GitHub UI.`}],n={"pull-request":[...i,...r,...s],repository:[...r,...s],global:[...s]};function a(e,t){return void 0===e?[]:(n[e]||[]).filter(t=>t.context===e).filter(e=>void 0===t||!("persona"in e)||void 0===e.persona||e.persona===t).map(e=>({question:e.name,prompt:e.prompt,intent:e.intent,mode:"task-oriented-assistive"}))}},22069:(e,t,o)=>{let i;o.d(t,{CB:()=>P,DW:()=>q,F2:()=>Z,FF:()=>b,JP:()=>A,Jz:()=>c,MB:()=>f,NJ:()=>M,OC:()=>T,Od:()=>O,P:()=>L,Q7:()=>X,QT:()=>d,Qt:()=>z,SI:()=>J,Sm:()=>H,Th:()=>Y,UU:()=>F,VR:()=>p,Vb:()=>$,Vc:()=>U,Xo:()=>D,Xs:()=>C,Y6:()=>k,Ye:()=>ee,Z6:()=>j,Z8:()=>R,Zr:()=>m,Zs:()=>h,aX:()=>w,fG:()=>_,hV:()=>ei,k4:()=>eo,lG:()=>K,lj:()=>E,mF:()=>y,mx:()=>et,nN:()=>v,p:()=>I,pF:()=>g,qS:()=>W,rT:()=>x,x_:()=>V,yR:()=>B,yh:()=>Q,z$:()=>G});var r=o(27851);o(66871);var s=o(96235),n=o(3032),a=o(31971),l=o(39226),u=o(33449);let c="/copilot",p=`${c}/spaces`,d=`${p}/new`,h="https://github.com/features/copilot#pricing",m="https://github.com/github-copilot/pro",g="https://github.com/settings/billing/budgets",f="https://github.com/settings/billing",_=50,y=20,w=5,v={400:"This message could not be processed.",401:"You\u2019re not authorized to use Copilot.",403:"Access denied. You do not have permission to view this.",404:"Resource not found. Please try again.",408:"Your network connection was interrupted. Please try again.",413:"Message too large. Please shorten it or remove some references and try again.",429:"GitHub API rate limit exceeded. Please wait and try again."},b={extensionForbidden:"Copilot extensions can\u2019t be used in shared conversations. Please unshare the conversation and try again."},q="I'm sorry but there was an error. Please try again.";async function I({authToken:e,basePath:t,body:o,integrationId:i,method:r,path:s,streamingResponse:n=!1,realIp:a,signal:u,apiVersion:c}){try{let p={Authorization:e.authorizationHeaderValue};for(let e of(i&&(p["copilot-integration-id"]=i),(0,l.f)())){let t=e.split("="),o=t[0]?.replaceAll("_","-"),i="1";t.length>1&&(i=t[1]),p[`X-Experiment-${o}`]=i}c&&(p["X-GitHub-Api-Version"]=c),n&&(p["Content-Type"]="text/event-stream"),a&&(p["X-Real-IP"]=a);let d=await fetch(t+s,{method:r,mode:"cors",cache:"no-cache",headers:p,body:JSON.stringify(o),signal:u});if(d.ok)return d;return{status:d.status,ok:!1,error:v[d.status]||q,response:d}}catch{return{status:500,ok:!1,error:q}}}function k({role:e,content:t,mediaContent:o,error:i,references:r=[],thread:s,confirmationResponses:n,clientSkillConfirmations:a,clientToolResults:l,parentMessageID:u,skillOptions:c}){return{id:crypto.randomUUID(),threadID:s?.id||"temp",role:e,content:t,mediaContent:o,createdAt:new Date().toISOString(),error:i,references:r,skillExecutions:[],clientConfirmations:n,clientToolResults:l,confirmations:a,parentMessageID:u,clientSide:!0,skillOptions:c}}function P(e){return e?.name||"New conversation"}function G(e){switch(e.type){case"figma":return e.title||e.url;case"file":case"folder":return N(e);case"file-diff":var t,o,i=e;let r=i.headFile?.path??i.baseFile?.path,s=r?.split("/").pop()??"";return i.selectedRange&&i.selectedRange.start?i.selectedRange.end&&i.selectedRange.start!==i.selectedRange.end?`${s} ${i.selectedRange.start}-${i.selectedRange.end}`:`${s} ${i.selectedRange.start}`:s;case"snippet":var n=e;if(n.title)return n.title;let a=n.path.split("/").pop(),l=`${n.range.start}-${n.range.end}`;return`${a}:${l}`;case"repository":return t=e,`${t.ownerLogin}/${t.name}`;case"symbol":case"docset":case"image":case"thread-scoped-file":return e.name;case"commit":return e.message;case"pull-request":case"draft-issue":case"web-search-result":case"loading":return e.title;case"tree-comparison":return o=e,`${o.baseRevision.substring(0,5)}..${o.headRevision.substring(0,5)}`;case"third-party":return e.displayName;case"workspace-terminal-log":return"terminal log";case"text":return e.name?e.name:"text reference";case"repo-instructions":return"copilot-instructions.md";case"issue":case"discussion":return e?.title||e.number.toString();case"org-instructions":return e?.owner;default:return"unrecognized reference"}}function $(e){switch(e.type){case"figma":case"web-search-result":return`${e.type}-${e.url}`;case"file":return`${e.type}-${e.repoOwner}/${e.repoName}@${e.commitOID}:${e.path}`;case"folder":return`${e.type}-${e.repoOwner}/${e.repoName}@${e.ref}:${e.path}`;case"file-changes":return`${e.type}-${e.repository.owner}/${e.repository.owner}@${e.ref}:${e.path}`;case"file-diff":return`${e.type}:${e.baseFile?.path}@${e.baseFile?.commitOID}-${e.headFile?.path}@${e.headFile?.commitOID}##${e.selectedRange?.start}-${e.selectedRange?.end}`;case"snippet":return`${e.type}-${e.repoOwner}/${e.repoName}@${e.commitOID}:${e.path}#${e.range.start}-${e.range.end}`;case"repository":return`${e.type}-${e.id}-${e.ownerLogin}/${e.name}`;case"symbol":return`${e.type}-${e.kind}-${e.name}`;case"docset":case"text":case"thread-scoped-file":return`${e.type}-${e.name}`;case"commit":return`${e.type}-@${e.oid}-${e.repository.owner}/${e.repository.name}`;case"pull-request":return`${e.type}-${e.id}-${e.repository.ownerLogin}/${e.repository.name}`;case"web-search":return`${e.type}-${e.query}`;case"workspace-terminal-log":return`${e.type}-${e.repoOwner}/${e.repoName}@${e.pullRequestID}`;case"repo-instructions":return`${e.type}-.github/custom-instructions.md`;case"image":return`${e.type}-${e.id}/${e.name}`;case"issue":return`${e.repository.owner}/${e.repository.name}#${e.number}`;case"draft-issue":return`${e.type}-${e.tag}`;case"loading":return`${e.type}-${e.id}`;default:return""}}let S=()=>i??(i=new Set(["commit","discussion","figma","file","file-v2","file-diff","folder","issue",...a.W.draftIssueUI?["draft-issue"]:[],"pull-request","org-instructions","repo-instructions","snippet","symbol","third-party","web-search","web-search-result","image","thread-scoped-file","loading","repository","docset"]));function x(e){let t=[];for(let o of e??[])if(S().has(o.type)){if("web-search"===o.type){for(let e of o.results){let o={...e,type:"web-search-result"};t.push(o)}continue}t.push(o)}return t}function A(e){switch(e.type){case"repository":return`/${e.ownerLogin}/${e.name}`;case"commit":return e.permalink;case"third-party":return e.displayUrl;case"snippet":if(("file"===e.type||"snippet"===e.type)&&e.languageName&&"markdown"===e.languageName.toLowerCase()){let t=new URL(e.url,window.location.origin);return t.search="plain=1",t.href}return e.url;case"issue":case"figma":case"file":case"file-diff":case"folder":case"pull-request":case"repo-instructions":case"org-instructions":case"web-search-result":return e.url;default:return"#"}}function C(e){return"#"!==e&&""!==e}function R(e){let t=e.path.split("/");return(t.pop(),0===t.length)?"/":t.join("/")}function D(e){return N(e)}function N(e){return e.path.split("/").pop()||e.path}function O(e,t){return{type:"file",url:new URL((0,s.IO9)({repo:t,commitish:t.refInfo.name,action:"blob",path:e}),window.location.origin).href,path:e,repoID:t.id,repoOwner:t.ownerLogin,repoName:t.name,ref:t.ref,commitOID:t.commitOID}}function U(e){return{id:crypto.randomUUID(),title:e,type:"loading",isClientOnly:!0}}function T(e,t){return{type:"folder",url:new URL((0,s.IO9)({repo:t,commitish:t.refInfo.name,action:"tree",path:e}),window.location.origin).href,path:e,repoID:t.id,repoOwner:t.ownerLogin,repoName:t.name,ref:t.ref}}function H(e){return e.symbol?.fully_qualified_name||""}function z(e,t){let o=H(e);return{type:"symbol",kind:"suggestionSymbol",name:o,suggestionDefinitions:[{identOffset:{start:e.symbol?.ident_start||0,end:e.symbol?.ident_end||0},extentOffset:{start:e.symbol?.extent_start||0,end:e.symbol?.extent_end||0},kind:e.symbol?.kind||"",fullyQualifiedName:o,repoID:t.id,repoOwner:t.ownerLogin,repoName:t.name,ref:e.commit_sha,commitOID:e.commit_sha,path:e.path}],languageID:e.language_id}}function F(e){return{...e,type:"docset"}}function W(e){return{...e,type:"repository"}}function E(e){return{owner:`${e} Instructions`,type:"org-instructions",url:"https://docs.github.com/en/copilot/customizing-copilot/adding-organization-custom-instructions-for-github-copilot"}}function L(e){return!!e&&"sourceRepos"in e}function j(e){return!!e&&!L(e)}function M(e){return e.reduce((e,t)=>("customInstructions"in t&&Array.isArray(t.customInstructions)&&e.push(...t.customInstructions),e),[])}function K(e,t){return M(t).length>0}function V(e,t){return e===t||void 0!==e&&void 0!==t&&$(e)===$(t)}function B(e,t){if(void 0===e&&void 0===t)return!0;if(void 0===e||void 0===t||e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(!V(e[o],t[o]))return!1;return!0}function J(e){return"file"===e.type}function Y(e,t){if("user"===e.role)return{name:t,avatarURL:`/${t}.png`,type:"user"};if(e.references){let t=e.references.find(e=>"github.agent"===e.type);if(t)return{name:t.login,avatarURL:t.avatarURL,type:"agent"}}return{name:"Copilot",avatarURL:"",type:"copilot"}}function Q(e){return"agent"===e.type}function X(e){let t=e.map(e=>Y(e,"")).filter(e=>e?.type==="agent"),o=new Map;for(let e of t)o.set(e.name,e);return Array.from(o.values())}function Z(e){let t=new Date(e.updatedAt).getTime();return Date.now()-t>144e5}function ee(e){if(!e)return[];let t=new Set;return e.filter(e=>{let o=JSON.stringify(e.confirmation);return!t.has(o)&&(t.add(o),!0)})}function et(e){let t="type"in e&&"string"==typeof e.type?e.type:"repository",o=t?t.replace(/(\.api|-v2)$/,"").toLowerCase():"default";if(!(0,r.G7)("copilot_task_oriented_assistive_prompts"))return{referenceType:t,suggestions:eo(u.y$[o]||u.y$.default).slice(0,3)};{let i="persona"in e&&"string"==typeof e.persona?e.persona:void 0,r=(0,n.N)(o,i),s=(0,n.N)("global"),a=r.length?r:u.y$[o];return a||(a=[...s,...s.length<3?eo(u.y$.default).slice(0,3-s.length):[]]),{referenceType:t,suggestions:eo(a).slice(0,3)}}}function eo(e){let t=e.slice();for(let e=t.length-1;e>0;e--){let o=Math.floor(Math.random()*(e+1)),i=t[e];t[e]=t[o],t[o]=i}return t}function ei(e,t){let o=t.split(" "),i=`${e} said`;return"Timeline"===e&&(i="The following action was initiated"),`${i}: ${o.slice(0,o.length<7?o.length:7).join(" ")}`}},31971:(e,t,o)=>{o.d(t,{W:()=>r});var i=o(27851);let r=new class CopilotFeatureFlags{get immersiveFigmaIntegration(){return(0,i.G7)("copilot_immersive_figma_integration")}get draftIssueTemplateFormUI(){return(0,i.G7)("copilot_immersive_draft_issue_template_form_ui")}get draftIssueUI(){return!(0,i.G7)("copilot_immersive_disable_draft_issue_ui")}get draftIssueTree(){return(0,i.G7)("copilot_immersive_draft_issue_tree")}get forceLegacyChatDefaultModel(){return(0,i.G7)("copilot_api_force_legacy_base_chat_model")}get domPageContext(){return(0,i.G7)("copilot_use_dom_page_context")}get immersiveServiceNavigation(){return this.customCopilots||this.loopsPlugin||this.workbenchPlugin}get copilotChatInterviewSurvey(){return(0,i.G7)("copilot_chat_interview_survey")}get newThreadNavigate(){return(0,i.G7)("copilot_immersive_new_thread_navigate")}get newImmersiveReferencesUI(){return(0,i.G7)("copilot_new_immersive_references_ui")}get attachImagesImmersive(){return(0,i.G7)("copilot_chat_attach_images")}get attachMultipleImages(){return(0,i.G7)("copilot_chat_attach_multiple_images")}get wholeAreaDragDrop(){return(0,i.G7)("copilot_chat_wholearea_dd")}get customCopilots(){return(0,i.G7)("copilot_custom_copilots")}get immersiveStructuredModelPicker(){return(0,i.G7)("copilot_immersive_structured_model_picker")}get spacesV3UI(){return(0,i.G7)("copilot_spaces_v3_ui")}get customCopilotsFeaturePreview(){return(0,i.G7)("copilot_custom_copilots_feature_preview")}get customCopilot128kWindow(){return(0,i.G7)("custom_copilots_128k_window")}get clientDOMSkills(){return(0,i.G7)("copilot_client_dom_skills")}get dotcomChatClientSideSkills(){return(0,i.G7)("dotcom_chat_client_side_skills")}get taskOrientedAssistive(){return(0,i.G7)("copilot_task_oriented_assistive")}get loopsPlugin(){return(0,i.G7)("copilot_pipes")}get workbenchPlugin(){return(0,i.G7)("copilot_workbench")}get workbenchVMAgentAttachments(){return(0,i.G7)("copilot_workbench_vm_agent_attachments")}get copilotChatOpeningThreadSwitch(){return(0,i.G7)("copilot_chat_opening_thread_switch")}get copilotReadSharedConversation(){return(0,i.G7)("copilot_read_shared_conversation")}get copilotDuplicateThread(){return(0,i.G7)("copilot_duplicate_thread")}get copilotShareActiveSubthread(){return(0,i.G7)("copilot_share_active_subthread")}get dotcomChatFileUpload(){return(0,i.G7)("copilot_dotcom_chat_file_upload")&&(0,i.G7)("copilot_chat_attachments")}get copilotChatO1Tools(){return(0,i.G7)("copilot_api_tools_for_non_streaming_models")}get bingGroundingServiceEnabled(){return(0,i.G7)("copilot_bing_search_use_azure_ai_agent_service")}get bingGroundingUIEnabled(){return(0,i.G7)("copilot_bing_search_use_grounding_ui")&&this.bingGroundingServiceEnabled}get showDeepCodeSearchButton(){return(0,i.G7)("copilot_show_deep_code_search_button")&&(0,i.G7)("copilot_api_search_agent_skill")}get deleteAllConversations(){return(0,i.G7)("copilot_delete_all_conversations")}get staffPromptDialog(){return(0,i.G7)("copilot_staff_prompt_dialog")}get workbenchTerminal(){return(0,i.G7)("copilot_workbench_terminal")}get workbenchPreviewAnalytics(){return(0,i.G7)("copilot_workbench_preview_analytics")}get workbenchRefreshOnWsod(){return(0,i.G7)("copilot_workbench_refresh_on_wsod")}get workbenchShowConnectionReloadBanner(){return(0,i.G7)("copilot_workbench_connection_reload_banner")}get workbenchIteratePanel(){return(0,i.G7)("copilot_workbench_iterate_panel")}get visionAllowedInClaude(){return(0,i.G7)("copilot_chat_vision_in_claude")}get visionSkipThreadCreate(){return(0,i.G7)("copilot_chat_vision_skip_thread_create")}get premiumRequestQuotasEnabled(){return(0,i.G7)("copilot_premium_request_quotas")}get freeToPaidTelemetry(){return(0,i.G7)("copilot_free_to_paid_telem")}get freeToPaidSettingsUpgrade(){return(0,i.G7)("copilot_ftp_settings_upgrade")}get singleUserIteration(){return(0,i.G7)("copilot_spark_single_user_iteration")}get freeToPaidYourCopilotSettings(){return(0,i.G7)("copilot_ftp_your_copilot_settings")}get freeToPaidUpgradeToProFromModels(){return(0,i.G7)("copilot_ftp_upgrade_to_pro_from_models")}get commitOnDefaultBranch(){return(0,i.G7)("spark_commit_on_default_branch")}get spacesIssuesPrsEnabled(){return(0,i.G7)("custom_copilots_issues_prs")}get customCopilotsFileUploads(){return(0,i.G7)("custom_copilots_file_uploads")}get spacesUploadedTextFileDownloadLinks(){return(0,i.G7)("copilot_spaces_uploaded_text_file_download_links")}get spacesImagesEnabled(){return(0,i.G7)("copilot_custom_copilots_images")}get spacesForkSelection(){return(0,i.G7)("copilot_spaces_support_forks")}get copilotPersistEditedDraftIssues(){return(0,i.G7)("copilot_immersive_draft_issue_persist_edited_issues_in_session")}get agentSessionsDirectCreationEnabled(){return(0,i.G7)("copilot_immersive_agent_sessions_direct_creation")}get sparkUseStreaming(){return(0,i.G7)("copilot_spark_use_streaming")}get sparkUseBillingHeaders(){return(0,i.G7)("copilot_spark_use_billing_headers")}get workbenchDefaultSonnet4(){return(0,i.G7)("workbench_default_sonnet4")}get sparkAllowEmptyCommit(){return(0,i.G7)("copilot_spark_allow_empty_commit")}get copilotMcp(){return(0,i.G7)("copilot_mcp")}get disableModelPickerWhileStreaming(){return(0,i.G7)("copilot_chat_disable_model_picker_while_streaming")}get sparkSyncRepositoryAfterIteration(){return(0,i.G7)("spark_sync_repository_after_iteration")}get sparkUseSeparateSuggestionArgs(){return(0,i.G7)("copilot_spark_use_separate_suggestion_args")}get groupNotifications(){return(0,i.G7)("copilot_chat_group_notifications")}get freeToPaidHyperspaceUpgradePrompt(){return(0,i.G7)("copilot_ftp_hyperspace_upgrade_prompt")}get reduceChatQuotaChecks(){return(0,i.G7)("copilot_chat_reduce_quota_checks")}get kbSemanticApiMigration(){return(0,i.G7)("kb_semantic_api_migration")}get immersiveAppShell(){return(0,i.G7)("copilot_chat_no_header")}get spacesInputMenuSelect(){return(0,i.G7)("copilot_spaces_input_menu_select")}get agentsViewV2(){return(0,i.G7)("copilot_agents_view_v2")}}},39226:(e,t,o)=>{o.d(t,{R:()=>n,f:()=>s});var i=o(85351);let r="copilot_experiments";function s(){let e=(0,i.A)("localStorage").getItem(r);return e?e.split(","):[]}function n(e){(0,i.A)("localStorage").setItem(r,e.join(","))}},33449:(e,t,o)=>{o.d(t,{Pe:()=>s,oo:()=>r,y$:()=>n});var i=o(99377);function r(e){let t=null!=e?`failing job ${e}`:"this failing job";return`Please find a solution for ${t}. Use the logs, job definition, and any referenced files where the failure occurred. Keep your response focused on the solution and include code suggestions when appropriate.`}let s={"Languages & frameworks":["Show me Python beginner projects.","Explain Java's garbage collection.","Start me off with Node.js.","Introduce me to Django best practices."],"Tools & environments":["Set up a local development environment.","Demonstrate the basics of Docker.","Get me started with Git.","Recommend popular VS Code extensions."],"Open source & contribution":["Suggest 10 open source projects I can contribute to.","Walk me through the GitHub Pull Request flow.","How do I start my own open source project?","Guide me through contributing to React's codebase."],"Best practices & concepts":["Explain the SOLID principles of object-oriented design.","Introduce me to test-driven development.","Describe common design patterns.","Teach me about RESTful API design."],"Web development":["Guide me through creating a basic website.","Introduce HTML5 and CSS3 features.","Explain responsive web design.","Start me off with Tailwind CSS."],"Databases & data":["Get me started with SQL queries.","Recommend popular NoSQL databases.","How to back up a database?","Give a walkthrough on normalizing a database."],"Algorithms & data structures":["Teach me basic sorting algorithms.","Explain binary search trees.","Introduce me to graph algorithms.","What is a hash table?"],"Security & authentication":["Give a guide on basic web security.","Show me how to set up OAuth.","What's a JSON Web Token?","Describe common encryption techniques."],"Mobile development":["Kickstart my journey with Android development.","Introduce me to iOS app basics.","Recommend cross-platform mobile frameworks.","Give a guide to the app store submission process."],"Cloud & DevOps":["Start me off with AWS basics.","How do I deploy apps on Azure DevOps?","Introduce me to Kubernetes.","What are the basics of continuous integration/continuous deployment?"],"Frontend frameworks & libraries":["Get me started with React.","Walk me through Vue.js essentials.","What are some best practices in Angular development?","How do I use Svelte for web apps?"],"Performance & optimization":["Teach me about website performance optimization.","Explain database indexing benefits.","What are some tips to optimize JavaScript code?","Give a guide to efficient API caching."]},n={repository:[{question:"What questions can I ask?"},{question:"What does this repository do?"},{question:"How should I get started exploring this repo?"},{question:"Can you tell me about this repository?"}],issue:[{question:"Summarize this issue."},{question:"What are the main points being discussed here?"},{question:"Suggest next steps for this issue."}],alert:[{question:"Summarize this alert."}],file:[{question:"Explain this file."},{question:"Summarize this file for me."},{question:"How can I improve this file?"}],"pull-request":[{question:"Summarize this pull request."},{question:"What commits are included in this PR?"},{question:"Can you tell me about the changes in this PR?"}],discussion:[{question:"Summarize this discussion."},{question:"Summarize the comments made by user in a discussion."},{question:"What were some key decisions made in this discussion?"}],job:[{question:"Why did this job fail?",intent:i.wh.actionsAgent},{question:"How can I fix this build?",intent:i.wh.actionsAgent}],default:[{question:"What questions can I ask?"},{question:"What is the best way to get started with Copilot?"},{question:"How do I clone this repository?"},{question:"How do I revert a commit?"},{question:"How do I add myself as a reviewer?"},{question:"How do I create a repository?"}],issues:[{question:"How do I create an issue?"},{question:"How do I filter issues by label?"},{question:"What are the most recently updated issues?"}],"pull-requests":[{question:"How do I create a pull request?"},{question:"How do I filter pull requests by label?"},{question:"How do I reopen a closed pull request?"}],discussions:[{question:"How do I start a new discussion?"},{question:"How do I filter discussions by category or tag?"},{question:"How do I search for a specific discussion?"}]}}}]);
//# sourceMappingURL=ui_packages_copilot-chat_utils_copilot-chat-helpers_ts-10ee2fd3bd6a.js.map