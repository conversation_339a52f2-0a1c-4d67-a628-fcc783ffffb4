"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["repositories","ui_packages_soft-navigate_soft-navigate_ts"],{67105:(e,t,r)=>{r.d(t,{$3:()=>a,HV:()=>i,Vb:()=>o});var n=r(7479);function o(e,t,r){let o={hydroEventPayload:e,hydroEventHmac:t,visitorPayload:"",visitorHmac:"",hydroClientContext:r},a=document.querySelector("meta[name=visitor-payload]");a instanceof HTMLMetaElement&&(o.visitorPayload=a.content);let i=document.querySelector("meta[name=visitor-hmac]")||"";i instanceof HTMLMetaElement&&(o.visitorHmac=i.content),(0,n.i)(o,!0)}function a(e){let t=e.getAttribute("data-hydro-view")||"";o(t,e.getAttribute("data-hydro-view-hmac")||"",e.getAttribute("data-hydro-client-context")||"")}function i(e){let t=e.getAttribute("data-hydro-click-payload")||"";o(t,e.getAttribute("data-hydro-click-hmac")||"",e.getAttribute("data-hydro-client-context")||"")}},35571:(e,t,r)=>{var n=r(39595);function o(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i}let a=class InputDemuxElement extends HTMLElement{connectedCallback(){this.control&&(this.storedInput=Array(this.control.children.length).fill("")),this.addEventListener("input",this.relayInput.bind(this)),this.addEventListener("keydown",this.relayKeydown.bind(this));let e=this.closest("details");e&&e.addEventListener("toggle",()=>{e.open&&this.source.focus()})}relayKeydown(e){if((this.isControlTab(e.target)||e.target===this.source)&&("ArrowDown"===e.key||"Tab"===e.key))e.preventDefault(),e.stopPropagation(),this.routeCustomEvent(new CustomEvent("focus-list"));else if("Escape"===e.key){let e=this.closest("details");e&&e.removeAttribute("open")}}isControlTab(e){return!!e&&!!this.control&&Array.from(this.control.children).includes(e)}relayInput(e){if(!e.target)return;let t=e.target.value;this.routeCustomEvent(new CustomEvent("input-entered",{detail:t}))}routeCustomEvent(e){this.sinks[this.selectedIndex].dispatchEvent(e)}get selectedIndex(){if(!this.control)return 0;let e=this.control.querySelector('[aria-selected="true"]');return e?Array.from(this.control.children).indexOf(e):0}storeInput(){this.storedInput[this.selectedIndex]=this.source.value}updateInput(e){this.source.value=this.storedInput[this.selectedIndex];let t=e.detail.relatedTarget.getAttribute("data-filter-placeholder");this.source.placeholder=t,this.source.setAttribute("aria-label",t),this.notifySelected()}notifySelected(){let e=this.sinks[this.selectedIndex],t=new CustomEvent("tab-selected");e.dispatchEvent(t)}};o([n.aC],a.prototype,"source",void 0),o([n.zV],a.prototype,"sinks",void 0),o([n.aC],a.prototype,"control",void 0),a=o([n.p_],a)},45323:(e,t,r)=>{function n(){return/Windows/.test(navigator.userAgent)?"windows":/Macintosh/.test(navigator.userAgent)?"mac":null}r.d(t,{u:()=>n}),(0,r(21403).lB)(".js-remove-unless-platform",function(e){!function(e){let t=(e.getAttribute("data-platforms")||"").split(","),r=n();return!!(r&&t.includes(r))}(e)&&e.remove()})},93702:(e,t,r)=>{r.d(t,{h:()=>GetRepoElement});var n=r(39595),o=r(45323),a=r(85351),i=r(67105);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i}let{getItem:c,setItem:d}=(0,a.A)("localStorage"),u="code-button-default-tab";let GetRepoElement=class GetRepoElement extends HTMLElement{showDownloadMessage(){let e=this.findPlatform();e&&this.showPlatform(e)}showCodespaces(){let e=this.findPlatform();e&&(this.showPlatform(e),this.loadAndUpdateContent())}showCodespaceSelector(){let e=this.findPlatform();e&&(this.showPlatform(e),this.codespaceSelector&&(this.codespaceSelector.hidden=!1))}showOpenOrCreateInCodespace(){this.openOrCreateInCodespace&&(this.openOrCreateInCodespace.hidden=!1)}removeOpenOrCreateInCodespace(){this.openOrCreateInCodespace&&this.openOrCreateInCodespace.remove()}refreshList(){this.shouldRefreshList&&(this.shouldRefreshList=!1,this.loadAndUpdateContent())}trackDelete(){this.shouldRefreshList=!0}hideSpinner(){this.codespaceLoadingMenu&&(this.codespaceLoadingMenu.hidden=!0),this.codespaceList&&(this.codespaceList.hidden=!1)}showSpinner(){this.codespaceLoadingMenu&&(this.codespaceLoadingMenu.hidden=!1),this.codespaceList&&(this.codespaceList.hidden=!0)}onDetailsToggle(e){for(let e of(this.modal.hidden=!1,this.platforms))e.hidden=!0;let t=e.target;if(t&&t.open){!this.hasForcedCodespaceTabDefault&&this.forceCodespaceTabDefault?(this.hasForcedCodespaceTabDefault=!0,this.selectDefaultTab(!0)):this.selectDefaultTab(!1);let e=this.copilotTip;e&&(0,i.$3)(e)}}onDetailsKeydown(e){if("Escape"===e.key){this.modal.hidden=!0;let t=e.target;t?.closest("details")?.removeAttribute("open")}}showPlatform(e){for(let t of(this.modal.hidden=!0,this.platforms))t.hidden=t.getAttribute("data-platform")!==e}findPlatform(){return(0,o.u)()}refreshOnError(){window.location.reload()}pollForVscode(e){this.showPlatform("vscode");let t=e.currentTarget.getAttribute("data-src");t&&this.vscodePoller.setAttribute("src",t)}backToCodespacesFromVscodePolling(){this.loadAndUpdateContent(),this.showPlatform("codespaces")}localTabSelected(){d(u,"local")}cloudTabSelected(){d(u,"cloud"),this.codespaceList?.id==="lazyLoadedCodespacesList"&&this.loadAndUpdateContent()}copilotTabSelected(){d(u,"copilot")}selectDefaultTab(e){let t=e?"cloud":c(u);if(!t)return;let r=this.querySelector(`button[data-tab="${t}"`);r&&r.click()}loadAndUpdateContent(){this.codespaceList?.setAttribute("src",this.codespaceList.getAttribute("data-src"))}constructor(...e){super(...e),s(this,"forceCodespaceTabDefault",!1),s(this,"shouldRefreshList",!1),s(this,"hasForcedCodespaceTabDefault",!1)}};l([n.CF],GetRepoElement.prototype,"forceCodespaceTabDefault",void 0),l([n.aC],GetRepoElement.prototype,"modal",void 0),l([n.aC],GetRepoElement.prototype,"codespaceForm",void 0),l([n.aC],GetRepoElement.prototype,"codespaceLoadingMenu",void 0),l([n.aC],GetRepoElement.prototype,"codespaceList",void 0),l([n.aC],GetRepoElement.prototype,"codespaceSelector",void 0),l([n.aC],GetRepoElement.prototype,"openOrCreateInCodespace",void 0),l([n.aC],GetRepoElement.prototype,"vscodePoller",void 0),l([n.zV],GetRepoElement.prototype,"platforms",void 0),l([n.aC],GetRepoElement.prototype,"copilotTip",void 0),GetRepoElement=l([n.p_],GetRepoElement)},35923:(e,t,r)=>{var n=r(62044),o=r(27552),a=r(97797),i=r(67105);(0,a.on)("tab-container-changed",".js-branches-tags-tabs",async function(e){let t,r,a=e.detail.relatedTarget,i=e.currentTarget;if(!a||!i)return;for(let e of i.querySelectorAll("[data-controls-ref-menu-id]")){if(!(e instanceof n.A||e instanceof o.A))return;let i=e.getAttribute("data-controls-ref-menu-id"),s=a.id===i;e.hidden=!s,s?r=e:t||(t=e.input?e.input.value:"")}let s=r&&r.input;s&&(r&&void 0!==t&&(s.value=t),s.focus())}),(0,a.on)("click",".js-branch-select-menu",e=>{let t=e.currentTarget;t instanceof HTMLDetailsElement&&!t.open&&(0,i.HV)(t)})},40053:(e,t,r)=>{r.d(t,{V:()=>CodeEditorExtensions});var n=r(39595);function o(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i}let a=class BypassReasonSelectorElement extends HTMLElement{reasonSelected(){this.submitBtn.disabled=!1}copyBlobFields(){let e=document.querySelector(".js-blob-form"),t=document.querySelector(".js-bypass-form");if(e instanceof HTMLFormElement&&t instanceof HTMLFormElement)for(let r of(this.createHiddenInputField(t,"value",document.querySelector(".js-blob-contents").value),this.createHiddenInputField(t,"description",document.querySelector("#commit-description-textarea").value),e.elements))r instanceof HTMLInputElement&&r.name&&!r.name.includes("gitignore")&&"authenticity_token"!==r.name&&this.createHiddenInputField(t,r.name,r.value)}createHiddenInputField(e,t,r){let n=document.createElement("input");n.type="hidden",n.name=t,n.id=`${t}_hidden`,n.value=r,e.appendChild(n),n.required=!0}};o([n.aC],a.prototype,"submitBtn",void 0),a=o([n.p_],a);var i=r(35707),s=r(25794);function l(e,t){if(e!==t)throw TypeError("Private static access of wrong provenance")}let CodeEditorExtensions=class CodeEditorExtensions{static annotateDiscoveredSecrets(e,t){if(!e)return;let r=(l(CodeEditorExtensions,CodeEditorExtensions),c).call(CodeEditorExtensions,e.getValue(),t);if(!r)return;let{startLine:n,endLine:o,startLineCharacter:a,endLineCharacter:i}=r;e.markText({line:n,ch:a},{line:o,ch:i},{className:"CodeMirror-lint-mark-error"})}static emitAnnotateDiscoveredSecrets(e,t,r){let n=(l(CodeEditorExtensions,CodeEditorExtensions),c).call(CodeEditorExtensions,e,t);if(!n)return;let{startLine:o,endLine:a,startLineCharacter:i,endLineCharacter:d}=n;(0,s.R)("secret-detected:lint",r,{from:{lineNumber:o,character:i},to:{lineNumber:a,character:d},severity:"error"})}static emitAnnotateDiscoveredSecretsWithLocation(e,t,r,n,o,a){let i=(l(CodeEditorExtensions,CodeEditorExtensions),d).call(CodeEditorExtensions,e,t,r,n,o);if(!i)return;let{parsedStartLine:c,parsedEndLine:u,startLineCharacter:f,endLineCharacter:p}=i;(0,s.R)("secret-detected:lint",a,{from:{lineNumber:c,character:f},to:{lineNumber:u,character:p},severity:"error"})}};function c(e,t){let r=JSON.parse(t);if(0===r.length||r[0].locations?.length===0)return null;let n=r[0].locations[0],o=e.split(/\r?\n/),a=n.start_line||n.startLine,s=n.end_line||n.endLine,l=n.start_line_byte_position||n.startLineBytePosition||0,c=n.end_line_byte_position||n.endLineBytePosition,d=a-1,u=s-1;if(!(d>u)&&!(d<0)&&!(u>o.length)&&o[d]&&o[u])return{startLine:d,endLine:u,startLineCharacter:(0,i.kN)(o[d],l),endLineCharacter:(0,i.kN)(o[u],c)}}function d(e,t,r,n,o){let a=e.split(/\r?\n/);if(!(--t>--r)&&!(t<0)&&!(r>a.length)&&a[t]&&a[r])return{parsedStartLine:t,parsedEndLine:r,startLineCharacter:(0,i.kN)(a[t],n),endLineCharacter:(0,i.kN)(a[r],o)}}},90342:(e,t,r)=>{(0,r(21403).lB)("#js-spoofed-commit-warning-trigger",{add:()=>{let e=document.getElementById("spoof-warning");if(!e)return;let t=document.querySelector(".commit-title");t&&t.classList.add("pb-1"),e.hidden=!1,e.removeAttribute("aria-hidden")}})},40151:(e,t,r)=>{var n=r(39595),o=r(45062),a=r(65461),i=r(20451),s=r(97797),l=r(66871);function c(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i}let u=class BranchFilterElement extends HTMLElement{submit(e){e.preventDefault()}resetField(e){if("Escape"!==(0,a.Vy)(e))return;let t=this.field.value.trim();this.field.value="",t&&this.search()}reset(){this.field.focus(),this.field.value="",(0,s.h)(this.field,"input")}get activeFilter(){return this.filters.find(e=>e.classList.contains("selected"))??null}async search(){this.originalSelectedItem||(this.originalSelectedItem=this.activeFilter);let e=this.field.value.trim().length>0,t=function(e){let t=e.form,r=e.value.trim();if(r){let e=new URL(t.action,window.location.origin),n=new URLSearchParams(e.search.slice(1)),o=t.elements.namedItem("utf8");return o instanceof HTMLInputElement&&n.append("utf8",o.value),n.append("query",r),e.search=n.toString(),e.toString()}return t.getAttribute("data-reset-url")}(this.field);for(let t of(this.classList.toggle("is-search-mode",e),this.classList.add("is-loading"),this.filters))t.classList.remove("selected");e?this.allFilter.classList.add("selected"):this.originalSelectedItem&&(this.originalSelectedItem.classList.add("selected"),this.originalSelectedItem=null),this.abortSearch?.abort();let{signal:r}=this.abortSearch=new AbortController;try{let e=await (0,i.Ts)(document,t,{signal:r});(0,l.bj)(t),this.result.textContent="",this.result.appendChild(e)}catch{}r.aborted||this.classList.remove("is-loading")}constructor(...e){super(...e),c(this,"abortSearch",null),c(this,"originalSelectedItem",null)}};d([n.aC],u.prototype,"field",void 0),d([n.aC],u.prototype,"result",void 0),d([n.aC],u.prototype,"allFilter",void 0),d([n.zV],u.prototype,"filters",void 0),d([(0,o.s)(100)],u.prototype,"search",null),u=d([n.p_],u);var f=r(22247),p=r(21403),m=r(12559),h=r(97325);(0,p.lB)(".repository-import",{subscribe:e=>(0,f.Rt)(e,"socket:message",function(e){let t=e.detail.data;t.redirect_to&&(document.location.href=t.redirect_to,e.stopImmediatePropagation())})}),(0,s.on)("change","input.js-repository-import-lfs-opt",function({currentTarget:e}){let t=parseInt(e.getAttribute("data-percent-used")||""),r=e.closest(".js-repository-import-lfs-container"),n=e.getAttribute("data-used")||"";r.querySelector(".js-repository-import-lfs-warn").classList.toggle("d-none",!(t>100)),r.querySelector(".js-usage-bar").classList.toggle("exceeded",t>=100),r.querySelector(".js-usage-bar").setAttribute("aria-label",`${t}%`),r.querySelector(".js-repository-import-lfs-progress").style.width=`${t}%`,r.querySelector("span.js-usage-text").textContent=n}),(0,m.JW)(".js-repository-import-author-form",async function(e,t){let r=await t.html();e.closest(".js-repository-import-author").replaceWith(r.html)}),(0,s.on)("click",".js-repository-import-projects-cancel-button",function(){let e=document.querySelector(".js-repository-import-projects-cancel-form");(0,h.k_)(e)});var v=r(36175);let g=!1,y=new URLSearchParams(window.location.search).get("profile_readme");function b(e,t,r,n){e?(t&&(t.disabled=!1),r&&r.classList.remove("color-fg-muted"),n&&(n.hidden=!1)):(t&&(t.disabled=!0),r&&r.classList.add("color-fg-muted"),n&&(n.hidden=!0))}function S(){let e=document.querySelector('.js-owner-container [aria-checked="true"]');if(!e||!e.closest(".form-group"))return}function E(e){(e?.target||document.querySelector(".js-privacy-toggle:checked"))&&j()}function j(){let e=document.querySelector(".js-repo-form"),t=e.querySelector(".js-repository-owner-choice:checked"),r=e.querySelector(".js-repo-name"),n=e.querySelector(".js-repo-url"),o=!n||!n.classList.contains("is-autocheck-errored"),a=!!document.querySelector(".js-page-repo-persisted")||!!t;(o=o&&a)&&r&&(o=r.classList.contains("is-autocheck-successful")),e.querySelector("button[type=submit]").disabled=!o}async function w(e){return(0,i.Ts)(document,e)}async function A(e){let t=e.getAttribute("data-pulse-diffstat-summary-url");try{var r,n;t&&(r=await w(t),(n=e).textContent="",n.appendChild(r))}catch{let t=e.querySelector(".js-blankslate-loading"),r=e.querySelector(".js-blankslate-error");t.classList.add("d-none"),r.classList.remove("d-none")}}(0,s.on)("focusout","#repository-owner",function(){S()}),(0,s.on)("click",".js-use-pages-url",function(e){let t=e.currentTarget,r=document.querySelector(".js-pages-url-input"),n=document.getElementById("page-url");r.readOnly=t.checked,r.value=n.textContent,t.checked?(r.classList.add("color-fg-subtle"),r.classList.add("color-bg-subtle"),r.classList.remove("color-bg-default"),r.classList.remove("color-fg-default")):(r.classList.remove("color-fg-subtle"),r.classList.remove("color-bg-subtle"),r.classList.add("color-fg-default"),r.classList.add("color-bg-default"))}),(0,s.on)("click",".js-privacy-toggle",function(){g=!0}),(0,s.on)("change",".js-privacy-toggle",E),(0,s.on)("details-menu-selected",".js-owner-container",function(){let e=document.querySelector(".js-repo-name");(0,s.h)(e,"input");let t=document.querySelector('.js-owner-container [aria-checked="true"]'),r="false"!==t.getAttribute("data-org-allow-public-repos"),n=document.querySelector(".js-privacy-toggle[value=public]");b(r,n,document.querySelector(".js-privacy-toggle-label-public"),document.querySelector(".js-public-description"));let o=function(e,t){if(e){let r=document.querySelector(`#new-repo-internal-visibility-${e}`);if(r){r.hidden=!1;let e=r.querySelector(".js-privacy-toggle[value=internal]");if(e instanceof HTMLInputElement)return"false"===t.getAttribute("data-org-allow-internal-repos")?e.disabled=!0:e.disabled=!1,e}}return null}(t.getAttribute("data-business-id"),t),a="false"!==t.getAttribute("data-org-allow-private-repos"),i=document.querySelector(".js-privacy-toggle[value=private]"),l=document.querySelector(".js-privacy-toggle-label-private"),c=document.querySelector(".js-private-description");b(a,i,l,c),S();let d="false"!==t.getAttribute("data-org-private-restricted-by-plan"),u="false"!==t.getAttribute("data-org-show-upgrade"),f=t.getAttribute("data-org-name"),p=f?document.querySelector(`a[data-upgrade-link="${f}"]`):null;a||!d?p&&(p.hidden=!0):p&&(p.hidden=!u);let m="true"===t.getAttribute("data-org-show-trade-controls"),h="true"===t.getAttribute("data-user-show-trade-controls"),v=m&&!a;(h||v)&&(i.disabled=!0,c&&(c.hidden=!0),p&&(p.hidden=!0)),function(e,t,r,n){let o=null;if("private"===e.getAttribute("data-default")&&n&&!n.disabled?o=n:"internal"===e.getAttribute("data-default")&&r&&!r.disabled?o=r:t&&!t.disabled?o=t:r&&!r.disabled&&(o=r),n&&!n.disabled&&"member"===y?o=n:t&&!t.disabled&&y&&(o=t),!o)return;let a=t&&t.disabled&&t.checked||n.disabled&&n.checked||r&&r.disabled&&r.checked,i=(!t||!t.checked)&&(!r||!r.checked)&&!n.checked;(!1===g||!0===a||!0===i)&&(o.checked=!0,(0,s.h)(o,"change"))}(t,n,o,i),function(e){for(let t of document.querySelectorAll(".js-with-permission-fields"))t.hidden=!e;let t=document.querySelector(".errored"),r=document.querySelector("dl.warn");t&&(t.hidden=!e),r&&(r.hidden=!e)}("yes"===t.getAttribute("data-permission")),E(),j()},{capture:!0}),(0,v.eC)(".js-repo-url",function(e){let t=e.target;if(!(t instanceof HTMLInputElement))return;let r=t.closest(".form-group");if(!(r instanceof HTMLDListElement))return;let n=document.querySelector(".js-insecure-url-warning"),o=document.querySelector(".js-svn-url-error"),a=document.querySelector(".js-git-url-error"),i=t.value.toLowerCase();n.hidden=!i.startsWith("http://"),o.hidden=!i.startsWith("svn://"),a.hidden=!i.startsWith("git://"),i.startsWith("svn://")||i.startsWith("git://")?(t.classList.add("is-autocheck-errored"),r.classList.add("errored")):(t.classList.remove("is-autocheck-errored"),r.classList.remove("errored")),j()}),(0,s.on)("change",".js-repo-init-setting-menu-option",j),r(35923),(0,p.lB)(".js-pulse-contribution-data",e=>{A(e)});var _=r(69676),C=r(52811),q=r(26559);async function L(e){let t=e.form;t.querySelector("#release_draft").value="1",k(e,"saving");let r=await fetch(t.action,{method:t.method,body:new FormData(t),headers:{Accept:"application/json",...(0,q.kt)()}});if(!r.ok)return void k(e,"failed");let n=await r.json();return k(e,"saved"),(0,C.C)(e),setTimeout(k,5e3,e,"default"),(0,s.h)(t,"release:saved",{release:n}),n}function T(e){let t=e.closest(".js-releases-marketplace-publish-container").querySelector(".js-releases-marketplace-publish-preview");e.checked?t.classList.remove("d-none"):t.classList.add("d-none")}function k(e,t){for(let r of e.querySelectorAll(".js-save-draft-button-state"))r.hidden=r.getAttribute("data-state")!==t;e.disabled="saving"===t}function x(e){let t=document.querySelector(".js-release-target-wrapper");if(null!=t){var r;switch(r=e,document.querySelector(".js-release-tag").setAttribute("data-state",r),e){case"valid":case"invalid":case"duplicate":case"branch_exists":t.hidden=!0;break;case"loading":break;default:t.hidden=!1}for(let t of document.querySelectorAll(".js-tag-status-message"))t.hidden=t.getAttribute("data-state")!==e,t.getAttribute("data-state")===e?t.setAttribute("role","status"):t.removeAttribute("role");G(),H("pending")}}function R(){return document.querySelector(".js-release-tag").getAttribute("data-state")}(0,s.on)("change",".js-releases-marketplace-publish-field",function(e){T(e.currentTarget)}),(0,p.lB)(".js-releases-marketplace-publish-field",function(e){T(e)}),(0,s.on)("click",".js-save-draft",function(e){let t=e.currentTarget;t.disabled||(L(t),e.preventDefault())}),(0,s.on)("click",".js-prerelease-checkbox",e=>{let t=e.currentTarget,r=e.currentTarget.closest("form").querySelector("#is_stored_latest"),n=document.querySelector(".js-latest-release-checkbox");n&&(t.checked?(n.checked=!1,n.disabled=!0):"1"===r.value?(n.checked=!0,n.disabled=!0):n.disabled=!1)}),(0,p.lB)(".js-latest-release-checkbox",function(e){e.closest("form").querySelector("#is_stored_latest").value=e.checked&&!0===e.disabled?"1":"0"}),(0,s.on)("release:saved",".js-release-form",function(e){let t=e.detail.release,r=e.currentTarget;r.setAttribute("action",t.update_url),t.update_authenticity_token&&(r.querySelector("input[name=authenticity_token]").value=t.update_authenticity_token),(0,l.bj)(t.edit_url);let n=r.querySelector("#release_id");if(!n.value){n.value=t.id;let e=document.createElement("input");e.type="hidden",e.name="_method",e.value="put",r.appendChild(e)}});let I=new WeakMap;function P(e){let t=e.querySelector('input[name="release[tag_name]"]:checked');return t?.value}async function O(e){let t=P(e);if(!t)return void x("empty");if(t===I.get(e))return;x("loading"),I.set(e,t);let r=new URL(e.getAttribute("data-url"),window.location.origin),n=new URLSearchParams(r.search.slice(1));n.append("tag_name",t),r.search=n.toString();let o=await fetch(r.toString(),{headers:{Accept:"application/json",...(0,q.kt)()}});if(!o.ok)return void x("invalid");let a=await o.json();"duplicate"===a.status&&parseInt(e.getAttribute("data-existing-id"))===parseInt(a.release_id)?x("valid"):(document.querySelector(".js-release-tag .js-edit-release-link").setAttribute("href",a.url),x(a.status)),$(e)}async function M(e){let t=e.offsetWidth;e.style.width=`${t}px`,H("loading"),e.setAttribute("aria-disabled","true");let r=e.getAttribute("data-repo-url"),n=new URL(`${r}/releases/notes`,window.location.origin),o=new URLSearchParams(n.search.slice(1));o.append("commitish",F()),o.append("tag_name",P(document)||""),o.append("previous_tag_name",document.querySelector('input[name="release[previous_tag_name]"]:checked')?.value||""),n.search=o.toString();let a=await fetch(n.toString(),{headers:{Accept:"application/json"}});if(a.ok){let e=await a.json();if(e.commitish===F()){let t=document.getElementById("release_body"),r="generated"===D()?"":t.value.trim();r?t.value=r.concat(`

`,e.body):t.value=e.body;let n=document.getElementById("release_name");n.value||(n.value=e.title),H("succeed"),(0,C.i)("Successfully generated release notes"),B(r?"generated-and-edited":"generated"),G(!0);let o=document.querySelector(".js-release-body-warning");o.textContent=e.warning_message,o.hidden=!e.warning_message}}else{H("failed"),e.setAttribute("aria-disabled","false");let t=await a.json();if(t&&t.error){let e=document.getElementById("form-error-alert");e.textContent=t.error,e.hidden=!1}}}(0,s.on)("click",".js-generate-release-notes",function(e){let t=e.currentTarget;"true"!==t.getAttribute("aria-disabled")&&M(t)});let N=["pending","loading","succeed","failed"];function H(e){if(N.map(t=>{let r=document.getElementById(`generate-icon-${t}`),n=document.getElementById("generate-btn-txt");r&&(t===e?(n&&n.setAttribute("hidden","true"),("succeed"===e||"failed"===e)&&setTimeout(()=>{r.setAttribute("hidden","true"),n&&n.removeAttribute("hidden")},2e3),r.removeAttribute("hidden")):r.setAttribute("hidden","true"))}),"failed"!==e){let e=document.getElementById("form-error-alert");e.textContent="",e.hidden=!0}}function B(e){document.getElementById("generated_notes_state").value=e}function D(){return document.getElementById("generated_notes_state").value}function F(){return"valid"===R()?P(document)||"":document.querySelector('input[name="release[target_commitish]"]:checked')?.value||""}function $(e){let t=e.closest("form").querySelector(".js-previewable-comment-form");if(!t)return;let r=t.getAttribute("data-base-preview-url");r||(r=String(t.getAttribute("data-preview-url")),t.setAttribute("data-base-preview-url",r));let n=e.querySelectorAll('input[name="release[tag_name]"], input[name="release[target_commitish]"]:checked'),o=new URL(r,window.location.origin),a=new URLSearchParams(o.search.slice(1));for(let e of n)e.value&&a.append(e.name,e.value);o.search=a.toString(),t.setAttribute("data-preview-url",o.toString())}function G(e=!1){let t=document.querySelector(".js-generate-release-notes"),r=document.getElementById("prev-tag-picker"),n=document.querySelector("tool-tip[for='generate-notes-btn']"),o=R(),a="valid"!==o&&"pending"!==o,i=e||a;t.setAttribute("aria-disabled",i.toString()),n&&(i?n.textContent=`${a?"Select a valid tag to a":"Clear existing notes to a"}utomatically add the Markdown for all the merged pull requests from this diff and contributors of this release`:n.textContent="Automatically add the Markdown for all the merged pull requests from this diff and contributors of this release"),r&&(r.hidden=i)}function U(e){if(""===e.value)G(),H("pending"),B("initial");else{let e=D();G("initial"!==e),"generated"===e&&B("generated-and-edited")}}(0,p.lB)(".js-release-tag",function(e){O(e)}),(0,p.lB)("details-menu",function(e){let t=e.closest("details");t&&(t.open?(0,_.iE)(e):t.addEventListener("toggle",()=>{t.open&&(0,_.iE)(e)},{once:!0}))}),(0,s.on)("click",".js-release-expand-btn",async function(e){let t=e.currentTarget.closest(".js-release-expandable"),r=t.getAttribute("data-expand-url"),n=await (0,i.Ts)(document,r);t?.replaceWith(n)}),(0,s.on)("click",".js-release-asset-untruncate-btn",async function(e){e.currentTarget.setAttribute("hidden","true");let t=e.currentTarget.parentNode?.querySelector(".js-truncate-assets-spinner");t&&t.removeAttribute("hidden");let r=e.currentTarget.closest(".js-truncated-assets-fragment"),n=r?.getAttribute("data-deferred-src");n&&r?.setAttribute("src",encodeURI(n))}),(0,p.lB)("#release_body",function(e){e.addEventListener("input",function(){U(e)}),U(e)}),(0,s.on)("change",".js-release-check-tag",function(e){O(e.currentTarget.closest(".js-release-tag"))}),(0,s.on)("submit",".js-release-form",function(e){e.submitter?e.submitter.getAttribute("publish-release")&&(document.querySelector("#release_draft").value="0"):document.querySelector("#release_draft").value="0"}),(0,p.lB)(".js-release-form .js-previewable-comment-form",function(e){$(e.closest("form").querySelector(".js-release-tag"))}),(0,p.lB)("#release_page_title",function(e){let t=e.textContent?.trim()||"";t.length&&(document.title=t)}),(0,p.lB)("#release-filter",function(e){e.value.length>0&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))});let V="<BRANCH>";(0,s.on)("auto-check-message-updated",".js-rename-branch-input",function(e){!function(e){let t=e.closest(".js-rename-branch-form"),r=e.value,n=r!==e.defaultValue&&""!==r,o=t.querySelector(".js-rename-branch-autocheck-message");if(o&&n){let e=!1;for(let n of((r=o.getAttribute("data-shell-safe-name")||V).includes("<")&&(e=!0),t.querySelectorAll(".js-rename-branch-new-name")))n.textContent=r;for(let n of((r=o.getAttribute("data-shell-safe-name-with-remote")||`origin/${V}`).includes("<")&&(e=!0),t.querySelectorAll(".js-rename-branch-new-name-with-remote")))n.textContent=r;let n=t.querySelector(".js-rename-branch-special-characters-documentation");n&&e&&(n.hidden=!1,n.removeAttribute("aria-hidden"))}}(e.currentTarget)});let W=e=>{let t=document.querySelector(z);t&&(t.disabled=0===e.value.length)},z='form.js-protected-branch-settings button[type="submit"]';(0,p.lB)(z,{add(){let e=document.getElementById("rule_field");e&&(W(e),e.addEventListener("input",()=>W(e)))}}),(0,s.on)("change",".js-template-repository-choice",function(e){let t=e.target,r=t.checked&&""!==t.value,n=t.form,o=n.querySelectorAll(".js-template-repository-setting"),a=n.querySelectorAll(".js-template-repository-name-display");if(r){let e=t.closest(".js-template-repository-choice-container").querySelector(".js-template-repository-name"),r=t.getAttribute("data-owner"),o=n.querySelector(`.js-repository-owner-choice[value="${r}"]`);if(o instanceof HTMLInputElement)o.checked=!0,(0,s.h)(o,"change");else{let e=n.querySelector(".js-repository-owner-choice.js-repository-owner-is-viewer");e.checked=!0,(0,s.h)(e,"change")}for(let t of a)t.textContent=e.textContent}else for(let e of a)e.textContent="";for(let e of o)e.hidden=!r});var K=r(91385),J=r(35908),X=r(78134);let Z=(0,r(5225).A)(K.fN);(0,v.Ff)("keydown",".js-tree-finder-field",e=>{"Escape"===e.key&&(e.preventDefault(),(0,l.OE)())}),(0,p.lB)(".js-tree-finder",e=>{let t=e.querySelector(".js-tree-finder-field"),r=e.querySelector(".js-tree-finder-virtual-filter"),n=e.querySelector(".js-tree-browser"),o=e.querySelector(".js-tree-browser-results"),a=e.querySelector(".js-tree-browser-result-template"),i=new J.A(t,o);r.filter=(e,t)=>""===t||(0,K.qA)(t,e)&&Z(t,e)>0,r.addEventListener("virtual-filter-input-filter",()=>{n.updating="lazy"}),r.addEventListener("virtual-filter-input-filtered",()=>{n.updating="eager"}),n.addEventListener("virtual-list-sort",e=>{e.preventDefault();let r=t.value;n.sort((e,t)=>Z(r,t)-Z(r,e))}),n.addEventListener("virtual-list-update",()=>{i.stop()}),n.addEventListener("virtual-list-updated",()=>{i.start(),i.navigate()}),n.addEventListener("virtual-list-render-item",e=>{if(!(e instanceof CustomEvent))return;let t=new X.i4(a,{item:e.detail.item,id:`entry-${Math.random().toString().substr(2,5)}`,urlEncodedItem:encodeURIComponent(e.detail.item).replaceAll("%2F","/")}),r=t.querySelector("marked-text");r&&(r.positions=K.Xq),e.detail.fragment.append(t)}),n.querySelector("ul").hidden=!1,t.focus(),i.start()});var Q=r(91707),Y=r(21715),ee=r(59519);let et=null,er=new WeakMap;function en(e){e.classList.remove("is-progress-bar");let t=e.closest(".js-upload-manifest-file-container");t.querySelector(".js-upload-progress").hidden=!0,t.querySelector(".js-upload-meter-text .js-upload-meter-filename").textContent=""}function eo(e){en(e.currentTarget)}async function ea(e){let t=e.getAttribute("data-redirect-url");try{let r=await (0,i.Ee)(e.getAttribute("data-poll-url"),void 0,void 0,[200,500],[202,404]);if(500===r.status&&r.body){let t="",n="",o=new TextDecoder("utf-8"),a=r.body.getReader();for(;;){let{value:e,done:r}=await a.read();if(r)break;t+=o.decode(e,{stream:!0})}let i=JSON.parse(t).job;if(i){n=i.error_message;let t=i.failed_runs;t&&0!==t.length||ei(n);let r=t[0].rule_run;if("secret_scanning"===r.rule_type){let t=r.violations.items[0].candidate,o=r.evaluation_metadata.scan_results[t].secrets;o.length||ei(n);let a=document.querySelector(".js-push-protection-bypass-csrf"),i=await fetch(e.getAttribute("data-secret-bypass-url"),{method:"POST",body:JSON.stringify({file:t,secrets:o,ruleRunId:r.id}),headers:{Accept:"application/json","Scoped-CSRF-Token":a.value}});if(i.ok){let e=await i.text();document.querySelector(".js-manifest-ready-check-failed").innerHTML=e;let t=document.getElementById("file-upload-detected-secret-dialog-id");t?.show()}}}ei(n)}else window.location.href=t}catch(t){document.querySelector(".js-manifest-ready-check").hidden=!0;let e=document.querySelector(".js-manifest-ready-check-failed");if(e.hidden=!1,t instanceof Error&&t.message){let r=e.children[1];if(!r)return;r.textContent=r.textContent.concat(" ",t.message,".")}}}function ei(e){if(e)throw Error(e);throw Error()}function es(e){return e.closest("form").querySelector("#release_id").value}(0,s.on)("file-attachment-accept",".js-upload-manifest-file",function(e){let{attachments:t}=e.detail,r=parseInt(e.currentTarget.getAttribute("data-directory-upload-max-files")||"",10);t.length>r&&(e.preventDefault(),e.currentTarget.classList.add("is-too-many"))}),(0,s.on)("document:drop",".js-upload-manifest-tree-view",async function(e){let{transfer:t}=e.detail,r=e.currentTarget,n=await Q.Attachment.traverse(t,!0),o=r.getAttribute("data-drop-url");document.addEventListener(Y.z.SUCCESS,()=>{document.querySelector(".js-upload-manifest-file").attach(n)},{once:!0}),(0,ee.softNavigate)(o)}),(0,s.on)("upload:setup",".js-upload-manifest-file",async function(e){let t,{batch:r,form:n,preprocess:o}=e.detail,a=e.currentTarget,i=a.closest(".js-upload-manifest-file-container").querySelector(".js-upload-progress");i.hidden=!1,a.classList.add("is-progress-bar");let s=i.querySelector(".js-upload-meter-text");function l(){n.append("upload_manifest_id",er.get(a))}if(s.querySelector(".js-upload-meter-range-start").textContent=String(r.uploaded()+1),s.querySelector(".js-upload-meter-range-end").textContent=String(r.size),er.get(a))return void l();if(et)return void o.push(et.then(l));let c=a.closest(".js-upload-manifest-file-container").querySelector(".js-upload-manifest-form");et=fetch(c.action,{method:c.method,body:new FormData(c),headers:{Accept:"application/json"}});let[d,u]=[new Promise(e=>{t=e}),t];o.push(d.then(l));let f=await et;if(!f.ok)return;let p=await f.json();document.querySelector(".js-manifest-commit-form").elements.namedItem("manifest_id").value=p.upload_manifest.id,er.set(a,p.upload_manifest.id),et=null,u()}),(0,s.on)("upload:start",".js-upload-manifest-file",function(e){let{attachment:t,batch:r}=e.detail,n=e.currentTarget.closest(".js-upload-manifest-file-container").querySelector(".js-upload-progress").querySelector(".js-upload-meter-text");n.querySelector(".js-upload-meter-range-start").textContent=r.uploaded()+1,n.querySelector(".js-upload-meter-filename").textContent=t.fullPath}),(0,s.on)("upload:complete",".js-upload-manifest-file",function(e){let{attachment:t,batch:r}=e.detail,n=document.querySelector(".js-manifest-commit-file-template").querySelector(".js-manifest-file-entry").cloneNode(!0);n.querySelector(".js-filename").textContent=t.fullPath;let o=n.querySelector('[aria-label="Remove this file"]');o&&(o.ariaLabel=`Remove ${t.fullPath}`);let a=t.id;n.querySelector(".js-remove-manifest-file-form").elements.namedItem("file_id").value=a;let i=document.querySelector(".js-manifest-file-list");i.hidden=!1,e.currentTarget.classList.add("is-file-list"),i.querySelector(".js-manifest-file-list-root").appendChild(n),r.isFinished()&&en(e.currentTarget)}),(0,s.on)("upload:progress",".js-upload-manifest-file",function(e){let{batch:t}=e.detail;e.currentTarget.closest(".js-upload-manifest-file-container").querySelector(".js-upload-meter").style.width=`${t.percent()}%`}),(0,s.on)("upload:error",".js-upload-manifest-file",eo),(0,s.on)("upload:invalid",".js-upload-manifest-file",eo),(0,m.JW)(".js-remove-manifest-file-form",async function(e,t){await t.html();let r=e.closest(".js-manifest-file-list-root");e.closest(".js-manifest-file-entry").remove(),r.hasChildNodes()||(r.closest(".js-manifest-file-list").hidden=!0,document.querySelector(".js-upload-manifest-file").classList.remove("is-file-list"))}),(0,p.lB)(".js-manifest-ready-check",{initialize(e){ea(e)}}),(0,p.lB)(".js-file-upload-detected-secret-dialog",{add(){let e=document.querySelector('[data-close-dialog-id="file-upload-detected-secret-dialog-id"]'),t=document.querySelector(".js-manifest-ready-check");t&&e instanceof HTMLElement&&(e.onclick=async e=>{e.preventDefault();let r=t.getAttribute("data-index-url");r&&(window.location.href=encodeURI(r))})}}),(0,s.on)("click",".js-release-remove-file",function(e){let t=e.currentTarget.closest(".js-release-file");t.classList.add("delete"),t.querySelector("input.destroy").value="true"}),(0,s.on)("click",".js-release-undo-remove-file",function(e){let t=e.currentTarget.closest(".js-release-file");t.classList.remove("delete"),t.querySelector("input.destroy").value=""});let el=null;function ec(e,t){t.append("release_id",es(e));let r=Array.from(document.querySelectorAll(".js-releases-field .js-release-file.delete .id"));if(r.length){let e=r.map(e=>e.value);t.append("deletion_candidates",e.join(","))}}(0,s.on)("release:saved",".js-release-form",function(e){let t=e.currentTarget;el=null;let r=!1;for(let e of t.querySelectorAll(".js-releases-field .js-release-file"))e.classList.contains("delete")?e.remove():e.classList.contains("js-template")||(r=!0);let n=t.querySelector(".js-releases-field");n.classList.toggle("not-populated",!r),n.classList.toggle("is-populated",r)}),(0,s.on)("upload:setup",".js-upload-release-file",function(e){let{form:t,preprocess:r}=e.detail,n=e.currentTarget;if(es(n))return void ec(n,t);el||(el=L(document.querySelector(".js-save-draft")));let o=ec.bind(null,n,t);r.push(el.then(o))}),(0,s.on)("upload:start",".js-upload-release-file",function(e){let t=e.detail.policy;e.currentTarget.querySelector(".js-upload-meter").classList.remove("d-none");let r=t.asset.replaced_asset;if(r)for(let e of document.querySelectorAll(".js-releases-field .js-release-file .id"))Number(e.value)===r&&e.closest(".js-release-file").remove()}),(0,s.on)("upload:complete",".js-upload-release-file",function(e){let{attachment:t}=e.detail,r=document.querySelector(".js-releases-field"),n=r.querySelector(".js-template").cloneNode(!0);n.classList.remove("d-none","js-template"),n.querySelector("input.id").value=t.id;let o=t.name||t.href.split("/").pop();for(let e of r.querySelectorAll(".js-release-file"))e.querySelector(".js-release-asset-filename")?.value===o&&"starter"===e.getAttribute("data-state")&&e.remove();for(let e of n.querySelectorAll(".js-release-asset-filename"))e instanceof HTMLInputElement?e.value=o:e.textContent=o;let a=`(${(t.file.size/1048576).toFixed(2)} MB)`;n.querySelector(".js-release-asset-filesize").textContent=a,n.setAttribute("data-state","uploaded"),r.appendChild(n),r.classList.remove("not-populated"),r.classList.add("is-populated"),e.currentTarget.querySelector(".js-upload-meter").classList.add("d-none")}),(0,s.on)("upload:progress",".js-upload-release-file",function(e){let{attachment:t}=e.detail;e.currentTarget.querySelector(".js-upload-meter").style.width=`${t.percent}%`});var ed=r(93702);function eu(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ef(e,t,r,n){var o,a=arguments.length,i=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)i=Reflect.decorate(e,t,r,n);else for(var s=e.length-1;s>=0;s--)(o=e[s])&&(i=(a<3?o(i):a>3?o(t,r,i):o(t,r))||i);return a>3&&i&&Object.defineProperty(t,r,i),i}let ep=class RepoCodespacesCountElement extends HTMLElement{connectedCallback(){(0,p.lB)("get-repo",{constructor:ed.h,add:e=>{this.handleGetRepoElement(e)}})}handleGetRepoElement(e){e.openOrCreateInCodespace&&(0===this.codespacesCount?e.showOpenOrCreateInCodespace():e.removeOpenOrCreateInCodespace())}constructor(...e){super(...e),eu(this,"codespacesCount",0)}};eu(ep,"attrPrefix",""),ef([n.CF],ep.prototype,"codespacesCount",void 0),ep=ef([n.p_],ep),r(35571),r(33284),r(90342),r(40053)},52811:(e,t,r)=>{r.d(t,{C:()=>i,i:()=>s});var n=r(96679),o=r(27851),a=r(46493);function i(e,t){(0,o.G7)("arianotify_comprehensive_migration")?s(l(e),{...t,element:t?.element??e}):(0,o.G7)("primer_live_region_element")&&t?.element===void 0?(0,a.Cj)(e,{politeness:t?.assertive?"assertive":"polite"}):s(l(e),t)}function s(e,t){let{assertive:r,element:i}=t??{};(0,o.G7)("arianotify_comprehensive_migration")&&"ariaNotify"in Element.prototype?(i||document.body).ariaNotify(e):(0,o.G7)("primer_live_region_element")&&void 0===i?(0,a.iP)(e,{politeness:r?"assertive":"polite"}):function(e,t,r){let o=r??n.XC?.querySelector(t?"#js-global-screen-reader-notice-assertive":"#js-global-screen-reader-notice");o&&(o.textContent===e?o.textContent=`${e}\u00A0`:o.textContent=e)}(e,r,i)}function l(e){return(e.getAttribute("aria-label")||e.innerText||"").trim()}},53005:(e,t,r)=>{r.d(t,{O:()=>i,S:()=>a});var n=r(96679);let o=n.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",a="X-GitHub-Client-Version";function i(){return o}},25794:(e,t,r)=>{r.d(t,{R:()=>n});function n(e,t,r){if(!t)return;let n=t.className.includes("cm-content")?t:t.querySelector(".cm-content");n&&n.dispatchEvent(new CustomEvent(e,{detail:r}))}},26559:(e,t,r)=>{r.d(t,{jC:()=>l,kt:()=>i,tV:()=>s});var n=r(53005),o=r(27851),a=r(88191);function i(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,a.wE)(e)};return(0,o.G7)("client_version_header")&&(t={...t,[n.S]:(0,n.O)()}),t}function s(e,t){for(let[r,n]of Object.entries(i(t)))e.set(r,n)}function l(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,r)=>{r.d(t,{$r:()=>i,M1:()=>s,li:()=>o,pS:()=>c,wE:()=>l});var n=r(96679);let o="X-Fetch-Nonce",a=new Set;function i(e){a.add(e)}function s(){return a.values().next().value||""}function l(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[o]=s():a.has(e)?t[o]=e:t[o]=Array.from(a).join(","),t}function c(){let e=n.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&i(e)}},20451:(e,t,r)=>{r.d(t,{Ee:()=>s,b4:()=>l,Ts:()=>i});let n=class ResponseError extends Error{constructor(e,t){super(`${e} for HTTP ${t.status}`),function(e,t,r){t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"response",void 0),this.response=t,this.name="ResponseError"}};var o=r(1739),a=r(26559);async function i(e,t,r){let i=new Request(t,r);(0,a.tV)(i.headers);let s=await self.fetch(i);if(s.status<200||s.status>=300)throw Error(`HTTP ${s.status}${s.statusText||""}`);return!function(e,t,r=!1){let o=t.headers.get("content-type")||"";if(!r&&!o.startsWith("text/html"))throw new n(`expected response with text/html, but was ${o}`,t);if(r&&!(o.startsWith("text/html")||o.startsWith("application/json")))throw new n(`expected response with text/html or application/json, but was ${o}`,t);let a=t.headers.get("x-html-safe");if(a){if(!e.includes(a))throw new n("response X-HTML-Safe nonce did not match",t)}else throw new n("missing X-HTML-Safe nonce",t)}(function(e){let t=[...e.querySelectorAll("meta[name=html-safe-nonce]")].map(e=>e.content);if(t.length<1)throw Error("could not find html-safe-nonce on document");return t}(e),s),(0,o.B)(e,await s.text())}function s(e,t,r=1e3,n=[200],o=[202]){return async function r(i){let s=new Request(e,t);(0,a.tV)(s.headers);let l=await self.fetch(s);if(o.includes(l.status))return await new Promise(e=>setTimeout(e,i)),r(1.5*i);if(n.includes(l.status))return l;if(l.status<200||l.status>=300)throw Error(`HTTP ${l.status}${l.statusText||""}`);throw Error(`Unexpected ${l.status} response status from poll endpoint`)}(r)}async function l(e,t,r){let{wait:n=500,acceptedStatusCodes:o=[200],max:i=3,attempt:s=0}=r||{},c=async()=>new Promise((r,l)=>{setTimeout(async()=>{try{let n=new Request(e,t);(0,a.tV)(n.headers);let l=await self.fetch(n);if(o.includes(l.status)||s+1===i)return r(l);r("retry")}catch(e){l(e)}},n*s)}),d=await c();return"retry"!==d?d:l(e,t,{wait:n,acceptedStatusCodes:o,max:i,attempt:s+1})}},97325:(e,t,r)=>{r.d(t,{Cy:()=>s,K3:()=>d,Z8:()=>l,k_:()=>a,lK:()=>u,m$:()=>i});var n=r(94982);function o(e,t,r){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:r}))}function a(e,t){t&&(function(e,t){if(!(e instanceof HTMLFormElement))throw TypeError("The specified element is not of type HTMLFormElement.");if(!(t instanceof HTMLElement))throw TypeError("The specified element is not of type HTMLElement.");if("submit"!==t.type)throw TypeError("The specified element is not a submit button.");if(!e||e!==t.form)throw Error("The specified element is not owned by the form element.")}(e,t),(0,n.A)(t)),o(e,"submit",!0)&&e.submit()}function i(e,t){if("boolean"==typeof t)if(e instanceof HTMLInputElement)e.checked=t;else throw TypeError("only checkboxes can be set to boolean value");else if("checkbox"===e.type)throw TypeError("checkbox can't be set to string value");else e.value=t;o(e,"change",!1)}function s(e,t){for(let r in t){let n=t[r],o=e.elements.namedItem(r);o instanceof HTMLInputElement?o.value=n:o instanceof HTMLTextAreaElement&&(o.value=n)}}function l(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),r=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==r&&"reset"!==r||e.isContentEditable}function c(e){return new URLSearchParams(e)}function d(e,t){let r=new URLSearchParams(e.search);for(let[e,n]of c(t))r.append(e,n);return r.toString()}function u(e){return c(new FormData(e)).toString()}},94982:(e,t,r)=>{function n(e){let t=e.closest("form");if(!(t instanceof HTMLFormElement))return;let r=o(t);if(e.name){let n=e.matches("input[type=submit]")?"Submit":"",o=e.value||n;r||((r=document.createElement("input")).type="hidden",r.classList.add("js-submit-button-value"),t.prepend(r)),r.name=e.name,r.value=o}else r&&r.remove()}function o(e){let t=e.querySelector("input.js-submit-button-value");return t instanceof HTMLInputElement?t:null}r.d(t,{A:()=>n,C:()=>o})},66871:(e,t,r)=>{r.d(t,{C3:()=>s,JV:()=>o,K3:()=>u,MM:()=>l,OE:()=>f,Zu:()=>d,bj:()=>a,jc:()=>c,kd:()=>i});var n=r(96679);function o(){return n.Kn?.state||{}}function a(e){p(o(),"",e)}function i(e){n.Kn?.pushState({appId:o().appId},"",e),m()}function s(e){p({...o(),...e},"",location.href)}function l(e){a(`?${e.toString()}${n.fV.hash}`)}function c(){a(n.fV.pathname+n.fV.hash)}function d(e){a(e.startsWith("#")?e:`#${e}`)}function u(){a(n.fV.pathname+n.fV.search)}function f(){n.Kn?.back()}function p(e,t,r){n.Kn?.replaceState(e,t,r),m()}function m(){n.cg?.dispatchEvent(new CustomEvent("statechange",{bubbles:!1,cancelable:!1}))}},65461:(e,t,r)=>{r.d(t,{JC:()=>n.JC,KK:()=>n.KK,SK:()=>a,Vy:()=>n.Vy,ai:()=>n.ai,oc:()=>n.oc,rd:()=>n.rd});var n=r(50515);let o=/(?:^|,)((?:[^,]|,(?=\+| |$))*(?:,(?=,))?)/g;function a(e){return Array.from(e.matchAll(o)).map(([,e])=>e)}},36175:(e,t,r)=>{r.d(t,{Ff:()=>l,eC:()=>c,uE:()=>s});var n=r(6986);let o=!1,a=new n.A;function i(e){let t=e.target;if(t instanceof HTMLElement&&t.nodeType!==Node.DOCUMENT_NODE)for(let e of a.matches(t))e.data.call(null,t)}function s(e,t){o||(o=!0,document.addEventListener("focus",i,!0)),a.add(e,t),document.activeElement instanceof HTMLElement&&document.activeElement.matches(e)&&t(document.activeElement)}function l(e,t,r){function n(t){let o=t.currentTarget;o&&(o.removeEventListener(e,r),o.removeEventListener("blur",n))}s(t,function(t){t.addEventListener(e,r),t.addEventListener("blur",n)})}function c(e,t){function r(e){let{currentTarget:n}=e;n&&(n.removeEventListener("input",t),n.removeEventListener("blur",r))}s(e,function(e){e.addEventListener("input",t),e.addEventListener("blur",r)})}},1739:(e,t,r)=>{r.d(t,{B:()=>n});function n(e,t){let r=e.createElement("template");return r.innerHTML=t,e.importNode(r.content,!0)}},12559:(e,t,r)=>{r.d(t,{Ax:()=>o.Ax,JW:()=>a,ZV:()=>o.ZV});var n=r(26559),o=r(13937);function a(e,t){(0,o.JW)(e,async(e,r,o)=>((0,n.tV)(o.headers),t(e,r,o)))}},24852:(e,t,r)=>{r.d(t,{Ai:()=>a,Gq:()=>n,SO:()=>o});let{getItem:n,setItem:o,removeItem:a}=(0,r(85351).A)("sessionStorage")},46320:(e,t,r)=>{r.d(t,{Kq:()=>SoftNavErrorEvent,RQ:()=>SoftNavEndEvent,gh:()=>SoftNavPayloadEvent,ni:()=>SoftNavSuccessEvent,sW:()=>SoftNavStartEvent});var n=r(21715);function o(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let a=class SoftNavEvent extends Event{constructor(e,t){super(t),o(this,"mechanism",void 0),this.mechanism=e}};let SoftNavStartEvent=class SoftNavStartEvent extends a{constructor(e){super(e,n.z.START)}};let SoftNavSuccessEvent=class SoftNavSuccessEvent extends a{constructor(e,t){super(e,n.z.SUCCESS),o(this,"visitCount",void 0),this.visitCount=t}};let SoftNavErrorEvent=class SoftNavErrorEvent extends a{constructor(e,t){super(e,n.z.ERROR),o(this,"error",void 0),this.error=t}};let SoftNavEndEvent=class SoftNavEndEvent extends a{constructor(e){super(e,n.z.END)}};let SoftNavPayloadEvent=class SoftNavPayloadEvent extends Event{constructor(e){super("soft-nav:payload"),o(this,"payload",void 0),o(this,"appPayload",void 0),this.payload=e.payload,this.appPayload=e.appPayload}}},97396:(e,t,r)=>{r.d(t,{Bu:()=>m,SC:()=>c,Ti:()=>f,iS:()=>d,k5:()=>l,o4:()=>u,rZ:()=>p});var n=r(21715),o=r(46320),a=r(7522),i=r(78284);let s=0;function l(){s=0,document.dispatchEvent(new Event(n.z.INITIAL)),(0,i.xT)()}function c(e){(0,i.LM)()||(document.dispatchEvent(new Event(n.z.PROGRESS_BAR.START)),document.dispatchEvent(new o.sW(e)),(0,i.Vy)(e),(0,i.ZW)(),(0,i.HK)(),(0,a.E5)())}function d(e={}){v(e)&&(s+=1,document.dispatchEvent(new o.ni((0,i.di)(),s)),f(e))}function u(e={}){if(!v(e))return;s=0;let t=(0,i.my)()||i.BW;document.dispatchEvent(new o.Kq((0,i.di)(),t)),h(),(0,a.Cd)(t),(0,i.xT)()}function f(e={}){if(!v(e))return;let t=(0,i.di)();h(),document.dispatchEvent(new o.RQ(t)),(0,i.Ff)(),(0,i.JA)(t)}function p(e={}){v(e)&&((0,a.Im)(),document.dispatchEvent(new Event(n.z.RENDER)))}function m(){document.dispatchEvent(new Event(n.z.FRAME_UPDATE))}function h(){document.dispatchEvent(new Event(n.z.PROGRESS_BAR.END))}function v({skipIfGoingToReactApp:e,allowedMechanisms:t=[]}={}){return(0,i.LM)()&&(0===t.length||t.includes((0,i.di)()))&&(!e||!(0,i.gc)())}},21715:(e,t,r)=>{r.d(t,{z:()=>n});let n=Object.freeze({INITIAL:"soft-nav:initial",START:"soft-nav:start",REPLACE_MECHANISM:"soft-nav:replace-mechanism",SUCCESS:"soft-nav:success",ERROR:"soft-nav:error",FRAME_UPDATE:"soft-nav:frame-update",END:"soft-nav:end",RENDER:"soft-nav:render",PROGRESS_BAR:{START:"soft-nav:progress-bar:start",END:"soft-nav:progress-bar:end"}})},7522:(e,t,r)=>{r.d(t,{Cd:()=>l,E5:()=>s,Im:()=>c,nW:()=>i});var n=r(7479),o=r(78284);let a="stats:soft-nav-duration",i={turbo:"TURBO",react:"REACT","turbo.frame":"FRAME",ui:"UI",hard:"HARD"};function s(){window.performance.clearResourceTimings(),window.performance.mark(a)}function l(e){(0,n.i)({turboFailureReason:e,turboStartUrl:(0,o.dR)(),turboEndUrl:window.location.href})}function c(){let e=function(){if(0===performance.getEntriesByName(a).length)return null;performance.measure(a,a);let e=performance.getEntriesByName(a).pop();return e?e.duration:null}();if(!e)return;let t=i[(0,o.di)()],r=Math.round(e);t===i.react&&document.dispatchEvent(new CustomEvent("staffbar-update",{detail:{duration:r}})),(0,n.i)({requestUrl:window.location.href,softNavigationTiming:{mechanism:t,destination:(0,o.fX)()||"rails",duration:r,initiator:(0,o.Pv)()||"rails"}})}},78284:(e,t,r)=>{r.d(t,{BW:()=>o,Ff:()=>p,HK:()=>b,JA:()=>_,LM:()=>m,Pv:()=>j,Vy:()=>f,ZW:()=>E,dR:()=>S,di:()=>h,fX:()=>w,gc:()=>A,k9:()=>y,my:()=>g,r7:()=>C,wG:()=>v,xT:()=>u});var n=r(24852);let o="reload",a="soft-nav:fail",i="soft-nav:fail-referrer",s="soft-nav:referrer",l="soft-nav:marker",c="soft-nav:react-app-name",d="soft-nav:latest-mechanism";function u(){(0,n.SO)(l,"0"),(0,n.Ai)(s),(0,n.Ai)(a),(0,n.Ai)(i),(0,n.Ai)(c),(0,n.Ai)(d)}function f(e){(0,n.SO)(l,e)}function p(){(0,n.SO)(l,"0")}function m(){let e=(0,n.Gq)(l);return e&&"0"!==e}function h(){return(0,n.Gq)(l)}function v(){return!!g()}function g(){return(0,n.Gq)(a)}function y(e){(0,n.SO)(a,e||o),(0,n.SO)(i,window.location.href)}function b(){(0,n.SO)(s,window.location.href)}function S(){return(0,n.Gq)(s)||document.referrer}function E(){let e=w();e?(0,n.SO)(c,e):(0,n.Ai)(c)}function j(){return(0,n.Gq)(c)}function w(){return document.querySelector('react-partial[partial-name="repos-overview"]')?"repos-overview":document.querySelector("react-app")?.getAttribute("app-name")}function A(){return!!document.querySelector("react-app")?.getAttribute("app-name")}function _(e){(0,n.SO)(d,e)}function C(){return(0,n.Gq)(d)}},59519:(e,t,r)=>{r.d(t,{softNavigate:()=>a});var n=r(97396),o=r(7332);let a=(e,t)=>{(0,n.SC)("turbo"),(0,o.YR)(e,{...t})}},22247:(e,t,r)=>{function n(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}r.d(t,{Rt:()=>o,Zz:()=>a,yU:()=>Subscription});let Subscription=class Subscription{constructor(e){n(this,"closed",void 0),n(this,"unsubscribe",void 0),this.closed=!1,this.unsubscribe=()=>{e(),this.closed=!0}}};function o(e,t,r,n={capture:!1}){return e.addEventListener(t,r,n),new Subscription(()=>{e.removeEventListener(t,r,n)})}function a(...e){return new Subscription(()=>{for(let t of e)t.unsubscribe()})}},35707:(e,t,r)=>{function n(e){let t=e.split("\u200D"),r=0;for(let e of t)r+=Array.from(e.split(/[\ufe00-\ufe0f]/).join("")).length;return r/t.length}function o(e,t,r,n=!0){let a=e.value.substring(0,e.selectionEnd||0),i=e.value.substring(e.selectionEnd||0);return s(e,(a=a.replace(t,r))+(i=i.replace(t,r)),a.length,n),r}function a(e,t,r){if(null===e.selectionStart||null===e.selectionEnd)return o(e,t,r);let n=e.value.substring(0,e.selectionStart),a=e.value.substring(e.selectionEnd);return s(e,n+r+a,n.length),r}function i(e,t,r={}){let n=e.selectionEnd||0,o=e.value.substring(0,n),a=e.value.substring(n),s=(""===e.value||o.match(/\n$/)?"":`
`)+t+(r.appendNewline?`
`:"");e.value=o+s+a;let l=n+s.length;return e.selectionStart=l,e.selectionEnd=l,e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1})),e.focus(),s}function s(e,t,r,n=!0){e.value=t,n&&(e.selectionStart=r,e.selectionEnd=r),e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1}))}function l(e,t){let r=[...e],n=new TextEncoder,o=new Uint8Array(4);for(let e=0;e<r.length;e++){let a=r[e],{written:i,read:s}=n.encodeInto(a,o);if(!i||!s)return -1;let l=i-s;if(0!==l&&(e<t&&(t-=l),e>=t))break}return t}r.d(t,{bV:()=>n,bc:()=>i,ee:()=>o,kN:()=>l,tJ:()=>a})}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_github_selector-observer_dist_index_esm_js","vendors-node_modules_morphdom_dist_morphdom-esm_js","vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_virtualized-list_es_inde-5cfb7e","vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-3eebbd","vendors-node_modules_github_mini-throttle_dist_decorators_js-node_modules_delegated-events_di-e161aa","vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_github_remote--3c9c82","app_assets_modules_github_ref-selector_ts"],()=>t(40151)),e.O()}]);
//# sourceMappingURL=repositories-faad5232052e.js.map