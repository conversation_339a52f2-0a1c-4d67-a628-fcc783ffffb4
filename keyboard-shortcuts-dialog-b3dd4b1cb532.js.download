"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["keyboard-shortcuts-dialog","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-4da1df"],{78924:(e,t,n)=>{n.d(t,{I:()=>r});let r=(0,n(96540).createContext)(null)},52811:(e,t,n)=>{n.d(t,{C:()=>a,i:()=>s});var r=n(96679),o=n(27851),i=n(46493);function a(e,t){(0,o.G7)("arianotify_comprehensive_migration")?s(l(e),{...t,element:t?.element??e}):(0,o.G7)("primer_live_region_element")&&t?.element===void 0?(0,i.Cj)(e,{politeness:t?.assertive?"assertive":"polite"}):s(l(e),t)}function s(e,t){let{assertive:n,element:a}=t??{};(0,o.G7)("arianotify_comprehensive_migration")&&"ariaNotify"in Element.prototype?(a||document.body).ariaNotify(e):(0,o.G7)("primer_live_region_element")&&void 0===a?(0,i.iP)(e,{politeness:n?"assertive":"polite"}):function(e,t,n){let o=n??r.XC?.querySelector(t?"#js-global-screen-reader-notice-assertive":"#js-global-screen-reader-notice");o&&(o.textContent===e?o.textContent=`${e}\u00A0`:o.textContent=e)}(e,n,a)}function l(e){return(e.getAttribute("aria-label")||e.innerText||"").trim()}},53005:(e,t,n)=>{n.d(t,{O:()=>a,S:()=>i});var r=n(96679);let o=r.cg?.document?.head?.querySelector('meta[name="release"]')?.content||"",i="X-GitHub-Client-Version";function a(){return o}},39627:(e,t,n)=>{n.d(t,{D:()=>i,Y:()=>a});var r=n(52811),o=n(96679);function i(e){if(!o.XC)return;let t=o.XC.querySelector("title"),n=o.XC.createElement("title");n.textContent=e,t?t.textContent!==e&&(t.replaceWith(n),(0,r.i)(e)):(o.XC.head.appendChild(n),(0,r.i)(e))}function a(e){return document.body.classList.contains("logged-out")?`${e} \xb7 GitHub`:e}},26559:(e,t,n)=>{n.d(t,{jC:()=>l,kt:()=>a,tV:()=>s});var r=n(53005),o=n(27851),i=n(88191);function a(e){let t={"X-Requested-With":"XMLHttpRequest",...(0,i.wE)(e)};return(0,o.G7)("client_version_header")&&(t={...t,[r.S]:(0,r.O)()}),t}function s(e,t){for(let[n,r]of Object.entries(a(t)))e.set(n,r)}function l(e){return{"X-GitHub-App-Type":e}}},88191:(e,t,n)=>{n.d(t,{$r:()=>a,M1:()=>s,li:()=>o,pS:()=>c,wE:()=>l});var r=n(96679);let o="X-Fetch-Nonce",i=new Set;function a(e){i.add(e)}function s(){return i.values().next().value||""}function l(e){let t={};return void 0!==e&&(t["X-Fetch-Nonce-To-Validate"]=e),void 0===e?t[o]=s():i.has(e)?t[o]=e:t[o]=Array.from(i).join(","),t}function c(){let e=r.XC?.head?.querySelector('meta[name="fetch-nonce"]')?.content||"";e&&a(e)}},38007:(e,t,n)=>{let r;n.d(t,{BI:()=>f,Ti:()=>p,lA:()=>h,sX:()=>m});var o=n(70837),i=n(18679),a=n(85351),s=n(7479);let{getItem:l}=(0,a.A)("localStorage"),c="dimension_",d=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","scid"];try{let e=(0,o.O)("octolytics");delete e.baseContext,r=new i.s(e)}catch{}function u(e){let t=(0,o.O)("octolytics").baseContext||{};if(t)for(let[e,n]of(delete t.app_id,delete t.event_url,delete t.host,Object.entries(t)))e.startsWith(c)&&(t[e.replace(c,"")]=n,delete t[e]);let n=document.querySelector("meta[name=visitor-payload]");for(let[e,r]of(n&&Object.assign(t,JSON.parse(atob(n.content))),new URLSearchParams(window.location.search)))d.includes(e.toLowerCase())&&(t[e]=r);return t.staff=(0,s.X)().toString(),Object.assign(t,e)}function h(e){r?.sendPageView(u(e))}function m(){return document.head?.querySelector('meta[name="current-catalog-service"]')?.content}function f(e,t={}){let n=m(),o=n?{service:n}:{};for(let[e,n]of Object.entries(t))null!=n&&(o[e]=`${n}`);r&&(u(o),r.sendEvent(e||"unknown",u(o)))}function p(e){return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,JSON.stringify(t)]))}},53442:(e,t,n)=>{var r=n(52497),o=n(74848),i=n(96540),a=n(42080),s=n(63867),l=n(55847),c=n(2724),d=n(96339);let u={ShortcutsGroupContainer:"ShortcutsGroupList-module__ShortcutsGroupContainer--joTOo",ShortcutsGroupHeader:"ShortcutsGroupList-module__ShortcutsGroupHeader--zr2Wb",ShortcutsList:"ShortcutsGroupList-module__ShortcutsList--B_HHQ",ShortcutItem:"ShortcutsGroupList-module__ShortcutItem--QB_cf",KeybindingContainer:"ShortcutsGroupList-module__KeybindingContainer--f21bV"};function h({group:{service:{name:e},commands:t}}){let n=(0,i.useId)();return(0,o.jsxs)("div",{className:u.ShortcutsGroupContainer,children:[(0,o.jsx)("h2",{id:n,className:u.ShortcutsGroupHeader,children:e}),(0,o.jsx)("ul",{role:"list","aria-labelledby":n,className:u.ShortcutsList,children:t.map(({id:e,name:t,keybinding:n})=>(0,o.jsxs)("li",{className:u.ShortcutItem,children:[(0,o.jsx)("div",{children:t}),(0,o.jsx)("div",{className:u.KeybindingContainer,children:(Array.isArray(n)?n:[n]).map((e,t)=>(0,o.jsxs)(i.Fragment,{children:[t>0&&" or ",(0,o.jsx)(d.U,{keys:e})]},e))})]},e))})]})}try{h.displayName||(h.displayName="ShortcutsGroupList")}catch{}let m={keyboardShortcuts:"Keyboard shortcuts",siteWideShortcuts:"Site-wide shortcuts",loading:"Loading"};n(45356);var f=n(73566),p=n(60039),g=n(65461);let y={LoadingStateContainer:"ShortcutsDialog-module__LoadingStateContainer--KaIsw",ColumnsContainer:"ShortcutsDialog-module__ColumnsContainer--yXxkn",Column:"ShortcutsDialog-module__Column--r8mAi",ShortcutsDialogRoot:"ShortcutsDialog-module__ShortcutsDialogRoot--JUCV9",FullWidthButton:"ShortcutsDialog-module__FullWidthButton--zRqTp"},b=()=>(0,o.jsxs)("div",{role:"status",className:y.LoadingStateContainer,children:[(0,o.jsx)(s.A,{size:"large"}),(0,o.jsx)("span",{className:"sr-only",children:m.loading})]}),v=(e,t)=>Array.isArray(e)?t?e.map(e=>(0,g.rd)(e)):e.map(e=>(0,g.rd)(e.replace(/ctrl/,"Mod+"))):t?(0,g.rd)(e??""):(0,g.rd)(e?.replace(/ctrl/,"Mod+")??""),_=({children:e})=>(0,o.jsx)("div",{className:y.ColumnsContainer,children:e}),w=({children:e})=>(0,o.jsx)("div",{className:y.Column,children:e}),C=({visible:e,onVisibleChange:t,docsUrl:n})=>{let[r,a]=(0,i.useState)({service:{id:"global",name:"Global"},commands:[]}),[s,d]=(0,i.useState)([]),[u,g]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{let t=(0,f.c)().filter(e=>"github"!==e.service.id),n=async()=>{g(!0);let e=document.querySelector("meta[name=github-keyboard-shortcuts]");if(!e)throw Error('The "github-keyboard-shortcuts" meta tag must be present');let n={contexts:e.content.replace(/-/g,"_")},r=`/site/keyboard_shortcuts?${new URLSearchParams(n).toString()}`,o=await (0,p.lS)(r,{method:"GET"});if(o.ok){let{global:e,...n}=(await o.json()).commands;a({service:{id:"global",name:m.siteWideShortcuts},commands:[...e.commands,...t.find(e=>"global"===e.service.id)?.commands??[]].map(e=>({...e,keybinding:v(e.keybinding,e.alwaysCtrl)}))}),d([...Object.values(n),...t].map(e=>({...e,commands:e.commands.map(e=>({...e,keybinding:v(e.keybinding,e.alwaysCtrl)}))})))}else d(t.map(e=>({...e,commands:e.commands.map(e=>({...e,keybinding:v(e.keybinding,e.alwaysCtrl)}))})));g(!1)};e&&n()},[e]),e)?(0,o.jsx)(c.l,{title:m.keyboardShortcuts,"aria-modal":"true",width:"xlarge",height:"large",onClose:()=>t(!1),className:y.ShortcutsDialogRoot,children:u?(0,o.jsx)(b,{}):(0,o.jsxs)(_,{children:[(0,o.jsx)(w,{children:s.map(e=>(0,o.jsx)(h,{group:e},e.service.id))}),(0,o.jsxs)(w,{children:[(0,o.jsx)(h,{group:r},r.service.id),(0,o.jsx)(l.Q,{as:"a",href:n,className:y.FullWidthButton,children:"View all keyboard shortcuts"})]})]})}):null};try{b.displayName||(b.displayName="LoadingState")}catch{}try{_.displayName||(_.displayName="Columns")}catch{}try{w.displayName||(w.displayName="Column")}catch{}try{C.displayName||(C.displayName="ShortcutsDialog")}catch{}function S({docsUrl:e}){let[t,n]=(0,i.useState)(!1);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(a.ak,{commands:{"keyboard-shortcuts-dialog:show-dialog":()=>n(!0)}}),(0,o.jsx)(C,{visible:t,onVisibleChange:n,docsUrl:e})]})}try{S.displayName||(S.displayName="KeyboardShortcutsDialog")}catch{}(0,r.k)("keyboard-shortcuts-dialog",{Component:S})},13233:(e,t,n)=>{n.d(t,{l:()=>r});let r=()=>void 0},7531:(e,t,n)=>{n.d(t,{Y:()=>r});function r(){let e={};return e.promise=new Promise((t,n)=>{e.resolve=t,e.reject=n}),e}},41764:(e,t,n)=>{n.d(t,{A:()=>s});let{getItem:r,setItem:o,removeItem:i}=(0,n(85351).A)("localStorage"),a="REACT_PROFILING_ENABLED",s={enable:()=>o(a,"true"),disable:()=>i(a),isEnabled:()=>!!r(a)}},64899:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(17515),o=n(96540);function i(){let e=(0,o.useRef)(!1),t=(0,o.useCallback)(()=>e.current,[]);return(0,r.N)(()=>(e.current=!0,()=>{e.current=!1}),[]),t}},17515:(e,t,n)=>{n.d(t,{N:()=>i});var r=n(96679),o=n(96540);let i=void 0!==r.cg?.document?.createElement?o.useLayoutEffect:o.useEffect},47019:(e,t,n)=>{n.d(t,{A:()=>i});var r=n(64899),o=n(96540);let i=function(e){let t=(0,r.A)(),[n,i]=(0,o.useState)(e);return[n,(0,o.useCallback)(e=>{t()&&i(e)},[t])]}},60039:(e,t,n)=>{n.d(t,{DI:()=>o,QJ:()=>a,Sr:()=>s,lS:()=>i});var r=n(26559);function o(e,t={}){var n=e;if(new URL(n,window.location.origin).origin!==window.location.origin)throw Error("Can not make cross-origin requests from verifiedFetch");let i=function(e){let t=new URL(e,window.location.href),n=new URL(window.location.href,window.location.origin),r=n.searchParams.get("_features");r&&!t.searchParams.has("_features")&&t.searchParams.set("_features",r);let o=n.searchParams.get("_tracing");return o&&!t.searchParams.has("_tracing")&&t.searchParams.set("_tracing",o),e.startsWith(window.location.origin)?t.href:`${t.pathname}${t.search}`}(e),a={...t.headers,"GitHub-Verified-Fetch":"true",...(0,r.kt)()};return fetch(i,{...t,headers:a})}function i(e,t){let n={...t?.headers??{},Accept:"application/json","Content-Type":"application/json"},r=t?.body?JSON.stringify(t.body):void 0;return o(e,{...t,body:r,headers:n})}function a(e,t={}){let n={...t.headers,"GitHub-Is-React":"true"};return o(e,{...t,headers:n})}function s(e,t){let n={...t?.headers??{},"GitHub-Is-React":"true"};return i(e,{...t,headers:n})}},26033:(e,t,n)=>{n.d(t,{y:()=>a});var r=n(74848),o=n(21728),i=n(78924);function a(e){let t,n,a,s=(0,o.c)(7),{children:l,appName:c,category:d,metadata:u}=e;return s[0]!==c||s[1]!==d||s[2]!==u?(n={appName:c,category:d,metadata:u},s[0]=c,s[1]=d,s[2]=u,s[3]=n):n=s[3],t=n,s[4]!==l||s[5]!==t?(a=(0,r.jsx)(i.I.Provider,{value:t,children:l}),s[4]=l,s[5]=t,s[6]=a):a=s[6],a}try{a.displayName||(a.displayName="AnalyticsProvider")}catch{}},60674:(e,t,n)=>{n.d(t,{BP:()=>u,D3:()=>d,O8:()=>l});var r=n(74848),o=n(21728),i=n(96540),a=n(96679),s=n(17515);let l={ServerRender:"ServerRender",ClientHydrate:"ClientHydrate",ClientRender:"ClientRender"},c=(0,i.createContext)(l.ClientRender);function d(e){let t,n,d,u,h=(0,o.c)(8),{wasServerRendered:m,children:f}=e;h[0]!==m?(t=()=>a.X3?l.ServerRender:m?l.ClientHydrate:l.ClientRender,h[0]=m,h[1]=t):t=h[1];let[p,g]=(0,i.useState)(t);return h[2]!==p?(n=()=>{p!==l.ClientRender&&g(l.ClientRender)},d=[p],h[2]=p,h[3]=n,h[4]=d):(n=h[3],d=h[4]),(0,s.N)(n,d),h[5]!==f||h[6]!==p?(u=(0,r.jsx)(c.Provider,{value:p,children:f}),h[5]=f,h[6]=p,h[7]=u):u=h[7],u}function u(){return(0,i.useContext)(c)}try{c.displayName||(c.displayName="RenderPhaseContext")}catch{}try{d.displayName||(d.displayName="RenderPhaseProvider")}catch{}},99543:(e,t,n)=>{n.d(t,{Qn:()=>l,T8:()=>d,Y6:()=>h,k6:()=>u});var r=n(74848),o=n(65556),i=n(96540),a=n(13233),s=n(47019);let l=5e3,c=(0,i.createContext)({addToast:a.l,addPersistedToast:a.l,clearPersistedToast:a.l}),d=(0,i.createContext)({toasts:[],persistedToast:null});function u({children:e}){let[t,n]=(0,s.A)([]),[a,u]=(0,i.useState)(null),{safeSetTimeout:h}=(0,o.A)(),m=(0,i.useCallback)(function(e){n([...t,e]),h(()=>n(t.slice(1)),l)},[t,h,n]),f=(0,i.useCallback)(function(e){u(e)},[u]),p=(0,i.useCallback)(function(){u(null)},[u]),g=(0,i.useMemo)(()=>({addToast:m,addPersistedToast:f,clearPersistedToast:p}),[f,m,p]),y=(0,i.useMemo)(()=>({toasts:t,persistedToast:a}),[t,a]);return(0,r.jsx)(c.Provider,{value:g,children:(0,r.jsx)(d.Provider,{value:y,children:e})})}function h(){return(0,i.useContext)(c)}try{c.displayName||(c.displayName="ToastContext")}catch{}try{d.displayName||(d.displayName="InternalToastsContext")}catch{}try{u.displayName||(u.displayName="ToastContextProvider")}catch{}},42218:(e,t,n)=>{n.d(t,{V:()=>h});var r=n(74848),o=n(96540),i=n(99543),a=n(38621),s=n(65556),l=n(16255);let c={info:"",success:"Toast--success",error:"Toast--error"},d={info:(0,r.jsx)(a.InfoIcon,{}),success:(0,r.jsx)(a.CheckIcon,{}),error:(0,r.jsx)(a.StopIcon,{})},u=({message:e,timeToLive:t,icon:n,type:i="info",role:a="log"})=>{let[u,h]=o.useState(!0),{safeSetTimeout:m}=(0,s.A)();return(0,o.useEffect)(()=>{t&&m(()=>h(!1),t-300)},[m,t]),(0,r.jsx)(l.Z,{children:(0,r.jsx)("div",{className:"p-1 position-fixed bottom-0 left-0 mb-3 ml-3",children:(0,r.jsxs)("div",{className:`Toast ${c[i]} ${u?"Toast--animateIn":"Toast--animateOut"}`,id:"ui-app-toast","data-testid":`ui-app-toast-${i}`,role:a,children:[(0,r.jsx)("span",{className:"Toast-icon",children:n||d[i]}),(0,r.jsx)("span",{className:"Toast-content",children:e})]})})})};try{u.displayName||(u.displayName="Toast")}catch{}function h(){let{toasts:e,persistedToast:t}=(0,o.useContext)(i.T8);return(0,r.jsxs)(r.Fragment,{children:[e.map((e,t)=>(0,r.jsx)(u,{message:e.message,icon:e.icon,timeToLive:i.Qn,type:e.type,role:e.role},t)),t&&(0,r.jsx)(u,{message:t.message,icon:t.icon,type:t.type,role:t.role})]})}try{h.displayName||(h.displayName="Toasts")}catch{}},39595:(e,t,n)=>{let r;n.d(t,{CF:()=>p,p_:()=>T,FB:()=>u,Se:()=>x,aC:()=>A,zV:()=>E});let o=new WeakSet,i=new WeakMap;function a(e=document){if(i.has(e))return i.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)d(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&s(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let r={get closed(){return t},unsubscribe(){t=!0,i.delete(e),n.disconnect()}};return i.set(e,r),r}function s(e){for(let t of e.querySelectorAll("[data-action]"))d(t);e instanceof Element&&e.hasAttribute("data-action")&&d(e)}function l(e){let t=e.currentTarget;for(let n of c(t))if(e.type===n.type){let r=t.closest(n.tag);o.has(r)&&"function"==typeof r[n.method]&&r[n.method](e);let i=t.getRootNode();if(i instanceof ShadowRoot&&o.has(i.host)&&i.host.matches(n.tag)){let t=i.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function d(e){for(let t of c(e))e.addEventListener(t.type,l)}function u(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let r of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!r.closest(n))return r}for(let r of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(r.closest(n)===e)return r}let h=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),m=(e,t="property")=>{let n=h(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},f="attr";function p(e,t){N(e,f).add(t)}let g=new WeakSet;function y(e,t){if(g.has(e))return;g.add(e);let n=Object.getPrototypeOf(e),r=n?.constructor?.attrPrefix??"data-";for(let o of(t||(t=N(n,f)),t)){let t=e[o],n=m(`${r}${o}`),i={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?i={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(i={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,o,i),o in e&&!e.hasAttribute(n)&&i.set.call(e,t)}}let b=new Map,v=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),_=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},r=()=>t.abort();document.addEventListener("mousedown",r,n),document.addEventListener("touchstart",r,n),document.addEventListener("keydown",r,n),document.addEventListener("pointerdown",r,n)}),w={ready:()=>v,firstInteraction:()=>_,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let r of e)if(r.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},C=new WeakMap;function S(e){cancelAnimationFrame(C.get(e)||0),C.set(e,requestAnimationFrame(()=>{for(let t of b.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let r=n?.getAttribute("data-load-on")||"ready",o=r in w?w[r]:w.ready;for(let e of b.get(t)||[])o(t).then(e);b.delete(t),C.delete(e)}}}))}function x(e,t){for(let[n,r]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))b.has(n)||b.set(n,new Set),b.get(n).add(r);j(document)}function j(e){r||(r=new MutationObserver(e=>{if(b.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&S(e)})),S(e),r.observe(e,{subtree:!0,childList:!0})}let k=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let r=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,r)};let o=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,r){t.attributeChangedCallback(this,e,n,r,o)};let i=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,i)},set(e){i=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",r=e=>m(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...N(e.prototype,f)].map(r).concat(t),set(e){t=e}})}(e),function(e){let t=h(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,r;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(y(e),o.add(e),e.shadowRoot&&(s(r=e.shadowRoot),a(r)),s(e),a(e.ownerDocument),t?.call(e),e.shadowRoot)&&(s(n=e.shadowRoot),a(n),j(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,r,o){y(e),"data-catalyst"!==t&&o&&o.call(e,t,n,r)}};function N(e,t){if(!Object.prototype.hasOwnProperty.call(e,k)){let t=e[k],n=e[k]=new Map;if(t)for(let[e,r]of t)n.set(e,new Set(r))}let n=e[k];return n.has(t)||n.set(t,new Set),n.get(t)}function A(e,t){N(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return u(this,t)}})}function E(e,t){N(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let r of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))r.closest(e)||n.push(r);for(let r of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))r.closest(e)===this&&n.push(r);return n}})}function T(e){new CatalystDelegate(e)}},50515:(e,t,n)=>{n.d(t,{JC:()=>b,KK:()=>SequenceTracker,Vy:()=>a,ai:()=>y,oc:()=>l,rd:()=>d});let Leaf=class Leaf{constructor(e){this.children=[],this.parent=e}delete(e){let t=this.children.indexOf(e);return -1!==t&&(this.children=this.children.slice(0,t).concat(this.children.slice(t+1)),0===this.children.length&&this.parent.delete(this),!0)}add(e){return this.children.push(e),this}};let RadixTrie=class RadixTrie{constructor(e){this.parent=null,this.children={},this.parent=e||null}get(e){return this.children[e]}insert(e){let t=this;for(let n=0;n<e.length;n+=1){let r=e[n],o=t.get(r);if(n===e.length-1)return o instanceof RadixTrie&&(t.delete(o),o=null),o||(o=new Leaf(t),t.children[r]=o),o;o instanceof Leaf&&(o=null),o||(o=new RadixTrie(t),t.children[r]=o),t=o}return t}delete(e){for(let t in this.children)if(this.children[t]===e){let e=delete this.children[t];return 0===Object.keys(this.children).length&&this.parent&&this.parent.delete(this),e}return!1}};let r={"\xa1":"1","\u2122":"2","\xa3":"3","\xa2":"4","\u221E":"5","\xa7":"6","\xb6":"7","\u2022":"8","\xaa":"9","\xba":"0","\u2013":"-","\u2260":"=","\u2044":"!","\u20AC":"@","\u2039":"#","\u203A":"$",\uFB01:"%",\uFB02:"^","\u2021":"&","\xb0":"*","\xb7":"(","\u201A":")","\u2014":"_","\xb1":"+",\u0153:"q","\u2211":"w","\xae":"r","\u2020":"t","\xa5":"y","\xf8":"o",\u03C0:"p","\u201C":"[","\u2018":"]","\xab":"\\",\u0152:"Q","\u201E":"W","\xb4":"E","\u2030":"R",\u02C7:"T","\xc1":"Y","\xa8":"U",\u02C6:"I","\xd8":"O","\u220F":"P","\u201D":"{","\u2019":"}","\xbb":"|","\xe5":"a","\xdf":"s","\u2202":"d",\u0192:"f","\xa9":"g","\u02D9":"h","\u2206":"j","\u02DA":"k","\xac":"l","\u2026":";","\xe6":"'","\xc5":"A","\xcd":"S","\xce":"D","\xcf":"F","\u02DD":"G","\xd3":"H","\xd4":"J","\uF8FF":"K","\xd2":"L","\xda":":","\xc6":'"',\u03A9:"z","\u2248":"x","\xe7":"c","\u221A":"v","\u222B":"b","\xb5":"m","\u2264":",","\u2265":".","\xf7":"/","\xb8":"Z","\u02DB":"X","\xc7":"C","\u25CA":"V",\u0131:"B","\u02DC":"N","\xc2":"M","\xaf":"<","\u02D8":">","\xbf":"?"},o={"`":"~",1:"!",2:"@",3:"#",4:"$",5:"%",6:"^",7:"&",8:"*",9:"(",0:")","-":"_","=":"+","[":"{","]":"}","\\":"|",";":":","'":'"',",":"<",".":">","/":"?",q:"Q",w:"W",e:"E",r:"R",t:"T",y:"Y",u:"U",i:"I",o:"O",p:"P",a:"A",s:"S",d:"D",f:"F",g:"G",h:"H",j:"J",k:"K",l:"L",z:"Z",x:"X",c:"C",v:"V",b:"B",n:"N",m:"M"},i={" ":"Space","+":"Plus"};function a(e,t=navigator.platform){var n,l,d;let{ctrlKey:u,altKey:h,metaKey:m,shiftKey:f,key:p}=e,g=[];for(let[e,t]of[u,h,m,f].entries())t&&g.push(s[e]);if(!s.includes(p)){let e=g.includes("Alt")&&c.test(t)&&null!=(n=r[p])?n:p,a=g.includes("Shift")&&c.test(t)&&null!=(l=o[e])?l:e,s=null!=(d=i[a])?d:a;g.push(s)}return g.join("+")}let s=["Control","Alt","Meta","Shift"];function l(e,t){let n;var r=function(e,t){var n;let r="undefined"==typeof window?void 0:window,o=c.test(null!=(n=null!=t?t:null==r?void 0:r.navigator.platform)?n:"")?"Meta":"Control";return e.replace("Mod",o)}(e,t);let o=r.split("+").pop(),i=[];for(let e of["Control","Alt","Meta","Shift"])r.includes(e)&&i.push(e);return o&&i.push(o),i.join("+")}let c=/Mac|iPod|iPhone|iPad/i;let SequenceTracker=class SequenceTracker{constructor({onReset:e}={}){this._path=[],this.timer=null,this.onReset=e}get path(){return this._path}get sequence(){return this._path.join(" ")}registerKeypress(e){this._path=[...this._path,a(e)],this.startTimer()}reset(){var e;this.killTimer(),this._path=[],null==(e=this.onReset)||e.call(this)}killTimer(){null!=this.timer&&window.clearTimeout(this.timer),this.timer=null}startTimer(){this.killTimer(),this.timer=window.setTimeout(()=>this.reset(),SequenceTracker.CHORD_TIMEOUT)}};function d(e){return e.split(" ").map(e=>l(e)).join(" ")}function u(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==n&&"reset"!==n&&"checkbox"!==n&&"radio"!==n&&"file"!==n||e.isContentEditable}SequenceTracker.CHORD_TIMEOUT=1500;let h=new RadixTrie,m=new WeakMap,f=h,p=new SequenceTracker({onReset(){f=h}});function g(e){if(e.defaultPrevented||!(e.target instanceof Node))return;if(u(e.target)){let t=e.target;if(!t.id||!t.ownerDocument.querySelector(`[data-hotkey-scope="${t.id}"]`))return}let t=f.get(a(e));if(!t)return void p.reset();if(p.registerKeypress(e),f=t,t instanceof Leaf){let r,o=e.target,i=!1,a=u(o);for(let e=t.children.length-1;e>=0;e-=1){let n=(r=t.children[e]).getAttribute("data-hotkey-scope");if(!a&&!n||a&&o.id===n){i=!0;break}}if(r&&i){var n=r;let t=new CustomEvent("hotkey-fire",{cancelable:!0,detail:{path:p.path}});n.dispatchEvent(t)&&(u(n)?n.focus():n.click()),e.preventDefault()}p.reset()}}function y(e,t){0===Object.keys(h.children).length&&document.addEventListener("keydown",g);let n=(function(e){let t=[],n=[""],r=!1;for(let o=0;o<e.length;o++){if(r&&","===e[o]){t.push(n),n=[""],r=!1;continue}if(" "===e[o]){n.push(""),r=!1;continue}r="+"!==e[o],n[n.length-1]+=e[o]}return t.push(n),t.map(e=>e.map(e=>l(e)).filter(e=>""!==e)).filter(e=>e.length>0)})(t||e.getAttribute("data-hotkey")||"").map(t=>h.insert(t).add(e));m.set(e,n)}function b(e){let t=m.get(e);if(t&&t.length)for(let n of t)n&&n.delete(e);0===Object.keys(h.children).length&&document.removeEventListener("keydown",g)}},18679:(e,t,n)=>{n.d(t,{s:()=>AnalyticsClient});let r=["utm_source","utm_medium","utm_campaign","utm_term","utm_content","scid"];var o=n(36301);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,o.y)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...function(){let e={};try{for(let[t,n]of new URLSearchParams(window.location.search)){let o=t.toLowerCase();r.includes(o)&&(e[o]=n)}return e}catch(e){return{}}}(),...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n=JSON.stringify({client_id:this.clientId,page_views:e,events:t,request_context:{referrer:function(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}(),user_agent:navigator.userAgent,screen_resolution:function(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}(),browser_resolution:function(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}(),browser_languages:navigator.languages?navigator.languages.join(","):navigator.language||"",pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}});try{if(navigator.sendBeacon)return void navigator.sendBeacon(this.collectorUrl,n)}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:n,keepalive:!1})}}},70837:(e,t,n)=>{n.d(t,{O:()=>r});function r(e="ha"){let t,n={};for(let r of Array.from(document.head.querySelectorAll(`meta[name^="${e}-"]`))){let{name:o,content:i}=r,a=o.replace(`${e}-`,"").replace(/-/g,"_");"url"===a?t=i:n[a]=i}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}}},e=>{var t=t=>e(e.s=t);e.O(0,["primer-react","react-core","react-lib","octicons-react","vendors-node_modules_oddbird_popover-polyfill_dist_popover-fn_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_stacktrace-parser_dist_s-1d3d52","vendors-node_modules_primer_behaviors_dist_esm_index_mjs","vendors-node_modules_emotion_is-prop-valid_dist_emotion-is-prop-valid_esm_js-node_modules_emo-b1c483","vendors-node_modules_cookie_index_js-node_modules_primer_live-region-element_dist_esm_index_j-1ca8f6","ui_packages_failbot_failbot_ts","ui_packages_ui-commands_ui-commands_ts"],()=>t(53442)),e.O()}]);
//# sourceMappingURL=keyboard-shortcuts-dialog-88d2af820c04.js.map