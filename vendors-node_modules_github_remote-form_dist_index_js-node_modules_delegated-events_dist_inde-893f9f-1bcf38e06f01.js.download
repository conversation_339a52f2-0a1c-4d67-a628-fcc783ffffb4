"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f"],{13937:(e,t,n)=>{let r;n.d(t,{Ax:()=>u,JW:()=>c,ZV:()=>a});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function i(){let e,t;return[new Promise(function(n,r){e=n,t=r}),e,t]}let o=[],s=[];function a(e){o.push(e)}function u(e){s.push(e)}function c(e,t){r||(r=new Map,"undefined"!=typeof document&&document.addEventListener("submit",l));let n=r.get(e)||[];r.set(e,[...n,t])}function l(e){let t;if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let n=e.target,a=function(e){let t=[],n=t=>"object"==typeof t?t===e:"string"==typeof t&&e.matches(t);for(let e of r.keys())if(n(e)){let n=r.get(e)||[];t.push(...n)}return t}(n);if(0===a.length)return;let u=function(e,t){let n={method:t?.formMethod||e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===n.method.toUpperCase()){let t=function(e){let t=new URLSearchParams;for(let[n,r]of[...new FormData(e).entries()])t.append(n,r.toString());return t.toString()}(e);t&&(n.url+=(~n.url.indexOf("?")?"&":"?")+t)}else n.body=new FormData(e);return n}(n,e instanceof SubmitEvent?e.submitter:null),[c,l,h]=i();e.preventDefault(),d(a,n,u,c).then(async e=>{if(e){for(let e of s)await e(n);f(u).then(l,h).catch(()=>{}).then(()=>{for(let e of o)e(n)})}else n.submit()},e=>{n.submit(),setTimeout(()=>{throw e})})}async function d(e,t,n,r){let o=!1;for(let s of e){let[e,a]=i(),u=()=>(o=!0,a(),r),c={text:u,json:()=>(n.headers.set("Accept","application/json"),u()),html:()=>(n.headers.set("Accept","text/html"),u())};await Promise.race([e,s(t,c,n)])}return o}async function f(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=JSON.parse(this.text);return delete this.json,this.json=e,this.json},get html(){return delete this.html,this.html=function(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}(document,this.text),this.html}};if(n.text=await t.text(),t.ok)return n;throw new ErrorWithResponse("request failed",n)}},97797:(e,t,n)=>{function r(){if(!(this instanceof r))return new r;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{h:()=>A,A:()=>O,on:()=>k});var i,o=window.document.documentElement,s=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.oMatchesSelector||o.msMatchesSelector;r.prototype.matchesSelector=function(e,t){return s.call(e,t)},r.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},r.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var u=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(u))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);else if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),r.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},i="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var l=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var n,r,i,o,s,a,u=(e=e.slice(0).concat(e.default)).length,c=t,d=[];do if(l.exec(""),(i=l.exec(c))&&(c=i[3],i[2]||!c)){for(n=0;n<u;n++)if(s=(a=e[n]).selector(i[1])){for(r=d.length,o=!1;r--;)if(d[r].index===a&&d[r].key===s){o=!0;break}o||d.push({index:a,key:s});break}}while(i)return d}function f(e,t){return e.id-t.id}r.prototype.logDefaultIndexUsed=function(){},r.prototype.add=function(e,t){var n,r,o,s,a,u,c,l,f=this.activeIndexes,h=this.selectors,p=this.selectorObjects;if("string"==typeof e){for(r=0,p[(n={id:this.uid++,selector:e,data:t}).id]=n,c=d(this.indexes,e);r<c.length;r++)s=(l=c[r]).key,(a=function(e,t){var n,r,i;for(n=0,r=e.length;n<r;n++)if(i=e[n],t.isPrototypeOf(i))return i}(f,o=l.index))||((a=Object.create(o)).map=new i,f.push(a)),o===this.indexes.default&&this.logDefaultIndexUsed(n),(u=a.map.get(s))||(u=[],a.map.set(s,u)),u.push(n);this.size++,h.push(e)}},r.prototype.remove=function(e,t){if("string"==typeof e){var n,r,i,o,s,a,u,c,l=this.activeIndexes,f=this.selectors=[],h=this.selectorObjects,p={},m=1==arguments.length;for(i=0,n=d(this.indexes,e);i<n.length;i++)for(r=n[i],o=l.length;o--;)if(a=l[o],r.index.isPrototypeOf(a)){if(u=a.map.get(r.key))for(s=u.length;s--;)(c=u[s]).selector===e&&(m||c.data===t)&&(u.splice(s,1),p[c.id]=!0);break}for(i in p)delete h[i],this.size--;for(i in h)f.push(h[i].selector)}},r.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,r,i,o,s,a,u,c={},l=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,r=d.length;t<r;t++)for(n=0,o=d[t],i=(s=this.matches(o)).length;n<i;n++)c[(u=s[n]).id]?a=c[u.id]:(a={id:u.id,selector:u.selector,data:u.data,elements:[]},c[u.id]=a,l.push(a)),a.elements.push(o);return l.sort(f)},r.prototype.matches=function(e){if(!e)return[];var t,n,r,i,o,s,a,u,c,l,d,h=this.activeIndexes,p={},m=[];for(t=0,i=h.length;t<i;t++)if(u=(a=h[t]).element(e)){for(n=0,o=u.length;n<o;n++)if(c=a.map.get(u[n]))for(r=0,s=c.length;r<s;r++)!p[d=(l=c[r]).id]&&this.matchesSelector(e,l.selector)&&(p[d]=!0,m.push(l))}return m.sort(f)};var h={},p={},m=new WeakMap,g=new WeakMap,v=new WeakMap,y=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function b(e,t,n){var r=e[t];return e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)},e}function w(){m.set(this,!0)}function x(){m.set(this,!0),g.set(this,!0)}function E(){return v.get(this)||null}function S(e,t){y&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||y.get})}function j(e){if(function(e){try{return e.eventPhase,!0}catch(e){return!1}}(e)){var t=(1===e.eventPhase?p:h)[e.type];if(t){var n=function(e,t,n){var r=[],i=t;do{if(1!==i.nodeType)break;var o=e.matches(i);if(o.length){var s={node:i,observers:o};n?r.unshift(s):r.push(s)}}while(i=i.parentElement)return r}(t,e.target,1===e.eventPhase);if(n.length){b(e,"stopPropagation",w),b(e,"stopImmediatePropagation",x),S(e,E);for(var r=0,i=n.length;r<i&&!m.get(e);r++){var o=n[r];v.set(e,o.node);for(var s=0,a=o.observers.length;s<a&&!g.get(e);s++)o.observers[s].data.call(o.node,e)}v.delete(e),S(e)}}}}function k(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!!i.capture,s=o?p:h,a=s[e];a||(a=new r,s[e]=a,document.addEventListener(e,j,o)),a.add(t,n)}function O(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!r.capture,o=i?p:h,s=o[e];s&&(s.remove(t,n),s.size||(delete o[e],document.removeEventListener(e,j,i)))}function A(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},74043:(e,t,n)=>{function r(e){let t="==".slice(0,(4-e.length%4)%4),n=atob(e.replace(/-/g,"+").replace(/_/g,"/")+t),r=new ArrayBuffer(n.length),i=new Uint8Array(r);for(let e=0;e<n.length;e++)i[e]=n.charCodeAt(e);return r}function i(e){let t=new Uint8Array(e),n="";for(let e of t)n+=String.fromCharCode(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}n.d(t,{$j:()=>w,Jt:()=>E,PG:()=>y,d5:()=>b,vt:()=>x});var o="copy",s="convert";function a(e,t,n){if(t===o)return n;if(t===s)return e(n);if(t instanceof Array)return n.map(n=>a(e,t[0],n));if(t instanceof Object){let r={};for(let[i,o]of Object.entries(t)){if(o.derive){let e=o.derive(n);void 0!==e&&(n[i]=e)}if(!(i in n)){if(o.required)throw Error(`Missing key: ${i}`);continue}if(null==n[i]){r[i]=null;continue}r[i]=a(e,o.schema,n[i])}return r}}function u(e,t){return{required:!0,schema:e,derive:t}}function c(e){return{required:!0,schema:e}}function l(e){return{required:!1,schema:e}}var d={type:c(o),id:c(s),transports:l(o)},f={appid:l(o),appidExclude:l(o),credProps:l(o)},h={appid:l(o),appidExclude:l(o),credProps:l(o)},p={publicKey:c({rp:c(o),user:c({id:c(s),name:c(o),displayName:c(o)}),challenge:c(s),pubKeyCredParams:c(o),timeout:l(o),excludeCredentials:l([d]),authenticatorSelection:l(o),attestation:l(o),extensions:l(f)}),signal:l(o)},m={type:c(o),id:c(o),rawId:c(s),authenticatorAttachment:l(o),response:c({clientDataJSON:c(s),attestationObject:c(s),transports:u(o,e=>{var t;return(null==(t=e.getTransports)?void 0:t.call(e))||[]})}),clientExtensionResults:u(h,e=>e.getClientExtensionResults())},g={mediation:l(o),publicKey:c({challenge:c(s),timeout:l(o),rpId:l(o),allowCredentials:l([d]),userVerification:l(o),extensions:l(f)}),signal:l(o)},v={type:c(o),id:c(o),rawId:c(s),authenticatorAttachment:l(o),response:c({clientDataJSON:c(s),authenticatorData:c(s),signature:c(s),userHandle:c(s)}),clientExtensionResults:u(h,e=>e.getClientExtensionResults())};function y(e){return a(r,p,e)}function b(e){return a(r,g,e)}function w(){return!!(navigator.credentials&&navigator.credentials.create&&navigator.credentials.get&&window.PublicKeyCredential)}async function x(e){let t=await navigator.credentials.create(e);return t.toJSON=()=>a(i,m,t),t}async function E(e){let t=await navigator.credentials.get(e);return t.toJSON=()=>a(i,v,t),t}}}]);
//# sourceMappingURL=vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-893f9f-557cf5fbe3c7.js.map