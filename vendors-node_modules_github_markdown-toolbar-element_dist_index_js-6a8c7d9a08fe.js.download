"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_markdown-toolbar-element_dist_index_js"],{78143:(e,t,n)=>{n.r(t),n.d(t,{default:()=>v});var o,l,i=function(e,t,n,o){if("a"===n&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?o:"a"===n?o.call(e):o?o.value:t.get(e)};let r=["[data-md-button]","md-header","md-bold","md-italic","md-quote","md-code","md-link","md-image","md-unordered-list","md-ordered-list","md-task-list","md-mention","md-ref","md-strikethrough"];function s(e){let t=[];for(let n of e.querySelectorAll(r.join(", ")))n.hidden||n.offsetWidth<=0&&n.offsetHeight<=0||n.closest("markdown-toolbar")===e&&t.push(n);return t}function d(e){return function(t){(" "===t.key||"Enter"===t.key)&&e(t)}}let a=new WeakMap,u={"header-1":{prefix:"# "},"header-2":{prefix:"## "},"header-3":{prefix:"### "},"header-4":{prefix:"#### "},"header-5":{prefix:"##### "},"header-6":{prefix:"###### "},bold:{prefix:"**",suffix:"**",trimFirst:!0},italic:{prefix:"_",suffix:"_",trimFirst:!0},quote:{prefix:"> ",multiline:!0,surroundWithNewlines:!0},code:{prefix:"`",suffix:"`",blockPrefix:"```",blockSuffix:"```"},link:{prefix:"[",suffix:"](url)",replaceNext:"url",scanFor:"https?://"},image:{prefix:"![",suffix:"](url)",replaceNext:"url",scanFor:"https?://"},"unordered-list":{prefix:"- ",multiline:!0,unorderedList:!0},"ordered-list":{prefix:"1. ",multiline:!0,orderedList:!0},"task-list":{prefix:"- [ ] ",multiline:!0,surroundWithNewlines:!0},mention:{prefix:"@",prefixSpace:!0},ref:{prefix:"#",prefixSpace:!0},strikethrough:{prefix:"~~",suffix:"~~",trimFirst:!0}};let MarkdownButtonElement=class MarkdownButtonElement extends HTMLElement{constructor(){super();let e=e=>{let t=a.get(this);t&&(e.preventDefault(),B(this,t))};this.addEventListener("keydown",d(e)),this.addEventListener("click",e)}connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","button")}click(){let e=a.get(this);e&&B(this,e)}};let MarkdownHeaderButtonElement=class MarkdownHeaderButtonElement extends MarkdownButtonElement{constructor(){super(...arguments),o.add(this)}connectedCallback(){let e=parseInt(this.getAttribute("level")||"3",10);i(this,o,"m",l).call(this,e)}static get observedAttributes(){return["level"]}attributeChangedCallback(e,t,n){if("level"!==e)return;let r=parseInt(n||"3",10);i(this,o,"m",l).call(this,r)}};o=new WeakSet,l=function(e){if(e<1||e>6)return;let t=`${"#".repeat(e)} `;a.set(this,{prefix:t})},window.customElements.get("md-header")||(window.MarkdownHeaderButtonElement=MarkdownHeaderButtonElement,window.customElements.define("md-header",MarkdownHeaderButtonElement));let MarkdownBoldButtonElement=class MarkdownBoldButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"**",suffix:"**",trimFirst:!0})}};window.customElements.get("md-bold")||(window.MarkdownBoldButtonElement=MarkdownBoldButtonElement,window.customElements.define("md-bold",MarkdownBoldButtonElement));let MarkdownItalicButtonElement=class MarkdownItalicButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"_",suffix:"_",trimFirst:!0})}};window.customElements.get("md-italic")||(window.MarkdownItalicButtonElement=MarkdownItalicButtonElement,window.customElements.define("md-italic",MarkdownItalicButtonElement));let MarkdownQuoteButtonElement=class MarkdownQuoteButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"> ",multiline:!0,surroundWithNewlines:!0})}};window.customElements.get("md-quote")||(window.MarkdownQuoteButtonElement=MarkdownQuoteButtonElement,window.customElements.define("md-quote",MarkdownQuoteButtonElement));let MarkdownCodeButtonElement=class MarkdownCodeButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"`",suffix:"`",blockPrefix:"```",blockSuffix:"```"})}};window.customElements.get("md-code")||(window.MarkdownCodeButtonElement=MarkdownCodeButtonElement,window.customElements.define("md-code",MarkdownCodeButtonElement));let MarkdownLinkButtonElement=class MarkdownLinkButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"[",suffix:"](url)",replaceNext:"url",scanFor:"https?://"})}};window.customElements.get("md-link")||(window.MarkdownLinkButtonElement=MarkdownLinkButtonElement,window.customElements.define("md-link",MarkdownLinkButtonElement));let MarkdownImageButtonElement=class MarkdownImageButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"![",suffix:"](url)",replaceNext:"url",scanFor:"https?://"})}};window.customElements.get("md-image")||(window.MarkdownImageButtonElement=MarkdownImageButtonElement,window.customElements.define("md-image",MarkdownImageButtonElement));let MarkdownUnorderedListButtonElement=class MarkdownUnorderedListButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"- ",multiline:!0,unorderedList:!0})}};window.customElements.get("md-unordered-list")||(window.MarkdownUnorderedListButtonElement=MarkdownUnorderedListButtonElement,window.customElements.define("md-unordered-list",MarkdownUnorderedListButtonElement));let MarkdownOrderedListButtonElement=class MarkdownOrderedListButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"1. ",multiline:!0,orderedList:!0})}};window.customElements.get("md-ordered-list")||(window.MarkdownOrderedListButtonElement=MarkdownOrderedListButtonElement,window.customElements.define("md-ordered-list",MarkdownOrderedListButtonElement));let MarkdownTaskListButtonElement=class MarkdownTaskListButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"- [ ] ",multiline:!0,surroundWithNewlines:!0})}};window.customElements.get("md-task-list")||(window.MarkdownTaskListButtonElement=MarkdownTaskListButtonElement,window.customElements.define("md-task-list",MarkdownTaskListButtonElement));let MarkdownMentionButtonElement=class MarkdownMentionButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"@",prefixSpace:!0})}};window.customElements.get("md-mention")||(window.MarkdownMentionButtonElement=MarkdownMentionButtonElement,window.customElements.define("md-mention",MarkdownMentionButtonElement));let MarkdownRefButtonElement=class MarkdownRefButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"#",prefixSpace:!0})}};window.customElements.get("md-ref")||(window.MarkdownRefButtonElement=MarkdownRefButtonElement,window.customElements.define("md-ref",MarkdownRefButtonElement));let MarkdownStrikethroughButtonElement=class MarkdownStrikethroughButtonElement extends MarkdownButtonElement{connectedCallback(){a.set(this,{prefix:"~~",suffix:"~~",trimFirst:!0})}};function c(e){let{target:t,currentTarget:n}=e;if(!(t instanceof Element))return;let o=t.closest("[data-md-button]");if(!o||o.closest("markdown-toolbar")!==n)return;let l=u[o.getAttribute("data-md-button")];l&&(e.preventDefault(),B(t,l))}function m(e){e.addEventListener("keydown",h),e.setAttribute("tabindex","0"),e.addEventListener("focus",f,{once:!0})}function w(e){e.removeEventListener("keydown",h),e.removeAttribute("tabindex"),e.removeEventListener("focus",f)}window.customElements.get("md-strikethrough")||(window.MarkdownStrikethroughButtonElement=MarkdownStrikethroughButtonElement,window.customElements.define("md-strikethrough",MarkdownStrikethroughButtonElement));let MarkdownToolbarElement=class MarkdownToolbarElement extends HTMLElement{connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","toolbar"),this.hasAttribute("data-no-focus")||m(this),this.addEventListener("keydown",d(c)),this.addEventListener("click",c)}attributeChangedCallback(e,t,n){"data-no-focus"===e&&(null===n?m(this):w(this))}disconnectedCallback(){w(this)}get field(){let e,t=this.getAttribute("for");if(!t)return null;let n="getRootNode"in this?this.getRootNode():document;return(n instanceof Document||n instanceof ShadowRoot)&&(e=n.getElementById(t)),e instanceof HTMLTextAreaElement?e:null}};function f({target:e}){if(!(e instanceof Element))return;e.removeAttribute("tabindex");let t="0";for(let n of s(e))n.setAttribute("tabindex",t),"0"===t&&(n.focus(),t="-1")}function h(e){let t=e.key;if("ArrowRight"!==t&&"ArrowLeft"!==t&&"Home"!==t&&"End"!==t)return;let n=e.currentTarget;if(!(n instanceof HTMLElement))return;let o=s(n),l=o.indexOf(e.target),i=o.length;if(-1===l)return;let r=0;"ArrowLeft"===t&&(r=l-1),"ArrowRight"===t&&(r=l+1),"End"===t&&(r=i-1),r<0&&(r=i-1),r>i-1&&(r=0);for(let e=0;e<i;e+=1)o[e].setAttribute("tabindex",e===r?"0":"-1");e.preventDefault(),o[r].focus()}function E(e){return e.trim().split(`
`).length>1}function k(e,t){return Array(t+1).join(e)}MarkdownToolbarElement.observedAttributes=["data-no-focus"],window.customElements.get("markdown-toolbar")||(window.MarkdownToolbarElement=MarkdownToolbarElement,window.customElements.define("markdown-toolbar",MarkdownToolbarElement));let x=null;function g(e){let t,n,o=e.value.slice(0,e.selectionStart),l=e.value.slice(e.selectionEnd),i=o.match(/\n*$/),r=l.match(/^\n*/),s=i?i[0].length:0,d=r?r[0].length:0;return o.match(/\S/)&&s<2&&(t=k(`
`,2-s)),l.match(/\S/)&&d<2&&(n=k(`
`,2-d)),null==t&&(t=""),null==n&&(n=""),{newlinesToAppend:t,newlinesToPrepend:n}}function p(e){let t=e.split(`
`),n=/^\d+\.\s+/,o=t.every(e=>n.test(e)),l=t;return o&&(l=t.map(e=>e.replace(n,""))),{text:l.join(`
`),processed:o}}function M(e){let t=e.split(`
`),n=t.every(e=>e.startsWith("- ")),o=t;return n&&(o=t.map(e=>e.slice(2,e.length))),{text:o.join(`
`),processed:n}}function b(e,t){return t?"- ":`${e+1}. `}function B(e,t){let n=e.closest("markdown-toolbar");if(!(n instanceof MarkdownToolbarElement))return;let o=Object.assign(Object.assign({},{prefix:"",suffix:"",blockPrefix:"",blockSuffix:"",multiline:!1,replaceNext:"",prefixSpace:!1,scanFor:"",surroundWithNewlines:!1,orderedList:!1,unorderedList:!1,trimFirst:!1}),t),l=n.field;if(l){let e;l.focus();let t=l.value.slice(l.selectionStart,l.selectionEnd);e=o.orderedList||o.unorderedList?function(e,t){let n,o,l,i=e.selectionStart===e.selectionEnd,r=e.selectionStart,s=e.selectionEnd,d=e.value.split(`
`),a=0;for(let t=0;t<d.length;t++){let n=d[t].length+1;e.selectionStart>=a&&e.selectionStart<a+n&&(e.selectionStart=a),e.selectionEnd>=a&&e.selectionEnd<a+n&&(e.selectionEnd=a+n-1),a+=n}let u=e.value.slice(e.selectionStart,e.selectionEnd),[c,m,w]=(l=t.orderedList?(n=M((o=p(u)).text)).text:(n=p((o=M(u)).text)).text,[o,n,l]),f=w.split(`
`).map((e,n)=>`${b(n,t.unorderedList)}${e}`),h=f.reduce((e,n,o)=>e+b(o,t.unorderedList).length,0),E=f.reduce((e,n,o)=>e+b(o,!t.unorderedList).length,0);if(c.processed)return i?s=r=Math.max(r-b(0,t.unorderedList).length,0):(r=e.selectionStart,s=e.selectionEnd-h),{text:w,selectionStart:r,selectionEnd:s};let{newlinesToAppend:k,newlinesToPrepend:x}=g(e),B=k+f.join(`
`)+x;return i?s=r=Math.max(r+b(0,t.unorderedList).length+k.length,0):m.processed?(r=Math.max(e.selectionStart+k.length,0),s=e.selectionEnd+k.length+h-E):(r=Math.max(e.selectionStart+k.length,0),s=e.selectionEnd+k.length+h),{text:B,selectionStart:r,selectionEnd:s}}(l,o):o.multiline&&E(t)?function(e,t){let{prefix:n,suffix:o,surroundWithNewlines:l}=t,i=e.value.slice(e.selectionStart,e.selectionEnd),r=e.selectionStart,s=e.selectionEnd,d=i.split(`
`);if(d.every(e=>e.startsWith(n)&&e.endsWith(o)))s=r+(i=d.map(e=>e.slice(n.length,e.length-o.length)).join(`
`)).length;else if(i=d.map(e=>n+e+o).join(`
`),l){let{newlinesToAppend:t,newlinesToPrepend:n}=g(e);r+=t.length,s=r+i.length,i=t+i+n}return{text:i,selectionStart:r,selectionEnd:s}}(l,o):function(e,t){let n,o,{prefix:l,suffix:i,blockPrefix:r,blockSuffix:s,replaceNext:d,prefixSpace:a,scanFor:u,surroundWithNewlines:c}=t,m=e.selectionStart,w=e.selectionEnd,f=e.value.slice(e.selectionStart,e.selectionEnd),h=E(f)&&r.length>0?`${r}
`:l,k=E(f)&&s.length>0?`
${s}`:i;if(a){let t=e.value[e.selectionStart-1];0===e.selectionStart||null==t||t.match(/\s/)||(h=` ${h}`)}f=function(e,t,n,o=!1){if(e.selectionStart===e.selectionEnd)e.selectionStart=function(e,t){let n=t;for(;e[n]&&null!=e[n-1]&&!e[n-1].match(/\s/);)n--;return n}(e.value,e.selectionStart),e.selectionEnd=function(e,t,n){let o=t,l=n?/\n/:/\s/;for(;e[o]&&!e[o].match(l);)o++;return o}(e.value,e.selectionEnd,o);else{let o=e.selectionStart-t.length,l=e.selectionEnd+n.length,i=e.value.slice(o,e.selectionStart)===t,r=e.value.slice(e.selectionEnd,l)===n;i&&r&&(e.selectionStart=o,e.selectionEnd=l)}return e.value.slice(e.selectionStart,e.selectionEnd)}(e,h,k,t.multiline);let x=e.selectionStart,p=e.selectionEnd,M=d.length>0&&k.indexOf(d)>-1&&f.length>0;if(c){let t=g(e);n=t.newlinesToAppend,o=t.newlinesToPrepend,h=n+l,k+=o}if(f.startsWith(h)&&f.endsWith(k)){let e=f.slice(h.length,f.length-k.length);if(m===w){let t=m-h.length;x=p=t=Math.min(t=Math.max(t,x),x+e.length)}else p=x+e.length;return{text:e,selectionStart:x,selectionEnd:p}}if(M)if(u.length>0&&f.match(u))return{text:h+(k=k.replace(d,f)),selectionStart:x=p=x+h.length,selectionEnd:p};else{let e=h+f+k;return p=(x=x+h.length+f.length+k.indexOf(d))+d.length,{text:e,selectionStart:x,selectionEnd:p}}{let e=h+f+k;x=m+h.length,p=w+h.length;let n=f.match(/^\s*|\s*$/g);if(t.trimFirst&&n){let t=n[0]||"",o=n[1]||"";e=t+h+f.trim()+k+o,x+=t.length,p-=o.length}return{text:e,selectionStart:x,selectionEnd:p}}}(l,o),function(e,{text:t,selectionStart:n,selectionEnd:o}){let l=e.selectionStart,i=e.value.slice(0,l),r=e.value.slice(e.selectionEnd);if(null===x||!0===x){e.contentEditable="true";try{x=document.execCommand("insertText",!1,t)}catch(e){x=!1}e.contentEditable="false"}if(x&&!e.value.slice(0,e.selectionStart).endsWith(t)&&(x=!1),!x){try{document.execCommand("ms-beginUndoUnit")}catch(e){}e.value=i+t+r;try{document.execCommand("ms-endUndoUnit")}catch(e){}e.dispatchEvent(new CustomEvent("input",{bubbles:!0,cancelable:!0}))}null!=n&&null!=o?e.setSelectionRange(n,o):e.setSelectionRange(l,e.selectionEnd)}(l,e)}}let v=MarkdownToolbarElement}}]);
//# sourceMappingURL=vendors-node_modules_github_markdown-toolbar-element_dist_index_js-12d65bdaa720.js.map