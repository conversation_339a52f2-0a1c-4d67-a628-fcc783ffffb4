"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-3eebbd"],{62044:(e,t,n)=>{n.d(t,{A:()=>o});let FilterInputElement=class FilterInputElement extends HTMLElement{constructor(){super(),this.currentQuery=null,this.filter=null,this.debounceInputChange=function(e){let t;return function(){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e()},300)}}(()=>r(this,!0)),this.boundFilterResults=()=>{r(this,!1)}}static get observedAttributes(){return["aria-owns"]}attributeChangedCallback(e,t){t&&"aria-owns"===e&&r(this,!1)}connectedCallback(){let e=this.input;e&&(e.setAttribute("autocomplete","off"),e.setAttribute("spellcheck","false"),e.addEventListener("focus",this.boundFilterResults),e.addEventListener("change",this.boundFilterResults),e.addEventListener("input",this.debounceInputChange))}disconnectedCallback(){let e=this.input;e&&(e.removeEventListener("focus",this.boundFilterResults),e.removeEventListener("change",this.boundFilterResults),e.removeEventListener("input",this.debounceInputChange))}get input(){let e=this.querySelector("input");return e instanceof HTMLInputElement?e:null}reset(){let e=this.input;e&&(e.value="",e.dispatchEvent(new Event("change",{bubbles:!0})))}};async function r(e,t=!1){let n=e.input;if(!n)return;let o=n.value.trim(),a=e.getAttribute("aria-owns");if(!a)return;let l=document.getElementById(a);if(!l)return;let s=l.hasAttribute("data-filter-list")?l:l.querySelector("[data-filter-list]");if(!s||(e.dispatchEvent(new CustomEvent("filter-input-start",{bubbles:!0})),t&&e.currentQuery===o))return;e.currentQuery=o;let c=e.filter||i,u=s.childElementCount,d=0,h=!1;for(let e of Array.from(s.children)){var f;if(!(e instanceof HTMLElement))continue;let t=(((f=e).querySelector("[data-filter-item-text]")||f).textContent||"").trim(),n=c(e,t,o);!0===n.hideNew&&(h=n.hideNew),e.hidden=!n.match,n.match&&d++}let p=l.querySelector("[data-filter-new-item]"),m=!!p&&o.length>0&&!h;p instanceof HTMLElement&&(p.hidden=!m,m&&function(e,t){let n=e.querySelector("[data-filter-new-item-text]");n&&(n.textContent=t);let r=e.querySelector("[data-filter-new-item-value]");(r instanceof HTMLInputElement||r instanceof HTMLButtonElement)&&(r.value=t)}(p,o)),function(e,t){let n=e.querySelector("[data-filter-empty-state]");n instanceof HTMLElement&&(n.hidden=t)}(l,d>0||m),e.dispatchEvent(new CustomEvent("filter-input-updated",{bubbles:!0,detail:{count:d,total:u}}))}function i(e,t,n){return{match:-1!==t.toLowerCase().indexOf(n.toLowerCase()),hideNew:t===n}}let o=FilterInputElement;window.customElements.get("filter-input")||(window.FilterInputElement=FilterInputElement,window.customElements.define("filter-input",FilterInputElement))},27552:(e,t,n)=>{n.d(t,{A:()=>a});let r=new WeakMap;let RemoteInputElement=class RemoteInputElement extends HTMLElement{constructor(){super();let e=i.bind(null,this,!0),t={currentQuery:null,oninput:function(e){let t;return function(n){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e(n)},300)}}(t=>e(t)),fetch:e,controller:null};r.set(this,t)}static get observedAttributes(){return["src"]}attributeChangedCallback(e,t){t&&"src"===e&&i(this,!1)}connectedCallback(){let e=this.input;if(!e)return;e.setAttribute("autocomplete","off"),e.setAttribute("spellcheck","false");let t=r.get(this);t&&(e.addEventListener("focus",t.fetch),e.addEventListener("change",t.fetch),e.addEventListener("input",t.oninput))}disconnectedCallback(){let e=this.input;if(!e)return;let t=r.get(this);t&&(e.removeEventListener("focus",t.fetch),e.removeEventListener("change",t.fetch),e.removeEventListener("input",t.oninput))}get input(){let e=this.querySelector("input, textarea");return e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement?e:null}get src(){return this.getAttribute("src")||""}set src(e){this.setAttribute("src",e)}};async function i(e,t,n){let i,a=e.input;if(!a)return;let l=r.get(e);if(!l)return;let s=a.value;if(t&&l.currentQuery===s)return;l.currentQuery=s;let c=e.src;if(!c)return;let u=document.getElementById(e.getAttribute("aria-owns")||"");if(!u)return;let d=new URL(c,window.location.href),h=new URLSearchParams(d.search);h.append(e.getAttribute("param")||"q",s),d.search=h.toString(),l.controller?l.controller.abort():(e.dispatchEvent(new CustomEvent("loadstart")),e.setAttribute("loading","")),l.controller="AbortController"in window?new AbortController:{signal:null,abort(){}};let f="";try{i=await o(e,d.toString(),{signal:l.controller.signal,credentials:"same-origin",headers:{accept:"text/fragment+html"}}),f=await i.text(),e.removeAttribute("loading"),l.controller=null}catch(t){t instanceof Error&&"AbortError"!==t.name&&(e.removeAttribute("loading"),l.controller=null);return}i&&i.ok?(u.innerHTML=f,e.dispatchEvent(new CustomEvent("remote-input-success",{bubbles:!0,detail:{eventType:n?n.type:void 0}}))):e.dispatchEvent(new CustomEvent("remote-input-error",{bubbles:!0}))}async function o(e,t,n){try{let r=await fetch(t,n);return e.dispatchEvent(new CustomEvent("load")),e.dispatchEvent(new CustomEvent("loadend")),r}catch(t){throw t instanceof Error&&(null==t?void 0:t.name)!=="AbortError"&&(e.dispatchEvent(new CustomEvent("error")),e.dispatchEvent(new CustomEvent("loadend"))),t}}let a=RemoteInputElement;window.customElements.get("remote-input")||(window.RemoteInputElement=RemoteInputElement,window.customElements.define("remote-input",RemoteInputElement))},91385:(e,t,n)=>{n.d(t,{Xq:()=>l,ai:()=>i,fN:()=>a,qA:()=>s});var r=-1/0,i=1/0;function o(e,t,n,i){for(var o=e.length,a=t.length,l=e.toLowerCase(),s=t.toLowerCase(),c=function(e){for(var t=e.length,n=Array(t),r="/",i=0;i<t;i++){var o,a=e[i];"/"===r?n[i]=.9:"-"===r||"_"===r||" "===r?n[i]=.8:"."===r?n[i]=.6:(o=r).toLowerCase()===o&&a.toUpperCase()===a?n[i]=.7:n[i]=0,r=a}return n}(t,c),u=0;u<o;u++){n[u]=Array(a),i[u]=Array(a);for(var d=r,h=u===o-1?-.005:-.01,f=0;f<a;f++)if(l[u]===s[f]){var p=r;u?f&&(p=Math.max(i[u-1][f-1]+c[f],n[u-1][f-1]+1)):p=-.005*f+c[f],n[u][f]=p,i[u][f]=d=Math.max(p,d+h)}else n[u][f]=r,i[u][f]=d+=h}}function a(e,t){var n=e.length,a=t.length;if(!n||!a)return r;if(n===a)return i;if(a>1024)return r;var l=Array(n),s=Array(n);return o(e,t,l,s),s[n-1][a-1]}function l(e,t){var n=e.length,i=t.length,a=Array(n);if(!n||!i)return a;if(n===i){for(var l=0;l<n;l++)a[l]=l;return a}if(i>1024)return a;var s=Array(n),c=Array(n);o(e,t,s,c);for(var u=!1,l=n-1,d=i-1;l>=0;l--)for(;d>=0;d--)if(s[l][d]!==r&&(u||s[l][d]===c[l][d])){u=l&&d&&c[l][d]===s[l-1][d-1]+1,a[l]=d--;break}return a}function s(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var n=e.length,r=0,i=0;r<n;r+=1)if(0===(i=t.indexOf(e[r],i)+1))return!1;return!0}},39595:(e,t,n)=>{let r;n.d(t,{CF:()=>m,p_:()=>R,FB:()=>d,Se:()=>L,aC:()=>x,zV:()=>M});let i=new WeakSet,o=new WeakMap;function a(e=document){if(o.has(e))return o.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)u(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&l(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let r={get closed(){return t},unsubscribe(){t=!0,o.delete(e),n.disconnect()}};return o.set(e,r),r}function l(e){for(let t of e.querySelectorAll("[data-action]"))u(t);e instanceof Element&&e.hasAttribute("data-action")&&u(e)}function s(e){let t=e.currentTarget;for(let n of c(t))if(e.type===n.type){let r=t.closest(n.tag);i.has(r)&&"function"==typeof r[n.method]&&r[n.method](e);let o=t.getRootNode();if(o instanceof ShadowRoot&&i.has(o.host)&&o.host.matches(n.tag)){let t=o.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*c(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function u(e){for(let t of c(e))e.addEventListener(t.type,s)}function d(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let r of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!r.closest(n))return r}for(let r of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(r.closest(n)===e)return r}let h=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),f=(e,t="property")=>{let n=h(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n},p="attr";function m(e,t){T(e,p).add(t)}let b=new WeakSet;function g(e,t){if(b.has(e))return;b.add(e);let n=Object.getPrototypeOf(e),r=n?.constructor?.attrPrefix??"data-";for(let i of(t||(t=T(n,p)),t)){let t=e[i],n=f(`${r}${i}`),o={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?o={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(o={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,i,o),i in e&&!e.hasAttribute(n)&&o.set.call(e,t)}}let w=new Map,v=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),y=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},r=()=>t.abort();document.addEventListener("mousedown",r,n),document.addEventListener("touchstart",r,n),document.addEventListener("keydown",r,n),document.addEventListener("pointerdown",r,n)}),E={ready:()=>v,firstInteraction:()=>y,visible:e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let r of e)if(r.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)})},C=new WeakMap;function A(e){cancelAnimationFrame(C.get(e)||0),C.set(e,requestAnimationFrame(()=>{for(let t of w.keys()){let n=e instanceof Element&&e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let r=n?.getAttribute("data-load-on")||"ready",i=r in E?E[r]:E.ready;for(let e of w.get(t)||[])i(t).then(e);w.delete(t),C.delete(e)}}}))}function L(e,t){for(let[n,r]of("string"==typeof e&&t&&(e={[e]:t}),Object.entries(e)))w.has(n)||w.set(n,new Set),w.get(n).add(r);k(document)}function k(e){r||(r=new MutationObserver(e=>{if(w.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&A(e)})),A(e),r.observe(e,{subtree:!0,childList:!0})}let S=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let r=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,r)};let i=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,r){t.attributeChangedCallback(this,e,n,r,i)};let o=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,o)},set(e){o=e}}),function(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",r=e=>f(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...T(e.prototype,p)].map(r).concat(t),set(e){t=e}})}(e),function(e){let t=h(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}}(e)}observedAttributes(e,t){return t}connectedCallback(e,t){var n,r;for(let t of(e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),e.querySelectorAll("template[data-shadowroot]")))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0));(g(e),i.add(e),e.shadowRoot&&(l(r=e.shadowRoot),a(r)),l(e),a(e.ownerDocument),t?.call(e),e.shadowRoot)&&(l(n=e.shadowRoot),a(n),k(e.shadowRoot))}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,r,i){g(e),"data-catalyst"!==t&&i&&i.call(e,t,n,r)}};function T(e,t){if(!Object.prototype.hasOwnProperty.call(e,S)){let t=e[S],n=e[S]=new Map;if(t)for(let[e,r]of t)n.set(e,new Set(r))}let n=e[S];return n.has(t)||n.set(t,new Set),n.get(t)}function x(e,t){T(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return d(this,t)}})}function M(e,t){T(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){let e=this.tagName.toLowerCase(),n=[];if(this.shadowRoot)for(let r of this.shadowRoot.querySelectorAll(`[data-targets~="${e}.${t}"]`))r.closest(e)||n.push(r);for(let r of this.querySelectorAll(`[data-targets~="${e}.${t}"]`))r.closest(e)===this&&n.push(r);return n}})}function R(e){new CatalystDelegate(e)}},46493:(e,t,n)=>{n.d(t,{Cj:()=>U,iP:()=>Q});let r={Less:"less",Equal:"equal",Greater:"greater"};var i,o,a,l,s,c,u,d,h,f,p,m,b,g,w,v,y,E=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},C=(e,t,n)=>(E(e,t,"read from private field"),n?n.call(e):t.get(e)),A=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},L=(e,t,n,r)=>(E(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),k=(e,t,n)=>(E(e,t,"access private method"),n);let MinHeap=class MinHeap{constructor({compareFn:e}){A(this,a),A(this,s),A(this,i,void 0),A(this,o,void 0),L(this,i,e),L(this,o,[])}insert(e){C(this,o).push(e),k(this,s,c).call(this)}pop(){let e=C(this,o)[0];return C(this,o)[C(this,o).length-1]&&(C(this,o)[0]=C(this,o)[C(this,o).length-1],C(this,o).pop()),k(this,a,l).call(this),e}peek(){return C(this,o)[0]}delete(e){let t=C(this,o).indexOf(e);-1!==t&&(T(C(this,o),t,C(this,o).length-1),C(this,o).pop(),k(this,a,l).call(this))}clear(){L(this,o,[])}get size(){return C(this,o).length}};function S(e){return Math.floor((e-1)/2)}function T(e,t,n){let r=e[t];e[t]=e[n],e[n]=r}i=new WeakMap,o=new WeakMap,a=new WeakSet,l=function(){let e=0;for(;2*e+1<C(this,o).length;){var t,n;let a=2*e+1;if(2*e+2<C(this,o).length&&C(this,i).call(this,(t=C(this,o),t[2*e+2]),(n=C(this,o),n[2*e+1]))===r.Less&&(a=2*e+2),C(this,i).call(this,C(this,o)[e],C(this,o)[a])===r.Less)break;T(C(this,o),e,a),e=a}},s=new WeakSet,c=function(){var e;let t=C(this,o).length-1;for(;t>0&&C(this,i).call(this,C(this,o)[t],(e=C(this,o),e[S(t)]))===r.Less;)T(C(this,o),t,S(t)),t=S(t)};var x=Object.defineProperty,M=(e,t,n)=>t in e?x(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,R=(e,t,n)=>(M(e,"symbol"!=typeof t?t+"":t,n),n),I=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},q=(e,t,n)=>(I(e,t,"read from private field"),n?n.call(e):t.get(e)),P=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},$=(e,t,n,r)=>(I(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n);let Deferred=class Deferred{constructor(){R(this,u,"Deferred"),P(this,d,void 0),P(this,h,void 0),P(this,f,void 0),$(this,d,new Promise((e,t)=>{$(this,h,e),$(this,f,t)}))}then(e,t){return Promise.prototype.then.apply(q(this,d),[e,t])}catch(e){return Promise.prototype.catch.apply(q(this,d),[e])}finally(e){return Promise.prototype.finally.apply(q(this,d),[e])}resolve(e){q(this,h).call(this,e)}reject(e){q(this,f).call(this,e)}getPromise(){return q(this,d)}};u=Symbol.toStringTag,d=new WeakMap,h=new WeakMap,f=new WeakMap;var O=(e,t,n)=>{if(!t.has(e))throw TypeError("Cannot "+n)},W=(e,t,n)=>(O(e,t,"read from private field"),n?n.call(e):t.get(e)),F=(e,t,n)=>{if(t.has(e))throw TypeError("Cannot add the same private member more than once");t instanceof WeakSet?t.add(e):t.set(e,n)},H=(e,t,n,r)=>(O(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),N=(e,t,n)=>(O(e,t,"access private method"),n);let LiveRegionElement=class LiveRegionElement extends HTMLElement{constructor(){if(super(),F(this,g),F(this,v),F(this,p,void 0),F(this,m,void 0),F(this,b,void 0),!this.shadowRoot){let e=(j||((j=document.createElement("template")).innerHTML=_),j);this.attachShadow({mode:"open"}).appendChild(e.content.cloneNode(!0))}H(this,p,!1),H(this,b,null),H(this,m,new MinHeap({compareFn:D}))}get delay(){let e=this.getAttribute("delay");return e?parseInt(e,10):150}set delay(e){this.setAttribute("delay",`${e}`)}announce(e,t={}){let{delayMs:n,politeness:r="polite"}=t,i=Date.now(),o=new Deferred,a={deferred:o,politeness:r,contents:e,scheduled:void 0!==n?i+n:i};return W(this,m).insert(a),N(this,g,w).call(this),{...o.getPromise(),cancel:()=>{W(this,m).delete(a),o.resolve()}}}announceFromElement(e,t){var n;let r,i=(r="",(n=e).hasAttribute("aria-label")?r=n.getAttribute("aria-label"):n.innerText?r=n.innerText:n.textContent&&(r=n.textContent),r?r.trim():"");return""!==i?this.announce(i,t):{...Promise.resolve(),cancel:B}}getMessage(e="polite"){let t=this.shadowRoot?.getElementById(e);if(!t)throw Error("Unable to find container for message");return t.textContent}clear(){null!==W(this,b)&&(clearTimeout(W(this,b)),H(this,b,null)),W(this,m).clear()}};p=new WeakMap,m=new WeakMap,b=new WeakMap,g=new WeakSet,w=function(){if(W(this,p))return;let e=W(this,m).peek();if(!e)return;null!==W(this,b)&&(clearTimeout(W(this,b)),H(this,b,null));let t=Date.now();if(e.scheduled<=t){(e=W(this,m).pop())&&N(this,v,y).call(this,e),N(this,g,w).call(this);return}let n=e.scheduled-t;H(this,b,window.setTimeout(()=>{H(this,b,null),N(this,g,w).call(this)},n))},v=new WeakSet,y=function(e){H(this,p,!0);let{contents:t,deferred:n,politeness:r}=e,i=this.shadowRoot?.getElementById(r);if(!i)throw H(this,p,!1),Error(`Unable to find container for message. Expected a container with id="${r}"`);i.textContent===t?i.textContent=`${t}\xa0`:i.textContent=t,null!==W(this,b)&&clearTimeout(W(this,b)),n.resolve(),this.delay>0?H(this,b,window.setTimeout(()=>{H(this,b,null),H(this,p,!1),N(this,g,w).call(this)},this.delay)):(H(this,b,null),H(this,p,!1),N(this,g,w).call(this))};let j=null,_=`
<style>
:host {
  clip-path: inset(50%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
</style>
<div id="polite" aria-live="polite" aria-atomic="true"></div>
<div id="assertive" aria-live="assertive" aria-atomic="true"></div>
`;function D(e,t){return e.politeness===t.politeness?e.scheduled===t.scheduled?r.Equal:e.scheduled<t.scheduled?r.Less:r.Greater:"assertive"===e.politeness&&"assertive"!==t.politeness?r.Less:"assertive"!==e.politeness&&"assertive"===t.politeness?r.Greater:r.Equal}function B(){}function Q(e,t={}){let n=z(t.from);if(!n){n=document.createElement("live-region"),t.appendTo?t.appendTo.appendChild(n):G(t.from).appendChild(n);let r=!1,i=()=>{r=!0};return{...X(V).then(()=>{if(!r){let r=n.announce(e,t);return i=r.cancel,r}}),cancel:()=>{i()}}}return n.announce(e,t)}function U(e,t={}){let n=z(t.from);if(!n){n=document.createElement("live-region"),t.appendTo?t.appendTo.appendChild(n):G(t.from).appendChild(n);let r=!1,i=()=>{r=!0};return{...X(V).then(()=>{if(!r){let r=n.announceFromElement(e,t);return i=r.cancel,r}}),cancel:()=>{i()}}}return n.announceFromElement(e,t)}function z(e){let t=null;return null!==(t=e?function(e){let t=e.closest("dialog"),n=e;for(;(n=n.parentElement)&&(!t||t.contains(n));)for(let e of n.childNodes)if(e instanceof LiveRegionElement)return e;return null}(e):null)||null!==(t=G(e).querySelector("live-region"))?t:null}function G(e){let t=document.body;if(e){let n=e.closest("dialog");n&&(t=n)}return t}customElements.get("live-region")||customElements.define("live-region",LiveRegionElement);let V=150;function X(e){return new Promise(t=>{setTimeout(t,e)})}}}]);
//# sourceMappingURL=vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-3eebbd-312502ee77f9.js.map